#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة فحص التبعيات والمكتبات
Dependencies and Libraries Checker

هذا الملف يفحص جميع التبعيات المطلوبة للبوت
This file checks all required dependencies for the bot
"""

import sys
import importlib
import subprocess
import logging
from typing import Dict, List, Tuple

# إعداد التسجيل
logging.basicConfig(
    format="%(asctime)s - %(levelname)s - %(message)s",
    level=logging.INFO,
    datefmt="%Y-%m-%d %H:%M:%S",
)
logger = logging.getLogger(__name__)

# قائمة المكتبات المطلوبة مع أسمائها في pip
REQUIRED_PACKAGES = {
    # المكتبات الأساسية
    'telegram': 'python-telegram-bot',
    'dotenv': 'python-dotenv', 
    'requests': 'requests',
    'httpx': 'httpx',
    
    # خادم الويب
    'flask': 'flask',
    'flask_cors': 'flask-cors',
    
    # قاعدة البيانات
    'supabase': 'supabase',
    
    # الجدولة
    'apscheduler': 'APScheduler',
    
    # الأمان
    'cryptography': 'cryptography',
    'jwt': 'PyJWT',
    
    # معالجة الصور
    'PIL': 'Pillow',
    
    # أدوات إضافية
    'dateutil': 'python-dateutil',
    'pytz': 'pytz',
    'psutil': 'psutil',
    'colorama': 'colorama'
}

# المكتبات الاختيارية
OPTIONAL_PACKAGES = {
    'bcrypt': 'bcrypt',
    'aiohttp': 'aiohttp',
    'websockets': 'websockets'
}

def check_python_version() -> bool:
    """فحص إصدار Python"""
    version = sys.version_info
    min_version = (3, 8)
    
    logger.info(f"🐍 إصدار Python: {version.major}.{version.minor}.{version.micro}")
    
    if (version.major, version.minor) >= min_version:
        logger.info("✅ إصدار Python مدعوم")
        return True
    else:
        logger.error(f"❌ إصدار Python غير مدعوم. يتطلب {min_version[0]}.{min_version[1]} أو أحدث")
        return False

def get_package_version(package_name: str) -> str:
    """الحصول على إصدار المكتبة"""
    try:
        module = importlib.import_module(package_name)
        
        # محاولة الحصول على الإصدار بطرق مختلفة
        version_attrs = ['__version__', 'version', 'VERSION']
        
        for attr in version_attrs:
            if hasattr(module, attr):
                version = getattr(module, attr)
                if isinstance(version, str):
                    return version
                elif hasattr(version, '__str__'):
                    return str(version)
        
        return "غير معروف"
        
    except Exception:
        return "غير متوفر"

def check_package_import(package_name: str) -> Tuple[bool, str]:
    """فحص إمكانية استيراد المكتبة"""
    try:
        importlib.import_module(package_name)
        version = get_package_version(package_name)
        return True, version
    except ImportError as e:
        return False, str(e)
    except Exception as e:
        return False, f"خطأ غير متوقع: {e}"

def check_pip_package(pip_name: str) -> Tuple[bool, str]:
    """فحص تثبيت المكتبة عبر pip"""
    try:
        result = subprocess.run([
            sys.executable, "-m", "pip", "show", pip_name
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            # استخراج الإصدار من الناتج
            for line in result.stdout.split('\n'):
                if line.startswith('Version:'):
                    return True, line.split(':', 1)[1].strip()
            return True, "غير معروف"
        else:
            return False, "غير مثبت"
            
    except subprocess.TimeoutExpired:
        return False, "انتهت مهلة الفحص"
    except Exception as e:
        return False, f"خطأ في الفحص: {e}"

def check_required_packages() -> Dict[str, Dict]:
    """فحص المكتبات المطلوبة"""
    logger.info("🔍 فحص المكتبات المطلوبة...")
    
    results = {}
    
    for import_name, pip_name in REQUIRED_PACKAGES.items():
        logger.info(f"   فحص {pip_name}...")
        
        # فحص الاستيراد
        import_success, import_info = check_package_import(import_name)
        
        # فحص التثبيت عبر pip
        pip_success, pip_info = check_pip_package(pip_name)
        
        results[pip_name] = {
            'import_name': import_name,
            'import_success': import_success,
            'import_info': import_info,
            'pip_success': pip_success,
            'pip_info': pip_info,
            'status': 'success' if import_success else 'failed',
            'required': True
        }
    
    return results

def check_optional_packages() -> Dict[str, Dict]:
    """فحص المكتبات الاختيارية"""
    logger.info("🔍 فحص المكتبات الاختيارية...")
    
    results = {}
    
    for import_name, pip_name in OPTIONAL_PACKAGES.items():
        logger.info(f"   فحص {pip_name}...")
        
        # فحص الاستيراد
        import_success, import_info = check_package_import(import_name)
        
        # فحص التثبيت عبر pip
        pip_success, pip_info = check_pip_package(pip_name)
        
        results[pip_name] = {
            'import_name': import_name,
            'import_success': import_success,
            'import_info': import_info,
            'pip_success': pip_success,
            'pip_info': pip_info,
            'status': 'success' if import_success else 'optional',
            'required': False
        }
    
    return results

def print_results(required_results: Dict, optional_results: Dict):
    """طباعة النتائج"""
    print("\n" + "=" * 70)
    print("📊 نتائج فحص التبعيات")
    print("=" * 70)
    
    # المكتبات المطلوبة
    print("\n🔴 المكتبات المطلوبة:")
    print("-" * 50)
    
    required_success = 0
    required_total = len(required_results)
    
    for package, info in required_results.items():
        status_icon = "✅" if info['import_success'] else "❌"
        version = info['import_info'] if info['import_success'] else "غير متوفر"
        
        print(f"{status_icon} {package:<25} {version}")
        
        if info['import_success']:
            required_success += 1
        else:
            print(f"   └─ خطأ: {info['import_info']}")
    
    # المكتبات الاختيارية
    print("\n🟡 المكتبات الاختيارية:")
    print("-" * 50)
    
    optional_success = 0
    optional_total = len(optional_results)
    
    for package, info in optional_results.items():
        status_icon = "✅" if info['import_success'] else "⚪"
        version = info['import_info'] if info['import_success'] else "غير متوفر"
        
        print(f"{status_icon} {package:<25} {version}")
        
        if info['import_success']:
            optional_success += 1
    
    # الملخص
    print("\n" + "=" * 70)
    print("📈 ملخص النتائج:")
    print(f"   🔴 المكتبات المطلوبة: {required_success}/{required_total}")
    print(f"   🟡 المكتبات الاختيارية: {optional_success}/{optional_total}")
    
    # التوصيات
    if required_success < required_total:
        print("\n⚠️ تحذير: بعض المكتبات المطلوبة مفقودة!")
        print("🔧 قم بتشغيل: python install_requirements.py")
    else:
        print("\n🎉 جميع المكتبات المطلوبة متوفرة!")
    
    return required_success == required_total

def generate_missing_packages_list(required_results: Dict) -> List[str]:
    """إنشاء قائمة بالمكتبات المفقودة"""
    missing = []
    
    for package, info in required_results.items():
        if not info['import_success']:
            missing.append(package)
    
    return missing

def main():
    """الدالة الرئيسية"""
    print("🔍 أداة فحص التبعيات والمكتبات")
    print("=" * 50)
    
    # فحص إصدار Python
    if not check_python_version():
        return 1
    
    # فحص المكتبات
    required_results = check_required_packages()
    optional_results = check_optional_packages()
    
    # طباعة النتائج
    all_required_available = print_results(required_results, optional_results)
    
    # إنشاء قائمة المكتبات المفقودة
    missing_packages = generate_missing_packages_list(required_results)
    
    if missing_packages:
        print(f"\n📋 المكتبات المفقودة: {', '.join(missing_packages)}")
        print("💡 لتثبيت المكتبات المفقودة:")
        print(f"   pip install {' '.join(missing_packages)}")
    
    return 0 if all_required_available else 1

if __name__ == "__main__":
    sys.exit(main())
