#!/usr/bin/env python3
# تحليل شامل لجميع مفاتيح API الموجودة

import os
import re
from dotenv import load_dotenv

# تحميل متغيرات البيئة
load_dotenv()

def analyze_existing_api_keys():
    """تحليل جميع مفاتيح API الموجودة بالفعل"""
    
    print("🔍 تحليل شامل لجميع مفاتيح API الموجودة")
    print("="*60)
    
    # قراءة ملف .env
    with open('.env', 'r', encoding='utf-8') as f:
        env_content = f.read()
    
    # تصنيف المفاتيح
    categories = {
        "✅ مفاتيح نشطة وتعمل": [],
        "🆕 مفاتيح جديدة مضافة": [],
        "⚠️ مفاتيح بقيم افتراضية": [],
        "❌ مفاتيح معطلة": []
    }
    
    # تحليل كل سطر
    lines = env_content.split('\n')
    for i, line in enumerate(lines, 1):
        line = line.strip()
        
        # تجاهل التعليقات والأسطر الفارغة
        if not line or line.startswith('#'):
            continue
            
        # فحص إذا كان السطر يحتوي على مفتاح API
        if '=' in line:
            key, value = line.split('=', 1)
            key = key.strip()
            value = value.strip()
            
            # تصنيف المفتاح
            if not value or value.startswith('your_') or value.endswith('_here'):
                categories["⚠️ مفاتيح بقيم افتراضية"].append({
                    'key': key,
                    'line': i,
                    'value': value[:20] + '...' if len(value) > 20 else value
                })
            elif value == 'REMOVED' or line.startswith('# '):
                categories["❌ مفاتيح معطلة"].append({
                    'key': key,
                    'line': i,
                    'reason': 'تم إزالته لتجنب الحظر' if 'GOOGLE_SEARCH' in key else 'معطل'
                })
            elif any(pattern in value for pattern in ['tvly-', 'AIza', 'hf_', 'apify_', 'FPSX', 'b6863038']):
                # مفاتيح تبدو حقيقية
                if any(pattern in key for pattern in ['TAVILY', 'ASSEMBLYAI', 'WIT_AI']):
                    categories["🆕 مفاتيح جديدة مضافة"].append({
                        'key': key,
                        'value': value[:8] + '...' + value[-4:] if len(value) > 12 else value,
                        'status': 'جديد'
                    })
                else:
                    categories["✅ مفاتيح نشطة وتعمل"].append({
                        'key': key,
                        'value': value[:8] + '...' + value[-4:] if len(value) > 12 else value,
                        'status': 'نشط'
                    })
    
    # عرض النتائج
    for category, items in categories.items():
        print(f"\n{category} ({len(items)} مفتاح):")
        print("-" * 50)
        
        if category == "✅ مفاتيح نشطة وتعمل":
            for item in items:
                print(f"  ✅ {item['key']}: {item['value']}")
                
        elif category == "🆕 مفاتيح جديدة مضافة":
            for item in items:
                print(f"  🆕 {item['key']}: {item['value']} ({item['status']})")
                
        elif category == "⚠️ مفاتيح بقيم افتراضية":
            for item in items[:10]:  # عرض أول 10 فقط
                print(f"  ⚠️ {item['key']} (السطر {item['line']})")
            if len(items) > 10:
                print(f"  ... و {len(items) - 10} مفتاح آخر")
                
        elif category == "❌ مفاتيح معطلة":
            for item in items:
                print(f"  ❌ {item['key']}: {item['reason']}")
    
    return categories

def check_whisper_configuration():
    """فحص إعدادات Whisper وربطه بـ OpenAI"""
    
    print(f"\n🎤 فحص إعدادات Whisper:")
    print("-" * 50)
    
    # فحص مفاتيح Whisper
    whisper_keys = {
        'WHISPER_API_KEY_1': os.getenv('WHISPER_API_KEY_1'),
        'WHISPER_API_KEY_2': os.getenv('WHISPER_API_KEY_2'),
        'OPENAI_API_KEY_1': os.getenv('OPENAI_API_KEY_1'),
        'OPENAI_API_KEY_2': os.getenv('OPENAI_API_KEY_2'),
        'OPENAI_API_KEY_3': os.getenv('OPENAI_API_KEY_3')
    }
    
    # فحص إعدادات Whisper الحالية
    current_whisper_config = {
        'WHISPER_API_URL': os.getenv('WHISPER_API_URL', 'غير محدد'),
        'WHISPER_API_KEY': os.getenv('WHISPER_API_KEY', 'غير محدد'),
        'HF_TOKEN': os.getenv('HF_TOKEN', 'غير محدد')
    }
    
    print("📋 إعدادات Whisper الحالية:")
    for key, value in current_whisper_config.items():
        if value and value != 'غير محدد':
            masked_value = f"{value[:8]}...{value[-4:]}" if len(value) > 12 else value
            print(f"  ✅ {key}: {masked_value}")
        else:
            print(f"  ❌ {key}: غير مضبوط")
    
    print(f"\n🔑 مفاتيح Whisper/OpenAI:")
    for key, value in whisper_keys.items():
        if value and not value.startswith('your_'):
            masked_value = f"{value[:8]}...{value[-4:]}" if len(value) > 12 else value
            print(f"  ✅ {key}: {masked_value}")
        else:
            print(f"  ❌ {key}: غير مضبوط أو قيمة افتراضية")
    
    # تحليل ربط Whisper بـ OpenAI
    print(f"\n🔗 تحليل ربط Whisper بـ OpenAI:")
    
    # فحص الكود للتأكد من الربط
    whisper_integration = {
        'hugging_face_spaces': current_whisper_config['WHISPER_API_URL'] != 'غير محدد',
        'openai_api_available': any(whisper_keys[k] for k in whisper_keys if 'OPENAI' in k),
        'local_whisper_support': True,  # موجود في الكود
        'enhanced_whisper_manager': True  # موجود في الكود
    }
    
    for integration, status in whisper_integration.items():
        status_icon = "✅" if status else "❌"
        integration_name = {
            'hugging_face_spaces': 'Hugging Face Spaces',
            'openai_api_available': 'OpenAI API متاح',
            'local_whisper_support': 'دعم Whisper المحلي',
            'enhanced_whisper_manager': 'مدير Whisper المحسن'
        }[integration]
        print(f"  {status_icon} {integration_name}")
    
    return whisper_integration

def generate_recommendations():
    """توليد توصيات لتحسين إعدادات API"""
    
    print(f"\n💡 توصيات التحسين:")
    print("-" * 50)
    
    recommendations = [
        {
            'priority': 'عالية',
            'action': 'إضافة مفاتيح OpenAI',
            'reason': 'لتفعيل Whisper API الرسمي من OpenAI',
            'keys': ['OPENAI_API_KEY_1', 'OPENAI_API_KEY_2', 'OPENAI_API_KEY_3']
        },
        {
            'priority': 'متوسطة',
            'action': 'إضافة مفاتيح YouTube Data API',
            'reason': 'لتحسين استخراج بيانات الفيديوهات',
            'keys': ['YOUTUBE_DATA_API_KEY_1', 'YOUTUBE_DATA_API_KEY_2', 'YOUTUBE_DATA_API_KEY_3']
        },
        {
            'priority': 'متوسطة',
            'action': 'إضافة مفاتيح Stability AI',
            'reason': 'لتوليد صور احترافية بالذكاء الاصطناعي',
            'keys': ['STABILITY_AI_API_KEY_1', 'STABILITY_AI_API_KEY_2']
        },
        {
            'priority': 'منخفضة',
            'action': 'إضافة مفاتيح Azure Speech',
            'reason': 'كبديل إضافي لتحويل الصوت إلى نص',
            'keys': ['AZURE_SPEECH_KEY_1', 'AZURE_SPEECH_KEY_2']
        },
        {
            'priority': 'منخفضة',
            'action': 'إضافة مفاتيح وسائل التواصل',
            'reason': 'للنشر على منصات متعددة',
            'keys': ['TWITTER_API_KEY_1', 'FACEBOOK_ACCESS_TOKEN_1', 'INSTAGRAM_ACCESS_TOKEN_1']
        }
    ]
    
    for rec in recommendations:
        priority_icon = {
            'عالية': '🔴',
            'متوسطة': '🟡', 
            'منخفضة': '🟢'
        }[rec['priority']]
        
        print(f"  {priority_icon} أولوية {rec['priority']}: {rec['action']}")
        print(f"     السبب: {rec['reason']}")
        print(f"     المفاتيح: {', '.join(rec['keys'])}")
        print()

def check_service_integration():
    """فحص تكامل الخدمات مع الكود"""
    
    print(f"\n🔧 فحص تكامل الخدمات:")
    print("-" * 50)
    
    # الخدمات المتكاملة مع الكود
    integrated_services = {
        'Gemini AI': {'status': '✅', 'usage': 'توليد المحتوى الرئيسي'},
        'Telegram Bot': {'status': '✅', 'usage': 'النشر على تيليجرام'},
        'Blogger API': {'status': '✅', 'usage': 'النشر على Blogger'},
        'RAWG API': {'status': '✅', 'usage': 'بيانات الألعاب'},
        'Freepik API': {'status': '✅', 'usage': 'صور وأيقونات'},
        'FluxAI': {'status': '✅', 'usage': 'توليد صور بالذكاء الاصطناعي'},
        'Apify': {'status': '✅', 'usage': 'تحميل فيديوهات YouTube'},
        'SerpAPI': {'status': '✅', 'usage': 'بحث متقدم'},
        'Search1API': {'status': '✅', 'usage': 'بحث بديل (3 مفاتيح)'},
        'Tavily API': {'status': '🆕', 'usage': 'بحث ذكي للأخبار (جديد)'},
        'AssemblyAI': {'status': '🆕', 'usage': 'تحويل صوت إلى نص (جديد)'},
        'Wit.ai': {'status': '🆕', 'usage': 'تحويل صوت إلى نص (جديد)'},
        'Whisper (HF)': {'status': '✅', 'usage': 'تحويل صوت محلي'},
        'Enhanced Search': {'status': '✅', 'usage': 'نظام بحث ذكي بديل'},
        'Internal Links': {'status': '✅', 'usage': 'روابط داخلية ذكية'},
        'Content Enhancement': {'status': '✅', 'usage': 'تحسين المحتوى والكتابة'}
    }
    
    for service, info in integrated_services.items():
        print(f"  {info['status']} {service}: {info['usage']}")

def main():
    """تشغيل التحليل الشامل"""
    
    # تحليل مفاتيح API
    categories = analyze_existing_api_keys()
    
    # فحص Whisper
    whisper_config = check_whisper_configuration()
    
    # فحص تكامل الخدمات
    check_service_integration()
    
    # توليد التوصيات
    generate_recommendations()
    
    # ملخص نهائي
    print(f"\n📊 الملخص النهائي:")
    print("="*60)
    
    total_active = len(categories["✅ مفاتيح نشطة وتعمل"])
    total_new = len(categories["🆕 مفاتيح جديدة مضافة"])
    total_default = len(categories["⚠️ مفاتيح بقيم افتراضية"])
    total_disabled = len(categories["❌ مفاتيح معطلة"])
    
    print(f"✅ مفاتيح نشطة: {total_active}")
    print(f"🆕 مفاتيح جديدة: {total_new}")
    print(f"⚠️ مفاتيح بقيم افتراضية: {total_default}")
    print(f"❌ مفاتيح معطلة: {total_disabled}")
    
    print(f"\n🎯 الحالة العامة:")
    if total_active + total_new >= 10:
        print("🟢 ممتاز - لديك مفاتيح كافية للتشغيل الأساسي")
    elif total_active + total_new >= 5:
        print("🟡 جيد - يمكن إضافة المزيد من المفاتيح للتحسين")
    else:
        print("🔴 يحتاج تحسين - أضف المزيد من المفاتيح الحقيقية")
    
    print(f"\n🔗 حالة Whisper:")
    if whisper_config['hugging_face_spaces']:
        print("✅ Whisper يعمل عبر Hugging Face Spaces")
    if whisper_config['openai_api_available']:
        print("✅ مفاتيح OpenAI متاحة لـ Whisper API")
    else:
        print("⚠️ لا توجد مفاتيح OpenAI - Whisper يعمل عبر HF فقط")
    
    print(f"\n🚀 الخطوة التالية:")
    print("1. أضف مفاتيح OpenAI للحصول على أفضل أداء لـ Whisper")
    print("2. أضف مفاتيح YouTube Data API لتحسين استخراج البيانات")
    print("3. اختبر الخدمات الجديدة (Tavily, AssemblyAI, Wit.ai)")
    print("4. فكر في إضافة مفاتيح Stability AI لتوليد صور احترافية")

if __name__ == "__main__":
    main()
