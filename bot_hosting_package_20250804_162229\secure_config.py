"""
إعدادات الحماية الآمنة للبوت
يحتوي على جميع المتغيرات الحساسة والإعدادات الأمنية
"""

import os
import logging
from typing import Optional
import base64
import hashlib
from dotenv import load_dotenv

# تحميل ملف .env
load_dotenv()

# إعداد التسجيل
logger = logging.getLogger(__name__)

class SecureConfig:
    """فئة الإعدادات الآمنة"""
    
    def __init__(self):
        self._load_environment_variables()
        self._validate_configuration()
    
    def _load_environment_variables(self):
        """تحميل متغيرات البيئة"""
        # إعدادات البوت الأساسية
        self.bot_token = os.environ.get("BOT_TOKEN", "")
        self.admin_chat_id = os.environ.get("ADMIN_CHAT_ID", "")
        self.admin_username = os.environ.get("ADMIN_USERNAME", "")
        
        # إعدادات قاعدة البيانات
        self.supabase_url = os.environ.get("SUPABASE_URL", "")
        self.supabase_key = os.environ.get("SUPABASE_KEY", "")
        self.supabase_service_key = os.environ.get("SUPABASE_SERVICE_KEY", "")
        
        # إعدادات الحماية
        self.encryption_key = os.environ.get("ENCRYPTION_KEY", self._generate_default_key())
        self.secret_key = os.environ.get("SECRET_KEY", self._generate_secret_key())
        
        # إعدادات الشبكة
        self.web_server_url = os.environ.get("WEB_SERVER_URL", "")
        self.proxy_url = os.environ.get("PROXY_URL", "")
        
        # إعدادات التحسين
        self.optimization_enabled = os.environ.get("OPTIMIZATION_ENABLED", "true").lower() == "true"
        self.low_resource_mode = os.environ.get("LOW_RESOURCE_MODE", "true").lower() == "true"
        self.debug_mode = os.environ.get("DEBUG", "false").lower() == "true"
        
        # إعدادات البيئة
        self.environment = os.environ.get("ENVIRONMENT", "production")
    
    def _generate_default_key(self) -> str:
        """توليد مفتاح تشفير افتراضي"""
        # استخدام معلومات النظام لتوليد مفتاح فريد
        system_info = f"{os.getcwd()}{self.bot_token}{self.admin_chat_id}"
        return hashlib.sha256(system_info.encode()).hexdigest()
    
    def _generate_secret_key(self) -> str:
        """توليد مفتاح سري"""
        return base64.b64encode(os.urandom(32)).decode()
    
    def _validate_configuration(self):
        """التحقق من صحة الإعدادات"""
        required_vars = {
            'BOT_TOKEN': self.bot_token,
            'ADMIN_CHAT_ID': self.admin_chat_id,
            'SUPABASE_URL': self.supabase_url,
            'SUPABASE_KEY': self.supabase_key
        }
        
        missing_vars = [var for var, value in required_vars.items() if not value]
        
        if missing_vars:
            logger.error(f"Missing required environment variables: {', '.join(missing_vars)}")
            raise ValueError(f"Missing required configuration: {', '.join(missing_vars)}")
        
        # التحقق من صحة URL قاعدة البيانات
        if not self.supabase_url.startswith(('http://', 'https://')):
            logger.error("Invalid Supabase URL format")
            raise ValueError("Supabase URL must start with http:// or https://")
        
        logger.info("Configuration validation passed")
    
    def get_database_config(self) -> dict:
        """الحصول على إعدادات قاعدة البيانات"""
        return {
            'url': self.supabase_url,
            'key': self.supabase_key,
            'service_key': self.supabase_service_key or self.supabase_key
        }
    
    def get_security_config(self) -> dict:
        """الحصول على إعدادات الحماية"""
        return {
            'encryption_key': self.encryption_key,
            'secret_key': self.secret_key,
            'admin_chat_id': self.admin_chat_id,
            'admin_username': self.admin_username
        }
    
    def get_network_config(self) -> dict:
        """الحصول على إعدادات الشبكة"""
        return {
            'web_server_url': self.web_server_url,
            'proxy_url': self.proxy_url,
            'optimization_enabled': self.optimization_enabled,
            'low_resource_mode': self.low_resource_mode
        }
    
    def is_production(self) -> bool:
        """فحص ما إذا كان النظام في بيئة الإنتاج"""
        return self.environment.lower() == 'production'
    
    def is_debug_enabled(self) -> bool:
        """فحص ما إذا كان وضع التطوير مفعل"""
        return self.debug_mode and not self.is_production()

# إنشاء كائن الإعدادات العامة
try:
    config = SecureConfig()
    logger.info("Secure configuration loaded successfully")
except Exception as e:
    logger.error(f"Failed to load secure configuration: {e}")
    # إنشاء إعدادات افتراضية للطوارئ
    config = None

# دوال مساعدة للوصول للإعدادات
def get_bot_token() -> str:
    """الحصول على رمز البوت"""
    if config:
        return config.bot_token
    return os.environ.get("BOT_TOKEN", "")

def get_admin_id() -> str:
    """الحصول على معرف المشرف"""
    if config:
        return config.admin_chat_id
    return os.environ.get("ADMIN_CHAT_ID", "")

def get_admin_username() -> str:
    """الحصول على اسم المشرف"""
    if config:
        return config.admin_username
    return os.environ.get("ADMIN_USERNAME", "")

def get_supabase_url() -> str:
    """الحصول على رابط Supabase"""
    if config:
        return config.supabase_url
    return os.environ.get("SUPABASE_URL", "")

def get_supabase_key() -> str:
    """الحصول على مفتاح Supabase"""
    if config:
        return config.supabase_key
    return os.environ.get("SUPABASE_KEY", "")

def get_supabase_service_key() -> str:
    """الحصول على مفتاح خدمة Supabase"""
    if config:
        return config.supabase_service_key or config.supabase_key
    return os.environ.get("SUPABASE_SERVICE_KEY", "") or get_supabase_key()

def get_encryption_key() -> str:
    """الحصول على مفتاح التشفير"""
    if config:
        return config.encryption_key
    return os.environ.get("ENCRYPTION_KEY", "default_key")

def get_web_server_url() -> str:
    """الحصول على رابط خادم الويب"""
    if config:
        return config.web_server_url
    return os.environ.get("WEB_SERVER_URL", "")

def is_optimization_enabled() -> bool:
    """فحص ما إذا كان التحسين مفعل"""
    if config:
        return config.optimization_enabled
    return os.environ.get("OPTIMIZATION_ENABLED", "true").lower() == "true"

def is_low_resource_mode() -> bool:
    """فحص ما إذا كان وضع الموارد المحدودة مفعل"""
    if config:
        return config.low_resource_mode
    return os.environ.get("LOW_RESOURCE_MODE", "true").lower() == "true"

def is_debug_mode() -> bool:
    """فحص ما إذا كان وضع التطوير مفعل"""
    if config:
        return config.is_debug_enabled()
    return os.environ.get("DEBUG", "false").lower() == "true"

def get_environment() -> str:
    """الحصول على بيئة التشغيل"""
    if config:
        return config.environment
    return os.environ.get("ENVIRONMENT", "production")

def validate_all_settings() -> bool:
    """التحقق من صحة جميع الإعدادات"""
    try:
        required_settings = [
            get_bot_token(),
            get_admin_id(),
            get_supabase_url(),
            get_supabase_key()
        ]
        
        return all(setting for setting in required_settings)
    except Exception as e:
        logger.error(f"Settings validation failed: {e}")
        return False

def get_safe_config_summary() -> dict:
    """الحصول على ملخص آمن للإعدادات (بدون البيانات الحساسة)"""
    return {
        'environment': get_environment(),
        'optimization_enabled': is_optimization_enabled(),
        'low_resource_mode': is_low_resource_mode(),
        'debug_mode': is_debug_mode(),
        'has_bot_token': bool(get_bot_token()),
        'has_admin_id': bool(get_admin_id()),
        'has_supabase_url': bool(get_supabase_url()),
        'has_supabase_key': bool(get_supabase_key()),
        'config_loaded': config is not None
    }

# تسجيل حالة التحميل
if config:
    logger.info("✅ Secure configuration system initialized successfully")
    if is_debug_mode():
        logger.debug(f"Configuration summary: {get_safe_config_summary()}")
else:
    logger.warning("⚠️ Secure configuration system failed to initialize, using fallback methods")
