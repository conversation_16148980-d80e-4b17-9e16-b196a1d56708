#!/usr/bin/env python3
"""
بدء سريع للنظام المتقدم
"""

import os
import sys
import subprocess
from datetime import datetime

def print_banner():
    """طباعة شعار النظام"""
    print("=" * 70)
    print("🎮 وكيل أخبار الألعاب - النظام المتقدم")
    print("=" * 70)
    print(f"📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("🤖 تطوير: نظام إدارة متقدم مع بوت تيليجرام")
    print("=" * 70)

def check_system():
    """فحص النظام"""
    print("\n🔍 فحص النظام...")
    
    # فحص Python
    python_version = sys.version.split()[0]
    print(f"🐍 Python: {python_version}")
    
    # فحص الملفات المهمة
    important_files = [
        "main_bot_only.py",
        "modules/enhanced_telegram_bot.py",
        "modules/agent_data_manager.py",
        "config/settings.py",
        ".env"
    ]
    
    missing_files = []
    for file_path in important_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} مفقود")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n⚠️ ملفات مفقودة: {len(missing_files)}")
        return False
    
    print("\n✅ جميع الملفات موجودة")
    return True

def show_options():
    """عرض الخيارات المتاحة"""
    print("\n🎯 الخيارات المتاحة:")
    print("1. 🤖 تشغيل البوت فقط (الوكيل متوقف)")
    print("2. 🚀 تشغيل البوت مع الواجهة الكاملة")
    print("3. 🧪 اختبار النظام")
    print("4. 📚 عرض الأدلة")
    print("5. ❌ خروج")

def run_bot_only():
    """تشغيل البوت فقط"""
    print("\n🤖 تشغيل البوت فقط...")
    print("💡 الوكيل متوقف - يمكن تشغيله من البوت")
    print("🔗 ابحث عن البوت في تيليجرام وأرسل /start")
    print("\n⌨️ اضغط Ctrl+C للإيقاف")
    
    try:
        subprocess.run([sys.executable, "main_bot_only.py"])
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف البوت")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل البوت: {e}")

def run_full_bot():
    """تشغيل البوت مع الواجهة الكاملة"""
    print("\n🚀 تشغيل البوت مع الواجهة الكاملة...")
    print("🔗 ابحث عن البوت في تيليجرام وأرسل /start")
    print("\n⌨️ اضغط Ctrl+C للإيقاف")
    
    try:
        subprocess.run([sys.executable, "run_telegram_bot.py"])
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف البوت")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل البوت: {e}")

def test_system():
    """اختبار النظام"""
    print("\n🧪 اختبار النظام...")
    
    try:
        result = subprocess.run([sys.executable, "test_advanced_system.py"], 
                              capture_output=True, text=True)
        
        print(result.stdout)
        if result.stderr:
            print("⚠️ تحذيرات:")
            print(result.stderr)
        
        if result.returncode == 0:
            print("\n✅ جميع الاختبارات نجحت!")
        else:
            print("\n❌ بعض الاختبارات فشلت")
            
    except Exception as e:
        print(f"❌ خطأ في تشغيل الاختبارات: {e}")

def show_guides():
    """عرض الأدلة المتاحة"""
    print("\n📚 الأدلة المتاحة:")
    
    guides = [
        ("ADVANCED_SYSTEM_GUIDE.md", "دليل النظام المتقدم الشامل"),
        ("TELEGRAM_BOT_README.md", "دليل بوت تيليجرام"),
        ("START_TELEGRAM_BOT.md", "دليل البدء السريع")
    ]
    
    for i, (file_name, description) in enumerate(guides, 1):
        if os.path.exists(file_name):
            print(f"{i}. ✅ {description} ({file_name})")
        else:
            print(f"{i}. ❌ {description} ({file_name}) - مفقود")
    
    print("\n💡 يمكنك فتح هذه الملفات في أي محرر نصوص")

def main():
    """الدالة الرئيسية"""
    print_banner()
    
    # فحص النظام
    if not check_system():
        print("\n❌ فشل فحص النظام. تأكد من وجود جميع الملفات المطلوبة")
        return
    
    while True:
        show_options()
        
        try:
            choice = input("\n🎯 اختر رقم الخيار: ").strip()
            
            if choice == "1":
                run_bot_only()
            elif choice == "2":
                run_full_bot()
            elif choice == "3":
                test_system()
            elif choice == "4":
                show_guides()
            elif choice == "5":
                print("\n👋 وداعاً!")
                break
            else:
                print("\n❌ خيار غير صحيح. اختر رقم من 1 إلى 5")
                
        except KeyboardInterrupt:
            print("\n\n👋 تم الإيقاف بواسطة المستخدم")
            break
        except Exception as e:
            print(f"\n❌ خطأ: {e}")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        sys.exit(1)
