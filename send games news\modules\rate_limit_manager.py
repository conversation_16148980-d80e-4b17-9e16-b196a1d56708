# مدير معدل الطلبات المتقدم
import time
import asyncio
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
import sqlite3
import os

from .logger import logger
from .advanced_cache_system import advanced_cache

class RateLimitType(Enum):
    """أنواع حدود المعدل"""
    PER_SECOND = "per_second"
    PER_MINUTE = "per_minute"
    PER_HOUR = "per_hour"
    PER_DAY = "per_day"
    PER_MONTH = "per_month"

@dataclass
class RateLimitRule:
    """قاعدة حد المعدل"""
    service: str
    limit_type: RateLimitType
    max_requests: int
    window_size: int  # بالثواني
    priority: int = 1  # 1=منخفض، 5=عالي
    cost_per_request: float = 0.0
    burst_allowed: bool = False
    burst_limit: int = 0

@dataclass
class RequestRecord:
    """سجل طلب"""
    service: str
    timestamp: float
    success: bool
    response_time: float
    cost: float = 0.0
    error_message: str = ""

class RateLimitManager:
    """مدير معدل الطلبات المتقدم"""
    
    def __init__(self, db_path: str = "cache/rate_limits.db"):
        self.db_path = db_path
        self.rules: Dict[str, List[RateLimitRule]] = {}
        self.request_history: Dict[str, List[RequestRecord]] = {}
        
        # إعدادات افتراضية للخدمات المختلفة
        self.default_rules = {
            'google_search': [
                RateLimitRule('google_search', RateLimitType.PER_DAY, 10000, 86400, 2, 0.005),
                RateLimitRule('google_search', RateLimitType.PER_MINUTE, 100, 60, 2, 0.005)
            ],
            'serpapi': [
                RateLimitRule('serpapi', RateLimitType.PER_MONTH, 5000, 2592000, 3, 0.01),
                RateLimitRule('serpapi', RateLimitType.PER_MINUTE, 20, 60, 3, 0.01)
            ],
            'tavily': [
                RateLimitRule('tavily', RateLimitType.PER_MONTH, 1000, 2592000, 4, 0.02),
                RateLimitRule('tavily', RateLimitType.PER_MINUTE, 10, 60, 4, 0.02)
            ],
            'newsapi': [
                RateLimitRule('newsapi', RateLimitType.PER_DAY, 1000, 86400, 2, 0.001),
                RateLimitRule('newsapi', RateLimitType.PER_MINUTE, 50, 60, 2, 0.001)
            ]
        }
        
        # إحصائيات
        self.stats = {
            'total_requests': 0,
            'blocked_requests': 0,
            'total_cost': 0.0,
            'services_used': {},
            'peak_usage_times': []
        }
        
        # تهيئة النظام
        self._init_database()
        self._load_rules()
        
        logger.info("⏱️ تم تهيئة مدير معدل الطلبات المتقدم")
    
    def _init_database(self):
        """تهيئة قاعدة البيانات"""
        try:
            os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
            
            with sqlite3.connect(self.db_path) as conn:
                # جدول القواعد
                conn.execute('''
                    CREATE TABLE IF NOT EXISTS rate_limit_rules (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        service TEXT NOT NULL,
                        limit_type TEXT NOT NULL,
                        max_requests INTEGER NOT NULL,
                        window_size INTEGER NOT NULL,
                        priority INTEGER DEFAULT 1,
                        cost_per_request REAL DEFAULT 0.0,
                        burst_allowed BOOLEAN DEFAULT FALSE,
                        burst_limit INTEGER DEFAULT 0,
                        created_at REAL NOT NULL,
                        updated_at REAL NOT NULL
                    )
                ''')
                
                # جدول سجل الطلبات
                conn.execute('''
                    CREATE TABLE IF NOT EXISTS request_history (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        service TEXT NOT NULL,
                        timestamp REAL NOT NULL,
                        success BOOLEAN NOT NULL,
                        response_time REAL NOT NULL,
                        cost REAL DEFAULT 0.0,
                        error_message TEXT DEFAULT ''
                    )
                ''')
                
                # فهارس للأداء
                conn.execute('CREATE INDEX IF NOT EXISTS idx_service ON rate_limit_rules(service)')
                conn.execute('CREATE INDEX IF NOT EXISTS idx_timestamp ON request_history(timestamp)')
                conn.execute('CREATE INDEX IF NOT EXISTS idx_service_timestamp ON request_history(service, timestamp)')
                
        except Exception as e:
            logger.error(f"❌ فشل في تهيئة قاعدة بيانات معدل الطلبات: {e}")
    
    def _load_rules(self):
        """تحميل القواعد من قاعدة البيانات أو إنشاء القواعد الافتراضية"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute('SELECT COUNT(*) FROM rate_limit_rules')
                rule_count = cursor.fetchone()[0]
                
                if rule_count == 0:
                    # إنشاء القواعد الافتراضية
                    self._create_default_rules()
                else:
                    # تحميل القواعد الموجودة
                    cursor = conn.execute('''
                        SELECT service, limit_type, max_requests, window_size, 
                               priority, cost_per_request, burst_allowed, burst_limit
                        FROM rate_limit_rules
                    ''')
                    
                    for row in cursor.fetchall():
                        service, limit_type, max_requests, window_size, priority, cost_per_request, burst_allowed, burst_limit = row
                        
                        rule = RateLimitRule(
                            service=service,
                            limit_type=RateLimitType(limit_type),
                            max_requests=max_requests,
                            window_size=window_size,
                            priority=priority,
                            cost_per_request=cost_per_request,
                            burst_allowed=bool(burst_allowed),
                            burst_limit=burst_limit
                        )
                        
                        if service not in self.rules:
                            self.rules[service] = []
                        self.rules[service].append(rule)
                
        except Exception as e:
            logger.error(f"❌ فشل في تحميل قواعد معدل الطلبات: {e}")
            self._create_default_rules()
    
    def _create_default_rules(self):
        """إنشاء القواعد الافتراضية"""
        try:
            current_time = time.time()
            
            with sqlite3.connect(self.db_path) as conn:
                for service, rules in self.default_rules.items():
                    self.rules[service] = rules
                    
                    for rule in rules:
                        conn.execute('''
                            INSERT INTO rate_limit_rules 
                            (service, limit_type, max_requests, window_size, priority, 
                             cost_per_request, burst_allowed, burst_limit, created_at, updated_at)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        ''', (
                            rule.service, rule.limit_type.value, rule.max_requests,
                            rule.window_size, rule.priority, rule.cost_per_request,
                            rule.burst_allowed, rule.burst_limit, current_time, current_time
                        ))
                
            logger.info("✅ تم إنشاء القواعد الافتراضية لمعدل الطلبات")
            
        except Exception as e:
            logger.error(f"❌ فشل في إنشاء القواعد الافتراضية: {e}")
    
    async def can_make_request(self, service: str, priority: int = 1) -> Tuple[bool, str, float]:
        """فحص ما إذا كان يمكن إجراء طلب"""
        try:
            if service not in self.rules:
                logger.warning(f"⚠️ لا توجد قواعد لخدمة {service}")
                return True, "No rules defined", 0.0
            
            current_time = time.time()
            
            # فحص جميع القواعد للخدمة
            for rule in self.rules[service]:
                # حساب عدد الطلبات في النافزة الزمنية
                window_start = current_time - rule.window_size
                recent_requests = self._get_recent_requests(service, window_start)
                
                # فحص الحد الأقصى
                if len(recent_requests) >= rule.max_requests:
                    # فحص إمكانية الاندفاع (burst)
                    if rule.burst_allowed and len(recent_requests) < rule.burst_limit:
                        wait_time = self._calculate_wait_time(service, rule)
                        return True, f"Burst allowed for {service}", wait_time
                    
                    # حساب وقت الانتظار
                    wait_time = self._calculate_wait_time(service, rule)
                    return False, f"Rate limit exceeded for {service} ({rule.limit_type.value})", wait_time
            
            # جميع القواعد تسمح بالطلب
            return True, "Request allowed", 0.0
            
        except Exception as e:
            logger.error(f"❌ فشل في فحص معدل الطلبات لـ {service}: {e}")
            return True, "Error in rate limit check", 0.0
    
    def _get_recent_requests(self, service: str, since_timestamp: float) -> List[RequestRecord]:
        """الحصول على الطلبات الحديثة"""
        try:
            # فحص التخزين المؤقت أولاً
            cache_key = f"recent_requests_{service}_{int(since_timestamp)}"
            cached_requests = advanced_cache.get(cache_key)
            
            if cached_requests is not None:
                return [RequestRecord(**req) for req in cached_requests]
            
            # البحث في قاعدة البيانات
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute('''
                    SELECT service, timestamp, success, response_time, cost, error_message
                    FROM request_history 
                    WHERE service = ? AND timestamp >= ?
                    ORDER BY timestamp DESC
                ''', (service, since_timestamp))
                
                requests = []
                for row in cursor.fetchall():
                    service_name, timestamp, success, response_time, cost, error_message = row
                    request = RequestRecord(
                        service=service_name,
                        timestamp=timestamp,
                        success=bool(success),
                        response_time=response_time,
                        cost=cost,
                        error_message=error_message
                    )
                    requests.append(request)
                
                # حفظ في التخزين المؤقت لمدة دقيقة
                cache_data = [asdict(req) for req in requests]
                advanced_cache.set(cache_key, cache_data, ttl=60)
                
                return requests
                
        except Exception as e:
            logger.error(f"❌ فشل في الحصول على الطلبات الحديثة: {e}")
            return []
    
    def _calculate_wait_time(self, service: str, rule: RateLimitRule) -> float:
        """حساب وقت الانتظار المطلوب"""
        try:
            current_time = time.time()
            window_start = current_time - rule.window_size
            recent_requests = self._get_recent_requests(service, window_start)
            
            if len(recent_requests) < rule.max_requests:
                return 0.0
            
            # العثور على أقدم طلب في النافزة
            oldest_request_time = min(req.timestamp for req in recent_requests)
            
            # حساب متى ستنتهي النافزة الحالية
            window_end_time = oldest_request_time + rule.window_size
            wait_time = max(0, window_end_time - current_time)
            
            return wait_time
            
        except Exception as e:
            logger.error(f"❌ فشل في حساب وقت الانتظار: {e}")
            return 60.0  # انتظار دقيقة واحدة كافتراضي
    
    async def record_request(self, service: str, success: bool, response_time: float, 
                           cost: float = 0.0, error_message: str = ""):
        """تسجيل طلب"""
        try:
            current_time = time.time()
            
            record = RequestRecord(
                service=service,
                timestamp=current_time,
                success=success,
                response_time=response_time,
                cost=cost,
                error_message=error_message
            )
            
            # حفظ في قاعدة البيانات
            with sqlite3.connect(self.db_path) as conn:
                conn.execute('''
                    INSERT INTO request_history 
                    (service, timestamp, success, response_time, cost, error_message)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (service, current_time, success, response_time, cost, error_message))
            
            # تحديث الإحصائيات
            self.stats['total_requests'] += 1
            self.stats['total_cost'] += cost
            
            if service not in self.stats['services_used']:
                self.stats['services_used'][service] = 0
            self.stats['services_used'][service] += 1
            
            # إضافة إلى السجل المحلي
            if service not in self.request_history:
                self.request_history[service] = []
            self.request_history[service].append(record)
            
            # تنظيف السجل المحلي (الاحتفاظ بآخر 1000 طلب فقط)
            if len(self.request_history[service]) > 1000:
                self.request_history[service] = self.request_history[service][-1000:]
            
        except Exception as e:
            logger.error(f"❌ فشل في تسجيل الطلب: {e}")
    
    def get_service_stats(self, service: str) -> Dict:
        """الحصول على إحصائيات خدمة معينة"""
        try:
            current_time = time.time()
            
            # إحصائيات آخر 24 ساعة
            day_ago = current_time - 86400
            recent_requests = self._get_recent_requests(service, day_ago)
            
            successful_requests = [req for req in recent_requests if req.success]
            failed_requests = [req for req in recent_requests if not req.success]
            
            total_cost = sum(req.cost for req in recent_requests)
            avg_response_time = sum(req.response_time for req in recent_requests) / len(recent_requests) if recent_requests else 0
            
            success_rate = len(successful_requests) / len(recent_requests) * 100 if recent_requests else 0
            
            return {
                'service': service,
                'total_requests_24h': len(recent_requests),
                'successful_requests': len(successful_requests),
                'failed_requests': len(failed_requests),
                'success_rate': round(success_rate, 2),
                'total_cost_24h': round(total_cost, 4),
                'avg_response_time': round(avg_response_time, 3),
                'rules_count': len(self.rules.get(service, []))
            }
            
        except Exception as e:
            logger.error(f"❌ فشل في جمع إحصائيات {service}: {e}")
            return {}
    
    def get_overall_stats(self) -> Dict:
        """الحصول على الإحصائيات العامة"""
        try:
            return {
                'total_requests': self.stats['total_requests'],
                'blocked_requests': self.stats['blocked_requests'],
                'total_cost_usd': round(self.stats['total_cost'], 4),
                'services_used': self.stats['services_used'],
                'active_services': len(self.rules),
                'cache_hit_rate': advanced_cache.get_stats().get('hit_rate', 0)
            }
            
        except Exception as e:
            logger.error(f"❌ فشل في جمع الإحصائيات العامة: {e}")
            return {}

# إنشاء مثيل عام
rate_limit_manager = RateLimitManager()
