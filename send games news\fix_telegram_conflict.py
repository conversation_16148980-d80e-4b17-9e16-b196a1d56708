#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح مشكلة Telegram getUpdates Conflict
حل مشكلة 'Conflict: terminated by other getUpdates request'
"""

import os
import sys
import asyncio
import requests
import time
from datetime import datetime
from typing import Optional

def clear_telegram_webhook(bot_token: str) -> bool:
    """مسح webhook للبوت لحل مشكلة getUpdates conflict"""
    try:
        print("🔧 مسح webhook للبوت...")
        
        # مسح webhook
        url = f"https://api.telegram.org/bot{bot_token}/deleteWebhook"
        response = requests.post(url, json={"drop_pending_updates": True}, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('ok'):
                print("✅ تم مسح webhook بنجاح")
                return True
            else:
                print(f"❌ فشل في مسح webhook: {result.get('description', 'خطأ غير معروف')}")
                return False
        else:
            print(f"❌ خطأ HTTP في مسح webhook: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في مسح webhook: {e}")
        return False

def get_bot_info(bot_token: str) -> Optional[dict]:
    """الحصول على معلومات البوت"""
    try:
        url = f"https://api.telegram.org/bot{bot_token}/getMe"
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('ok'):
                return result.get('result')
        return None
        
    except Exception as e:
        print(f"❌ خطأ في الحصول على معلومات البوت: {e}")
        return None

def clear_pending_updates(bot_token: str) -> bool:
    """مسح التحديثات المعلقة"""
    try:
        print("🔄 مسح التحديثات المعلقة...")
        
        # الحصول على التحديثات مع offset عالي لمسحها
        url = f"https://api.telegram.org/bot{bot_token}/getUpdates"
        params = {
            "offset": -1,  # آخر تحديث
            "limit": 1,
            "timeout": 0
        }
        
        response = requests.get(url, params=params, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('ok'):
                updates = result.get('result', [])
                if updates:
                    # الحصول على آخر update_id ومسح كل شيء بعده
                    last_update_id = updates[-1]['update_id']
                    
                    # مسح التحديثات
                    params['offset'] = last_update_id + 1
                    response = requests.get(url, params=params, timeout=10)
                    
                    if response.status_code == 200:
                        print("✅ تم مسح التحديثات المعلقة")
                        return True
                else:
                    print("ℹ️ لا توجد تحديثات معلقة")
                    return True
        
        print("❌ فشل في مسح التحديثات المعلقة")
        return False
        
    except Exception as e:
        print(f"❌ خطأ في مسح التحديثات المعلقة: {e}")
        return False

def check_bot_status(bot_token: str) -> dict:
    """فحص حالة البوت"""
    try:
        print("🔍 فحص حالة البوت...")
        
        # معلومات البوت
        bot_info = get_bot_info(bot_token)
        if not bot_info:
            return {"status": "error", "message": "فشل في الحصول على معلومات البوت"}
        
        # فحص webhook
        url = f"https://api.telegram.org/bot{bot_token}/getWebhookInfo"
        response = requests.get(url, timeout=10)
        
        webhook_info = {}
        if response.status_code == 200:
            result = response.json()
            if result.get('ok'):
                webhook_info = result.get('result', {})
        
        return {
            "status": "success",
            "bot_info": bot_info,
            "webhook_info": webhook_info,
            "has_webhook": bool(webhook_info.get('url')),
            "pending_updates": webhook_info.get('pending_update_count', 0)
        }
        
    except Exception as e:
        return {"status": "error", "message": f"خطأ في فحص حالة البوت: {e}"}

def fix_telegram_conflict():
    """إصلاح مشكلة Telegram getUpdates conflict"""
    print("🚀 إصلاح مشكلة Telegram getUpdates Conflict")
    print("=" * 60)
    
    # الحصول على token البوت
    bot_token = None
    
    # محاولة الحصول على token من متغيرات البيئة
    possible_tokens = [
        os.getenv('BOT_TOKEN'),
        os.getenv('TELEGRAM_BOT_TOKEN'),
        '**********************************************'  # fallback
    ]
    
    for token in possible_tokens:
        if token and token.strip():
            bot_token = token.strip()
            break
    
    if not bot_token:
        print("❌ لم يتم العثور على token البوت")
        return False
    
    print(f"🤖 استخدام البوت: {bot_token[:10]}...")
    
    # فحص حالة البوت
    status = check_bot_status(bot_token)
    if status["status"] == "error":
        print(f"❌ {status['message']}")
        return False
    
    bot_info = status["bot_info"]
    webhook_info = status["webhook_info"]
    
    print(f"✅ البوت: @{bot_info.get('username', 'غير معروف')}")
    print(f"📝 الاسم: {bot_info.get('first_name', 'غير معروف')}")
    
    if status["has_webhook"]:
        print(f"🌐 Webhook نشط: {webhook_info.get('url')}")
        print(f"📊 التحديثات المعلقة: {webhook_info.get('pending_update_count', 0)}")
        
        # مسح webhook
        if not clear_telegram_webhook(bot_token):
            print("❌ فشل في مسح webhook")
            return False
    else:
        print("ℹ️ لا يوجد webhook نشط")
    
    # مسح التحديثات المعلقة
    if not clear_pending_updates(bot_token):
        print("⚠️ تحذير: فشل في مسح التحديثات المعلقة")
    
    # انتظار قصير للتأكد من تطبيق التغييرات
    print("⏳ انتظار تطبيق التغييرات...")
    time.sleep(3)
    
    # فحص نهائي
    final_status = check_bot_status(bot_token)
    if final_status["status"] == "success":
        if not final_status["has_webhook"]:
            print("✅ تم حل مشكلة getUpdates conflict بنجاح!")
            print("🎉 البوت جاهز للاستخدام مع polling")
            return True
        else:
            print("⚠️ لا يزال هناك webhook نشط")
            return False
    else:
        print(f"❌ فشل في الفحص النهائي: {final_status['message']}")
        return False

def main():
    """الدالة الرئيسية"""
    try:
        success = fix_telegram_conflict()
        
        if success:
            print("\n" + "=" * 60)
            print("🎉 تم إصلاح مشكلة Telegram بنجاح!")
            print("📋 الخطوات التالية:")
            print("1. تشغيل البوت: python main.py")
            print("2. أو تشغيل بوت Telegram: python run_telegram_bot.py")
            print("3. تأكد من عدم تشغيل أكثر من instance واحد")
            return True
        else:
            print("\n" + "=" * 60)
            print("❌ فشل في إصلاح مشكلة Telegram")
            print("💡 حلول بديلة:")
            print("1. تأكد من إيقاف جميع instances الأخرى للبوت")
            print("2. انتظر 5-10 دقائق ثم حاول مرة أخرى")
            print("3. تحقق من صحة token البوت")
            return False
            
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف العملية بواسطة المستخدم")
        return False
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
