# ملف متغيرات البيئة - Environment Variables
# انسخ هذا الملف إلى .env وأدخل القيم الحقيقية
# Copy this file to .env and enter the real values

# ===== إعدادات البوت الأساسية =====
# Bot Token from @BotFather
BOT_TOKEN=7605181405:AAEBua03X_QdD6GkCqKWR1P02EtXIbnSkx4

# Admin Chat ID (your Telegram user ID)
ADMIN_CHAT_ID=7513880877

# Admin Username (optional, for display purposes)
ADMIN_USERNAME=Kim880198

# ===== إعدادات قاعدة البيانات =====
# Supabase Database URL
SUPABASE_URL=https://ytqxxodyecdeosnqoure.supabase.co

# Supabase API Key (anon key)
SUPABASE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4

# ===== إعدادات Firebase (للتخزين) =====
# Firebase Project ID
FIREBASE_PROJECT_ID=your_firebase_project_id_here

# Firebase Storage Bucket
FIREBASE_STORAGE_BUCKET=your_firebase_storage_bucket_here

# Firebase API Key
FIREBASE_API_KEY=your_firebase_api_key_here

# ===== إعدادات Gemini AI =====
# Gemini API Keys (separated by commas for multiple keys)
GEMINI_API_KEYS=key1,key2,key3

# ===== إعدادات الأمان =====
# Secret key for encryption (generate a random 32-byte key)
ENCRYPTION_SECRET=your_32_byte_encryption_secret_here

# JWT Secret for session management
JWT_SECRET=your_jwt_secret_here

# ===== إعدادات اختيارية =====
# Environment (development/production)
ENVIRONMENT=production

# Debug mode (true/false)
DEBUG=false

# Log level (DEBUG/INFO/WARNING/ERROR/CRITICAL)
LOG_LEVEL=INFO

# Maximum file size for uploads (in MB)
MAX_FILE_SIZE_MB=50

# Rate limiting settings
MAX_REQUESTS_PER_MINUTE=30
MAX_REQUESTS_PER_HOUR=200

# Session timeout (in hours)
SESSION_TIMEOUT_HOURS=24

# ===== إعدادات الشبكة =====
# Proxy settings (if needed)
HTTP_PROXY=
HTTPS_PROXY=

# Webhook URL (if using webhooks instead of polling)
WEBHOOK_URL=

# Webhook secret token
WEBHOOK_SECRET=

# ===== إعدادات التخزين =====
# Local storage path for temporary files
TEMP_STORAGE_PATH=./temp

# Maximum storage size (in GB)
MAX_STORAGE_SIZE_GB=10

# ===== إعدادات التنبيهات =====
# Email settings for critical alerts
SMTP_SERVER=
SMTP_PORT=587
SMTP_USERNAME=
SMTP_PASSWORD=
ALERT_EMAIL=

# ===== إعدادات النسخ الاحتياطي =====
# Backup frequency (in hours)
BACKUP_FREQUENCY_HOURS=24

# Backup retention (in days)
BACKUP_RETENTION_DAYS=30

# Remote backup location
BACKUP_REMOTE_URL=

# ===== إعدادات المراقبة =====
# Monitoring service URL
MONITORING_URL=

# Health check interval (in minutes)
HEALTH_CHECK_INTERVAL=5

# ===== ملاحظات مهمة =====
# 1. لا تشارك هذا الملف مع أي شخص
# 2. أضف .env إلى .gitignore
# 3. استخدم كلمات مرور قوية
# 4. غيّر المفاتيح بانتظام
# 5. احتفظ بنسخة احتياطية آمنة من هذا الملف

# ===== كيفية إنشاء مفاتيح آمنة =====
# يمكنك استخدام Python لإنشاء مفاتيح عشوائية:
# import secrets
# print(secrets.token_hex(32))  # للمفاتيح العامة
# print(secrets.token_urlsafe(32))  # للمفاتيح الآمنة للURL
