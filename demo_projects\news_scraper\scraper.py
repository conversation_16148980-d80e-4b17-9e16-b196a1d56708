#!/usr/bin/env python3
"""
أداة استخراج الأخبار من المواقع
News Web Scraper Tool
"""

import requests
from bs4 import BeautifulSoup
import json
import csv
from datetime import datetime
import time

class NewsScraper:
    """أداة استخراج الأخبار من مواقع مختلفة"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
    def scrape_website(self, url: str, selectors: dict) -> list:
        """
        استخراج الأخبار من موقع محدد
        
        Args:
            url: رابط الموقع
            selectors: محددات CSS للعناصر
            
        Returns:
            قائمة بالأخبار المستخرجة
        """
        try:
            response = self.session.get(url)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            articles = []
            
            for article in soup.select(selectors['article']):
                title = article.select_one(selectors['title'])
                link = article.select_one(selectors['link'])
                summary = article.select_one(selectors['summary'])
                date = article.select_one(selectors['date'])
                
                articles.append({
                    'title': title.get_text().strip() if title else '',
                    'link': link.get('href') if link else '',
                    'summary': summary.get_text().strip() if summary else '',
                    'date': date.get_text().strip() if date else '',
                    'source': url,
                    'scraped_at': datetime.now().isoformat()
                })
                
            return articles
            
        except Exception as e:
            print(f"خطأ في استخراج البيانات من {url}: {e}")
            return []
    
    def save_to_json(self, articles: list, filename: str):
        """حفظ البيانات في ملف JSON"""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(articles, f, ensure_ascii=False, indent=2)
        print(f"✅ تم حفظ {len(articles)} خبر في {filename}")
    
    def save_to_csv(self, articles: list, filename: str):
        """حفظ البيانات في ملف CSV"""
        if not articles:
            return
            
        with open(filename, 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=articles[0].keys())
            writer.writeheader()
            writer.writerows(articles)
        print(f"✅ تم حفظ {len(articles)} خبر في {filename}")

def main():
    """الدالة الرئيسية"""
    scraper = NewsScraper()
    
    # إعدادات المواقع
    websites = {
        'example_news': {
            'url': 'https://example-news.com',
            'selectors': {
                'article': '.article',
                'title': '.title',
                'link': 'a',
                'summary': '.summary',
                'date': '.date'
            }
        }
    }
    
    all_articles = []
    
    for site_name, config in websites.items():
        print(f"🔍 استخراج الأخبار من {site_name}...")
        articles = scraper.scrape_website(config['url'], config['selectors'])
        all_articles.extend(articles)
        time.sleep(2)  # تأخير بين الطلبات
    
    # حفظ النتائج
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    scraper.save_to_json(all_articles, f"news_{timestamp}.json")
    scraper.save_to_csv(all_articles, f"news_{timestamp}.csv")
    
    print(f"🎉 تم استخراج {len(all_articles)} خبر بنجاح!")

if __name__ == "__main__":
    main()
