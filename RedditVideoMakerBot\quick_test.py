#!/usr/bin/env python3
"""
اختبار سريع للنظام الجديد
"""

def test_imports():
    """اختبار استيراد الوحدات"""
    print("🔍 اختبار استيراد الوحدات...")
    
    try:
        from automation.telegram_api_manager import TelegramAPIManager
        print("✅ TelegramAPIManager")
    except Exception as e:
        print(f"❌ TelegramAPIManager: {e}")
    
    try:
        from TTS.smart_tts_manager import SmartTTSManager
        print("✅ SmartTTSManager")
    except Exception as e:
        print(f"❌ SmartTTSManager: {e}")
    
    try:
        from TTS.elevenlabs import elevenlabs
        print("✅ ElevenLabs with free support")
    except Exception as e:
        print(f"❌ ElevenLabs: {e}")
    
    try:
        from automation.smart_monitor import smart_monitor
        print("✅ SmartMonitor")
    except Exception as e:
        print(f"❌ SmartMonitor: {e}")

def test_config():
    """اختبار التكوين"""
    print("\n🔧 اختبار التكوين...")
    
    try:
        from utils import settings
        
        # فحص إعدادات TTS
        tts_config = settings.config["settings"]["tts"]
        print(f"✅ Voice choice: {tts_config.get('voice_choice', 'غير محدد')}")
        print(f"✅ ElevenLabs free: {tts_config.get('elevenlabs_use_free', False)}")
        print(f"✅ ElevenLabs voice ID: {tts_config.get('elevenlabs_voice_id', 'غير محدد')}")
        
        # فحص إعدادات التيليجرام
        telegram_config = settings.config.get("telegram", {})
        bot_token = telegram_config.get("bot_token", "")
        if bot_token:
            print(f"✅ Telegram bot token: {bot_token[:10]}...")
        else:
            print("⚠️ Telegram bot token غير محدد")
            
    except Exception as e:
        print(f"❌ خطأ في التكوين: {e}")

def test_tts_manager():
    """اختبار مدير TTS الذكي"""
    print("\n🎤 اختبار مدير TTS الذكي...")
    
    try:
        from TTS.smart_tts_manager import SmartTTSManager
        
        manager = SmartTTSManager()
        print("✅ تم إنشاء SmartTTSManager")
        
        # فحص المحركات المتاحة
        available_engines = []
        for engine_name in manager.engines.keys():
            if manager._is_engine_available(engine_name):
                available_engines.append(engine_name)
        
        print(f"✅ المحركات المتاحة: {', '.join(available_engines)}")
        
        # تقرير الحالة
        status_report = manager.get_status_report()
        print("✅ تقرير الحالة متاح")
        
    except Exception as e:
        print(f"❌ خطأ في مدير TTS: {e}")

def main():
    """الدالة الرئيسية للاختبار"""
    print("""
    🧪 اختبار سريع للنظام الذكي
    ================================
    """)
    
    test_imports()
    test_config()
    test_tts_manager()
    
    print("""
    ================================
    ✅ انتهى الاختبار!
    
    للتشغيل:
    1. مدير التيليجرام: python start_telegram_manager.py
    2. النظام الكامل: python start_smart_system.py
    3. البوت العادي: python main.py
    """)

if __name__ == "__main__":
    main()
