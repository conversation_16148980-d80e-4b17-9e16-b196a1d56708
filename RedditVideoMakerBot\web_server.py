#!/usr/bin/env python3
"""
خادم ويب بسيط لمراقبة حالة البوت على Render
"""

import os
import json
import logging
from datetime import datetime
from flask import Flask, jsonify, render_template_string
from pathlib import Path
import threading
import time

app = Flask(__name__)
logger = logging.getLogger(__name__)

# قالب HTML بسيط لعرض حالة البوت
HTML_TEMPLATE = """
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مراقبة بوت Reddit Video Maker</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .status-card {
            background: rgba(255, 255, 255, 0.2);
            padding: 20px;
            margin: 15px 0;
            border-radius: 10px;
            border-left: 4px solid #4CAF50;
        }
        .status-error {
            border-left-color: #f44336;
        }
        .status-warning {
            border-left-color: #ff9800;
        }
        .logs-container {
            background: rgba(0, 0, 0, 0.3);
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
            max-height: 400px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }
        .refresh-btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
        }
        .refresh-btn:hover {
            background: #45a049;
        }
    </style>
    <script>
        function refreshPage() {
            location.reload();
        }
        
        // تحديث تلقائي كل 30 ثانية
        setInterval(refreshPage, 30000);
    </script>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 مراقبة بوت Reddit Video Maker</h1>
            <p>آخر تحديث: {{ current_time }}</p>
            <button class="refresh-btn" onclick="refreshPage()">🔄 تحديث</button>
        </div>
        
        <div class="status-card {{ status_class }}">
            <h3>📊 حالة البوت</h3>
            <p><strong>الحالة:</strong> {{ bot_status }}</p>
            <p><strong>وقت التشغيل:</strong> {{ uptime }}</p>
            <p><strong>عدد الفيديوهات المنتجة:</strong> {{ videos_count }}</p>
        </div>
        
        <div class="status-card">
            <h3>⚙️ معلومات النظام</h3>
            <p><strong>Python Version:</strong> {{ python_version }}</p>
            <p><strong>Platform:</strong> {{ platform }}</p>
            <p><strong>Environment:</strong> {{ environment }}</p>
        </div>
        
        <div class="logs-container">
            <h3>📝 آخر السجلات</h3>
            <pre>{{ recent_logs }}</pre>
        </div>
    </div>
</body>
</html>
"""

class BotMonitor:
    def __init__(self):
        self.start_time = datetime.now()
        self.videos_count = 0
        self.bot_status = "يعمل"
        
    def get_status(self):
        """الحصول على حالة البوت"""
        uptime = datetime.now() - self.start_time
        uptime_str = str(uptime).split('.')[0]  # إزالة الميكروثواني
        
        # قراءة السجلات الحديثة
        recent_logs = self.get_recent_logs()
        
        # تحديد حالة البوت
        status_class = "status-card"
        if "error" in recent_logs.lower() or "خطأ" in recent_logs:
            status_class += " status-error"
            self.bot_status = "خطأ"
        elif "warning" in recent_logs.lower() or "تحذير" in recent_logs:
            status_class += " status-warning"
            self.bot_status = "تحذير"
        else:
            self.bot_status = "يعمل بشكل طبيعي"
        
        return {
            'current_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'bot_status': self.bot_status,
            'uptime': uptime_str,
            'videos_count': self.videos_count,
            'python_version': os.sys.version.split()[0],
            'platform': os.name,
            'environment': 'Render' if os.getenv('RENDER') else 'Local',
            'recent_logs': recent_logs,
            'status_class': status_class
        }
    
    def get_recent_logs(self):
        """قراءة آخر السجلات"""
        try:
            log_file = Path('logs/main.log')
            if log_file.exists():
                with open(log_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    # أخذ آخر 20 سطر
                    recent_lines = lines[-20:] if len(lines) > 20 else lines
                    return ''.join(recent_lines)
            else:
                return "لا توجد سجلات متاحة"
        except Exception as e:
            return f"خطأ في قراءة السجلات: {e}"

# إنشاء مراقب البوت
monitor = BotMonitor()

@app.route('/')
def dashboard():
    """الصفحة الرئيسية لمراقبة البوت"""
    status = monitor.get_status()
    return render_template_string(HTML_TEMPLATE, **status)

@app.route('/api/status')
def api_status():
    """API للحصول على حالة البوت"""
    return jsonify(monitor.get_status())

@app.route('/health')
def health_check():
    """فحص صحة الخدمة"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'uptime': str(datetime.now() - monitor.start_time)
    })

@app.route('/logs')
def get_logs():
    """الحصول على السجلات"""
    logs = monitor.get_recent_logs()
    return jsonify({'logs': logs})

def run_web_server():
    """تشغيل خادم الويب"""
    port = int(os.environ.get('PORT', 5000))
    app.run(host='0.0.0.0', port=port, debug=False)

if __name__ == '__main__':
    run_web_server()
