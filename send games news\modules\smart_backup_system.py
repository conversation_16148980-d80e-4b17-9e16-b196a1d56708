#!/usr/bin/env python3
"""
نظام النسخ الاحتياطي الذكي
"""

import os
import shutil
import json
import sqlite3
import zipfile
import hashlib
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import threading
from pathlib import Path

class SmartBackupSystem:
    """نظام النسخ الاحتياطي الذكي"""
    
    def __init__(self, backup_base_dir: str = "backups"):
        self.backup_base_dir = backup_base_dir
        self.lock = threading.RLock()
        
        # إعدادات النسخ الاحتياطي
        self.settings = {
            "max_backups": 10,  # الحد الأقصى للنسخ المحفوظة
            "auto_cleanup_days": 30,  # حذف النسخ الأقدم من 30 يوم
            "compression_enabled": True,  # ضغط النسخ الاحتياطية
            "verify_integrity": True,  # التحقق من سلامة النسخ
            "incremental_backup": False  # النسخ التدريجي (مستقبلاً)
        }
        
        # إنشاء مجلد النسخ الاحتياطي
        os.makedirs(self.backup_base_dir, exist_ok=True)
        
        # قاعدة بيانات النسخ الاحتياطية
        self.db_path = os.path.join(self.backup_base_dir, "backup_index.db")
        self._init_backup_database()
    
    def _init_backup_database(self):
        """تهيئة قاعدة بيانات النسخ الاحتياطية"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS backups (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        backup_id TEXT UNIQUE NOT NULL,
                        backup_type TEXT NOT NULL,
                        created_at TEXT NOT NULL,
                        size_bytes INTEGER NOT NULL,
                        file_count INTEGER NOT NULL,
                        compressed BOOLEAN DEFAULT FALSE,
                        verified BOOLEAN DEFAULT FALSE,
                        metadata TEXT,
                        notes TEXT
                    )
                """)
                
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS backup_files (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        backup_id TEXT NOT NULL,
                        original_path TEXT NOT NULL,
                        backup_path TEXT NOT NULL,
                        file_hash TEXT,
                        file_size INTEGER,
                        FOREIGN KEY (backup_id) REFERENCES backups (backup_id)
                    )
                """)
        except Exception as e:
            print(f"خطأ في تهيئة قاعدة بيانات النسخ الاحتياطية: {e}")
    
    def create_full_backup(self, backup_type: str = "manual", notes: str = "") -> Dict[str, Any]:
        """إنشاء نسخة احتياطية كاملة"""
        with self.lock:
            backup_id = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            backup_dir = os.path.join(self.backup_base_dir, backup_id)
            
            backup_info = {
                "backup_id": backup_id,
                "backup_type": backup_type,
                "created_at": datetime.now().isoformat(),
                "backup_dir": backup_dir,
                "files": [],
                "errors": [],
                "total_size": 0,
                "success": False
            }
            
            try:
                os.makedirs(backup_dir, exist_ok=True)
                
                # قائمة الملفات المهمة للنسخ الاحتياطي
                important_files = [
                    # مفاتيح API والإعدادات
                    "data/api_keys.db",
                    "config/blogger_token.json",
                    "config/settings.py",
                    ".env",
                    
                    # قاعدة بيانات المقالات
                    "data/articles.db",
                    
                    # ملفات التكوين الأخرى
                    "config/api_config.py",
                    "config/sources_blacklist.py",
                    "config/bot_config.json"
                ]
                
                # نسخ الملفات
                for file_path in important_files:
                    if os.path.exists(file_path):
                        try:
                            # إنشاء مسار النسخة الاحتياطية
                            backup_file_path = os.path.join(backup_dir, file_path)
                            os.makedirs(os.path.dirname(backup_file_path), exist_ok=True)
                            
                            # نسخ الملف
                            shutil.copy2(file_path, backup_file_path)
                            
                            # حساب الهاش للتحقق من السلامة
                            file_hash = self._calculate_file_hash(file_path)
                            file_size = os.path.getsize(file_path)
                            
                            backup_info["files"].append({
                                "original_path": file_path,
                                "backup_path": backup_file_path,
                                "file_hash": file_hash,
                                "file_size": file_size
                            })
                            
                            backup_info["total_size"] += file_size
                            
                        except Exception as e:
                            backup_info["errors"].append({
                                "file": file_path,
                                "error": str(e)
                            })
                
                # ضغط النسخة الاحتياطية إذا كان مفعلاً
                if self.settings["compression_enabled"]:
                    compressed_path = f"{backup_dir}.zip"
                    self._compress_backup(backup_dir, compressed_path)
                    
                    # حذف المجلد غير المضغوط
                    shutil.rmtree(backup_dir)
                    backup_info["backup_dir"] = compressed_path
                    backup_info["compressed"] = True
                
                # حفظ معلومات النسخة الاحتياطية
                self._save_backup_info(backup_info, notes)
                
                # التحقق من سلامة النسخة
                if self.settings["verify_integrity"]:
                    backup_info["verified"] = self._verify_backup_integrity(backup_info)
                
                backup_info["success"] = True
                
                # تنظيف النسخ القديمة
                self._cleanup_old_backups()
                
            except Exception as e:
                backup_info["error"] = str(e)
                # حذف النسخة الفاشلة
                if os.path.exists(backup_dir):
                    shutil.rmtree(backup_dir)
            
            return backup_info
    
    def restore_backup(self, backup_id: str, target_dir: str = ".") -> Dict[str, Any]:
        """استعادة نسخة احتياطية"""
        restore_info = {
            "backup_id": backup_id,
            "success": False,
            "restored_files": [],
            "errors": []
        }
        
        try:
            # البحث عن النسخة الاحتياطية
            backup_path = os.path.join(self.backup_base_dir, backup_id)
            compressed_path = f"{backup_path}.zip"
            
            if os.path.exists(compressed_path):
                # استخراج النسخة المضغوطة
                temp_dir = f"{backup_path}_temp"
                with zipfile.ZipFile(compressed_path, 'r') as zip_ref:
                    zip_ref.extractall(temp_dir)
                backup_path = temp_dir
            
            if not os.path.exists(backup_path):
                restore_info["error"] = "النسخة الاحتياطية غير موجودة"
                return restore_info
            
            # استعادة الملفات
            for root, dirs, files in os.walk(backup_path):
                for file in files:
                    source_file = os.path.join(root, file)
                    relative_path = os.path.relpath(source_file, backup_path)
                    target_file = os.path.join(target_dir, relative_path)
                    
                    try:
                        # إنشاء المجلد إذا لم يكن موجوداً
                        os.makedirs(os.path.dirname(target_file), exist_ok=True)
                        
                        # نسخ الملف
                        shutil.copy2(source_file, target_file)
                        
                        restore_info["restored_files"].append({
                            "source": source_file,
                            "target": target_file
                        })
                        
                    except Exception as e:
                        restore_info["errors"].append({
                            "file": relative_path,
                            "error": str(e)
                        })
            
            # حذف المجلد المؤقت إذا كان موجوداً
            if 'temp_dir' in locals() and os.path.exists(temp_dir):
                shutil.rmtree(temp_dir)
            
            restore_info["success"] = len(restore_info["errors"]) == 0
            
        except Exception as e:
            restore_info["error"] = str(e)
        
        return restore_info
    
    def list_backups(self) -> List[Dict[str, Any]]:
        """قائمة النسخ الاحتياطية"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                rows = conn.execute("""
                    SELECT * FROM backups ORDER BY created_at DESC
                """).fetchall()
                
                backups = []
                for row in rows:
                    backup_dict = dict(row)
                    
                    # إضافة معلومات إضافية
                    backup_path = os.path.join(self.backup_base_dir, backup_dict["backup_id"])
                    compressed_path = f"{backup_path}.zip"
                    
                    if os.path.exists(compressed_path):
                        backup_dict["actual_size"] = os.path.getsize(compressed_path)
                        backup_dict["exists"] = True
                    elif os.path.exists(backup_path):
                        backup_dict["actual_size"] = self._get_directory_size(backup_path)
                        backup_dict["exists"] = True
                    else:
                        backup_dict["exists"] = False
                    
                    backups.append(backup_dict)
                
                return backups
                
        except Exception as e:
            print(f"خطأ في قائمة النسخ الاحتياطية: {e}")
            return []
    
    def delete_backup(self, backup_id: str) -> bool:
        """حذف نسخة احتياطية"""
        try:
            # حذف الملفات
            backup_path = os.path.join(self.backup_base_dir, backup_id)
            compressed_path = f"{backup_path}.zip"
            
            if os.path.exists(compressed_path):
                os.remove(compressed_path)
            elif os.path.exists(backup_path):
                shutil.rmtree(backup_path)
            
            # حذف من قاعدة البيانات
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("DELETE FROM backup_files WHERE backup_id = ?", (backup_id,))
                conn.execute("DELETE FROM backups WHERE backup_id = ?", (backup_id,))
            
            return True
            
        except Exception as e:
            print(f"خطأ في حذف النسخة الاحتياطية: {e}")
            return False
    
    def _compress_backup(self, source_dir: str, target_zip: str):
        """ضغط النسخة الاحتياطية"""
        with zipfile.ZipFile(target_zip, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk(source_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    arc_name = os.path.relpath(file_path, source_dir)
                    zipf.write(file_path, arc_name)
    
    def _calculate_file_hash(self, file_path: str) -> str:
        """حساب هاش الملف للتحقق من السلامة"""
        hash_md5 = hashlib.md5()
        try:
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except:
            return ""
    
    def _verify_backup_integrity(self, backup_info: Dict[str, Any]) -> bool:
        """التحقق من سلامة النسخة الاحتياطية"""
        try:
            for file_info in backup_info["files"]:
                backup_path = file_info["backup_path"]
                expected_hash = file_info["file_hash"]
                
                if os.path.exists(backup_path):
                    actual_hash = self._calculate_file_hash(backup_path)
                    if actual_hash != expected_hash:
                        return False
                else:
                    return False
            
            return True
        except:
            return False
    
    def _save_backup_info(self, backup_info: Dict[str, Any], notes: str = ""):
        """حفظ معلومات النسخة الاحتياطية في قاعدة البيانات"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                # حفظ معلومات النسخة الأساسية
                conn.execute("""
                    INSERT INTO backups (backup_id, backup_type, created_at, size_bytes, 
                                       file_count, compressed, verified, metadata, notes)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    backup_info["backup_id"],
                    backup_info["backup_type"],
                    backup_info["created_at"],
                    backup_info["total_size"],
                    len(backup_info["files"]),
                    backup_info.get("compressed", False),
                    backup_info.get("verified", False),
                    json.dumps(backup_info, ensure_ascii=False),
                    notes
                ))
                
                # حفظ معلومات الملفات
                for file_info in backup_info["files"]:
                    conn.execute("""
                        INSERT INTO backup_files (backup_id, original_path, backup_path, 
                                                file_hash, file_size)
                        VALUES (?, ?, ?, ?, ?)
                    """, (
                        backup_info["backup_id"],
                        file_info["original_path"],
                        file_info["backup_path"],
                        file_info["file_hash"],
                        file_info["file_size"]
                    ))
        except Exception as e:
            print(f"خطأ في حفظ معلومات النسخة الاحتياطية: {e}")
    
    def _cleanup_old_backups(self):
        """تنظيف النسخ الاحتياطية القديمة"""
        try:
            backups = self.list_backups()
            
            # حذف النسخ الزائدة عن الحد الأقصى
            if len(backups) > self.settings["max_backups"]:
                old_backups = backups[self.settings["max_backups"]:]
                for backup in old_backups:
                    self.delete_backup(backup["backup_id"])
            
            # حذف النسخ الأقدم من المدة المحددة
            cutoff_date = datetime.now() - timedelta(days=self.settings["auto_cleanup_days"])
            for backup in backups:
                backup_date = datetime.fromisoformat(backup["created_at"])
                if backup_date < cutoff_date:
                    self.delete_backup(backup["backup_id"])
                    
        except Exception as e:
            print(f"خطأ في تنظيف النسخ القديمة: {e}")
    
    def _get_directory_size(self, directory: str) -> int:
        """حساب حجم المجلد"""
        total_size = 0
        try:
            for dirpath, dirnames, filenames in os.walk(directory):
                for filename in filenames:
                    file_path = os.path.join(dirpath, filename)
                    try:
                        total_size += os.path.getsize(file_path)
                    except:
                        pass
        except:
            pass
        return total_size

# إنشاء مثيل عام
smart_backup_system = SmartBackupSystem()
