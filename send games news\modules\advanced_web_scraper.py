# نظام متطور لاستخراج البيانات من الويب
import aiohttp
import asyncio
import json
import time
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple
import hashlib
import re
from urllib.parse import urljoin, urlparse, quote
from bs4 import BeautifulSoup
import random
from .logger import logger
from .database import db
from .serpapi_search import serpapi_search
from config.settings import BotConfig, google_search_api_manager

class AdvancedWebScraper:
    """نظام متطور لاستخراج البيانات من الويب باستخدام تقنيات متقدمة"""
    
    def __init__(self):
        # إعدادات Brave Search API
        self.brave_search_key = getattr(BotConfig, 'BRAVE_SEARCH_KEY', '')
        self.brave_search_url = 'https://api.search.brave.com/res/v1/web/search'
        
        # إعدادات Google Custom Search مع مدير المفاتيح
        self.google_search_api_manager = google_search_api_manager
        self.google_search_key = getattr(BotConfig, 'GOOGLE_SEARCH_KEY', '')
        self.google_search_engine_id = getattr(BotConfig, 'GOOGLE_SEARCH_ENGINE_ID', '')
        self.google_search_url = 'https://www.googleapis.com/customsearch/v1'
        
        # User Agents متنوعة لتجنب الحظر
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:109.0) Gecko/20100101 Firefox/121.0'
        ]
        
        # مواقع الألعاب المتخصصة للاستخراج المباشر - محدثة 2025
        self.gaming_sites = {
            'ign.com': {
                'selectors': {
                    'title': 'h1, .headline, .article-title, [data-cy="headline"]',
                    'content': 'article, .article-content, .article-body, .content-body',
                    'date': 'time, .publish-date, .date, [datetime]',
                    'author': '.author-name, .byline, .author, [rel="author"]'
                },
                'rate_limit': 2
            },
            'gamespot.com': {
                'selectors': {
                    'title': 'h1, .article-title, .headline, .js-article-title',
                    'content': 'article, .article-body, .js-content-entity-body, .content',
                    'date': 'time, .publish-date, .js-publish-date, .date',
                    'author': '.author, .js-author-name, .byline, .author-name'
                },
                'rate_limit': 2
            },
            'polygon.com': {
                'selectors': {
                    'title': 'h1, .duet--article--dangerously-set-cms-markup, .headline, .article-title',
                    'content': 'article, .duet--article--article-body, .article-content, .content',
                    'date': 'time, .publish-date, .date',
                    'author': '.author-name, .author, .byline'
                },
                'rate_limit': 3
            },
            'kotaku.com': {
                'selectors': {
                    'title': 'h1, .headline, .article-title, .post-title',
                    'content': 'article, .post-content, .article-content, .content',
                    'date': 'time, .publish-time, .publish-date, .date',
                    'author': '.author, .byline, .author-name, .post-author'
                },
                'rate_limit': 3
            },
            'eurogamer.net': {
                'selectors': {
                    'title': 'h1, .article_header, .headline, .article-title',
                    'content': 'article, .article_body_content, .article-content, .content',
                    'date': 'time, .article_date, .publish-date, .date',
                    'author': '.article_author, .author, .byline, .author-name'
                },
                'rate_limit': 2
            },
            'pcgamer.com': {
                'selectors': {
                    'title': 'h1, .article-title, .headline',
                    'content': 'article, .article-content, .content',
                    'date': 'time, .publish-date, .date',
                    'author': '.author, .byline, .author-name'
                },
                'rate_limit': 2
            }
        }
        
        # إحصائيات الاستخراج
        self.extraction_stats = {
            'serpapi_calls': 0,
            'brave_search_calls': 0,
            'google_search_calls': 0,
            'direct_scraping_calls': 0,
            'successful_extractions': 0,
            'failed_extractions': 0,
            'total_articles_extracted': 0
        }
        
        # تخزين مؤقت للنتائج
        self.cache = {}
        self.cache_duration = 1800  # 30 دقيقة
    
    async def comprehensive_gaming_search(self, 
                                        query: str, 
                                        max_results: int = 30,
                                        include_direct_scraping: bool = True) -> List[Dict]:
        """بحث شامل عن أخبار الألعاب باستخدام محركات بحث متعددة"""
        try:
            logger.info(f"🔍 بدء البحث الشامل عن: {query}")
            
            all_results = []

            # 1. البحث باستخدام SerpAPI (الأولوية الأولى - أفضل بديل لـ Google)
            if serpapi_search.enabled:
                serpapi_results = await self._search_with_serpapi(query, max_results // 2)
                all_results.extend(serpapi_results)
                logger.info(f"🔍 SerpAPI: {len(serpapi_results)} نتيجة")

            # 2. البحث باستخدام Brave Search (احتياطي)
            if self.brave_search_key:
                brave_results = await self._search_with_brave(query, max_results // 4)
                all_results.extend(brave_results)

            # 3. البحث باستخدام Google Custom Search (احتياطي أخير)
            if self.google_search_key and self.google_search_engine_id and len(all_results) < 5:
                google_results = await self._search_with_google(query, max_results // 4)
                all_results.extend(google_results)
            
            # 3. الاستخراج المباشر من المواقع المتخصصة
            if include_direct_scraping:
                direct_results = await self._direct_scraping_from_gaming_sites(query, max_results // 3)
                all_results.extend(direct_results)
            
            # تصفية وتنظيف النتائج
            filtered_results = await self._filter_and_enhance_results(all_results)
            
            # ترتيب حسب الجودة والصلة
            sorted_results = self._sort_by_relevance_and_quality(filtered_results, query)
            
            self.extraction_stats['total_articles_extracted'] += len(sorted_results)
            
            logger.info(f"✅ تم العثور على {len(sorted_results)} نتيجة عالية الجودة")
            
            return sorted_results[:max_results]
            
        except Exception as e:
            logger.error(f"❌ فشل في البحث الشامل: {e}")
            return []

    async def _search_with_serpapi(self, query: str, max_results: int) -> List[Dict]:
        """البحث باستخدام SerpAPI عبر RapidAPI - البديل الأفضل لـ Google"""
        try:
            logger.info("🚀 البحث باستخدام SerpAPI (أفضل بديل لـ Google)...")

            # استخدام SerpAPI للبحث
            results = await serpapi_search.search(
                query=f"{query} gaming news",
                num_results=max_results,
                tbm='nws',  # البحث في الأخبار
                tbs='qdr:w'  # الأسبوع الماضي
            )

            # معالجة النتائج وتحويلها للتنسيق المطلوب
            processed_results = []
            for result in results:
                processed_result = {
                    'title': result.get('title', ''),
                    'url': result.get('link', ''),
                    'summary': result.get('snippet', ''),
                    'source': result.get('source', 'SerpAPI'),
                    'published_date': result.get('date', ''),
                    'content_quality': result.get('relevance_score', 5),
                    'search_engine': 'SerpAPI',
                    'extraction_method': 'serpapi_search'
                }

                # فلترة النتائج عالية الجودة فقط
                if (len(processed_result['title']) > 15 and
                    len(processed_result['summary']) > 50 and
                    processed_result['content_quality'] >= 3):
                    processed_results.append(processed_result)

            self.extraction_stats['serpapi_calls'] += 1
            logger.info(f"🚀 SerpAPI: {len(processed_results)} نتيجة عالية الجودة")

            return processed_results

        except Exception as e:
            logger.error(f"❌ خطأ في SerpAPI: {e}")
            return []

    async def _search_with_brave(self, query: str, max_results: int) -> List[Dict]:
        """البحث باستخدام Brave Search API"""
        try:
            logger.info("🦁 البحث باستخدام Brave Search...")
            
            headers = {
                'Accept': 'application/json',
                'Accept-Encoding': 'gzip',
                'X-Subscription-Token': self.brave_search_key,
                'User-Agent': random.choice(self.user_agents)
            }
            
            params = {
                'q': f'{query} gaming video games',
                'count': max_results,
                'search_lang': 'en',
                'country': 'US',
                'safesearch': 'moderate',
                'freshness': 'pw'  # past week
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(self.brave_search_url, headers=headers, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        results = []
                        if data.get('web', {}).get('results'):
                            for result in data['web']['results']:
                                processed_result = self._process_brave_result(result)
                                if processed_result:
                                    results.append(processed_result)
                        
                        self.extraction_stats['brave_search_calls'] += 1
                        logger.info(f"🦁 Brave Search: {len(results)} نتيجة")
                        
                        return results
                    else:
                        logger.warning(f"⚠️ خطأ في Brave Search: {response.status}")
                        return []
            
        except Exception as e:
            logger.error(f"❌ خطأ في Brave Search: {e}")
            return []
    
    async def _search_with_google(self, query: str, max_results: int) -> List[Dict]:
        """البحث باستخدام النظام الذكي البديل (بدون Google Search API)"""
        try:
            logger.info("🔍 البحث باستخدام النظام الذكي البديل...")

            # استخدام النظام الذكي البديل
            from .intelligent_search_system import intelligent_search

            results = await intelligent_search.search(
                query=query,
                max_results=max_results,
                search_type='gaming_news'
            )

            if results:
                logger.info(f"✅ النظام الذكي: {len(results)} نتيجة")
                return results
            else:
                logger.warning("⚠️ لم يتم العثور على نتائج من النظام الذكي")
                return []

        except Exception as e:
            logger.error(f"❌ خطأ في النظام الذكي البديل: {e}")
            return []
    
    async def _direct_scraping_from_gaming_sites(self, query: str, max_results: int) -> List[Dict]:
        """الاستخراج المباشر من مواقع الألعاب المتخصصة"""
        try:
            logger.info("🎮 الاستخراج المباشر من مواقع الألعاب...")
            
            all_results = []
            
            for site_domain, site_config in self.gaming_sites.items():
                try:
                    # بناء URL البحث للموقع
                    search_url = f"https://{site_domain}/search?q={quote(query)}"
                    
                    # استخراج المقالات من الموقع
                    site_results = await self._scrape_gaming_site(search_url, site_config, max_results // len(self.gaming_sites))
                    all_results.extend(site_results)
                    
                    # احترام حدود المعدل
                    await asyncio.sleep(site_config.get('rate_limit', 2))
                    
                except Exception as e:
                    logger.warning(f"⚠️ فشل في استخراج من {site_domain}: {e}")
                    continue
            
            self.extraction_stats['direct_scraping_calls'] += len(self.gaming_sites)
            logger.info(f"🎮 الاستخراج المباشر: {len(all_results)} مقال")
            
            return all_results
            
        except Exception as e:
            logger.error(f"❌ خطأ في الاستخراج المباشر: {e}")
            return []
    
    async def _scrape_gaming_site(self, url: str, site_config: Dict, max_articles: int) -> List[Dict]:
        """استخراج المقالات من موقع ألعاب محدد"""
        try:
            headers = {
                'User-Agent': random.choice(self.user_agents),
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1'
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=headers, timeout=30) as response:
                    if response.status == 200:
                        html_content = await response.text()
                        soup = BeautifulSoup(html_content, 'html.parser')
                        
                        articles = []

                        # البحث عن روابط المقالات بطرق متعددة
                        article_links = self._find_article_links_enhanced(soup, url)

                        logger.debug(f"وجد {len(article_links)} رابط مقال محتمل في {url}")

                        for link_url in article_links[:max_articles]:
                            try:
                                # استخراج تفاصيل المقال
                                article_data = await self._extract_article_details(link_url, site_config)
                                if article_data:
                                    articles.append(article_data)

                                # تأخير قصير بين المقالات
                                await asyncio.sleep(1)

                            except Exception as e:
                                logger.debug(f"خطأ في استخراج مقال من {link_url}: {e}")
                                continue
                        
                        return articles
                    else:
                        return []
            
        except Exception as e:
            logger.debug(f"خطأ في استخراج من {url}: {e}")
            return []

    def _process_brave_result(self, result: Dict) -> Optional[Dict]:
        """معالجة نتيجة من Brave Search"""
        try:
            if not result.get('title') or not result.get('url'):
                return None

            return {
                'title': result['title'],
                'url': result['url'],
                'summary': result.get('description', ''),
                'content': '',  # سيتم ملؤه لاحقاً
                'published_date': self._extract_date_from_result(result),
                'source': urlparse(result['url']).netloc,
                'source_type': 'brave_search',
                'extraction_method': 'search_api',
                'quality_score': self._calculate_search_result_quality(result),
                'relevance_score': 0  # سيتم حسابه لاحقاً
            }

        except Exception as e:
            logger.debug(f"خطأ في معالجة نتيجة Brave: {e}")
            return None

    def _process_google_result(self, result: Dict) -> Optional[Dict]:
        """معالجة نتيجة من Google Search"""
        try:
            if not result.get('title') or not result.get('link'):
                return None

            return {
                'title': result['title'],
                'url': result['link'],
                'summary': result.get('snippet', ''),
                'content': '',
                'published_date': self._extract_date_from_result(result),
                'source': urlparse(result['link']).netloc,
                'source_type': 'google_search',
                'extraction_method': 'search_api',
                'quality_score': self._calculate_search_result_quality(result),
                'relevance_score': 0
            }

        except Exception as e:
            logger.debug(f"خطأ في معالجة نتيجة Google: {e}")
            return None

    def _is_article_link(self, href: str) -> bool:
        """فحص ما إذا كان الرابط يؤدي إلى مقال - محسن"""
        if not href:
            return False

        href_lower = href.lower()

        # تجنب الروابط غير المرغوبة أولاً
        avoid_patterns = [
            'javascript:', 'mailto:', '#', '/tag/', '/category/',
            '/author/', '/search/', '/login', '/register', '/contact',
            '/about', '/privacy', '/terms', '/sitemap', '/rss',
            '.pdf', '.jpg', '.png', '.gif', '.mp4', '.mp3', '.css', '.js'
        ]

        for pattern in avoid_patterns:
            if pattern in href_lower:
                return False

        # أنماط روابط المقالات - أكثر مرونة
        article_indicators = [
            # أنماط مباشرة
            '/article', '/news', '/review', '/feature', '/story',
            '/post', '/blog', '/guide', '/preview', '/interview',

            # أنماط خاصة بالألعاب
            '/gaming', '/games', '/esports', '/pc-gaming', '/console',
            '/playstation', '/xbox', '/nintendo', '/steam',

            # أنماط التاريخ
            '/2024/', '/2025/', '/2023/',

            # أنماط أخرى
            '/watch', '/video'
        ]

        # فحص وجود مؤشرات المقالات
        has_article_indicator = any(indicator in href_lower for indicator in article_indicators)

        # فحص وجود أرقام (معرفات المقالات)
        has_numbers = any(char.isdigit() for char in href)

        # فحص طول الرابط (المقالات عادة لها روابط أطول)
        is_long_enough = len(href) > 15

        # قبول الرابط إذا كان له مؤشر مقال أو (له أرقام وطويل بما فيه الكفاية)
        return has_article_indicator or (has_numbers and is_long_enough)

    def _find_article_links_enhanced(self, soup: BeautifulSoup, base_url: str) -> List[str]:
        """البحث المحسن عن روابط المقالات في الصفحة"""
        article_links = set()  # استخدام set لتجنب التكرار

        # استراتيجيات متعددة للبحث عن روابط المقالات
        search_strategies = [
            # 1. البحث في عناصر المقالات المحددة
            {'selector': 'article a[href]', 'priority': 1},
            {'selector': '.article a[href]', 'priority': 1},
            {'selector': '.news-item a[href]', 'priority': 1},
            {'selector': '.post a[href]', 'priority': 1},

            # 2. البحث في العناوين
            {'selector': 'h1 a[href], h2 a[href], h3 a[href]', 'priority': 2},
            {'selector': '.headline a[href]', 'priority': 2},
            {'selector': '.title a[href]', 'priority': 2},

            # 3. البحث في قوائم المقالات
            {'selector': '.article-list a[href]', 'priority': 3},
            {'selector': '.news-list a[href]', 'priority': 3},
            {'selector': '.post-list a[href]', 'priority': 3},

            # 4. البحث العام في الروابط
            {'selector': 'a[href]', 'priority': 4}
        ]

        for strategy in search_strategies:
            try:
                links = soup.select(strategy['selector'])

                for link in links:
                    href = link.get('href')
                    if href and self._is_article_link(href):
                        # تحويل إلى رابط كامل
                        full_url = urljoin(base_url, href)

                        # تجنب الروابط المكررة
                        if full_url not in article_links:
                            article_links.add(full_url)

                            # إذا وجدنا روابط كافية من استراتيجية عالية الأولوية، توقف
                            if strategy['priority'] <= 2 and len(article_links) >= 10:
                                break

                # إذا وجدنا روابط كافية، توقف
                if len(article_links) >= 15:
                    break

            except Exception as e:
                logger.debug(f"خطأ في استراتيجية البحث {strategy['selector']}: {e}")
                continue

        # تحويل إلى قائمة وترتيب
        links_list = list(article_links)

        # ترتيب الروابط حسب الأولوية (الروابط الأقصر أولاً عادة)
        links_list.sort(key=len)

        logger.debug(f"تم العثور على {len(links_list)} رابط مقال فريد")

        return links_list[:20]  # أقصى 20 رابط

    async def _extract_article_details(self, url: str, site_config: Dict) -> Optional[Dict]:
        """استخراج تفاصيل المقال من الصفحة"""
        try:
            headers = {
                'User-Agent': random.choice(self.user_agents),
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Referer': urlparse(url).scheme + '://' + urlparse(url).netloc
            }

            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=headers, timeout=30) as response:
                    if response.status == 200:
                        html_content = await response.text()
                        soup = BeautifulSoup(html_content, 'html.parser')

                        # استخراج العناصر باستخدام selectors الموقع
                        selectors = site_config.get('selectors', {})

                        title = self._extract_with_selectors(soup, selectors.get('title', 'h1'))
                        content = self._extract_with_selectors(soup, selectors.get('content', '.content'))
                        date_str = self._extract_with_selectors(soup, selectors.get('date', 'time'))
                        author = self._extract_with_selectors(soup, selectors.get('author', '.author'))

                        if title and len(title) > 10:
                            return {
                                'title': title,
                                'url': url,
                                'content': content[:2000] if content else '',  # حد أقصى 2000 حرف
                                'summary': content[:300] if content else '',  # ملخص 300 حرف
                                'published_date': self._parse_date_string(date_str),
                                'author': author,
                                'source': urlparse(url).netloc,
                                'source_type': 'direct_scraping',
                                'extraction_method': 'web_scraping',
                                'quality_score': self._calculate_article_quality(title, content),
                                'relevance_score': 0
                            }

                    return None

        except Exception as e:
            logger.debug(f"خطأ في استخراج تفاصيل المقال من {url}: {e}")
            return None

    def _extract_with_selectors(self, soup: BeautifulSoup, selectors: str) -> str:
        """استخراج النص باستخدام CSS selectors متعددة مع تشخيص تلقائي"""
        if not selectors:
            return ''

        # تجربة selectors متعددة مفصولة بفاصلة
        selector_list = [s.strip() for s in selectors.split(',')]

        successful_selector = None
        for selector in selector_list:
            try:
                element = soup.select_one(selector)
                if element:
                    text = element.get_text(strip=True)
                    if text and len(text) > 5:
                        successful_selector = selector
                        return text
            except Exception:
                continue

        # إذا فشلت جميع selectors، سجل للتشخيص
        if not successful_selector:
            self._log_selector_failure(selectors, soup)

        return ''

    def _log_selector_failure(self, failed_selectors: str, soup: BeautifulSoup):
        """تسجيل فشل selectors للتشخيص اللاحق"""
        try:
            # البحث عن selectors بديلة محتملة
            potential_selectors = []

            # البحث عن عناوين
            for tag in ['h1', 'h2', 'h3']:
                elements = soup.find_all(tag)
                if elements:
                    for element in elements[:3]:
                        if element.get('class'):
                            potential_selectors.append(f"{tag}.{'.'.join(element['class'])}")
                        else:
                            potential_selectors.append(tag)

            # البحث عن محتوى
            for tag in ['article', 'div', 'section']:
                elements = soup.find_all(tag)
                for element in elements[:5]:
                    if element.get('class'):
                        class_name = '.'.join(element['class'])
                        if any(keyword in class_name.lower() for keyword in ['content', 'article', 'body', 'text']):
                            potential_selectors.append(f"{tag}.{class_name}")

            if potential_selectors:
                logger.debug(f"⚠️ Selectors فشلت: {failed_selectors}")
                logger.debug(f"💡 Selectors محتملة: {', '.join(potential_selectors[:5])}")

        except Exception as e:
            logger.debug(f"خطأ في تشخيص selectors: {e}")

    def _extract_date_from_result(self, result: Dict) -> Optional[datetime]:
        """استخراج التاريخ من نتيجة البحث"""
        # محاولة استخراج التاريخ من مصادر مختلفة
        date_fields = ['publishedAt', 'datePublished', 'date', 'published']

        for field in date_fields:
            if field in result:
                return self._parse_date_string(result[field])

        return None

    def _parse_date_string(self, date_str: str) -> Optional[datetime]:
        """تحليل نص التاريخ إلى datetime"""
        if not date_str:
            return None

        try:
            # صيغ مختلفة للتاريخ
            formats = [
                '%Y-%m-%dT%H:%M:%SZ',
                '%Y-%m-%dT%H:%M:%S.%fZ',
                '%Y-%m-%d %H:%M:%S',
                '%Y-%m-%d',
                '%d/%m/%Y',
                '%m/%d/%Y'
            ]

            for fmt in formats:
                try:
                    return datetime.strptime(date_str, fmt)
                except ValueError:
                    continue

            # استخدام dateutil كخيار أخير
            from dateutil import parser
            return parser.parse(date_str)

        except Exception:
            return datetime.now()

    def _calculate_search_result_quality(self, result: Dict) -> float:
        """حساب جودة نتيجة البحث"""
        score = 0.0

        title = result.get('title', '')
        description = result.get('description', '') or result.get('snippet', '')

        # نقاط العنوان
        if len(title) > 20:
            score += 2.0
        if any(word in title.lower() for word in ['gaming', 'game', 'video']):
            score += 2.0

        # نقاط الوصف
        if len(description) > 50:
            score += 1.0
        if len(description) > 150:
            score += 1.0

        # نقاط المصدر
        url = result.get('url', '') or result.get('link', '')
        domain = urlparse(url).netloc.lower()

        trusted_domains = ['ign.com', 'gamespot.com', 'polygon.com', 'kotaku.com']
        if any(trusted in domain for trusted in trusted_domains):
            score += 3.0

        return min(score, 10.0)

    def _calculate_article_quality(self, title: str, content: str) -> float:
        """حساب جودة المقال المستخرج"""
        score = 0.0

        # نقاط العنوان
        if title and len(title) > 15:
            score += 2.0
        if title and len(title) > 40:
            score += 1.0

        # نقاط المحتوى
        if content and len(content) > 200:
            score += 2.0
        if content and len(content) > 500:
            score += 2.0
        if content and len(content) > 1000:
            score += 1.0

        # نقاط الكلمات المفتاحية
        text_to_check = f"{title} {content}".lower()
        gaming_keywords = ['game', 'gaming', 'video', 'player', 'console', 'pc']

        keyword_count = sum(1 for keyword in gaming_keywords if keyword in text_to_check)
        score += min(keyword_count * 0.5, 2.0)

        return min(score, 10.0)

    async def _filter_and_enhance_results(self, results: List[Dict]) -> List[Dict]:
        """تصفية وتحسين النتائج"""
        try:
            filtered_results = []
            seen_urls = set()
            seen_titles = set()

            for result in results:
                # فحص التكرار
                url = result.get('url', '')
                title = result.get('title', '').lower().strip()

                if url in seen_urls or title in seen_titles:
                    continue

                # فحص الجودة الأساسية
                if len(title) < 10 or not url:
                    continue

                # تحسين المحتوى إذا كان فارغاً
                if not result.get('content') and result.get('summary'):
                    result['content'] = result['summary']

                # إضافة معلومات إضافية
                result['extraction_timestamp'] = datetime.now().isoformat()
                result['domain'] = urlparse(url).netloc

                # فحص التكرار في قاعدة البيانات
                is_duplicate, _ = db.is_duplicate_content(
                    result.get('content', ''),
                    result.get('title', ''),
                    []
                )

                if not is_duplicate:
                    filtered_results.append(result)
                    seen_urls.add(url)
                    seen_titles.add(title)
                    self.extraction_stats['successful_extractions'] += 1
                else:
                    self.extraction_stats['failed_extractions'] += 1

            return filtered_results

        except Exception as e:
            logger.error(f"❌ خطأ في تصفية النتائج: {e}")
            return results

    def _sort_by_relevance_and_quality(self, results: List[Dict], query: str) -> List[Dict]:
        """ترتيب النتائج حسب الصلة والجودة"""
        try:
            def calculate_relevance_score(result):
                title = result.get('title', '').lower()
                content = result.get('content', '').lower()
                summary = result.get('summary', '').lower()

                query_words = query.lower().split()
                relevance_score = 0

                # نقاط العنوان
                for word in query_words:
                    if word in title:
                        relevance_score += 3

                # نقاط المحتوى
                for word in query_words:
                    if word in content:
                        relevance_score += 1
                    if word in summary:
                        relevance_score += 2

                return relevance_score

            # حساب نقاط الصلة لكل نتيجة
            for result in results:
                result['relevance_score'] = calculate_relevance_score(result)

            # ترتيب حسب الصلة والجودة
            def sort_key(result):
                quality_score = result.get('quality_score', 0)
                relevance_score = result.get('relevance_score', 0)

                # نقاط إضافية للمقالات الحديثة
                pub_date = result.get('published_date')
                if pub_date:
                    days_old = (datetime.now() - pub_date).days
                    freshness_score = max(0, 5 - days_old * 0.2)
                else:
                    freshness_score = 0

                # نقاط إضافية للمصادر الموثوقة
                domain = result.get('domain', '').lower()
                trust_score = 2 if any(trusted in domain for trusted in ['ign.com', 'gamespot.com', 'polygon.com']) else 0

                return quality_score + relevance_score + freshness_score + trust_score

            return sorted(results, key=sort_key, reverse=True)

        except Exception as e:
            logger.error(f"❌ خطأ في ترتيب النتائج: {e}")
            return results

    def get_extraction_stats(self) -> Dict:
        """الحصول على إحصائيات الاستخراج"""
        total_calls = sum([
            self.extraction_stats['brave_search_calls'],
            self.extraction_stats['google_search_calls'],
            self.extraction_stats['direct_scraping_calls']
        ])

        success_rate = 0
        if total_calls > 0:
            success_rate = (self.extraction_stats['successful_extractions'] /
                          max(1, self.extraction_stats['successful_extractions'] + self.extraction_stats['failed_extractions'])) * 100

        return {
            **self.extraction_stats,
            'total_calls': total_calls,
            'success_rate': success_rate,
            'average_articles_per_call': self.extraction_stats['total_articles_extracted'] / max(1, total_calls)
        }

    async def test_all_search_engines(self) -> Dict:
        """اختبار جميع محركات البحث"""
        test_results = {
            'brave_search': {'available': False, 'error': None, 'results_found': 0},
            'google_search': {'available': False, 'error': None, 'results_found': 0},
            'direct_scraping': {'available': True, 'error': None, 'results_found': 0},
            'overall_status': False
        }

        # اختبار Brave Search
        if self.brave_search_key:
            try:
                results = await self._search_with_brave('gaming news', 3)
                test_results['brave_search']['available'] = True
                test_results['brave_search']['results_found'] = len(results)
            except Exception as e:
                test_results['brave_search']['error'] = str(e)
        else:
            test_results['brave_search']['error'] = 'API key not provided'

        # اختبار Google Search
        if self.google_search_key and self.google_search_engine_id:
            try:
                results = await self._search_with_google('gaming news', 3)
                test_results['google_search']['available'] = True
                test_results['google_search']['results_found'] = len(results)
            except Exception as e:
                test_results['google_search']['error'] = str(e)
        else:
            test_results['google_search']['error'] = 'API key or search engine ID not provided'

        # اختبار الاستخراج المباشر
        try:
            results = await self._direct_scraping_from_gaming_sites('gaming', 3)
            test_results['direct_scraping']['results_found'] = len(results)
        except Exception as e:
            test_results['direct_scraping']['error'] = str(e)
            test_results['direct_scraping']['available'] = False

        # تحديد الحالة العامة
        available_methods = sum(1 for method in test_results.values()
                              if isinstance(method, dict) and method.get('available', False))
        test_results['overall_status'] = available_methods > 0

        return test_results

    async def search_trending_gaming_topics(self, max_results: int = 20) -> List[Dict]:
        """البحث عن المواضيع الرائجة في الألعاب"""
        try:
            trending_queries = [
                'trending games 2025',
                'viral gaming news',
                'popular video games',
                'hot gaming topics',
                'breaking gaming news'
            ]

            all_results = []

            for query in trending_queries:
                results = await self.comprehensive_gaming_search(query, max_results // len(trending_queries))
                all_results.extend(results)

            # إزالة التكرار وترتيب حسب الرواج
            unique_results = []
            seen_titles = set()

            for result in all_results:
                title = result.get('title', '').lower()
                if title not in seen_titles:
                    seen_titles.add(title)
                    unique_results.append(result)

            return unique_results[:max_results]

        except Exception as e:
            logger.error(f"❌ فشل في البحث عن المواضيع الرائجة: {e}")
            return []

# إنشاء كائن عام للوصول
advanced_web_scraper = AdvancedWebScraper()
