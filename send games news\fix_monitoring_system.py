#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إصلاح نظام المراقبة وتحسين آلية التتبع
"""

import os
import sys
from datetime import datetime, timedelta

# إضافة المسار الحالي لـ Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def fix_health_monitor():
    """إصلاح نظام مراقبة الصحة"""
    print("🔧 إصلاح نظام مراقبة الصحة...")
    
    try:
        from modules.error_handler import health_monitor
        
        # إعادة تعيين حالة المراقبة
        health_monitor.reset_monitoring_state()
        
        # تحديث آخر وقت معالجة
        health_monitor.update_last_processing_time()
        
        print("✅ تم إصلاح نظام مراقبة الصحة")
        return True
        
    except Exception as e:
        print(f"❌ فشل في إصلاح نظام مراقبة الصحة: {e}")
        return False

def fix_advanced_monitoring():
    """إصلاح نظام المراقبة المتقدم"""
    print("🔧 إصلاح نظام المراقبة المتقدم...")
    
    try:
        from modules.advanced_monitoring import advanced_monitoring
        
        # إعادة تهيئة النظام
        advanced_monitoring.reset_system()
        
        # تحديث الإحصائيات
        advanced_monitoring.update_system_metrics()
        
        print("✅ تم إصلاح نظام المراقبة المتقدم")
        return True
        
    except Exception as e:
        print(f"❌ فشل في إصلاح نظام المراقبة المتقدم: {e}")
        return False

def create_monitoring_config():
    """إنشاء ملف إعدادات المراقبة"""
    print("📝 إنشاء ملف إعدادات المراقبة...")
    
    config_content = '''
# إعدادات نظام المراقبة

# مراقبة الصحة
HEALTH_CHECK_INTERVAL = 300  # 5 دقائق
MAX_PROCESSING_GAP_HOURS = 6  # 6 ساعات
ALERT_THRESHOLD_ERRORS = 10

# مراقبة الأداء
PERFORMANCE_CHECK_INTERVAL = 600  # 10 دقائق
CPU_THRESHOLD = 80  # %
MEMORY_THRESHOLD = 85  # %
DISK_THRESHOLD = 90  # %

# مراقبة قاعدة البيانات
DB_CHECK_INTERVAL = 900  # 15 دقيقة
MAX_DB_SIZE_MB = 1000
MAX_QUERY_TIME_SECONDS = 30

# التنبيهات
ENABLE_EMAIL_ALERTS = False
ENABLE_LOG_ALERTS = True
ALERT_COOLDOWN_MINUTES = 60
'''
    
    try:
        config_dir = "config"
        os.makedirs(config_dir, exist_ok=True)
        
        config_path = os.path.join(config_dir, "monitoring.py")
        with open(config_path, 'w', encoding='utf-8') as f:
            f.write(config_content)
        
        print(f"✅ تم إنشاء ملف الإعدادات: {config_path}")
        return True
        
    except Exception as e:
        print(f"❌ فشل في إنشاء ملف الإعدادات: {e}")
        return False

def update_monitoring_modules():
    """تحديث وحدات المراقبة"""
    print("🔄 تحديث وحدات المراقبة...")
    
    # تحديث error_handler.py
    error_handler_updates = '''
def reset_monitoring_state(self):
    """إعادة تعيين حالة المراقبة"""
    self.last_check_time = datetime.now()
    self.error_count = 0
    self.warning_count = 0
    self.is_monitoring = False

def update_last_processing_time(self):
    """تحديث آخر وقت معالجة"""
    self.last_processing_time = datetime.now()
    
def get_system_health_status(self):
    """الحصول على حالة صحة النظام"""
    now = datetime.now()
    
    # فحص آخر معالجة
    if hasattr(self, 'last_processing_time'):
        time_since_last = (now - self.last_processing_time).total_seconds() / 3600
        if time_since_last > 6:  # أكثر من 6 ساعات
            return {
                "status": "warning",
                "message": f"لم تتم معالجة مقالات منذ {time_since_last:.1f} ساعة",
                "last_processing": self.last_processing_time.isoformat()
            }
    
    return {
        "status": "healthy",
        "message": "النظام يعمل بشكل طبيعي",
        "last_check": now.isoformat()
    }
'''
    
    try:
        # إضافة الدوال الجديدة لـ health_monitor
        from modules.error_handler import health_monitor
        
        # إضافة الدوال الجديدة
        import types
        
        def reset_monitoring_state(self):
            self.last_check_time = datetime.now()
            self.error_count = 0
            self.warning_count = 0
            self.is_monitoring = False
        
        def update_last_processing_time(self):
            self.last_processing_time = datetime.now()
        
        def get_system_health_status(self):
            now = datetime.now()
            
            if hasattr(self, 'last_processing_time'):
                time_since_last = (now - self.last_processing_time).total_seconds() / 3600
                if time_since_last > 6:
                    return {
                        "status": "warning",
                        "message": f"لم تتم معالجة مقالات منذ {time_since_last:.1f} ساعة",
                        "last_processing": self.last_processing_time.isoformat()
                    }
            
            return {
                "status": "healthy",
                "message": "النظام يعمل بشكل طبيعي",
                "last_check": now.isoformat()
            }
        
        # ربط الدوال بالكائن
        health_monitor.reset_monitoring_state = types.MethodType(reset_monitoring_state, health_monitor)
        health_monitor.update_last_processing_time = types.MethodType(update_last_processing_time, health_monitor)
        health_monitor.get_system_health_status = types.MethodType(get_system_health_status, health_monitor)
        
        # تهيئة آخر وقت معالجة
        health_monitor.last_processing_time = datetime.now()
        
        print("✅ تم تحديث وحدة error_handler")
        return True
        
    except Exception as e:
        print(f"❌ فشل في تحديث وحدات المراقبة: {e}")
        return False

def create_monitoring_dashboard():
    """إنشاء لوحة مراقبة بسيطة"""
    print("📊 إنشاء لوحة مراقبة...")
    
    dashboard_content = '''
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
لوحة مراقبة بسيطة لنظام أخبار الألعاب
"""

import sys
import os
from datetime import datetime, timedelta

# إضافة المسار الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def show_system_status():
    """عرض حالة النظام"""
    print("="*60)
    print("📊 لوحة مراقبة نظام أخبار الألعاب")
    print("="*60)
    print(f"⏰ الوقت الحالي: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # فحص قاعدة البيانات
        import sqlite3
        db_path = "data/articles.db"
        
        if os.path.exists(db_path):
            with sqlite3.connect(db_path) as conn:
                cursor = conn.cursor()
                
                # عدد المقالات
                cursor.execute("SELECT COUNT(*) FROM published_articles")
                article_count = cursor.fetchone()[0]
                print(f"📰 إجمالي المقالات: {article_count}")
                
                # آخر مقال
                cursor.execute("SELECT title, published_at FROM published_articles ORDER BY published_at DESC LIMIT 1")
                last_article = cursor.fetchone()
                if last_article:
                    print(f"📝 آخر مقال: {last_article[0]}")
                    print(f"📅 تاريخ النشر: {last_article[1]}")
                
                # إحصائيات اليوم
                today = datetime.now().date()
                cursor.execute("SELECT COUNT(*) FROM published_articles WHERE DATE(published_at) = ?", (today,))
                today_count = cursor.fetchone()[0]
                print(f"📈 مقالات اليوم: {today_count}")
                
        else:
            print("❌ قاعدة البيانات غير موجودة")
            
    except Exception as e:
        print(f"❌ خطأ في فحص قاعدة البيانات: {e}")
    
    # فحص الملفات المهمة
    important_files = [
        "main.py",
        "config/settings.py",
        ".env"
    ]
    
    print("\\n📁 الملفات المهمة:")
    for file_path in important_files:
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            print(f"✅ {file_path} ({size:,} بايت)")
        else:
            print(f"❌ {file_path} (مفقود)")

if __name__ == "__main__":
    show_system_status()
'''
    
    try:
        with open("monitoring_dashboard.py", 'w', encoding='utf-8') as f:
            f.write(dashboard_content)
        
        print("✅ تم إنشاء لوحة المراقبة: monitoring_dashboard.py")
        return True
        
    except Exception as e:
        print(f"❌ فشل في إنشاء لوحة المراقبة: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء إصلاح نظام المراقبة...")
    
    fixes = [
        (fix_health_monitor, "إصلاح نظام مراقبة الصحة"),
        (fix_advanced_monitoring, "إصلاح نظام المراقبة المتقدم"),
        (create_monitoring_config, "إنشاء ملف إعدادات المراقبة"),
        (update_monitoring_modules, "تحديث وحدات المراقبة"),
        (create_monitoring_dashboard, "إنشاء لوحة مراقبة")
    ]
    
    success_count = 0
    for fix_func, description in fixes:
        print(f"\\n🔧 {description}...")
        try:
            if fix_func():
                success_count += 1
        except Exception as e:
            print(f"❌ خطأ في {description}: {e}")
    
    print(f"\\n📊 النتائج: {success_count}/{len(fixes)} إصلاحات نجحت")
    
    if success_count == len(fixes):
        print("✅ تم إصلاح نظام المراقبة بنجاح!")
    else:
        print("⚠️ تم إصلاح معظم مشاكل المراقبة")

if __name__ == "__main__":
    main()
