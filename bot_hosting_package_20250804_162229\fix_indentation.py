#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح شامل لمشاكل المسافات البادئة في main.py
"""

import re
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def fix_main_function_indentation():
    """إصلاح المسافات البادئة في دالة main"""
    
    with open('main.py', 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # البحث عن بداية دالة main
    main_start = -1
    for i, line in enumerate(lines):
        if 'async def main():' in line:
            main_start = i
            break
    
    if main_start == -1:
        logger.error("لم يتم العثور على دالة main")
        return False
    
    # إصلاح المسافات البادئة من بداية دالة main
    fixed_lines = lines[:main_start + 1]  # الاحتفاظ بكل شيء قبل main
    
    # معالجة محتوى دالة main
    in_main = True
    try_level = 0
    base_indent = 4  # المسافة الأساسية لدالة main
    
    for i in range(main_start + 1, len(lines)):
        line = lines[i]
        stripped = line.strip()
        
        # تخطي الأسطر الفارغة والتعليقات
        if not stripped or stripped.startswith('#'):
            fixed_lines.append(line)
            continue
        
        # التحقق من نهاية دالة main
        if line.startswith('def ') or line.startswith('async def ') or line.startswith('class '):
            # وصلنا لدالة أو فئة جديدة، انتهت دالة main
            in_main = False
            fixed_lines.extend(lines[i:])  # إضافة باقي الملف كما هو
            break
        
        if in_main:
            # تحديد المسافة البادئة المناسبة
            current_indent = base_indent
            
            # التحقق من الكلمات المفتاحية
            if stripped.startswith('try:'):
                try_level += 1
                fixed_line = ' ' * current_indent + stripped + '\n'
            elif stripped.startswith('except') or stripped.startswith('finally'):
                # except/finally في نفس مستوى try
                indent_level = base_indent + (try_level - 1) * 4
                fixed_line = ' ' * indent_level + stripped + '\n'
            elif stripped.startswith(('if ', 'elif ', 'else:', 'for ', 'while ', 'with ')):
                # بيانات التحكم
                current_indent = base_indent + try_level * 4
                fixed_line = ' ' * current_indent + stripped + '\n'
            else:
                # بيانات عادية
                current_indent = base_indent + try_level * 4
                
                # إذا كان السطر السابق ينتهي بـ :، زيد المسافة البادئة
                prev_line = fixed_lines[-1].strip() if fixed_lines else ""
                if prev_line.endswith(':'):
                    current_indent += 4
                
                fixed_line = ' ' * current_indent + stripped + '\n'
            
            fixed_lines.append(fixed_line)
        else:
            fixed_lines.append(line)
    
    # كتابة الملف المصحح
    with open('main.py', 'w', encoding='utf-8') as f:
        f.writelines(fixed_lines)
    
    logger.info("✅ تم إصلاح المسافات البادئة في دالة main")
    return True

def fix_specific_indentation_issues():
    """إصلاح مشاكل محددة في المسافات البادئة"""
    
    with open('main.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # إصلاحات محددة
    fixes = [
        # إصلاح if statements
        (r'^(\s*)if ([^:]+):\n(\s*)([^\s])', r'\1if \2:\n\1    \4'),
        # إصلاح else statements  
        (r'^(\s*)else:\n(\s*)([^\s])', r'\1else:\n\1    \3'),
        # إصلاح try statements
        (r'^(\s*)try:\n(\s*)([^\s])', r'\1try:\n\1    \3'),
        # إصلاح except statements
        (r'^(\s*)except ([^:]+):\n(\s*)([^\s])', r'\1except \2:\n\1    \4'),
    ]
    
    for pattern, replacement in fixes:
        content = re.sub(pattern, replacement, content, flags=re.MULTILINE)
    
    with open('main.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    logger.info("✅ تم إصلاح مشاكل المسافات البادئة المحددة")

def validate_syntax():
    """التحقق من صحة الكود النحوية"""
    try:
        with open('main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        compile(content, 'main.py', 'exec')
        logger.info("✅ الكود صحيح نحوياً")
        return True
        
    except SyntaxError as e:
        logger.error(f"❌ خطأ نحوي في السطر {e.lineno}: {e.msg}")
        if e.text:
            logger.error(f"   النص: {e.text.strip()}")
        return False
    except Exception as e:
        logger.error(f"❌ خطأ في التحقق: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    logger.info("🔧 بدء إصلاح المسافات البادئة...")
    
    # 1. إصلاح دالة main
    logger.info("1️⃣ إصلاح دالة main...")
    if not fix_main_function_indentation():
        logger.error("❌ فشل في إصلاح دالة main")
        return False
    
    # 2. إصلاح مشاكل محددة
    logger.info("2️⃣ إصلاح مشاكل محددة...")
    fix_specific_indentation_issues()
    
    # 3. التحقق من صحة الكود
    logger.info("3️⃣ التحقق من صحة الكود...")
    if validate_syntax():
        logger.info("🎉 تم إصلاح جميع مشاكل المسافات البادئة!")
        return True
    else:
        logger.error("❌ لا تزال هناك مشاكل في الكود")
        return False

if __name__ == "__main__":
    main()
