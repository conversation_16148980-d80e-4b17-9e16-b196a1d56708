#!/usr/bin/env python3
"""
اختبار نظام الخلفيات للتأكد من عمله بشكل صحيح
"""

import json
from pathlib import Path
from video_creation.background import get_background_config, download_background_video, download_background_audio
from utils.console import print_step, print_substep

def test_background_loading():
    """اختبار تحميل إعدادات الخلفية"""
    print_step("🧪 اختبار تحميل إعدادات الخلفية...")
    
    try:
        # اختبار تحميل إعدادات الفيديو
        video_config = get_background_config("video")
        print_substep(f"إعدادات الفيديو: {video_config}", style="green")
        
        # اختبار تحميل إعدادات الصوت
        audio_config = get_background_config("audio")
        print_substep(f"إعدادات الصوت: {audio_config}", style="green")
        
        return video_config, audio_config
    except Exception as e:
        print_substep(f"خطأ في تحميل الإعدادات: {e}", style="red")
        return None, None

def test_file_detection():
    """اختبار اكتشاف الملفات الموجودة"""
    print_step("🔍 اختبار اكتشاف الملفات...")
    
    video_dir = Path("assets/backgrounds/video/")
    audio_dir = Path("assets/backgrounds/audio/")
    
    print_substep("ملفات الفيديو الموجودة:")
    for file_path in video_dir.glob("*.mp4"):
        size_mb = file_path.stat().st_size / (1024 * 1024)
        print_substep(f"  • {file_path.name} ({size_mb:.1f} MB)", style="green")
    
    print_substep("ملفات الصوت الموجودة:")
    for file_path in audio_dir.glob("*.mp3"):
        size_mb = file_path.stat().st_size / (1024 * 1024)
        print_substep(f"  • {file_path.name} ({size_mb:.1f} MB)", style="green")

def test_download_functions():
    """اختبار دوال التحميل"""
    print_step("📥 اختبار دوال التحميل...")
    
    # الحصول على الإعدادات
    video_config, audio_config = test_background_loading()
    
    if video_config and audio_config:
        try:
            # اختبار تحميل الفيديو (يجب أن يكتشف الملف الموجود)
            print_substep("اختبار تحميل الفيديو...")
            download_background_video(video_config)
            
            # اختبار تحميل الصوت (يجب أن يكتشف الملف الموجود)
            print_substep("اختبار تحميل الصوت...")
            download_background_audio(audio_config)
            
            print_substep("✅ اختبار التحميل نجح!", style="green")
            return True
        except Exception as e:
            print_substep(f"❌ خطأ في اختبار التحميل: {e}", style="red")
            return False
    else:
        print_substep("❌ فشل في الحصول على الإعدادات", style="red")
        return False

def check_config_files():
    """فحص ملفات الإعدادات"""
    print_step("📋 فحص ملفات الإعدادات...")
    
    config_files = [
        "utils/background_videos.json",
        "utils/background_audios.json",
        "config.toml"
    ]
    
    all_good = True
    for config_file in config_files:
        if Path(config_file).exists():
            print_substep(f"✅ {config_file} موجود", style="green")
        else:
            print_substep(f"❌ {config_file} مفقود", style="red")
            all_good = False
    
    return all_good

def show_current_settings():
    """عرض الإعدادات الحالية"""
    print_step("⚙️ الإعدادات الحالية...")
    
    # قراءة config.toml
    try:
        import toml
        with open("config.toml", 'r', encoding='utf-8') as f:
            config = toml.load(f)
        
        bg_settings = config.get('settings', {}).get('background', {})
        print_substep(f"خلفية الفيديو: {bg_settings.get('background_video', 'غير محدد')}")
        print_substep(f"خلفية الصوت: {bg_settings.get('background_audio', 'غير محدد')}")
        print_substep(f"مستوى الصوت: {bg_settings.get('background_audio_volume', 'غير محدد')}")
    except Exception as e:
        print_substep(f"خطأ في قراءة config.toml: {e}", style="red")

def main():
    """الدالة الرئيسية للاختبار"""
    print_step("🧪 بدء اختبار نظام الخلفيات...")
    
    # 1. فحص ملفات الإعدادات
    if not check_config_files():
        print_substep("❌ بعض ملفات الإعدادات مفقودة", style="red")
        return
    
    # 2. عرض الإعدادات الحالية
    show_current_settings()
    
    # 3. اختبار اكتشاف الملفات
    test_file_detection()
    
    # 4. اختبار دوال التحميل
    if test_download_functions():
        print_step("✅ جميع الاختبارات نجحت! النظام جاهز للعمل")
        print_substep("يمكنك الآن تشغيل python main.py بدون مشاكل", style="green")
    else:
        print_step("❌ بعض الاختبارات فشلت")
        print_substep("تحقق من الأخطاء أعلاه", style="red")

if __name__ == "__main__":
    main()
