#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط للتحقق من المشاكل
Simple test to check for issues
"""

import os
import sys
import logging

# تحميل متغيرات البيئة
try:
    from dotenv import load_dotenv
    load_dotenv()
    print("✅ تم تحميل ملف .env")
except ImportError:
    print("⚠️ python-dotenv غير مثبت")
except Exception as e:
    print(f"❌ خطأ في تحميل .env: {e}")

# إعداد التسجيل
logging.basicConfig(
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    level=logging.INFO,
    datefmt="%Y-%m-%d %H:%M:%S",
)
logger = logging.getLogger(__name__)

def test_environment():
    """اختبار متغيرات البيئة"""
    print("🔍 اختبار متغيرات البيئة...")
    
    required_vars = ['BOT_TOKEN', 'SUPABASE_URL', 'SUPABASE_KEY', 'ADMIN_CHAT_ID']
    missing_vars = []
    
    for var in required_vars:
        value = os.environ.get(var)
        if value:
            print(f"✅ {var}: موجود")
        else:
            print(f"❌ {var}: مفقود")
            missing_vars.append(var)
    
    return len(missing_vars) == 0

def test_imports():
    """اختبار الاستيرادات الأساسية"""
    print("🔍 اختبار الاستيرادات...")
    
    imports_to_test = [
        ('telegram', 'python-telegram-bot'),
        ('requests', 'requests'),
        ('flask', 'flask'),
        ('apscheduler', 'apscheduler')
    ]
    
    failed_imports = []
    for module, description in imports_to_test:
        try:
            __import__(module)
            print(f"✅ {description}: متوفر")
        except ImportError:
            print(f"❌ {description}: غير متوفر")
            failed_imports.append(module)
    
    return len(failed_imports) == 0

def test_supabase_connection():
    """اختبار الاتصال مع Supabase"""
    print("🔍 اختبار الاتصال مع Supabase...")
    
    try:
        import requests
        
        supabase_url = os.environ.get('SUPABASE_URL')
        supabase_key = os.environ.get('SUPABASE_KEY')
        
        if not supabase_url or not supabase_key:
            print("❌ متغيرات Supabase مفقودة")
            return False
        
        # اختبار الاتصال
        headers = {
            'apikey': supabase_key,
            'Authorization': f'Bearer {supabase_key}',
            'Content-Type': 'application/json'
        }
        
        # محاولة الوصول لجدول mods
        url = f"{supabase_url}/rest/v1/mods?limit=1"
        response = requests.get(url, headers=headers, timeout=10)
        
        if response.status_code == 200:
            print("✅ الاتصال مع Supabase يعمل")
            print(f"✅ جدول mods متاح")
            return True
        else:
            print(f"⚠️ استجابة غير متوقعة: {response.status_code}")
            print(f"   الرسالة: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاتصال مع Supabase: {e}")
        return False

def test_telegram_connection():
    """اختبار الاتصال مع Telegram"""
    print("🔍 اختبار الاتصال مع Telegram...")
    
    try:
        import requests
        
        bot_token = os.environ.get('BOT_TOKEN')
        if not bot_token:
            print("❌ BOT_TOKEN مفقود")
            return False
        
        # اختبار getMe
        url = f"https://api.telegram.org/bot{bot_token}/getMe"
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('ok'):
                bot_info = data.get('result', {})
                print(f"✅ البوت متصل: {bot_info.get('first_name', 'Unknown')}")
                return True
            else:
                print(f"❌ خطأ من Telegram: {data}")
                return False
        else:
            print(f"❌ فشل في الاتصال: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاتصال مع Telegram: {e}")
        return False

def test_file_structure():
    """اختبار هيكل الملفات"""
    print("🔍 اختبار هيكل الملفات...")
    
    required_files = [
        'main.py',
        'supabase_client.py',
        'web_server.py',
        'telegram_web_app.py',
        '.env'
    ]
    
    missing_files = []
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}: موجود")
        else:
            print(f"❌ {file}: مفقود")
            missing_files.append(file)
    
    return len(missing_files) == 0

def main():
    """الدالة الرئيسية"""
    print("🔧 اختبار بسيط للنظام")
    print("=" * 50)
    
    tests = [
        ("متغيرات البيئة", test_environment),
        ("الاستيرادات", test_imports),
        ("هيكل الملفات", test_file_structure),
        ("الاتصال مع Supabase", test_supabase_connection),
        ("الاتصال مع Telegram", test_telegram_connection)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 اختبار: {test_name}")
        try:
            if test_func():
                print(f"✅ {test_name}: نجح")
                passed += 1
            else:
                print(f"❌ {test_name}: فشل")
        except Exception as e:
            print(f"❌ {test_name}: خطأ - {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 النتائج: {passed}/{total} اختبارات نجحت")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت!")
        print("📋 النظام جاهز لتشغيل البوت")
        
        # اختبار تشغيل البوت
        print("\n🚀 اختبار تشغيل البوت...")
        try:
            # استيراد main بدون تشغيله
            import importlib.util
            spec = importlib.util.spec_from_file_location("main_module", "main.py")
            main_module = importlib.util.module_from_spec(spec)
            
            print("✅ تم تحميل main.py بنجاح")
            print("📋 يمكنك الآن تشغيل البوت:")
            print("   python main.py")
            
        except Exception as e:
            print(f"❌ مشكلة في تحميل main.py: {e}")
            
    elif passed >= total - 1:
        print("⚠️ معظم الاختبارات نجحت")
        print("📋 قد يعمل البوت مع بعض القيود")
    else:
        print("❌ عدة اختبارات فشلت")
        print("🔧 يرجى إصلاح المشاكل أعلاه")
    
    return passed >= total - 1

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
