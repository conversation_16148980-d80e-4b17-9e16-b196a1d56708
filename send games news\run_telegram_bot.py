#!/usr/bin/env python3
"""
تشغيل بوت تيليجرام المحسن لإدارة وكيل أخبار الألعاب
"""

import asyncio
import sys
import os
from datetime import datetime

# إضافة المسار الحالي لـ Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.enhanced_telegram_bot import enhanced_telegram_bot
from config.settings import BotConfig

def check_requirements():
    """التحقق من المتطلبات الأساسية"""
    print("🔍 فحص المتطلبات الأساسية...")
    
    # التحقق من توكن تيليجرام
    if not BotConfig.TELEGRAM_BOT_TOKEN:
        print("❌ خطأ: لا يوجد توكن تيليجرام في الإعدادات")
        print("💡 تأكد من إضافة TELEGRAM_BOT_TOKEN في ملف .env")
        return False
    
    # التحقق من معرف المدير
    if not BotConfig.TELEGRAM_ADMIN_ID:
        print("⚠️ تحذير: لا يوجد معرف مدير محدد")
        print("💡 سيتم السماح للجميع بالوصول لوظائف الإدارة")
    
    # التحقق من وجود المجلدات المطلوبة
    required_dirs = ["data", "logs", "modules"]
    for dir_name in required_dirs:
        if not os.path.exists(dir_name):
            print(f"📁 إنشاء مجلد: {dir_name}")
            os.makedirs(dir_name, exist_ok=True)
    
    # التحقق من وجود الملفات المهمة
    important_files = ["config/settings.py", ".env"]
    for file_name in important_files:
        if not os.path.exists(file_name):
            print(f"⚠️ تحذير: ملف مهم مفقود: {file_name}")
    
    print("✅ تم فحص المتطلبات بنجاح")
    return True

def print_bot_info():
    """طباعة معلومات البوت"""
    print("\n" + "="*60)
    print("🤖 بوت تيليجرام لإدارة وكيل أخبار الألعاب")
    print("="*60)
    print(f"📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🔗 توكن البوت: {BotConfig.TELEGRAM_BOT_TOKEN[:20]}...")
    print(f"👤 معرف المدير: {BotConfig.TELEGRAM_ADMIN_ID}")
    print(f"📊 حالة النظام: جاهز للتشغيل")
    print("="*60)
    
    print("\n🎯 الوظائف المتاحة:")
    print("• 📊 مراقبة حالة الوكيل")
    print("• 🔑 إدارة مفاتيح API")
    print("• 🚀 تشغيل/إيقاف الوكيل")
    print("• ⚙️ إعدادات النظام")
    print("• 📈 الإحصائيات والتقارير")
    print("• 🧹 تنظيف وصيانة النظام")
    
    print("\n💡 للبدء:")
    print("1. ابحث عن البوت في تيليجرام")
    print("2. أرسل /start للحصول على القائمة الرئيسية")
    print("3. استخدم الأزرار التفاعلية للتنقل")
    
    if BotConfig.TELEGRAM_ADMIN_ID:
        print(f"\n🔐 ملاحظة: وظائف الإدارة متاحة فقط للمدير: {BotConfig.TELEGRAM_ADMIN_ID}")
    else:
        print("\n⚠️ تحذير: لم يتم تحديد مدير - الجميع لديه صلاحيات إدارة!")
    
    print("\n🔄 لإيقاف البوت: اضغط Ctrl+C")
    print("="*60)

async def main():
    """الدالة الرئيسية"""
    print("🚀 بدء تشغيل بوت تيليجرام المحسن...")
    
    # فحص المتطلبات
    if not check_requirements():
        print("❌ فشل في فحص المتطلبات - توقف التشغيل")
        return
    
    # طباعة معلومات البوت
    print_bot_info()
    
    try:
        # بدء تشغيل البوت
        print("\n🤖 جاري تشغيل البوت...")
        await enhanced_telegram_bot.start_bot()
        
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف البوت بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل البوت: {e}")
        import traceback
        traceback.print_exc()
    finally:
        print("👋 تم إغلاق البوت")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 وداعاً!")
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        import traceback
        traceback.print_exc()
