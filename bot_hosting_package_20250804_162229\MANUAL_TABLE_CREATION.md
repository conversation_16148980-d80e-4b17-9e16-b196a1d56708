
# 📋 تعليمات إنشاء الجداول يدوياً
# Manual Table Creation Instructions

تاريخ الإنشاء: 2025-08-04 20:23:52.372643

## 🔧 الخطوات المطلوبة:

### 1. الدخول إلى Supabase Dashboard
- اذهب إلى: https://app.supabase.com
- سجل الدخول إلى حسابك
- اختر مشروعك

### 2. فتح SQL Editor
- من القائمة الجانبية، اختر "SQL Editor"
- انقر على "New Query"

### 3. تنفيذ ملفات SQL
قم بتنفيذ الملفات التالية بالترتيب:

1. create_all_missing_tables_20250804_202339.sql
2. create_all_tables_fixed.sql
3. create_missing_tables_20250804_202335.sql
4. create_missing_tables_20250804_202336.sql
5. create_missing_tables_20250804_202337.sql
6. create_missing_tables_20250804_202338.sql
7. create_missing_tables_20250804_202339.sql
8. create_missing_tables_20250804_202341.sql
9. create_missing_tables_20250804_202342.sql

### 4. التحقق من النجاح
بعد تنفيذ الملفات، شغل هذا الاستعلام للتحقق:

```sql
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
ORDER BY table_name;
```

### 5. تشغيل البوت
بعد إنشاء الجداول بنجاح:

```bash
python main.py
```

## 🆘 في حالة المشاكل:
- تأكد من صحة إعدادات SUPABASE_URL و SUPABASE_KEY
- تأكد من وجود صلاحيات إنشاء الجداول
- راجع سجلات Supabase للأخطاء التفصيلية
