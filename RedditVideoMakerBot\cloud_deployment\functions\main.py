#!/usr/bin/env python3
"""
Cloud Function محسنة لإنشاء الفيديوهات
تعمل ضمن حدود Google Cloud Free Tier
"""

import os
import json
import logging
import tempfile
import shutil
from pathlib import Path
from typing import Dict, Any, Optional
import functions_framework
from google.cloud import storage
import requests

# إعداد السجلات
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# إعدادات Free Tier
FREE_TIER_CONFIG = {
    'max_memory_mb': 256,
    'max_timeout_seconds': 540,  # 9 دقائق
    'max_temp_storage_mb': 100,  # 100 MB للملفات المؤقتة
    'cleanup_immediately': True,
    'compress_assets': True
}

class FreeTierVideoProcessor:
    """معالج فيديو محسن لـ Free Tier"""
    
    def __init__(self):
        self.project_id = os.environ.get('GOOGLE_CLOUD_PROJECT')
        self.storage_client = storage.Client()
        self.temp_dir = None
        self.cleanup_files = []
        
    def __enter__(self):
        """إنشاء مجلد مؤقت"""
        self.temp_dir = tempfile.mkdtemp()
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        """تنظيف الملفات المؤقتة"""
        self.cleanup()
        
    def cleanup(self):
        """تنظيف فوري للملفات"""
        try:
            if self.temp_dir and os.path.exists(self.temp_dir):
                shutil.rmtree(self.temp_dir)
                logger.info("🗑️ تم تنظيف الملفات المؤقتة")
        except Exception as e:
            logger.warning(f"تحذير في التنظيف: {e}")
            
    def check_memory_usage(self):
        """فحص استخدام الذاكرة"""
        try:
            import psutil
            memory_mb = psutil.virtual_memory().used / (1024 * 1024)
            if memory_mb > FREE_TIER_CONFIG['max_memory_mb'] * 0.8:
                logger.warning(f"⚠️ استخدام ذاكرة مرتفع: {memory_mb:.1f} MB")
                return False
            return True
        except ImportError:
            return True  # إذا لم تكن psutil متوفرة
            
    def download_from_storage(self, bucket_name: str, blob_name: str, 
                            local_path: str) -> bool:
        """تحميل ملف من Cloud Storage"""
        try:
            bucket = self.storage_client.bucket(bucket_name)
            blob = bucket.blob(blob_name)
            blob.download_to_filename(local_path)
            self.cleanup_files.append(local_path)
            return True
        except Exception as e:
            logger.error(f"خطأ في تحميل الملف: {e}")
            return False
            
    def upload_to_storage(self, local_path: str, bucket_name: str, 
                         blob_name: str) -> bool:
        """رفع ملف إلى Cloud Storage"""
        try:
            bucket = self.storage_client.bucket(bucket_name)
            blob = bucket.blob(blob_name)
            blob.upload_from_filename(local_path)
            return True
        except Exception as e:
            logger.error(f"خطأ في رفع الملف: {e}")
            return False
            
    def create_lightweight_video(self, reddit_data: Dict[str, Any]) -> Optional[str]:
        """إنشاء فيديو بطريقة محسنة للموارد المحدودة"""
        try:
            # فحص الذاكرة
            if not self.check_memory_usage():
                raise MemoryError("استخدام ذاكرة مرتفع")
                
            logger.info("🎬 بدء إنشاء فيديو محسن...")
            
            # إعدادات مبسطة للفيديو
            video_config = {
                'resolution': (720, 1280),  # دقة أقل لتوفير الذاكرة
                'fps': 24,  # إطارات أقل
                'quality': 'medium',  # جودة متوسطة
                'max_duration': 45,  # 45 ثانية كحد أقصى
                'audio_bitrate': '64k',  # جودة صوت أقل
                'video_bitrate': '500k'  # جودة فيديو أقل
            }
            
            # استخدام مكتبات مبسطة
            video_path = self._create_simple_video(reddit_data, video_config)
            
            if video_path and os.path.exists(video_path):
                # فحص حجم الملف
                file_size_mb = os.path.getsize(video_path) / (1024 * 1024)
                logger.info(f"📊 حجم الفيديو: {file_size_mb:.1f} MB")
                
                if file_size_mb > 50:  # إذا كان الملف كبيراً
                    logger.warning("⚠️ حجم الفيديو كبير، سيتم ضغطه...")
                    video_path = self._compress_video(video_path)
                    
                return video_path
            else:
                raise Exception("فشل في إنشاء الفيديو")
                
        except Exception as e:
            logger.error(f"خطأ في إنشاء الفيديو: {e}")
            return None
            
    def _create_simple_video(self, reddit_data: Dict[str, Any], 
                           config: Dict[str, Any]) -> str:
        """إنشاء فيديو مبسط"""
        # هذه دالة مبسطة - في التطبيق الحقيقي ستحتوي على منطق إنشاء الفيديو
        # باستخدام مكتبات خفيفة مثل moviepy مع إعدادات محسنة
        
        video_path = os.path.join(self.temp_dir, "output_video.mp4")
        
        # محاكاة إنشاء فيديو (استبدل بالكود الحقيقي)
        logger.info("🎥 إنشاء فيديو مبسط...")
        
        # إنشاء ملف فيديو وهمي للاختبار
        with open(video_path, 'wb') as f:
            f.write(b'fake_video_content')
            
        return video_path
        
    def _compress_video(self, video_path: str) -> str:
        """ضغط الفيديو لتوفير المساحة"""
        compressed_path = video_path.replace('.mp4', '_compressed.mp4')
        
        # محاكاة ضغط الفيديو
        logger.info("🗜️ ضغط الفيديو...")
        shutil.copy2(video_path, compressed_path)
        
        # حذف الملف الأصلي
        os.remove(video_path)
        
        return compressed_path

@functions_framework.http
def process_video(request):
    """
    Cloud Function لمعالجة الفيديو
    محسنة للعمل ضمن حدود Free Tier
    """
    try:
        # فحص طريقة الطلب
        if request.method != 'POST':
            return {'error': 'يُسمح بـ POST فقط'}, 405
            
        # الحصول على البيانات
        request_json = request.get_json(silent=True)
        if not request_json:
            return {'error': 'بيانات JSON مطلوبة'}, 400
            
        logger.info("🚀 بدء معالجة طلب إنشاء فيديو...")
        
        # استخدام معالج الفيديو المحسن
        with FreeTierVideoProcessor() as processor:
            
            # محاكاة بيانات Reddit (في التطبيق الحقيقي ستأتي من Reddit API)
            reddit_data = {
                'title': 'عنوان تجريبي من Reddit',
                'content': 'محتوى تجريبي للفيديو',
                'subreddit': 'AskReddit'
            }
            
            # إنشاء الفيديو
            video_path = processor.create_lightweight_video(reddit_data)
            
            if not video_path:
                return {'error': 'فشل في إنشاء الفيديو'}, 500
                
            # رفع الفيديو إلى Cloud Storage
            bucket_name = f"{processor.project_id}-video-results"
            blob_name = f"videos/{request_json.get('type', 'default')}_{int(time.time())}.mp4"
            
            if processor.upload_to_storage(video_path, bucket_name, blob_name):
                
                # إنشاء محتوى بواسطة Gemini (مبسط)
                video_metadata = generate_video_metadata(reddit_data)
                
                # رفع الفيديو على YouTube (إذا كان مطلوباً)
                youtube_url = None
                if request_json.get('upload_youtube', True):
                    youtube_url = upload_to_youtube_simple(video_path, video_metadata)
                
                # إرسال إشعار Telegram
                send_telegram_notification({
                    'status': 'success',
                    'video_url': youtube_url,
                    'metadata': video_metadata,
                    'storage_path': f"gs://{bucket_name}/{blob_name}"
                })
                
                return {
                    'success': True,
                    'message': 'تم إنشاء الفيديو بنجاح',
                    'youtube_url': youtube_url,
                    'storage_path': f"gs://{bucket_name}/{blob_name}",
                    'metadata': video_metadata
                }
            else:
                return {'error': 'فشل في رفع الفيديو'}, 500
                
    except Exception as e:
        logger.error(f"خطأ في معالجة الطلب: {e}")
        
        # إرسال إشعار خطأ
        send_telegram_notification({
            'status': 'error',
            'error': str(e)
        })
        
        return {'error': f'خطأ في المعالجة: {str(e)}'}, 500

def generate_video_metadata(reddit_data: Dict[str, Any]) -> Dict[str, Any]:
    """إنشاء metadata للفيديو باستخدام Gemini (مبسط)"""
    try:
        # استدعاء مبسط لـ Gemini API
        # في التطبيق الحقيقي، استخدم automation/gemini_content_generator.py
        
        return {
            'title': f"قصة مثيرة من Reddit! 🔥",
            'description': f"قصة رائعة من r/{reddit_data.get('subreddit', 'AskReddit')}\n\n#Shorts #Reddit #قصص",
            'hashtags': ['#Shorts', '#Reddit', '#قصص', '#مثير', '#ترفيه']
        }
    except Exception as e:
        logger.error(f"خطأ في إنشاء metadata: {e}")
        return {
            'title': 'قصة من Reddit',
            'description': 'قصة مثيرة من Reddit',
            'hashtags': ['#Reddit', '#Shorts']
        }

def upload_to_youtube_simple(video_path: str, metadata: Dict[str, Any]) -> Optional[str]:
    """رفع مبسط على YouTube"""
    try:
        # استخدام automation/youtube_uploader.py مع تحسينات
        logger.info("📤 رفع الفيديو على YouTube...")
        
        # محاكاة رفع YouTube
        return f"https://youtube.com/watch?v=fake_video_id"
        
    except Exception as e:
        logger.error(f"خطأ في رفع YouTube: {e}")
        return None

def send_telegram_notification(data: Dict[str, Any]):
    """إرسال إشعار Telegram مبسط"""
    try:
        # استخدام automation/telegram_bot.py
        logger.info("📱 إرسال إشعار Telegram...")
        
        # محاكاة إرسال إشعار
        if data['status'] == 'success':
            message = f"✅ تم إنشاء فيديو جديد!\n🔗 {data.get('youtube_url', 'غير متوفر')}"
        else:
            message = f"❌ خطأ في إنشاء الفيديو: {data.get('error', 'غير معروف')}"
            
        logger.info(f"إشعار Telegram: {message}")
        
    except Exception as e:
        logger.error(f"خطأ في إرسال إشعار Telegram: {e}")

# دالة للاختبار المحلي
if __name__ == "__main__":
    import time
    
    # محاكاة طلب HTTP
    class MockRequest:
        def __init__(self):
            self.method = 'POST'
            
        def get_json(self, silent=True):
            return {'type': 'test', 'upload_youtube': False}
    
    # اختبار الدالة
    result = process_video(MockRequest())
    print(f"نتيجة الاختبار: {result}")
