#!/usr/bin/env python3
"""
اختبار أولوية ElevenLabs TTS
يتحقق من عمل المفاتيح الجديدة والصوت المجاني
"""

import os
import sys
import logging
from pathlib import Path

# إعداد السجلات
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_elevenlabs_import():
    """اختبار استيراد ElevenLabs"""
    print("🔍 اختبار استيراد ElevenLabs...")
    
    try:
        from TTS.elevenlabs import elevenlabs
        print("✅ تم استيراد ElevenLabs بنجاح")
        return True
    except ImportError as e:
        print(f"❌ فشل في استيراد ElevenLabs: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ في استيراد ElevenLabs: {e}")
        return False

def test_api_keys():
    """اختبار مفاتيح API الجديدة"""
    print("\n🔑 اختبار مفاتيح API...")
    
    try:
        from TTS.elevenlabs import elevenlabs
        engine = elevenlabs()
        
        print(f"📋 عدد المفاتيح المتاحة: {len(engine.api_keys)}")
        
        working_keys = 0
        for i, api_key in enumerate(engine.api_keys):
            print(f"🔍 اختبار المفتاح {i+1}: {api_key[:10]}...")
            
            if engine.test_api_key(api_key):
                working_keys += 1
                print(f"✅ المفتاح {i+1} يعمل")
            else:
                print(f"❌ المفتاح {i+1} لا يعمل")
        
        print(f"\n📊 النتيجة: {working_keys}/{len(engine.api_keys)} مفاتيح تعمل")
        return working_keys > 0
        
    except Exception as e:
        print(f"❌ خطأ في اختبار المفاتيح: {e}")
        return False

def test_free_voice():
    """اختبار الصوت المجاني"""
    print("\n🎵 اختبار الصوت المجاني...")
    
    try:
        from TTS.elevenlabs import elevenlabs
        engine = elevenlabs()
        
        print(f"🆔 معرف الصوت المجاني: {engine.free_voice_id}")
        print(f"📝 اسم الصوت الافتراضي: {engine.default_voice_name}")
        
        # محاولة الحصول على معلومات الصوت
        for api_key in engine.api_keys:
            try:
                voices = engine.get_available_voices(api_key)
                if voices:
                    print(f"✅ تم الحصول على {len(voices)} صوت متاح")
                    
                    # البحث عن الصوت المجاني
                    free_voice_found = any(voice['voice_id'] == engine.free_voice_id for voice in voices)
                    if free_voice_found:
                        print(f"✅ تم العثور على الصوت المجاني: {engine.free_voice_id}")
                    else:
                        print(f"⚠️ لم يتم العثور على الصوت المجاني، سيتم استخدام صوت افتراضي")
                    
                    return True
            except:
                continue
        
        print("⚠️ لم يتم الحصول على معلومات الأصوات، لكن سيتم المحاولة")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الصوت المجاني: {e}")
        return False

def test_tts_generation():
    """اختبار إنشاء TTS فعلي"""
    print("\n🎤 اختبار إنشاء TTS...")
    
    try:
        from TTS.elevenlabs import elevenlabs
        
        # إنشاء مجلد مؤقت للاختبار
        test_dir = Path("test_audio")
        test_dir.mkdir(exist_ok=True)
        
        test_text = "Hello, this is a test of ElevenLabs TTS with the new API keys and free voice."
        test_file = test_dir / "test_elevenlabs.mp3"
        
        print(f"📝 النص: {test_text}")
        print(f"📁 الملف: {test_file}")
        
        engine = elevenlabs()
        success = engine.run(test_text, str(test_file))
        
        if test_file.exists():
            file_size = test_file.stat().st_size
            print(f"✅ تم إنشاء الملف الصوتي: {file_size} bytes")
            
            if file_size > 1000:  # أكبر من 1KB
                print("✅ حجم الملف معقول")
                return True
            else:
                print("⚠️ حجم الملف صغير جداً")
                return False
        else:
            print("❌ لم يتم إنشاء الملف الصوتي")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في إنشاء TTS: {e}")
        return False

def test_smart_tts_manager():
    """اختبار مدير TTS الذكي"""
    print("\n🧠 اختبار مدير TTS الذكي...")
    
    try:
        from TTS.smart_tts_manager import SmartTTSManager
        
        manager = SmartTTSManager()
        
        # فحص ترتيب الأولوية
        priority_engines = manager._get_priority_engines()
        print(f"🎯 ترتيب الأولوية: {priority_engines}")
        
        if priority_engines[0] == "ElevenLabs":
            print("✅ ElevenLabs له الأولوية الأولى")
        else:
            print("❌ ElevenLabs ليس له الأولوية الأولى")
            return False
        
        # فحص توفر ElevenLabs
        if manager._check_elevenlabs_availability():
            print("✅ ElevenLabs متوفر")
        else:
            print("❌ ElevenLabs غير متوفر")
            return False
        
        # اختبار إنشاء صوت
        test_dir = Path("test_audio")
        test_dir.mkdir(exist_ok=True)
        test_file = test_dir / "test_smart_manager.mp3"
        
        success = manager.generate_speech("Testing smart TTS manager with ElevenLabs priority.", str(test_file))
        
        if success and test_file.exists():
            print("✅ مدير TTS الذكي يعمل بشكل صحيح")
            return True
        else:
            print("❌ فشل مدير TTS الذكي")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في مدير TTS الذكي: {e}")
        return False

def test_config_settings():
    """اختبار إعدادات التكوين"""
    print("\n⚙️ اختبار إعدادات التكوين...")
    
    try:
        from utils import settings
        
        # فحص إعدادات ElevenLabs
        tts_config = settings.config["settings"]["tts"]
        
        print(f"🎵 محرك الصوت المختار: {tts_config['voice_choice']}")
        print(f"🔑 مفتاح API الحالي: {tts_config['elevenlabs_api_key'][:10]}...")
        print(f"🆔 معرف الصوت: {tts_config['elevenlabs_voice_id']}")
        
        # فحص المفاتيح المتعددة
        api_keys = settings.config["settings"]["tts"]["api_keys"]["elevenlabs_keys"]
        print(f"📋 عدد المفاتيح المتاحة: {len(api_keys)}")
        
        # فحص ترتيب الأولوية
        priority = settings.config["settings"]["tts"]["priority_order"]["primary"]
        print(f"🎯 ترتيب الأولوية: {priority}")
        
        if tts_config['voice_choice'] == "ElevenLabs" and priority[0] == "ElevenLabs":
            print("✅ إعدادات التكوين صحيحة")
            return True
        else:
            print("⚠️ إعدادات التكوين تحتاج تحديث")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في فحص الإعدادات: {e}")
        return False

def cleanup_test_files():
    """تنظيف ملفات الاختبار"""
    try:
        test_dir = Path("test_audio")
        if test_dir.exists():
            for file in test_dir.glob("*.mp3"):
                file.unlink()
            test_dir.rmdir()
            print("🧹 تم تنظيف ملفات الاختبار")
    except:
        pass

async def run_comprehensive_test():
    """تشغيل اختبار شامل"""
    print("🧪 اختبار شامل لأولوية ElevenLabs TTS")
    print("=" * 60)
    
    tests = [
        ("استيراد ElevenLabs", test_elevenlabs_import),
        ("مفاتيح API", test_api_keys),
        ("الصوت المجاني", test_free_voice),
        ("إنشاء TTS", test_tts_generation),
        ("مدير TTS الذكي", test_smart_tts_manager),
        ("إعدادات التكوين", test_config_settings),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {e}")
            results[test_name] = False
    
    # تلخيص النتائج
    print("\n" + "=" * 60)
    print("📊 ملخص نتائج الاختبار")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nالنتيجة النهائية: {passed}/{total} اختبارات نجحت")
    
    if passed == total:
        print("🎉 ممتاز! ElevenLabs يعمل بأولوية قصوى")
    elif passed >= total * 0.7:
        print("👍 جيد! معظم الاختبارات نجحت")
    else:
        print("❌ يحتاج إصلاحات")
    
    # تنظيف ملفات الاختبار
    cleanup_test_files()
    
    return passed == total

if __name__ == "__main__":
    import asyncio
    
    try:
        success = asyncio.run(run_comprehensive_test())
        
        print(f"\n{'='*60}")
        print("🏁 انتهى اختبار ElevenLabs")
        print(f"{'='*60}")
        
        if success:
            print("\n🚀 ElevenLabs جاهز للاستخدام مع أولوية قصوى!")
        else:
            print("\n🔧 يرجى مراجعة الأخطاء وإصلاحها")
        
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الاختبار بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ حرج في الاختبار: {e}")
        import traceback
        print(f"تفاصيل الخطأ:\n{traceback.format_exc()}")
