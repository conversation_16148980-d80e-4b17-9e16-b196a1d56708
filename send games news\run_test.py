#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل اختبار سريع للنظام المحدث
"""

import asyncio
import sys
import os
from datetime import datetime

# إضافة المجلد الحالي للمسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

async def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء اختبار النظام المحدث...")
    print("="*60)
    
    try:
        # اختبار الاستيراد
        print("📦 اختبار استيراد الوحدات...")
        from main import GamingNewsBot
        print("✅ تم استيراد GamingNewsBot بنجاح")
        
        # إنشاء مثيل من البوت
        print("\n🤖 إنشاء مثيل من البوت...")
        bot = GamingNewsBot()
        print("✅ تم إنشاء البوت بنجاح")
        
        # اختبار الدوال الجديدة
        print("\n🔍 اختبار الدوال الجديدة...")
        
        # اختبار البحث التاريخي
        print("📅 اختبار البحث التاريخي...")
        historical_content = await bot._search_historical_content()
        print(f"✅ البحث التاريخي: {len(historical_content)} محتوى")
        
        # اختبار المحتوى الرائج
        print("🔥 اختبار المحتوى الرائج...")
        trending_content = await bot._create_trending_content()
        print(f"✅ المحتوى الرائج: {len(trending_content)} محتوى")
        
        # اختبار المصادر العميقة
        print("🌐 اختبار المصادر العميقة...")
        deeper_content = await bot._search_deeper_sources()
        print(f"✅ المصادر العميقة: {len(deeper_content)} محتوى")
        
        # إحصائيات إجمالية
        total_content = len(historical_content) + len(trending_content) + len(deeper_content)
        
        print("\n" + "="*60)
        print("📊 نتائج الاختبار:")
        print(f"📅 محتوى تاريخي: {len(historical_content)}")
        print(f"🔥 محتوى رائج: {len(trending_content)}")
        print(f"🌐 مصادر عميقة: {len(deeper_content)}")
        print(f"📈 إجمالي المحتوى: {total_content}")
        
        if total_content > 0:
            print("\n🎉 النظام يعمل بنجاح!")
            print("✅ تم حل مشكلة عدم وجود محتوى جديد")
            print("🚀 يمكنك الآن تشغيل: python main.py")
            
            # عرض عينة من المحتوى
            if trending_content:
                print(f"\n📝 عينة من المحتوى الرائج:")
                for i, content in enumerate(trending_content[:2], 1):
                    print(f"  {i}. {content.get('title', 'بدون عنوان')[:50]}...")
        else:
            print("\n⚠️ لم يتم إنشاء محتوى - قد تحتاج إلى مراجعة الإعدادات")
        
        print("\n" + "="*60)
        print(f"⏱️ انتهى الاختبار في: {datetime.now().strftime('%H:%M:%S')}")
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        print("💡 تأكد من وجود جميع الملفات المطلوبة")
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        print("💡 راجع الأخطاء وأعد المحاولة")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⌨️ تم إيقاف الاختبار بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ عام: {e}")
