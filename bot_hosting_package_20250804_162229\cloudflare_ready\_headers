# Headers for Cloudflare Pages - Minecraft Mod Bot
# Security and Performance Headers

/*
  # Security Headers
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  X-XSS-Protection: 1; mode=block
  Referrer-Policy: strict-origin-when-cross-origin
  Permissions-Policy: camera=(), microphone=(), geolocation=(), payment=()
  
  # Content Security Policy
  Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' https://fonts.googleapis.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https: http:; connect-src 'self' https://ytqxxodyecdeosnqoure.supabase.co https://via.placeholder.com; frame-ancestors 'none';
  
  # Performance Headers
  Cache-Control: public, max-age=31536000, immutable
  
  # CORS Headers for API requests
  Access-Control-Allow-Origin: *
  Access-Control-Allow-Methods: GET, POST, OPTIONS
  Access-Control-Allow-Headers: Content-Type, Authorization, apikey

# Specific headers for HTML files
/*.html
  Cache-Control: public, max-age=3600
  Content-Type: text/html; charset=utf-8

# Specific headers for CSS files
/*.css
  Cache-Control: public, max-age=31536000, immutable
  Content-Type: text/css; charset=utf-8

# Specific headers for JavaScript files
/*.js
  Cache-Control: public, max-age=31536000, immutable
  Content-Type: application/javascript; charset=utf-8

# Specific headers for images
/*.png
  Cache-Control: public, max-age=31536000, immutable
  Content-Type: image/png

/*.jpg
  Cache-Control: public, max-age=31536000, immutable
  Content-Type: image/jpeg

/*.jpeg
  Cache-Control: public, max-age=31536000, immutable
  Content-Type: image/jpeg

/*.gif
  Cache-Control: public, max-age=31536000, immutable
  Content-Type: image/gif

/*.svg
  Cache-Control: public, max-age=31536000, immutable
  Content-Type: image/svg+xml

/*.webp
  Cache-Control: public, max-age=31536000, immutable
  Content-Type: image/webp

# Headers for API endpoints
/api/*
  Cache-Control: no-cache, no-store, must-revalidate
  Access-Control-Allow-Origin: *
  Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS
  Access-Control-Allow-Headers: Content-Type, Authorization, apikey, Prefer

# Headers for fonts
/*.woff
  Cache-Control: public, max-age=31536000, immutable
  Content-Type: font/woff

/*.woff2
  Cache-Control: public, max-age=31536000, immutable
  Content-Type: font/woff2

/*.ttf
  Cache-Control: public, max-age=31536000, immutable
  Content-Type: font/ttf

/*.eot
  Cache-Control: public, max-age=31536000, immutable
  Content-Type: application/vnd.ms-fontobject
