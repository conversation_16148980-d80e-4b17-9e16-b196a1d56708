# 🤝 دليل المساهمة في GitHub Uploader Tool

نرحب بمساهماتكم في تطوير أداة GitHub Uploader! هذا الدليل سيساعدكم على فهم كيفية المساهمة بفعالية.

## 📋 جدول المحتويات

- [كيفية المساهمة](#كيفية-المساهمة)
- [الإبلاغ عن الأخطاء](#الإبلاغ-عن-الأخطاء)
- [اقتراح ميزات جديدة](#اقتراح-ميزات-جديدة)
- [تطوير الكود](#تطوير-الكود)
- [معايير الكود](#معايير-الكود)
- [تشغيل الاختبارات](#تشغيل-الاختبارات)
- [إرسال Pull Request](#إرسال-pull-request)

## 🚀 كيفية المساهمة

### 1. Fork المشروع
```bash
# انقر على زر Fork في GitHub
# ثم استنسخ المشروع محلياً
git clone https://github.com/yourusername/github-uploader.git
cd github-uploader
```

### 2. إعداد البيئة التطويرية
```bash
# إنشاء بيئة افتراضية
python -m venv venv

# تفعيل البيئة الافتراضية
# على Windows:
venv\Scripts\activate
# على Linux/Mac:
source venv/bin/activate

# تثبيت المتطلبات
pip install -r requirements.txt
pip install -r requirements-dev.txt  # إذا كان موجوداً
```

### 3. إنشاء فرع جديد
```bash
git checkout -b feature/amazing-feature
# أو
git checkout -b bugfix/fix-issue-123
```

## 🐛 الإبلاغ عن الأخطاء

عند الإبلاغ عن خطأ، يرجى تضمين:

### معلومات النظام
- نظام التشغيل (Windows/Linux/Mac)
- إصدار Python
- إصدار Git
- إصدار الأداة

### وصف المشكلة
- وصف واضح للمشكلة
- خطوات إعادة إنتاج المشكلة
- السلوك المتوقع مقابل السلوك الفعلي
- رسائل الخطأ (إن وجدت)
- لقطات شاشة (إن أمكن)

### مثال على تقرير خطأ
```markdown
## وصف المشكلة
الأداة تفشل في رفع المشاريع التي تحتوي على ملفات بأسماء عربية.

## خطوات إعادة الإنتاج
1. إنشاء مشروع يحتوي على ملف باسم "ملف_عربي.py"
2. تشغيل الأداة
3. محاولة رفع المشروع

## السلوك المتوقع
رفع المشروع بنجاح مع جميع الملفات

## السلوك الفعلي
خطأ في encoding: UnicodeEncodeError

## معلومات النظام
- OS: Windows 10
- Python: 3.9.7
- Git: 2.33.0
```

## ✨ اقتراح ميزات جديدة

نرحب بالاقتراحات! يرجى:

1. **البحث أولاً**: تأكد من عدم وجود اقتراح مشابه
2. **وصف الميزة**: اشرح الميزة المقترحة بوضوح
3. **تبرير الحاجة**: لماذا هذه الميزة مفيدة؟
4. **أمثلة الاستخدام**: كيف ستُستخدم الميزة؟

### قالب اقتراح ميزة
```markdown
## وصف الميزة
إضافة دعم لرفع المشاريع على GitLab

## المبرر
العديد من المطورين يستخدمون GitLab بدلاً من GitHub

## الاستخدام المقترح
```python
uploader = GitLabUploader(token)
uploader.upload_project(...)
```

## التفاصيل الإضافية
- دعم GitLab API
- واجهة مشابهة لـ GitHub
- إعدادات منفصلة
```

## 💻 تطوير الكود

### هيكل المشروع
```
github-uploader/
├── github_uploader.py    # الملف الرئيسي
├── config.py            # الإعدادات
├── examples.py          # الأمثلة
├── test_uploader.py     # الاختبارات
├── requirements.txt     # المتطلبات
├── setup.py            # ملف التثبيت
└── docs/               # التوثيق
```

### إضافة ميزة جديدة
1. **تحديث الكود**: أضف الميزة في الملف المناسب
2. **إضافة اختبارات**: اكتب اختبارات للميزة الجديدة
3. **تحديث التوثيق**: حدث README و التوثيق
4. **إضافة أمثلة**: أضف أمثلة في examples.py

## 📏 معايير الكود

### Python Style Guide
- اتبع [PEP 8](https://www.python.org/dev/peps/pep-0008/)
- استخدم أسماء متغيرات واضحة
- أضف docstrings للدوال والكلاسات
- استخدم type hints عند الإمكان

### مثال على كود جيد
```python
def upload_project(self, project_path: str, repo_name: str, 
                  description: str = "", private: bool = False) -> bool:
    """
    رفع مشروع كامل على GitHub
    
    Args:
        project_path: مسار المشروع المحلي
        repo_name: اسم المستودع على GitHub
        description: وصف المستودع
        private: هل المستودع خاص
        
    Returns:
        True إذا تم بنجاح، False إذا فشل
    """
    # تنفيذ الدالة...
```

### التعليقات
- اكتب التعليقات باللغة العربية للوضوح
- اشرح المنطق المعقد
- تجنب التعليقات الواضحة

## 🧪 تشغيل الاختبارات

### تشغيل جميع الاختبارات
```bash
python test_uploader.py
```

### تشغيل اختبارات محددة
```bash
python -m unittest test_uploader.TestGitHubUploader.test_detect_project_type_python
```

### فحص التغطية
```bash
pip install coverage
coverage run test_uploader.py
coverage report
coverage html  # لتقرير HTML
```

### معايير الاختبارات
- اكتب اختبارات لكل دالة جديدة
- تأكد من تغطية الحالات الاستثنائية
- استخدم أسماء اختبارات واضحة
- أضف تعليقات للاختبارات المعقدة

## 📤 إرسال Pull Request

### قبل الإرسال
1. **تشغيل الاختبارات**: تأكد من نجاح جميع الاختبارات
2. **فحص الكود**: راجع الكود للتأكد من جودته
3. **تحديث التوثيق**: حدث README إذا لزم الأمر
4. **تحديث CHANGELOG**: أضف التغييرات في CHANGELOG.md

### عنوان PR
استخدم عناوين واضحة:
- `feat: إضافة دعم GitLab`
- `fix: إصلاح مشكلة encoding الملفات العربية`
- `docs: تحديث دليل التثبيت`
- `test: إضافة اختبارات لتحديد نوع المشروع`

### وصف PR
```markdown
## نوع التغيير
- [ ] إصلاح خطأ (bugfix)
- [x] ميزة جديدة (feature)
- [ ] تحسين الأداء (performance)
- [ ] تحديث التوثيق (docs)

## الوصف
إضافة دعم لرفع المشاريع على GitLab مع API مشابه لـ GitHub.

## الاختبارات
- [x] تم إضافة اختبارات جديدة
- [x] جميع الاختبارات تنجح
- [x] تم اختبار الميزة يدوياً

## التوثيق
- [x] تم تحديث README
- [x] تم تحديث CHANGELOG
- [x] تم إضافة أمثلة
```

## 🎯 أولويات التطوير

### مطلوب حالياً
- [ ] دعم GitLab و Bitbucket
- [ ] واجهة مستخدم رسومية (GUI)
- [ ] دعم GitHub Actions
- [ ] تحسين معالجة الأخطاء
- [ ] دعم المزيد من لغات البرمجة

### مرحب به
- تحسين الأداء
- إضافة اختبارات
- تحسين التوثيق
- ترجمة لغات أخرى
- إصلاح الأخطاء

## 📞 التواصل

- **GitHub Issues**: للأخطاء والاقتراحات
- **Email**: <EMAIL>
- **Discord**: [رابط الخادم]

## 🙏 شكر خاص

شكراً لجميع المساهمين:
- [@contributor1](https://github.com/contributor1)
- [@contributor2](https://github.com/contributor2)

---

**نقدر مساهماتكم في جعل هذه الأداة أفضل للمجتمع العربي! 🚀**
