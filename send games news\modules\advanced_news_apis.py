# نظام متطور للبحث واستخراج الأخبار باستخدام APIs متعددة
import aiohttp
import asyncio
import json
import time
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple
import hashlib
import re
from urllib.parse import urljoin, urlparse
from .logger import logger
from .database import db
from config.settings import BotConfig

class AdvancedNewsAPIs:
    """نظام متطور لجمع الأخبار من APIs متعددة"""
    
    def __init__(self):
        # مفاتيح APIs للأخبار
        self.newsapi_key = getattr(BotConfig, 'NEWSAPI_KEY', '')
        self.newsdata_key = getattr(BotConfig, 'NEWSDATA_KEY', 'pub_6a04788f4edc429a8fb798dc3af6a6fb')
        self.thenewsapi_key = getattr(BotConfig, 'THENEWSAPI_KEY', '')
        self.gnews_key = getattr(BotConfig, 'GNEWS_KEY', '')
        
        # إعدادات APIs
        self.api_endpoints = {
            'newsapi': 'https://newsapi.org/v2/everything',
            'newsdata': 'https://newsdata.io/api/1/news',
            'thenewsapi': 'https://api.thenewsapi.com/v1/news/all',
            'gnews': 'https://gnews.io/api/v4/search'
        }
        
        # كلمات مفتاحية محسنة للألعاب
        self.gaming_keywords = [
            'video games', 'gaming news', 'game release', 'gaming industry',
            'esports', 'game review', 'gaming update', 'new game',
            'gaming technology', 'game development', 'gaming console',
            'mobile gaming', 'PC gaming', 'gaming announcement',
            'game trailer', 'gaming event', 'game launch'
        ]
        
        # مصادر موثوقة للألعاب
        self.trusted_gaming_sources = [
            'ign.com', 'gamespot.com', 'polygon.com', 'kotaku.com',
            'eurogamer.net', 'gamesindustry.biz', 'gamedeveloper.com',
            'pcgamer.com', 'gameinformer.com', 'destructoid.com',
            'rockpapershotgun.com', 'gamesradar.com'
        ]
        
        # إحصائيات الاستخدام
        self.usage_stats = {
            'newsapi_calls': 0,
            'newsdata_calls': 0,
            'thenewsapi_calls': 0,
            'gnews_calls': 0,
            'total_articles_found': 0,
            'successful_extractions': 0,
            'failed_extractions': 0
        }
        
        # تخزين مؤقت للنتائج
        self.cache = {}
        self.cache_duration = 3600  # ساعة واحدة
    
    async def search_gaming_news_comprehensive(self, 
                                             keywords: List[str] = None, 
                                             max_articles: int = 50,
                                             days_back: int = 7) -> List[Dict]:
        """بحث شامل عن أخبار الألعاب من جميع APIs"""
        try:
            logger.info("🔍 بدء البحث الشامل عن أخبار الألعاب من APIs متعددة...")
            
            if not keywords:
                keywords = self.gaming_keywords[:10]  # أفضل 10 كلمات مفتاحية
            
            all_articles = []
            
            # البحث في جميع APIs بالتوازي
            search_tasks = []
            
            # 1. NewsAPI
            if self.newsapi_key:
                search_tasks.append(self._search_newsapi(keywords, max_articles//4, days_back))
            
            # 2. NewsData.io
            if self.newsdata_key:
                search_tasks.append(self._search_newsdata(keywords, max_articles//4, days_back))
            
            # 3. TheNewsAPI
            if self.thenewsapi_key:
                search_tasks.append(self._search_thenewsapi(keywords, max_articles//4, days_back))
            
            # 4. GNews
            if self.gnews_key:
                search_tasks.append(self._search_gnews(keywords, max_articles//4, days_back))
            
            # تنفيذ البحث بالتوازي
            if search_tasks:
                results = await asyncio.gather(*search_tasks, return_exceptions=True)
                
                for result in results:
                    if isinstance(result, list):
                        all_articles.extend(result)
                    elif isinstance(result, Exception):
                        logger.warning(f"⚠️ خطأ في أحد APIs: {result}")
            
            # تصفية وتنظيف النتائج
            filtered_articles = await self._filter_and_clean_articles(all_articles)
            
            # ترتيب حسب الجودة والحداثة
            sorted_articles = self._sort_articles_by_quality(filtered_articles)
            
            self.usage_stats['total_articles_found'] += len(sorted_articles)
            
            logger.info(f"✅ تم العثور على {len(sorted_articles)} مقال عالي الجودة من APIs متعددة")
            
            return sorted_articles[:max_articles]
            
        except Exception as e:
            logger.error(f"❌ فشل في البحث الشامل: {e}")
            return []
    
    async def _search_newsapi(self, keywords: List[str], max_results: int, days_back: int) -> List[Dict]:
        """البحث في NewsAPI"""
        try:
            logger.info("📰 البحث في NewsAPI...")
            
            articles = []
            from_date = (datetime.now() - timedelta(days=days_back)).strftime('%Y-%m-%d')
            
            async with aiohttp.ClientSession() as session:
                for keyword in keywords[:5]:  # أفضل 5 كلمات مفتاحية
                    params = {
                        'apiKey': self.newsapi_key,
                        'q': f'{keyword} AND (gaming OR games OR video)',
                        'language': 'en',
                        'sortBy': 'publishedAt',
                        'from': from_date,
                        'pageSize': max_results // 5
                    }
                    
                    async with session.get(self.api_endpoints['newsapi'], params=params) as response:
                        if response.status == 200:
                            data = await response.json()
                            
                            if data.get('articles'):
                                for article in data['articles']:
                                    processed_article = self._process_newsapi_article(article)
                                    if processed_article:
                                        articles.append(processed_article)
                        
                        await asyncio.sleep(1)  # احترام حدود API
            
            self.usage_stats['newsapi_calls'] += len(keywords[:5])
            logger.info(f"📰 NewsAPI: تم العثور على {len(articles)} مقال")
            
            return articles
            
        except Exception as e:
            logger.error(f"❌ خطأ في NewsAPI: {e}")
            return []
    
    async def _search_newsdata(self, keywords: List[str], max_results: int, days_back: int) -> List[Dict]:
        """البحث في NewsData.io مع تحسينات للحصول على محتوى ألعاب عالي الجودة"""
        try:
            logger.info("📊 البحث في NewsData.io...")

            articles = []

            # استراتيجيات بحث محسنة ومبسطة للألعاب (تجنب المعاملات المعقدة التي تسبب خطأ 422)
            gaming_search_strategies = [
                {
                    'q': 'gaming',
                    'language': 'en',
                    'size': 10
                },
                {
                    'q': 'video games',
                    'language': 'en',
                    'size': 10
                },
                {
                    'q': 'esports',
                    'language': 'en',
                    'size': 10
                }
            ]

            async with aiohttp.ClientSession() as session:
                for strategy in gaming_search_strategies:
                    params = {
                        'apikey': self.newsdata_key,
                    }

                    # إضافة معاملات الاستراتيجية
                    params.update(strategy)

                    try:
                        async with session.get(self.api_endpoints['newsdata'], params=params) as response:
                            if response.status == 200:
                                data = await response.json()

                                if data.get('results'):
                                    logger.info(f"📊 NewsData.io: وجد {len(data['results'])} مقال للاستراتيجية")

                                    for article in data['results']:
                                        processed_article = self._process_newsdata_article(article)
                                        if processed_article:
                                            articles.append(processed_article)
                                else:
                                    logger.warning(f"📊 NewsData.io: لا توجد نتائج للاستراتيجية")
                            elif response.status == 422:
                                # خطأ 422 يعني معاملات غير صحيحة
                                error_text = await response.text()
                                logger.warning(f"📊 NewsData.io: خطأ 422 - معاملات غير صحيحة: {error_text}")
                            else:
                                error_text = await response.text()
                                logger.warning(f"📊 NewsData.io: خطأ {response.status}: {error_text}")

                    except Exception as e:
                        logger.warning(f"📊 NewsData.io: خطأ في الاستراتيجية: {e}")
                        continue

                    await asyncio.sleep(3)  # احترام حدود API مع تأخير أطول

            self.usage_stats['newsdata_calls'] += len(gaming_search_strategies)
            logger.info(f"📊 NewsData.io: تم العثور على {len(articles)} مقال عالي الجودة")

            return articles

        except Exception as e:
            logger.error(f"❌ خطأ في NewsData.io: {e}")
            return []
    
    async def _search_thenewsapi(self, keywords: List[str], max_results: int, days_back: int) -> List[Dict]:
        """البحث في TheNewsAPI"""
        try:
            logger.info("🗞️ البحث في TheNewsAPI...")
            
            articles = []
            
            async with aiohttp.ClientSession() as session:
                for keyword in keywords[:3]:
                    params = {
                        'api_token': self.thenewsapi_key,
                        'search': f'{keyword} gaming',
                        'language': 'en',
                        'limit': max_results // 3
                    }
                    
                    async with session.get(self.api_endpoints['thenewsapi'], params=params) as response:
                        if response.status == 200:
                            data = await response.json()
                            
                            if data.get('data'):
                                for article in data['data']:
                                    processed_article = self._process_thenewsapi_article(article)
                                    if processed_article:
                                        articles.append(processed_article)
                        
                        await asyncio.sleep(1)
            
            self.usage_stats['thenewsapi_calls'] += len(keywords[:3])
            logger.info(f"🗞️ TheNewsAPI: تم العثور على {len(articles)} مقال")
            
            return articles
            
        except Exception as e:
            logger.error(f"❌ خطأ في TheNewsAPI: {e}")
            return []
    
    async def _search_gnews(self, keywords: List[str], max_results: int, days_back: int) -> List[Dict]:
        """البحث في GNews"""
        try:
            logger.info("🌐 البحث في GNews...")
            
            articles = []
            
            async with aiohttp.ClientSession() as session:
                for keyword in keywords[:3]:
                    params = {
                        'token': self.gnews_key,
                        'q': f'{keyword} gaming',
                        'lang': 'en',
                        'max': max_results // 3
                    }
                    
                    async with session.get(self.api_endpoints['gnews'], params=params) as response:
                        if response.status == 200:
                            data = await response.json()
                            
                            if data.get('articles'):
                                for article in data['articles']:
                                    processed_article = self._process_gnews_article(article)
                                    if processed_article:
                                        articles.append(processed_article)
                        
                        await asyncio.sleep(1)
            
            self.usage_stats['gnews_calls'] += len(keywords[:3])
            logger.info(f"🌐 GNews: تم العثور على {len(articles)} مقال")
            
            return articles
            
        except Exception as e:
            logger.error(f"❌ خطأ في GNews: {e}")
            return []

    def _process_newsapi_article(self, article: Dict) -> Optional[Dict]:
        """معالجة مقال من NewsAPI"""
        try:
            if not article.get('title') or not article.get('url'):
                return None

            # فحص الجودة
            if not self._is_gaming_related(article.get('title', '') + ' ' + article.get('description', '')):
                return None

            return {
                'title': article['title'],
                'url': article['url'],
                'content': article.get('content', ''),
                'summary': article.get('description', ''),
                'published_date': self._parse_date(article.get('publishedAt')),
                'source': article.get('source', {}).get('name', 'NewsAPI'),
                'source_type': 'newsapi',
                'author': article.get('author', ''),
                'image_url': article.get('urlToImage', ''),
                'quality_score': self._calculate_quality_score(article),
                'extraction_method': 'newsapi'
            }

        except Exception as e:
            logger.debug(f"خطأ في معالجة مقال NewsAPI: {e}")
            return None

    def _process_newsdata_article(self, article: Dict) -> Optional[Dict]:
        """معالجة مقال من NewsData.io مع فلترة محسنة"""
        try:
            if not article.get('title') or not article.get('link'):
                return None

            # فحص أكثر دقة للمحتوى المتعلق بالألعاب
            title = article.get('title', '').lower()
            description = article.get('description', '').lower()
            content = article.get('content', '').lower()

            # كلمات مفتاحية قوية للألعاب
            strong_gaming_keywords = [
                'video game', 'gaming', 'esports', 'e-sports', 'gamer', 'gameplay',
                'playstation', 'xbox', 'nintendo', 'steam', 'pc gaming', 'mobile gaming',
                'console', 'game review', 'game trailer', 'game announcement',
                'indie game', 'aaa game', 'multiplayer', 'single player', 'rpg',
                'fps', 'mmorpg', 'battle royale', 'game developer', 'game studio'
            ]

            # كلمات يجب تجنبها (رياضة، قمار، إلخ)
            exclude_keywords = [
                'football game', 'basketball game', 'baseball game', 'soccer game',
                'gambling', 'casino', 'poker', 'betting', 'lottery',
                'board game', 'card game', 'olympic games', 'sports game'
            ]

            full_text = f"{title} {description} {content}"

            # فحص الكلمات المستبعدة أولاً
            if any(exclude_word in full_text for exclude_word in exclude_keywords):
                return None

            # فحص وجود كلمات الألعاب القوية
            gaming_score = sum(1 for keyword in strong_gaming_keywords if keyword in full_text)

            if gaming_score < 1:  # يجب وجود كلمة واحدة على الأقل
                return None

            # حساب نقاط الجودة
            quality_score = self._calculate_newsdata_quality_score(article, gaming_score)

            if quality_score < 3:  # حد أدنى للجودة
                return None

            return {
                'title': article['title'],
                'url': article['link'],
                'content': article.get('content', ''),
                'summary': article.get('description', ''),
                'published_date': self._parse_date(article.get('pubDate')),
                'source': article.get('source_id', 'NewsData'),
                'source_type': 'newsdata',
                'author': '',
                'image_url': article.get('image_url', ''),
                'quality_score': quality_score,
                'gaming_relevance_score': gaming_score,
                'extraction_method': 'newsdata'
            }

        except Exception as e:
            logger.debug(f"خطأ في معالجة مقال NewsData: {e}")
            return None

    def _calculate_newsdata_quality_score(self, article: Dict, gaming_score: int) -> int:
        """حساب نقاط الجودة لمقال NewsData.io"""
        score = 0

        # نقاط الصلة بالألعاب
        score += min(gaming_score * 2, 6)  # حد أقصى 6 نقاط

        # جودة العنوان
        title = article.get('title', '')
        if len(title) > 20 and len(title) < 100:
            score += 2

        # وجود وصف
        if article.get('description') and len(article.get('description', '')) > 50:
            score += 1

        # وجود صورة
        if article.get('image_url'):
            score += 1

        # مصادر موثوقة
        trusted_sources = [
            'ign', 'gamespot', 'polygon', 'kotaku', 'eurogamer', 'pcgamer',
            'gamesradar', 'destructoid', 'gameinformer', 'rockpapershotgun'
        ]
        source_id = article.get('source_id', '').lower()
        if any(trusted in source_id for trusted in trusted_sources):
            score += 2

        return min(score, 10)  # حد أقصى 10 نقاط

    def _process_thenewsapi_article(self, article: Dict) -> Optional[Dict]:
        """معالجة مقال من TheNewsAPI"""
        try:
            if not article.get('title') or not article.get('url'):
                return None

            if not self._is_gaming_related(article.get('title', '') + ' ' + article.get('description', '')):
                return None

            return {
                'title': article['title'],
                'url': article['url'],
                'content': article.get('snippet', ''),
                'summary': article.get('description', ''),
                'published_date': self._parse_date(article.get('published_at')),
                'source': article.get('source', 'TheNewsAPI'),
                'source_type': 'thenewsapi',
                'author': '',
                'image_url': article.get('image_url', ''),
                'quality_score': self._calculate_quality_score(article),
                'extraction_method': 'thenewsapi'
            }

        except Exception as e:
            logger.debug(f"خطأ في معالجة مقال TheNewsAPI: {e}")
            return None

    def _process_gnews_article(self, article: Dict) -> Optional[Dict]:
        """معالجة مقال من GNews"""
        try:
            if not article.get('title') or not article.get('url'):
                return None

            if not self._is_gaming_related(article.get('title', '') + ' ' + article.get('description', '')):
                return None

            return {
                'title': article['title'],
                'url': article['url'],
                'content': article.get('content', ''),
                'summary': article.get('description', ''),
                'published_date': self._parse_date(article.get('publishedAt')),
                'source': article.get('source', {}).get('name', 'GNews'),
                'source_type': 'gnews',
                'author': '',
                'image_url': article.get('image', ''),
                'quality_score': self._calculate_quality_score(article),
                'extraction_method': 'gnews'
            }

        except Exception as e:
            logger.debug(f"خطأ في معالجة مقال GNews: {e}")
            return None

    def _is_gaming_related(self, text: str) -> bool:
        """فحص ما إذا كان النص متعلق بالألعاب"""
        text_lower = text.lower()

        gaming_terms = [
            'game', 'gaming', 'video game', 'esports', 'console', 'pc gaming',
            'mobile game', 'indie game', 'aaa game', 'game developer',
            'game industry', 'game release', 'game review', 'gameplay',
            'gamer', 'gaming news', 'game trailer', 'game announcement'
        ]

        # يجب أن يحتوي على مصطلح واحد على الأقل
        return any(term in text_lower for term in gaming_terms)

    def _calculate_quality_score(self, article: Dict) -> float:
        """حساب نقاط الجودة للمقال"""
        score = 0.0

        # نقاط العنوان
        title = article.get('title', '')
        if len(title) > 20:
            score += 2.0
        if len(title) > 50:
            score += 1.0

        # نقاط المحتوى
        content = article.get('content', '') or article.get('description', '')
        if len(content) > 100:
            score += 2.0
        if len(content) > 300:
            score += 1.0

        # نقاط المصدر
        source = article.get('source', {})
        if isinstance(source, dict):
            source_name = source.get('name', '').lower()
        else:
            source_name = str(source).lower()

        if any(trusted in source_name for trusted in self.trusted_gaming_sources):
            score += 3.0

        # نقاط الصورة
        if article.get('urlToImage') or article.get('image_url') or article.get('image'):
            score += 1.0

        # نقاط المؤلف
        if article.get('author'):
            score += 0.5

        return min(score, 10.0)  # حد أقصى 10 نقاط

    def _parse_date(self, date_str: str) -> Optional[datetime]:
        """تحليل التاريخ من صيغ مختلفة"""
        if not date_str:
            return None

        try:
            # صيغ مختلفة للتاريخ
            formats = [
                '%Y-%m-%dT%H:%M:%SZ',
                '%Y-%m-%dT%H:%M:%S.%fZ',
                '%Y-%m-%d %H:%M:%S',
                '%Y-%m-%d',
                '%a, %d %b %Y %H:%M:%S %Z'
            ]

            for fmt in formats:
                try:
                    return datetime.strptime(date_str, fmt)
                except ValueError:
                    continue

            # إذا فشلت جميع الصيغ، استخدم dateutil
            from dateutil import parser
            return parser.parse(date_str)

        except Exception as e:
            logger.debug(f"فشل في تحليل التاريخ {date_str}: {e}")
            return datetime.now()

    async def _filter_and_clean_articles(self, articles: List[Dict]) -> List[Dict]:
        """تصفية وتنظيف المقالات"""
        try:
            filtered_articles = []
            seen_urls = set()
            seen_titles = set()

            for article in articles:
                # فحص التكرار
                url = article.get('url', '')
                title = article.get('title', '').lower().strip()

                if url in seen_urls or title in seen_titles:
                    continue

                # فحص الجودة الأساسية
                if len(title) < 10 or not url:
                    continue

                # فحص التكرار في قاعدة البيانات
                is_duplicate, _ = db.is_duplicate_content(
                    article.get('content', ''),
                    article.get('title', ''),
                    []
                )

                if not is_duplicate:
                    # تنظيف البيانات
                    article['title'] = self._clean_text(article['title'])
                    article['summary'] = self._clean_text(article.get('summary', ''))
                    article['content'] = self._clean_text(article.get('content', ''))

                    filtered_articles.append(article)
                    seen_urls.add(url)
                    seen_titles.add(title)

            return filtered_articles

        except Exception as e:
            logger.error(f"❌ خطأ في تصفية المقالات: {e}")
            return articles

    def _clean_text(self, text: str) -> str:
        """تنظيف النص من الرموز غير المرغوبة"""
        if not text:
            return ''

        # إزالة HTML tags
        text = re.sub(r'<[^>]+>', '', text)

        # إزالة الأسطر الفارغة المتعددة
        text = re.sub(r'\n\s*\n', '\n\n', text)

        # إزالة المسافات الزائدة
        text = re.sub(r'\s+', ' ', text)

        return text.strip()

    def _sort_articles_by_quality(self, articles: List[Dict]) -> List[Dict]:
        """ترتيب المقالات حسب الجودة والحداثة"""
        try:
            def sort_key(article):
                quality_score = article.get('quality_score', 0)

                # نقاط إضافية للمقالات الحديثة
                pub_date = article.get('published_date')
                if pub_date:
                    days_old = (datetime.now() - pub_date).days
                    freshness_score = max(0, 5 - days_old * 0.5)  # نقاط تتناقص مع الوقت
                else:
                    freshness_score = 0

                return quality_score + freshness_score

            return sorted(articles, key=sort_key, reverse=True)

        except Exception as e:
            logger.error(f"❌ خطأ في ترتيب المقالات: {e}")
            return articles

    def get_usage_stats(self) -> Dict:
        """الحصول على إحصائيات الاستخدام"""
        total_calls = sum([
            self.usage_stats['newsapi_calls'],
            self.usage_stats['newsdata_calls'],
            self.usage_stats['thenewsapi_calls'],
            self.usage_stats['gnews_calls']
        ])

        success_rate = 0
        if total_calls > 0:
            success_rate = (self.usage_stats['successful_extractions'] / total_calls) * 100

        return {
            **self.usage_stats,
            'total_api_calls': total_calls,
            'success_rate': success_rate,
            'average_articles_per_call': self.usage_stats['total_articles_found'] / max(1, total_calls)
        }

    async def test_all_apis(self) -> Dict:
        """اختبار جميع APIs المتاحة"""
        test_results = {
            'newsapi': {'available': False, 'error': None, 'articles_found': 0},
            'newsdata': {'available': False, 'error': None, 'articles_found': 0},
            'thenewsapi': {'available': False, 'error': None, 'articles_found': 0},
            'gnews': {'available': False, 'error': None, 'articles_found': 0},
            'overall_status': False
        }

        # اختبار NewsAPI
        if self.newsapi_key:
            try:
                articles = await self._search_newsapi(['gaming'], 5, 1)
                test_results['newsapi']['available'] = True
                test_results['newsapi']['articles_found'] = len(articles)
            except Exception as e:
                test_results['newsapi']['error'] = str(e)
        else:
            test_results['newsapi']['error'] = 'API key not provided'

        # اختبار NewsData
        if self.newsdata_key:
            try:
                articles = await self._search_newsdata(['gaming'], 5, 1)
                test_results['newsdata']['available'] = True
                test_results['newsdata']['articles_found'] = len(articles)
            except Exception as e:
                test_results['newsdata']['error'] = str(e)
        else:
            test_results['newsdata']['error'] = 'API key not provided'

        # اختبار TheNewsAPI
        if self.thenewsapi_key:
            try:
                articles = await self._search_thenewsapi(['gaming'], 5, 1)
                test_results['thenewsapi']['available'] = True
                test_results['thenewsapi']['articles_found'] = len(articles)
            except Exception as e:
                test_results['thenewsapi']['error'] = str(e)
        else:
            test_results['thenewsapi']['error'] = 'API key not provided'

        # اختبار GNews
        if self.gnews_key:
            try:
                articles = await self._search_gnews(['gaming'], 5, 1)
                test_results['gnews']['available'] = True
                test_results['gnews']['articles_found'] = len(articles)
            except Exception as e:
                test_results['gnews']['error'] = str(e)
        else:
            test_results['gnews']['error'] = 'API key not provided'

        # تحديد الحالة العامة
        available_apis = sum(1 for api in test_results.values()
                           if isinstance(api, dict) and api.get('available', False))
        test_results['overall_status'] = available_apis > 0

        return test_results

    async def search_specific_game_news(self, game_name: str, max_articles: int = 20) -> List[Dict]:
        """البحث عن أخبار لعبة محددة"""
        try:
            logger.info(f"🎮 البحث عن أخبار خاصة بـ: {game_name}")

            # كلمات مفتاحية مخصصة للعبة
            game_keywords = [
                f'"{game_name}"',
                f'{game_name} game',
                f'{game_name} update',
                f'{game_name} news',
                f'{game_name} release'
            ]

            articles = await self.search_gaming_news_comprehensive(
                keywords=game_keywords,
                max_articles=max_articles,
                days_back=30  # شهر كامل للألعاب المحددة
            )

            # فلترة إضافية للتأكد من الصلة
            filtered_articles = []
            for article in articles:
                title_content = f"{article.get('title', '')} {article.get('summary', '')}".lower()
                if game_name.lower() in title_content:
                    filtered_articles.append(article)

            logger.info(f"🎮 تم العثور على {len(filtered_articles)} مقال خاص بـ {game_name}")
            return filtered_articles

        except Exception as e:
            logger.error(f"❌ فشل في البحث عن أخبار {game_name}: {e}")
            return []

    async def get_trending_gaming_topics(self, days_back: int = 3) -> List[Dict]:
        """الحصول على المواضيع الرائجة في الألعاب"""
        try:
            logger.info("🔥 البحث عن المواضيع الرائجة في الألعاب...")

            trending_keywords = [
                'trending games', 'viral gaming', 'popular games',
                'gaming trends', 'hot gaming news', 'breaking gaming'
            ]

            articles = await self.search_gaming_news_comprehensive(
                keywords=trending_keywords,
                max_articles=30,
                days_back=days_back
            )

            # تحليل المواضيع الرائجة
            trending_topics = self._analyze_trending_topics(articles)

            logger.info(f"🔥 تم تحديد {len(trending_topics)} موضوع رائج")
            return trending_topics

        except Exception as e:
            logger.error(f"❌ فشل في تحليل المواضيع الرائجة: {e}")
            return []

    def _analyze_trending_topics(self, articles: List[Dict]) -> List[Dict]:
        """تحليل المواضيع الرائجة من المقالات"""
        try:
            topic_counts = {}

            for article in articles:
                title = article.get('title', '').lower()

                # استخراج أسماء الألعاب والمواضيع
                common_games = [
                    'minecraft', 'fortnite', 'call of duty', 'fifa', 'gta',
                    'cyberpunk', 'witcher', 'assassins creed', 'valorant',
                    'league of legends', 'overwatch', 'apex legends'
                ]

                for game in common_games:
                    if game in title:
                        topic_counts[game] = topic_counts.get(game, 0) + 1

                # مواضيع عامة
                general_topics = [
                    'update', 'release', 'trailer', 'review', 'dlc',
                    'beta', 'patch', 'announcement', 'esports', 'tournament'
                ]

                for topic in general_topics:
                    if topic in title:
                        topic_counts[topic] = topic_counts.get(topic, 0) + 1

            # ترتيب المواضيع حسب الشعبية
            sorted_topics = sorted(topic_counts.items(), key=lambda x: x[1], reverse=True)

            trending_topics = []
            for topic, count in sorted_topics[:10]:  # أفضل 10 مواضيع
                trending_topics.append({
                    'topic': topic,
                    'mention_count': count,
                    'trend_score': count * 10,  # نقاط الرواج
                    'category': 'game' if topic in ['minecraft', 'fortnite', 'call of duty'] else 'general'
                })

            return trending_topics

        except Exception as e:
            logger.error(f"❌ خطأ في تحليل المواضيع الرائجة: {e}")
            return []

# إنشاء كائن عام للوصول
advanced_news_apis = AdvancedNewsAPIs()
