#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاحات الاتصال والتأكد من عمل البوت بشكل صحيح
Test connection fixes and ensure bot works properly
"""

import os
import sys
import logging
import traceback
from datetime import datetime

# إعداد التسجيل
logging.basicConfig(
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    level=logging.INFO,
    datefmt="%Y-%m-%d %H:%M:%S",
)
logger = logging.getLogger(__name__)

def test_environment_variables():
    """اختبار متغيرات البيئة"""
    logger.info("🔍 اختبار متغيرات البيئة...")
    
    # تحميل متغيرات البيئة
    try:
        from dotenv import load_dotenv
        load_dotenv()
        logger.info("✅ تم تحميل ملف .env بنجاح")
    except ImportError:
        logger.warning("⚠️ python-dotenv غير مثبت")
        return False
    except Exception as e:
        logger.error(f"❌ فشل في تحميل ملف .env: {e}")
        return False
    
    # فحص المتغيرات المطلوبة
    required_vars = {
        'BOT_TOKEN': os.environ.get('BOT_TOKEN'),
        'SUPABASE_URL': os.environ.get('SUPABASE_URL'),
        'SUPABASE_KEY': os.environ.get('SUPABASE_KEY'),
        'ADMIN_CHAT_ID': os.environ.get('ADMIN_CHAT_ID')
    }
    
    all_present = True
    for var_name, var_value in required_vars.items():
        if var_value:
            logger.info(f"✅ {var_name}: موجود")
        else:
            logger.error(f"❌ {var_name}: مفقود")
            all_present = False
    
    return all_present

def test_imports():
    """اختبار الاستيرادات"""
    logger.info("🔍 اختبار الاستيرادات...")
    
    imports_to_test = [
        ('telegram', 'مكتبة python-telegram-bot'),
        ('supabase_client', 'عميل Supabase'),
        ('network_config', 'إعدادات الشبكة'),
        ('web_server', 'خادم الويب'),
        ('telegram_web_app', 'تطبيق تلجرام الويب')
    ]
    
    all_imports_ok = True
    for module_name, description in imports_to_test:
        try:
            __import__(module_name)
            logger.info(f"✅ {description}: متوفر")
        except ImportError as e:
            logger.error(f"❌ {description}: غير متوفر - {e}")
            all_imports_ok = False
        except Exception as e:
            logger.warning(f"⚠️ {description}: مشكلة في التحميل - {e}")
    
    return all_imports_ok

def test_network_connectivity():
    """اختبار الاتصال بالشبكة"""
    logger.info("🔍 اختبار الاتصال بالشبكة...")
    
    # اختبار DNS
    try:
        import socket
        socket.create_connection(("*******", 53), timeout=10)
        logger.info("✅ DNS متاح")
    except Exception as e:
        logger.error(f"❌ DNS غير متاح: {e}")
        return False
    
    # اختبار حل أسماء النطاقات
    try:
        socket.gethostbyname("api.telegram.org")
        logger.info("✅ حل أسماء النطاقات يعمل")
    except Exception as e:
        logger.error(f"❌ فشل في حل أسماء النطاقات: {e}")
        return False
    
    # اختبار الاتصال مع Telegram
    try:
        import requests
        response = requests.get("https://api.telegram.org", timeout=30)
        if response.status_code == 200:
            logger.info("✅ الاتصال مع Telegram API يعمل")
        else:
            logger.warning(f"⚠️ استجابة غير متوقعة من Telegram: {response.status_code}")
    except Exception as e:
        logger.error(f"❌ فشل في الاتصال مع Telegram: {e}")
        return False
    
    return True

def test_supabase_connection():
    """اختبار الاتصال مع Supabase"""
    logger.info("🔍 اختبار الاتصال مع Supabase...")
    
    try:
        import supabase_client
        if supabase_client.test_supabase_connection():
            logger.info("✅ الاتصال مع Supabase يعمل")
            return True
        else:
            logger.error("❌ فشل في الاتصال مع Supabase")
            return False
    except Exception as e:
        logger.error(f"❌ خطأ في اختبار Supabase: {e}")
        return False

def test_security_systems():
    """اختبار أنظمة الحماية"""
    logger.info("🔍 اختبار أنظمة الحماية...")
    
    try:
        # اختبار نظام الحماية الأساسي
        try:
            from security_config import security_check
            logger.info("✅ نظام الحماية الأساسي متوفر")
        except ImportError:
            logger.warning("⚠️ نظام الحماية الأساسي غير متوفر")
        
        # اختبار التحسينات الأمنية
        try:
            from security_enhancements import security_manager
            logger.info("✅ التحسينات الأمنية متوفرة")
        except ImportError:
            logger.warning("⚠️ التحسينات الأمنية غير متوفرة")
        
        return True
    except Exception as e:
        logger.error(f"❌ خطأ في اختبار أنظمة الحماية: {e}")
        return False

def test_file_structure():
    """اختبار هيكل الملفات"""
    logger.info("🔍 اختبار هيكل الملفات...")
    
    required_files = [
        'main.py',
        'supabase_client.py',
        'web_server.py',
        'telegram_web_app.py',
        'network_config.py',
        'requirements.txt',
        '.env.example'
    ]
    
    all_files_present = True
    for file_name in required_files:
        if os.path.exists(file_name):
            logger.info(f"✅ {file_name}: موجود")
        else:
            logger.error(f"❌ {file_name}: مفقود")
            all_files_present = False
    
    return all_files_present

def test_notifications_tables():
    """اختبار جداول الإشعارات"""
    logger.info("🔍 اختبار جداول الإشعارات...")
    
    try:
        import supabase_client
        result = supabase_client.create_notifications_tables()
        if result:
            logger.info("✅ جداول الإشعارات تعمل بشكل صحيح")
        else:
            logger.warning("⚠️ مشكلة في جداول الإشعارات، ولكن البوت سيعمل")
        return True
    except Exception as e:
        logger.warning(f"⚠️ خطأ في اختبار جداول الإشعارات: {e}")
        return True  # لا نريد إيقاف البوت بسبب هذا

def run_all_tests():
    """تشغيل جميع الاختبارات"""
    logger.info("🚀 بدء اختبار إصلاحات الاتصال...")
    logger.info("=" * 60)
    
    tests = [
        ("متغيرات البيئة", test_environment_variables),
        ("هيكل الملفات", test_file_structure),
        ("الاستيرادات", test_imports),
        ("الاتصال بالشبكة", test_network_connectivity),
        ("الاتصال مع Supabase", test_supabase_connection),
        ("أنظمة الحماية", test_security_systems),
        ("جداول الإشعارات", test_notifications_tables)
    ]
    
    results = {}
    for test_name, test_func in tests:
        try:
            logger.info(f"\n📋 اختبار: {test_name}")
            result = test_func()
            results[test_name] = result
            status = "✅ نجح" if result else "❌ فشل"
            logger.info(f"   النتيجة: {status}")
        except Exception as e:
            logger.error(f"   خطأ في الاختبار: {e}")
            logger.error(f"   التفاصيل: {traceback.format_exc()}")
            results[test_name] = False
    
    # عرض الملخص
    logger.info("\n" + "=" * 60)
    logger.info("📊 ملخص النتائج:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ نجح" if result else "❌ فشل"
        logger.info(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\nالنتيجة النهائية: {passed}/{total} اختبارات نجحت")
    
    if passed == total:
        logger.info("🎉 جميع الاختبارات نجحت! البوت جاهز للتشغيل.")
        return True
    elif passed >= total - 2:  # السماح بفشل اختبارين كحد أقصى
        logger.warning("⚠️ معظم الاختبارات نجحت. البوت قد يعمل مع بعض القيود.")
        return True
    else:
        logger.error("❌ فشل في عدة اختبارات. يرجى مراجعة الأخطاء أعلاه.")
        return False

def main():
    """الدالة الرئيسية"""
    print(f"🔧 اختبار إصلاحات الاتصال - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    success = run_all_tests()
    
    if success:
        print("\n🎉 الاختبارات مكتملة بنجاح!")
        print("📋 يمكنك الآن تشغيل البوت باستخدام:")
        print("   python main.py")
        sys.exit(0)
    else:
        print("\n❌ بعض الاختبارات فشلت.")
        print("🔧 يرجى مراجعة الأخطاء أعلاه وإصلاحها.")
        sys.exit(1)

if __name__ == "__main__":
    main()
