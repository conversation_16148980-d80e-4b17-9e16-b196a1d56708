@echo off
chcp 65001 >nul
title GitHub Uploader - العرض التوضيحي

echo ================================================
echo 🚀 عرض توضيحي لأداة GitHub Uploader
echo ================================================
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت على النظام
    echo يرجى تثبيت Python من: https://python.org
    pause
    exit /b 1
)

echo ✅ تم العثور على Python
echo.

REM تشغيل العرض التوضيحي
echo 🎯 تشغيل العرض التوضيحي...
echo.
python demo.py

echo.
echo ================================================
echo 📋 خطوات استخدام الأداة الفعلية:
echo ================================================
echo.
echo 1. احصل على GitHub Personal Access Token:
echo    https://github.com/settings/tokens
echo.
echo 2. شغل الأداة الرئيسية:
echo    python github_uploader.py
echo.
echo 3. أو استخدم ملف التشغيل:
echo    run_uploader.bat
echo.
echo 4. اتبع التعليمات لرفع مشاريعك
echo.
echo ================================================
echo 📚 مشاريع تجريبية تم إنشاؤها:
echo ================================================
echo.
echo - demo_projects/telegram_news_bot/
echo - demo_projects/discord_music_bot/
echo - demo_projects/news_scraper/
echo.
echo يمكنك استخدام هذه المشاريع لاختبار الأداة!
echo.

pause
