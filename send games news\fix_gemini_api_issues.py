#!/usr/bin/env python3
"""
إصلاح شامل لمشاكل Gemini API keys
"""

import sys
import os
import logging
import json
import requests
from pathlib import Path
from datetime import datetime

# إضافة مسار المشروع
sys.path.append(str(Path(__file__).parent))

# إعداد التسجيل
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_gemini_key(api_key: str) -> dict:
    """اختبار مفتاح Gemini API"""
    try:
        url = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-pro:generateContent"
        
        headers = {
            'Content-Type': 'application/json'
        }
        
        params = {
            'key': api_key
        }
        
        payload = {
            "contents": [{
                "parts": [{
                    "text": "Hello, test message"
                }]
            }],
            "generationConfig": {
                "temperature": 0.1,
                "maxOutputTokens": 50,
            }
        }
        
        response = requests.post(url, headers=headers, params=params, json=payload, timeout=30)
        
        if response.status_code == 200:
            return {"status": "valid", "message": "المفتاح يعمل بشكل صحيح"}
        elif response.status_code == 403:
            error_data = response.json()
            error_message = error_data.get('error', {}).get('message', 'غير محدد')
            
            if 'API_KEY_INVALID' in error_message or 'blacklisted' in error_message.lower():
                return {"status": "blacklisted", "message": f"المفتاح محظور: {error_message}"}
            elif 'PERMISSION_DENIED' in error_message:
                return {"status": "permission_denied", "message": f"رفض الإذن: {error_message}"}
            else:
                return {"status": "error", "message": f"خطأ 403: {error_message}"}
        elif response.status_code == 429:
            return {"status": "rate_limited", "message": "تم تجاوز حد الاستخدام"}
        else:
            return {"status": "error", "message": f"خطأ HTTP {response.status_code}"}
            
    except Exception as e:
        return {"status": "error", "message": f"خطأ في الاتصال: {str(e)}"}

def get_gemini_keys_from_config():
    """استخراج مفاتيح Gemini من ملف الإعدادات"""
    try:
        from config.settings import google_api_manager, BotConfig
        
        keys = []
        
        # من مدير المفاتيح
        if google_api_manager:
            keys.extend(google_api_manager.keys)
        
        # من المتغيرات المباشرة
        if hasattr(BotConfig, 'GEMINI_API_KEY') and BotConfig.GEMINI_API_KEY:
            if BotConfig.GEMINI_API_KEY not in keys:
                keys.append(BotConfig.GEMINI_API_KEY)
        
        # من متغيرات البيئة
        env_key = os.getenv('GEMINI_API_KEY')
        if env_key and env_key not in keys:
            keys.append(env_key)
        
        return keys
        
    except Exception as e:
        logger.error(f"❌ فشل في استخراج المفاتيح من الإعدادات: {e}")
        return []

def add_new_gemini_keys():
    """إضافة مفاتيح Gemini جديدة"""
    new_keys = [
        # مفاتيح جديدة للاختبار (يجب استبدالها بمفاتيح حقيقية)
        "AIzaSyDjjgZIkGyC0i8RVM9IA14WFJBqmEQHhHE",  # المفتاح الجديد المذكور
        # يمكن إضافة المزيد هنا
    ]
    
    logger.info(f"🔑 إضافة {len(new_keys)} مفتاح جديد للاختبار...")
    
    valid_keys = []
    blacklisted_keys = []
    
    for key in new_keys:
        logger.info(f"🧪 اختبار المفتاح: {key[:10]}...")
        result = test_gemini_key(key)
        
        if result["status"] == "valid":
            valid_keys.append(key)
            logger.info(f"✅ المفتاح صالح: {key[:10]}...")
        elif result["status"] == "blacklisted":
            blacklisted_keys.append(key)
            logger.warning(f"🚫 المفتاح محظور: {key[:10]}... - {result['message']}")
        else:
            logger.error(f"❌ المفتاح غير صالح: {key[:10]}... - {result['message']}")
    
    return valid_keys, blacklisted_keys

def update_api_key_manager():
    """تحديث مدير مفاتيح API"""
    try:
        # قراءة ملف الإعدادات
        config_path = Path("config/settings.py")
        if not config_path.exists():
            logger.error("❌ ملف الإعدادات غير موجود")
            return False
        
        with open(config_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # إضافة تحسينات لمدير المفاتيح
        improvements = '''
# تحسينات مدير مفاتيح Gemini API
def create_enhanced_google_api_manager():
    """إنشاء مدير مفاتيح محسن مع معالجة أفضل للأخطاء"""
    try:
        # مفاتيح إضافية للاحتياط
        additional_keys = [
            "AIzaSyDjjgZIkGyC0i8RVM9IA14WFJBqmEQHhHE",  # المفتاح الجديد
            # يمكن إضافة المزيد هنا
        ]
        
        # دمج المفاتيح
        all_keys = google_api_keys.copy()
        for key in additional_keys:
            if key and key not in all_keys:
                all_keys.append(key)
        
        if all_keys:
            enhanced_manager = ApiKeyManager(
                api_keys=all_keys, 
                service_name="Google Enhanced",
                auto_recovery_minutes=30,  # استرداد أسرع
                load_balancing=True
            )
            return enhanced_manager
        else:
            return None
            
    except Exception as e:
        print(f"❌ خطأ في إنشاء مدير المفاتيح المحسن: {e}")
        return None

# استبدال المدير الحالي بالمحسن
if google_api_keys:
    google_api_manager_enhanced = create_enhanced_google_api_manager()
    if google_api_manager_enhanced:
        google_api_manager = google_api_manager_enhanced
'''
        
        # إضافة التحسينات إذا لم تكن موجودة
        if "create_enhanced_google_api_manager" not in content:
            # البحث عن مكان مناسب للإضافة
            if "google_api_manager = ApiKeyManager" in content:
                content = content.replace(
                    "google_api_manager = ApiKeyManager",
                    improvements + "\n# المدير الأصلي (سيتم استبداله)\n# google_api_manager = ApiKeyManager"
                )
            else:
                content += improvements
        
        # حفظ الملف
        with open(config_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        logger.info("✅ تم تحديث مدير مفاتيح API")
        return True
        
    except Exception as e:
        logger.error(f"❌ فشل في تحديث مدير المفاتيح: {e}")
        return False

def create_gemini_error_handler():
    """إنشاء معالج أخطاء محسن لـ Gemini"""
    handler_code = '''#!/usr/bin/env python3
"""
معالج أخطاء محسن لـ Gemini API
"""

import logging
from typing import Optional, Dict, Any
from modules.logger import logger

class GeminiErrorHandler:
    """معالج أخطاء متقدم لـ Gemini API"""
    
    def __init__(self, api_manager):
        self.api_manager = api_manager
        self.error_counts = {}
        self.max_retries = 3
    
    def handle_api_error(self, error: Exception, current_key: str) -> Optional[str]:
        """معالجة أخطاء API وإرجاع مفتاح بديل"""
        try:
            error_str = str(error).lower()
            
            # تحديد نوع الخطأ
            if any(keyword in error_str for keyword in ['permission_denied', 'blacklisted', 'invalid']):
                logger.warning(f"🚫 مفتاح محظور أو غير صالح: {current_key[:10]}...")
                
                # وضع المفتاح في القائمة السوداء
                if hasattr(self.api_manager, 'mark_key_failed'):
                    self.api_manager.mark_key_failed(current_key)
                else:
                    # استخدام rotate_key كبديل
                    self.api_manager.rotate_key()
                
                # الحصول على مفتاح جديد
                try:
                    new_key = self.api_manager.get_key()
                    logger.info(f"🔄 التبديل إلى مفتاح جديد: {new_key[:10]}...")
                    return new_key
                except Exception as e:
                    logger.error(f"❌ فشل في الحصول على مفتاح بديل: {e}")
                    return None
            
            elif 'rate_limit' in error_str or '429' in error_str:
                logger.warning(f"⏰ تم تجاوز حد الاستخدام للمفتاح: {current_key[:10]}...")
                
                # تأخير مؤقت للمفتاح
                if hasattr(self.api_manager, 'mark_key_failed'):
                    self.api_manager.mark_key_failed(current_key)
                
                # محاولة مفتاح آخر
                try:
                    new_key = self.api_manager.get_key()
                    return new_key
                except Exception:
                    return None
            
            else:
                logger.warning(f"⚠️ خطأ غير معروف في Gemini API: {error}")
                return current_key  # الاحتفاظ بنفس المفتاح
                
        except Exception as e:
            logger.error(f"❌ خطأ في معالج الأخطاء: {e}")
            return None

# إنشاء مثيل عام
gemini_error_handler = None

def get_gemini_error_handler():
    """الحصول على معالج الأخطاء"""
    global gemini_error_handler
    
    if gemini_error_handler is None:
        try:
            from config.settings import google_api_manager
            if google_api_manager:
                gemini_error_handler = GeminiErrorHandler(google_api_manager)
        except Exception as e:
            logger.error(f"❌ فشل في إنشاء معالج أخطاء Gemini: {e}")
    
    return gemini_error_handler
'''
    
    try:
        with open("modules/gemini_error_handler.py", 'w', encoding='utf-8') as f:
            f.write(handler_code)
        logger.info("✅ تم إنشاء معالج أخطاء Gemini")
        return True
    except Exception as e:
        logger.error(f"❌ فشل في إنشاء معالج الأخطاء: {e}")
        return False

def main():
    """الدالة الرئيسية لإصلاح مشاكل Gemini API"""
    logger.info("🚀 بدء إصلاح مشاكل Gemini API keys...")
    
    success_count = 0
    total_steps = 5
    
    # 1. اختبار المفاتيح الحالية
    logger.info("\\n🧪 الخطوة 1: اختبار المفاتيح الحالية...")
    try:
        current_keys = get_gemini_keys_from_config()
        logger.info(f"📋 تم العثور على {len(current_keys)} مفتاح في الإعدادات")
        
        valid_count = 0
        for key in current_keys:
            result = test_gemini_key(key)
            if result["status"] == "valid":
                valid_count += 1
                logger.info(f"✅ مفتاح صالح: {key[:10]}...")
            else:
                logger.warning(f"❌ مفتاح غير صالح: {key[:10]}... - {result['message']}")
        
        logger.info(f"📊 النتيجة: {valid_count}/{len(current_keys)} مفتاح صالح")
        if valid_count > 0:
            success_count += 1
            
    except Exception as e:
        logger.error(f"❌ فشل في اختبار المفاتيح الحالية: {e}")
    
    # 2. إضافة مفاتيح جديدة
    logger.info("\\n🔑 الخطوة 2: إضافة مفاتيح جديدة...")
    try:
        valid_keys, blacklisted_keys = add_new_gemini_keys()
        if valid_keys:
            logger.info(f"✅ تم العثور على {len(valid_keys)} مفتاح جديد صالح")
            success_count += 1
        else:
            logger.warning("⚠️ لم يتم العثور على مفاتيح جديدة صالحة")
    except Exception as e:
        logger.error(f"❌ فشل في إضافة مفاتيح جديدة: {e}")
    
    # 3. تحديث مدير المفاتيح
    logger.info("\\n⚙️ الخطوة 3: تحديث مدير مفاتيح API...")
    if update_api_key_manager():
        success_count += 1
    
    # 4. إنشاء معالج الأخطاء
    logger.info("\\n🛠️ الخطوة 4: إنشاء معالج أخطاء Gemini...")
    if create_gemini_error_handler():
        success_count += 1
    
    # 5. إنشاء تقرير الحالة
    logger.info("\\n📊 الخطوة 5: إنشاء تقرير الحالة...")
    try:
        report = {
            "timestamp": datetime.now().isoformat(),
            "total_keys_tested": len(current_keys) if 'current_keys' in locals() else 0,
            "valid_keys": valid_count if 'valid_count' in locals() else 0,
            "new_keys_added": len(valid_keys) if 'valid_keys' in locals() else 0,
            "blacklisted_keys": len(blacklisted_keys) if 'blacklisted_keys' in locals() else 0,
            "fixes_applied": success_count
        }
        
        with open("gemini_api_status_report.json", 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        logger.info("✅ تم إنشاء تقرير الحالة")
        success_count += 1
        
    except Exception as e:
        logger.error(f"❌ فشل في إنشاء التقرير: {e}")
    
    # النتيجة النهائية
    logger.info(f"\\n🎯 اكتمل الإصلاح: {success_count}/{total_steps} خطوات نجحت")
    
    if success_count >= 3:
        logger.info("✅ تم إصلاح معظم مشاكل Gemini API بنجاح!")
        logger.info("🔄 يرجى إعادة تشغيل الوكيل لتطبيق التحديثات")
    else:
        logger.warning(f"⚠️ تم إصلاح {success_count} من أصل {total_steps} مشاكل")
        logger.info("📋 يرجى مراجعة الأخطاء أعلاه وإصلاحها يدوياً")

if __name__ == "__main__":
    main()
