# مدير استهلاك API المتقدم - حماية من الاستهلاك المفرط
import asyncio
import time
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
import aiofiles
import hashlib

from .logger import logger

class APIProvider(Enum):
    """مقدمي خدمات API"""
    TAVILY = "tavily"
    SERPAPI = "serpapi"
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    GOOGLE = "google"
    BING = "bing"
    LOCAL = "local"

class UsagePriority(Enum):
    """أولوية الاستخدام"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4

@dataclass
class APILimits:
    """حدود API"""
    daily_limit: int
    hourly_limit: int
    minute_limit: int
    cost_per_request: float
    max_daily_cost: float
    reset_time: str  # UTC time when limits reset

@dataclass
class APIUsage:
    """استخدام API"""
    provider: APIProvider
    requests_today: int
    requests_this_hour: int
    requests_this_minute: int
    cost_today: float
    last_request_time: datetime
    consecutive_failures: int
    is_available: bool

@dataclass
class CacheEntry:
    """مدخل التخزين المؤقت"""
    query_hash: str
    response: Any
    timestamp: datetime
    ttl: int  # seconds
    provider: APIProvider
    hit_count: int

class APIUsageManager:
    """مدير استهلاك API المتقدم"""
    
    def __init__(self):
        # إعدادات الحماية
        self.protection_config = {
            'enable_caching': True,
            'cache_ttl_default': 3600,  # ساعة واحدة
            'cache_ttl_news': 1800,     # 30 دقيقة للأخبار
            'cache_ttl_reviews': 7200,  # ساعتين للمراجعات
            'max_cache_size': 10000,
            'enable_rate_limiting': True,
            'enable_cost_monitoring': True,
            'enable_fallback_cascade': True,
            'max_consecutive_failures': 3,
            'failure_cooldown_minutes': 15
        }
        
        # حدود API لكل مقدم خدمة
        self.api_limits = {
            APIProvider.TAVILY: APILimits(
                daily_limit=35,
                hourly_limit=10,
                minute_limit=2,
                cost_per_request=0.001,
                max_daily_cost=0.035,
                reset_time="00:00"
            ),
            APIProvider.SERPAPI: APILimits(
                daily_limit=100,
                hourly_limit=25,
                minute_limit=5,
                cost_per_request=0.01,
                max_daily_cost=1.0,
                reset_time="00:00"
            ),
            APIProvider.OPENAI: APILimits(
                daily_limit=1000,
                hourly_limit=100,
                minute_limit=10,
                cost_per_request=0.002,
                max_daily_cost=2.0,
                reset_time="00:00"
            ),
            APIProvider.ANTHROPIC: APILimits(
                daily_limit=500,
                hourly_limit=50,
                minute_limit=5,
                cost_per_request=0.003,
                max_daily_cost=1.5,
                reset_time="00:00"
            ),
            APIProvider.LOCAL: APILimits(
                daily_limit=10000,
                hourly_limit=1000,
                minute_limit=100,
                cost_per_request=0.0,
                max_daily_cost=0.0,
                reset_time="00:00"
            )
        }
        
        # تتبع الاستخدام الحالي
        self.current_usage = {
            provider: APIUsage(
                provider=provider,
                requests_today=0,
                requests_this_hour=0,
                requests_this_minute=0,
                cost_today=0.0,
                last_request_time=datetime.now(),
                consecutive_failures=0,
                is_available=True
            ) for provider in APIProvider
        }
        
        # التخزين المؤقت
        self.cache = {}
        self.cache_stats = {
            'total_requests': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'cache_saves': 0
        }
        
        # إحصائيات الحماية
        self.protection_stats = {
            'blocked_requests': 0,
            'cost_savings': 0.0,
            'fallback_uses': 0,
            'rate_limit_hits': 0
        }
        
        # ترتيب الأولوية للمقدمين
        self.provider_priority = [
            APIProvider.LOCAL,      # أولوية عالية - مجاني
            APIProvider.TAVILY,     # أولوية متوسطة - رخيص
            APIProvider.SERPAPI,    # أولوية منخفضة - مكلف
            APIProvider.OPENAI,     # للذكاء الاصطناعي فقط
            APIProvider.ANTHROPIC   # للذكاء الاصطناعي فقط
        ]
        
        logger.info("🛡️ تم تهيئة مدير استهلاك API المتقدم")
    
    async def can_make_request(self, provider: APIProvider, priority: UsagePriority = UsagePriority.NORMAL) -> Tuple[bool, str]:
        """فحص إمكانية إجراء طلب API"""
        try:
            usage = self.current_usage[provider]
            limits = self.api_limits[provider]
            
            # فحص التوفر العام
            if not usage.is_available:
                return False, f"المقدم {provider.value} غير متوفر حالياً"
            
            # فحص الفشل المتتالي
            if usage.consecutive_failures >= self.protection_config['max_consecutive_failures']:
                cooldown_end = usage.last_request_time + timedelta(minutes=self.protection_config['failure_cooldown_minutes'])
                if datetime.now() < cooldown_end:
                    return False, f"المقدم {provider.value} في فترة تهدئة"
            
            # فحص الحدود اليومية
            if usage.requests_today >= limits.daily_limit:
                return False, f"تم تجاوز الحد اليومي للمقدم {provider.value}"
            
            # فحص الحدود بالساعة
            if usage.requests_this_hour >= limits.hourly_limit:
                return False, f"تم تجاوز الحد بالساعة للمقدم {provider.value}"
            
            # فحص الحدود بالدقيقة
            if usage.requests_this_minute >= limits.minute_limit:
                return False, f"تم تجاوز الحد بالدقيقة للمقدم {provider.value}"
            
            # فحص التكلفة اليومية
            if usage.cost_today >= limits.max_daily_cost:
                return False, f"تم تجاوز الحد المالي اليومي للمقدم {provider.value}"
            
            # فحص الأولوية
            if priority == UsagePriority.LOW and usage.requests_today > limits.daily_limit * 0.8:
                return False, f"الأولوية منخفضة والاستخدام عالي للمقدم {provider.value}"
            
            return True, "يمكن إجراء الطلب"
            
        except Exception as e:
            logger.error(f"❌ فشل في فحص إمكانية الطلب: {e}")
            return False, f"خطأ في فحص الطلب: {e}"
    
    async def get_cached_response(self, query: str, provider: APIProvider = None) -> Optional[Any]:
        """البحث في التخزين المؤقت"""
        if not self.protection_config['enable_caching']:
            return None
        
        try:
            query_hash = self._generate_query_hash(query, provider)
            
            if query_hash in self.cache:
                entry = self.cache[query_hash]
                
                # فحص انتهاء الصلاحية
                if datetime.now() - entry.timestamp < timedelta(seconds=entry.ttl):
                    entry.hit_count += 1
                    self.cache_stats['cache_hits'] += 1
                    
                    logger.debug(f"💾 استخدام التخزين المؤقت للاستعلام: {query[:50]}...")
                    return entry.response
                else:
                    # إزالة المدخل المنتهي الصلاحية
                    del self.cache[query_hash]
            
            self.cache_stats['cache_misses'] += 1
            return None
            
        except Exception as e:
            logger.error(f"❌ فشل في البحث في التخزين المؤقت: {e}")
            return None
    
    async def cache_response(self, query: str, response: Any, provider: APIProvider, query_type: str = "general"):
        """حفظ الاستجابة في التخزين المؤقت"""
        if not self.protection_config['enable_caching'] or not response:
            return
        
        try:
            # تحديد TTL بناءً على نوع الاستعلام
            ttl = self._get_cache_ttl(query_type)
            
            query_hash = self._generate_query_hash(query, provider)
            
            # إزالة مدخلات قديمة إذا امتلأ التخزين المؤقت
            if len(self.cache) >= self.protection_config['max_cache_size']:
                await self._cleanup_cache()
            
            # حفظ المدخل الجديد
            self.cache[query_hash] = CacheEntry(
                query_hash=query_hash,
                response=response,
                timestamp=datetime.now(),
                ttl=ttl,
                provider=provider,
                hit_count=0
            )
            
            self.cache_stats['cache_saves'] += 1
            logger.debug(f"💾 تم حفظ الاستجابة في التخزين المؤقت: {query[:50]}...")
            
        except Exception as e:
            logger.error(f"❌ فشل في حفظ التخزين المؤقت: {e}")
    
    async def record_api_usage(self, provider: APIProvider, success: bool, cost: float = None):
        """تسجيل استخدام API"""
        try:
            usage = self.current_usage[provider]
            limits = self.api_limits[provider]
            
            # تحديث العدادات
            usage.requests_today += 1
            usage.requests_this_hour += 1
            usage.requests_this_minute += 1
            usage.last_request_time = datetime.now()
            
            # تحديث التكلفة
            if cost is not None:
                usage.cost_today += cost
            else:
                usage.cost_today += limits.cost_per_request
            
            # تحديث حالة النجاح/الفشل
            if success:
                usage.consecutive_failures = 0
                usage.is_available = True
            else:
                usage.consecutive_failures += 1
                if usage.consecutive_failures >= self.protection_config['max_consecutive_failures']:
                    usage.is_available = False
                    logger.warning(f"⚠️ تم تعطيل المقدم {provider.value} مؤقتاً بسبب الفشل المتتالي")
            
            # إعادة تعيين العدادات عند الحاجة
            await self._reset_counters_if_needed(provider)
            
        except Exception as e:
            logger.error(f"❌ فشل في تسجيل استخدام API: {e}")
    
    async def get_best_available_provider(self, query_type: str = "search", priority: UsagePriority = UsagePriority.NORMAL) -> Optional[APIProvider]:
        """الحصول على أفضل مقدم متاح"""
        try:
            # ترتيب المقدمين حسب الأولوية والتوفر
            available_providers = []
            
            for provider in self.provider_priority:
                # تخطي مقدمي الذكاء الاصطناعي للبحث العادي
                if query_type == "search" and provider in [APIProvider.OPENAI, APIProvider.ANTHROPIC]:
                    continue
                
                can_use, reason = await self.can_make_request(provider, priority)
                if can_use:
                    # حساب نقاط الأولوية
                    score = self._calculate_provider_score(provider, priority)
                    available_providers.append((provider, score))
            
            if available_providers:
                # ترتيب حسب النقاط
                available_providers.sort(key=lambda x: x[1], reverse=True)
                best_provider = available_providers[0][0]
                
                logger.debug(f"🎯 أفضل مقدم متاح: {best_provider.value}")
                return best_provider
            
            logger.warning("⚠️ لا يوجد مقدم API متاح حالياً")
            return None
            
        except Exception as e:
            logger.error(f"❌ فشل في اختيار المقدم: {e}")
            return None
    
    def _calculate_provider_score(self, provider: APIProvider, priority: UsagePriority) -> float:
        """حساب نقاط المقدم"""
        usage = self.current_usage[provider]
        limits = self.api_limits[provider]
        
        score = 100.0
        
        # تقليل النقاط بناءً على الاستخدام
        daily_usage_ratio = usage.requests_today / limits.daily_limit
        score -= daily_usage_ratio * 30
        
        # تقليل النقاط بناءً على التكلفة
        cost_ratio = usage.cost_today / limits.max_daily_cost if limits.max_daily_cost > 0 else 0
        score -= cost_ratio * 20
        
        # تقليل النقاط بناءً على الفشل
        score -= usage.consecutive_failures * 10
        
        # زيادة النقاط للمقدمين المجانيين
        if limits.cost_per_request == 0:
            score += 50
        
        # تعديل بناءً على الأولوية
        if priority == UsagePriority.CRITICAL:
            score += 20
        elif priority == UsagePriority.LOW:
            score -= 10
        
        return max(score, 0)
    
    def _generate_query_hash(self, query: str, provider: APIProvider = None) -> str:
        """إنشاء hash للاستعلام"""
        content = f"{query.lower().strip()}"
        if provider:
            content += f"_{provider.value}"
        
        return hashlib.md5(content.encode()).hexdigest()
    
    def _get_cache_ttl(self, query_type: str) -> int:
        """تحديد TTL للتخزين المؤقت"""
        ttl_mapping = {
            'news': self.protection_config['cache_ttl_news'],
            'reviews': self.protection_config['cache_ttl_reviews'],
            'guides': self.protection_config['cache_ttl_default'] * 2,
            'general': self.protection_config['cache_ttl_default']
        }
        
        return ttl_mapping.get(query_type, self.protection_config['cache_ttl_default'])
    
    async def _cleanup_cache(self):
        """تنظيف التخزين المؤقت"""
        try:
            # إزالة المدخلات المنتهية الصلاحية
            expired_keys = []
            now = datetime.now()
            
            for key, entry in self.cache.items():
                if now - entry.timestamp >= timedelta(seconds=entry.ttl):
                    expired_keys.append(key)
            
            for key in expired_keys:
                del self.cache[key]
            
            # إزالة المدخلات الأقل استخداماً إذا لا يزال التخزين ممتلئاً
            if len(self.cache) >= self.protection_config['max_cache_size']:
                # ترتيب حسب عدد الاستخدامات
                sorted_entries = sorted(self.cache.items(), key=lambda x: x[1].hit_count)
                
                # إزالة 20% من المدخلات الأقل استخداماً
                remove_count = int(len(sorted_entries) * 0.2)
                for i in range(remove_count):
                    key = sorted_entries[i][0]
                    del self.cache[key]
            
            logger.debug(f"🧹 تم تنظيف التخزين المؤقت: {len(self.cache)} مدخل متبقي")
            
        except Exception as e:
            logger.error(f"❌ فشل في تنظيف التخزين المؤقت: {e}")
    
    async def _reset_counters_if_needed(self, provider: APIProvider):
        """إعادة تعيين العدادات عند الحاجة"""
        try:
            usage = self.current_usage[provider]
            now = datetime.now()
            
            # إعادة تعيين العداد اليومي
            if now.date() > usage.last_request_time.date():
                usage.requests_today = 0
                usage.cost_today = 0.0
                usage.consecutive_failures = 0
                usage.is_available = True
            
            # إعادة تعيين العداد بالساعة
            if now.hour != usage.last_request_time.hour:
                usage.requests_this_hour = 0
            
            # إعادة تعيين العداد بالدقيقة
            if now.minute != usage.last_request_time.minute:
                usage.requests_this_minute = 0
            
        except Exception as e:
            logger.error(f"❌ فشل في إعادة تعيين العدادات: {e}")
    
    def get_usage_report(self) -> Dict[str, Any]:
        """تقرير شامل عن الاستخدام"""
        report = {
            'timestamp': datetime.now().isoformat(),
            'providers': {},
            'cache_stats': self.cache_stats.copy(),
            'protection_stats': self.protection_stats.copy(),
            'recommendations': []
        }
        
        total_requests = 0
        total_cost = 0.0
        
        for provider, usage in self.current_usage.items():
            limits = self.api_limits[provider]
            
            provider_report = {
                'requests_today': usage.requests_today,
                'daily_limit': limits.daily_limit,
                'usage_percentage': (usage.requests_today / limits.daily_limit * 100) if limits.daily_limit > 0 else 0,
                'cost_today': usage.cost_today,
                'max_daily_cost': limits.max_daily_cost,
                'cost_percentage': (usage.cost_today / limits.max_daily_cost * 100) if limits.max_daily_cost > 0 else 0,
                'is_available': usage.is_available,
                'consecutive_failures': usage.consecutive_failures
            }
            
            report['providers'][provider.value] = provider_report
            total_requests += usage.requests_today
            total_cost += usage.cost_today
        
        # إضافة الإحصائيات الإجمالية
        report['totals'] = {
            'total_requests': total_requests,
            'total_cost': total_cost,
            'cache_hit_rate': (self.cache_stats['cache_hits'] / max(self.cache_stats['total_requests'], 1)) * 100
        }
        
        # إضافة التوصيات
        if total_cost > 5.0:
            report['recommendations'].append("التكلفة اليومية عالية - فعل التخزين المؤقت")
        
        if self.cache_stats['cache_hits'] / max(self.cache_stats['total_requests'], 1) < 0.3:
            report['recommendations'].append("معدل استخدام التخزين المؤقت منخفض - راجع إعدادات TTL")
        
        return report
    
    async def optimize_usage(self):
        """تحسين الاستخدام تلقائياً"""
        try:
            logger.info("🔧 بدء تحسين استخدام API")
            
            # تنظيف التخزين المؤقت
            await self._cleanup_cache()
            
            # إعادة تفعيل المقدمين المعطلين إذا انتهت فترة التهدئة
            for provider, usage in self.current_usage.items():
                if not usage.is_available:
                    cooldown_end = usage.last_request_time + timedelta(minutes=self.protection_config['failure_cooldown_minutes'])
                    if datetime.now() >= cooldown_end:
                        usage.is_available = True
                        usage.consecutive_failures = 0
                        logger.info(f"🟢 تم إعادة تفعيل المقدم {provider.value}")
            
            # تحسين إعدادات التخزين المؤقت بناءً على الاستخدام
            hit_rate = self.cache_stats['cache_hits'] / max(self.cache_stats['total_requests'], 1)
            
            if hit_rate < 0.3:
                # زيادة TTL للتخزين المؤقت
                self.protection_config['cache_ttl_default'] = min(7200, self.protection_config['cache_ttl_default'] * 1.2)
                logger.info(f"📈 تم زيادة TTL للتخزين المؤقت إلى {self.protection_config['cache_ttl_default']} ثانية")
            
            elif hit_rate > 0.8:
                # تقليل TTL للحصول على بيانات أحدث
                self.protection_config['cache_ttl_default'] = max(1800, self.protection_config['cache_ttl_default'] * 0.9)
                logger.info(f"📉 تم تقليل TTL للتخزين المؤقت إلى {self.protection_config['cache_ttl_default']} ثانية")
            
            logger.info("✅ تم تحسين استخدام API")
            
        except Exception as e:
            logger.error(f"❌ فشل في تحسين الاستخدام: {e}")

# إنشاء مثيل عام
api_usage_manager = APIUsageManager()

# دوال مساعدة
async def can_use_api(provider: APIProvider, priority: UsagePriority = UsagePriority.NORMAL) -> bool:
    """فحص إمكانية استخدام API"""
    can_use, _ = await api_usage_manager.can_make_request(provider, priority)
    return can_use

async def get_cached_result(query: str, provider: APIProvider = None) -> Optional[Any]:
    """البحث في التخزين المؤقت"""
    return await api_usage_manager.get_cached_response(query, provider)

async def cache_result(query: str, response: Any, provider: APIProvider, query_type: str = "general"):
    """حفظ في التخزين المؤقت"""
    await api_usage_manager.cache_response(query, response, provider, query_type)

async def record_usage(provider: APIProvider, success: bool, cost: float = None):
    """تسجيل الاستخدام"""
    await api_usage_manager.record_api_usage(provider, success, cost)
