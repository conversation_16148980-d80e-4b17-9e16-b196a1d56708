#!/usr/bin/env python3
"""
تشغيل بوت Telegram التفاعلي مع الأزرار
يوفر واجهة كاملة للتحكم في النظام
"""

import asyncio
import logging
import sys
from pathlib import Path

# إعداد السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/telegram_bot.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

async def main():
    """الدالة الرئيسية لتشغيل البوت التفاعلي"""
    try:
        print("🤖 بدء تشغيل بوت Telegram التفاعلي...")
        
        # استيراد البوت
        from automation.telegram_bot import EnhancedTelegramBot
        
        # إنشاء البوت
        bot = EnhancedTelegramBot()
        
        # تشغيل البوت التفاعلي
        await bot.start_interactive_bot()
        
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف البوت بواسطة المستخدم")
    except Exception as e:
        logger.error(f"❌ خطأ في تشغيل البوت: {e}")
        print(f"❌ خطأ: {e}")
        sys.exit(1)

if __name__ == "__main__":
    # التأكد من وجود مجلد logs
    Path("logs").mkdir(exist_ok=True)
    
    # تشغيل البوت
    asyncio.run(main())
