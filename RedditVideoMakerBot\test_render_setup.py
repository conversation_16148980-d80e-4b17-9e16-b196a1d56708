#!/usr/bin/env python3
"""
اختبار إعداد Render
يتحقق من جميع المتطلبات قبل النشر
"""

import os
import sys
import logging
import json
from pathlib import Path
from datetime import datetime

# إعداد السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger(__name__)

def test_environment_variables():
    """اختبار متغيرات البيئة"""
    logger.info("🔍 اختبار متغيرات البيئة...")
    
    # متغيرات مطلوبة
    required_vars = {
        "REDDIT_CLIENT_ID": "معرف تطبيق Reddit",
        "REDDIT_CLIENT_SECRET": "سر تطبيق Reddit",
        "REDDIT_USERNAME": "اسم مستخدم Reddit", 
        "REDDIT_PASSWORD": "كلمة مرور Reddit"
    }
    
    # متغيرات اختيارية
    optional_vars = {
        "TELEGRAM_BOT_TOKEN": "رمز بوت Telegram",
        "TELEGRAM_CHAT_ID": "معرف محادثة Telegram",
        "GEMINI_API_KEY": "مفتاح Gemini AI",
        "ELEVENLABS_API_KEY": "مفتاح ElevenLabs"
    }
    
    results = {
        "required": {},
        "optional": {},
        "system": {}
    }
    
    # فحص المتغيرات المطلوبة
    for var, desc in required_vars.items():
        value = os.getenv(var)
        if value and not value.startswith("YOUR_"):
            results["required"][var] = "✅ موجود"
            logger.info(f"✅ {desc}: موجود")
        else:
            results["required"][var] = "❌ مفقود"
            logger.error(f"❌ {desc}: مفقود")
    
    # فحص المتغيرات الاختيارية
    for var, desc in optional_vars.items():
        value = os.getenv(var)
        if value and not value.startswith("YOUR_"):
            results["optional"][var] = "✅ موجود"
            logger.info(f"✅ {desc}: موجود")
        else:
            results["optional"][var] = "⚠️ مفقود (اختياري)"
            logger.warning(f"⚠️ {desc}: مفقود (اختياري)")
    
    # فحص متغيرات النظام
    system_vars = ["PYTHONUNBUFFERED", "PYTHONIOENCODING", "PORT"]
    for var in system_vars:
        value = os.getenv(var)
        if value:
            results["system"][var] = f"✅ {value}"
            logger.info(f"✅ {var}: {value}")
        else:
            results["system"][var] = "⚠️ غير مضبوط"
            logger.warning(f"⚠️ {var}: غير مضبوط")
    
    return results

def test_file_structure():
    """اختبار هيكل الملفات"""
    logger.info("📁 اختبار هيكل الملفات...")
    
    required_files = [
        "main.py",
        "start_render.py", 
        "web_server.py",
        "config_render.py",
        "requirements.txt",
        "Procfile",
        "render.yaml"
    ]
    
    optional_files = [
        "config.toml",
        "client_secret.json",
        "service_account.json"
    ]
    
    results = {"required": {}, "optional": {}}
    
    # فحص الملفات المطلوبة
    for file_name in required_files:
        file_path = Path(file_name)
        if file_path.exists():
            size = file_path.stat().st_size
            results["required"][file_name] = f"✅ موجود ({size} bytes)"
            logger.info(f"✅ {file_name}: موجود ({size} bytes)")
        else:
            results["required"][file_name] = "❌ مفقود"
            logger.error(f"❌ {file_name}: مفقود")
    
    # فحص الملفات الاختيارية
    for file_name in optional_files:
        file_path = Path(file_name)
        if file_path.exists():
            size = file_path.stat().st_size
            results["optional"][file_name] = f"✅ موجود ({size} bytes)"
            logger.info(f"✅ {file_name}: موجود ({size} bytes)")
        else:
            results["optional"][file_name] = "⚠️ مفقود (اختياري)"
            logger.warning(f"⚠️ {file_name}: مفقود (اختياري)")
    
    return results

def test_directories():
    """اختبار المجلدات"""
    logger.info("📂 اختبار المجلدات...")
    
    required_dirs = [
        "logs",
        "results",
        "assets",
        "assets/temp",
        "assets/backgrounds"
    ]
    
    results = {}
    
    for dir_name in required_dirs:
        dir_path = Path(dir_name)
        if dir_path.exists() and dir_path.is_dir():
            results[dir_name] = "✅ موجود"
            logger.info(f"✅ {dir_name}: موجود")
        else:
            # إنشاء المجلد إذا لم يكن موجوداً
            try:
                dir_path.mkdir(parents=True, exist_ok=True)
                results[dir_name] = "✅ تم إنشاؤه"
                logger.info(f"✅ {dir_name}: تم إنشاؤه")
            except Exception as e:
                results[dir_name] = f"❌ خطأ: {e}"
                logger.error(f"❌ {dir_name}: خطأ في الإنشاء - {e}")
    
    return results

def test_imports():
    """اختبار استيراد المكتبات"""
    logger.info("📦 اختبار استيراد المكتبات...")
    
    required_modules = [
        "flask",
        "praw", 
        "requests",
        "toml",
        "rich"
    ]
    
    optional_modules = [
        "google.generativeai",
        "telegram",
        "schedule"
    ]
    
    results = {"required": {}, "optional": {}}
    
    # فحص المكتبات المطلوبة
    for module in required_modules:
        try:
            __import__(module)
            results["required"][module] = "✅ متوفر"
            logger.info(f"✅ {module}: متوفر")
        except ImportError as e:
            results["required"][module] = f"❌ مفقود: {e}"
            logger.error(f"❌ {module}: مفقود - {e}")
    
    # فحص المكتبات الاختيارية
    for module in optional_modules:
        try:
            __import__(module)
            results["optional"][module] = "✅ متوفر"
            logger.info(f"✅ {module}: متوفر")
        except ImportError:
            results["optional"][module] = "⚠️ مفقود (اختياري)"
            logger.warning(f"⚠️ {module}: مفقود (اختياري)")
    
    return results

def generate_report(test_results):
    """إنشاء تقرير الاختبار"""
    logger.info("📊 إنشاء تقرير الاختبار...")
    
    report = {
        "timestamp": datetime.now().isoformat(),
        "test_results": test_results,
        "summary": {
            "total_tests": 0,
            "passed_tests": 0,
            "failed_tests": 0,
            "warnings": 0
        }
    }
    
    # حساب الإحصائيات
    for category in test_results.values():
        if isinstance(category, dict):
            for result in category.values():
                report["summary"]["total_tests"] += 1
                if "✅" in str(result):
                    report["summary"]["passed_tests"] += 1
                elif "❌" in str(result):
                    report["summary"]["failed_tests"] += 1
                elif "⚠️" in str(result):
                    report["summary"]["warnings"] += 1
    
    # حفظ التقرير
    report_file = Path("render_test_report.json")
    try:
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        logger.info(f"✅ تم حفظ التقرير: {report_file}")
    except Exception as e:
        logger.error(f"❌ خطأ في حفظ التقرير: {e}")
    
    return report

def main():
    """الدالة الرئيسية للاختبار"""
    logger.info("=" * 60)
    logger.info("🧪 اختبار إعداد Reddit Video Maker Bot لـ Render")
    logger.info("=" * 60)
    
    test_results = {}
    
    try:
        # تشغيل الاختبارات
        test_results["environment_variables"] = test_environment_variables()
        test_results["file_structure"] = test_file_structure()
        test_results["directories"] = test_directories()
        test_results["imports"] = test_imports()
        
        # إنشاء التقرير
        report = generate_report(test_results)
        
        # عرض الملخص
        logger.info("=" * 60)
        logger.info("📊 ملخص الاختبار:")
        logger.info(f"   إجمالي الاختبارات: {report['summary']['total_tests']}")
        logger.info(f"   نجح: {report['summary']['passed_tests']}")
        logger.info(f"   فشل: {report['summary']['failed_tests']}")
        logger.info(f"   تحذيرات: {report['summary']['warnings']}")
        
        # تحديد حالة الاستعداد
        if report['summary']['failed_tests'] == 0:
            logger.info("🎉 البوت جاهز للنشر على Render!")
            return True
        else:
            logger.error("❌ يجب إصلاح الأخطاء قبل النشر")
            return False
            
    except Exception as e:
        logger.error(f"❌ خطأ في الاختبار: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
