# GitHub Uploader Tool - Environment Variables
# أداة رفع المشاريع على GitHub - متغيرات البيئة

# GitHub Settings
GITHUB_TOKEN=your_github_personal_access_token_here
GITHUB_USERNAME=your_github_username

# Application Settings
LANGUAGE=ar                    # ar (العربية) أو en (English)
AUTO_CREATE_GITIGNORE=true     # إنشاء .gitignore تلقائياً
AUTO_CREATE_README=true        # إنشاء README.md تلقائياً
DEFAULT_PRIVATE=false          # المستودعات خاصة افتراضياً
DEFAULT_BRANCH=main            # الفرع الافتراضي

# Git Settings
GIT_USER_NAME=Your Name
GIT_USER_EMAIL=<EMAIL>

# Logging Settings
LOG_LEVEL=INFO                 # DEBUG, INFO, WARNING, ERROR
LOG_FILE=uploader.log
ENABLE_LOGGING=true

# Security Settings
SAVE_TOKEN=false               # حفظ الـ token محلياً
ENCRYPT_TOKEN=true             # تشفير الـ token المحفوظ

# API Settings
API_TIMEOUT=30                 # مهلة الاتصال بالثواني
MAX_RETRIES=3                  # عدد المحاولات عند الفشل

# Project Detection Settings
AUTO_DETECT_TYPE=true          # تحديد نوع المشروع تلقائياً
INCLUDE_HIDDEN_FILES=false     # تضمين الملفات المخفية

# Upload Settings
DEFAULT_COMMIT_MESSAGE=Initial commit
CONFIRM_BEFORE_UPLOAD=true     # تأكيد قبل الرفع
SHOW_PROGRESS=true             # عرض شريط التقدم

# Advanced Settings
BATCH_UPLOAD=false             # رفع متعدد
CREATE_RELEASES=false          # إنشاء releases تلقائياً
SETUP_CI_CD=false              # إعداد CI/CD تلقائياً

# Docker Settings (للاستخدام مع Docker)
DOCKER_PROJECTS_PATH=/app/projects
DOCKER_OUTPUT_PATH=/app/output

# Development Settings (للتطوير فقط)
DEBUG_MODE=false
VERBOSE_OUTPUT=false
SKIP_TESTS=false

# Backup Settings
BACKUP_BEFORE_UPLOAD=false     # نسخ احتياطي قبل الرفع
BACKUP_PATH=./backups

# Notification Settings
ENABLE_NOTIFICATIONS=false     # تفعيل الإشعارات
NOTIFICATION_EMAIL=<EMAIL>
WEBHOOK_URL=                   # رابط webhook للإشعارات

# Performance Settings
MAX_FILE_SIZE=100MB            # أقصى حجم ملف
MAX_PROJECT_SIZE=1GB           # أقصى حجم مشروع
PARALLEL_UPLOADS=false         # رفع متوازي

# UI Settings
COLORED_OUTPUT=true            # نص ملون
SHOW_EMOJIS=true              # عرض الرموز التعبيرية
PROGRESS_BAR_STYLE=bar         # نمط شريط التقدم

# Cache Settings
ENABLE_CACHE=true              # تفعيل التخزين المؤقت
CACHE_DURATION=3600            # مدة التخزين المؤقت بالثواني
CACHE_PATH=./.cache

# Template Settings
CUSTOM_README_TEMPLATE=        # مسار قالب README مخصص
CUSTOM_GITIGNORE_TEMPLATE=     # مسار قالب .gitignore مخصص
INCLUDE_LICENSE=true           # تضمين ملف LICENSE

# Integration Settings
SLACK_WEBHOOK=                 # رابط Slack webhook
DISCORD_WEBHOOK=               # رابط Discord webhook
TEAMS_WEBHOOK=                 # رابط Microsoft Teams webhook

# Analytics Settings (اختياري)
ENABLE_ANALYTICS=false         # تفعيل التحليلات
ANALYTICS_ID=                  # معرف التحليلات

# Error Handling
CONTINUE_ON_ERROR=false        # المتابعة عند حدوث خطأ
ERROR_LOG_FILE=errors.log      # ملف سجل الأخطاء
SEND_ERROR_REPORTS=false       # إرسال تقارير الأخطاء

# Experimental Features (تجريبي)
ENABLE_AI_DESCRIPTIONS=false   # وصف تلقائي بالذكاء الاصطناعي
AUTO_TAG_RELEASES=false        # وسم الإصدارات تلقائياً
SMART_GITIGNORE=false          # .gitignore ذكي

# Notes:
# 1. انسخ هذا الملف إلى .env وعدل القيم حسب احتياجاتك
# 2. لا تشارك ملف .env مع أي شخص (يحتوي على معلومات حساسة)
# 3. أضف .env إلى .gitignore لتجنب رفعه على GitHub
# 4. استخدم قيم true/false للمتغيرات المنطقية
# 5. اترك المتغيرات فارغة إذا كنت لا تريد استخدامها
