# نظام التعلم التكيفي للبحث الذكي
import asyncio
import json
import time
import sqlite3
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from enum import Enum
from collections import defaultdict, deque
import pickle
import os

from .logger import logger
from .database import db
from .api_usage_manager import api_usage_manager, APIProvider, UsagePriority, can_use_api

class LearningMetric(Enum):
    """مقاييس التعلم"""
    RELEVANCE_SCORE = "relevance_score"
    QUALITY_SCORE = "quality_score"
    USER_ENGAGEMENT = "user_engagement"
    CLICK_THROUGH_RATE = "click_through_rate"
    TIME_TO_RESULT = "time_to_result"
    SUCCESS_RATE = "success_rate"

class LearningStrategy(Enum):
    """استراتيجيات التعلم"""
    REINFORCEMENT = "reinforcement"     # تعزيز النتائج الناجحة
    EXPLORATION = "exploration"         # استكشاف طرق جديدة
    EXPLOITATION = "exploitation"       # استغلال المعرفة الحالية
    ADAPTIVE = "adaptive"              # تكيف مع التغييرات
    COLLABORATIVE = "collaborative"     # تعلم من مصادر متعددة

@dataclass
class LearningEvent:
    """حدث تعلم"""
    timestamp: datetime
    query: str
    strategy_used: str
    engine_used: str
    results_count: int
    success_metrics: Dict[str, float]
    context: Dict[str, Any]
    feedback_score: float
    learning_value: float

@dataclass
class LearningPattern:
    """نمط تعلم"""
    pattern_id: str
    pattern_type: str
    conditions: Dict[str, Any]
    actions: Dict[str, Any]
    success_rate: float
    confidence: float
    usage_count: int
    last_updated: datetime

class AdaptiveLearningSystem:
    """نظام التعلم التكيفي للبحث الذكي"""
    
    def __init__(self):
        self.db_path = "cache/adaptive_learning.db"
        self.patterns_path = "cache/learning_patterns.pkl"
        
        # ذاكرة التعلم
        self.learning_memory = deque(maxlen=1000)  # آخر 1000 حدث تعلم
        self.pattern_cache = {}
        self.strategy_performance = defaultdict(list)
        self.engine_performance = defaultdict(dict)
        
        # إعدادات التعلم
        self.learning_config = {
            'min_events_for_pattern': 5,
            'confidence_threshold': 0.7,
            'adaptation_rate': 0.1,
            'exploration_rate': 0.2,
            'pattern_decay_rate': 0.05,
            'max_patterns': 100
        }
        
        # مقاييس الأداء
        self.performance_metrics = {
            'total_learning_events': 0,
            'patterns_discovered': 0,
            'successful_adaptations': 0,
            'failed_adaptations': 0,
            'average_improvement': 0.0
        }
        
        # تهيئة النظام
        self._initialize_database()
        self._load_learning_patterns()
        
        logger.info("🧠 تم تهيئة نظام التعلم التكيفي")
    
    def _initialize_database(self):
        """تهيئة قاعدة بيانات التعلم"""
        try:
            os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
            
            with sqlite3.connect(self.db_path) as conn:
                # جدول أحداث التعلم
                conn.execute('''
                    CREATE TABLE IF NOT EXISTS learning_events (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        timestamp REAL NOT NULL,
                        query TEXT NOT NULL,
                        strategy_used TEXT NOT NULL,
                        engine_used TEXT NOT NULL,
                        results_count INTEGER NOT NULL,
                        success_metrics TEXT NOT NULL,
                        context TEXT NOT NULL,
                        feedback_score REAL NOT NULL,
                        learning_value REAL NOT NULL
                    )
                ''')
                
                # جدول الأنماط المكتشفة
                conn.execute('''
                    CREATE TABLE IF NOT EXISTS discovered_patterns (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        pattern_id TEXT UNIQUE NOT NULL,
                        pattern_type TEXT NOT NULL,
                        conditions TEXT NOT NULL,
                        actions TEXT NOT NULL,
                        success_rate REAL NOT NULL,
                        confidence REAL NOT NULL,
                        usage_count INTEGER NOT NULL,
                        last_updated REAL NOT NULL
                    )
                ''')
                
                # جدول أداء الاستراتيجيات
                conn.execute('''
                    CREATE TABLE IF NOT EXISTS strategy_performance (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        strategy_name TEXT NOT NULL,
                        context TEXT NOT NULL,
                        performance_score REAL NOT NULL,
                        usage_count INTEGER NOT NULL,
                        last_used REAL NOT NULL
                    )
                ''')
                
                # إنشاء الفهارس
                conn.execute('CREATE INDEX IF NOT EXISTS idx_timestamp ON learning_events(timestamp)')
                conn.execute('CREATE INDEX IF NOT EXISTS idx_strategy ON learning_events(strategy_used)')
                conn.execute('CREATE INDEX IF NOT EXISTS idx_pattern_id ON discovered_patterns(pattern_id)')
                
            logger.info("✅ تم تهيئة قاعدة بيانات التعلم")
            
        except Exception as e:
            logger.error(f"❌ فشل في تهيئة قاعدة بيانات التعلم: {e}")
    
    def _load_learning_patterns(self):
        """تحميل أنماط التعلم المحفوظة"""
        try:
            if os.path.exists(self.patterns_path):
                with open(self.patterns_path, 'rb') as f:
                    data = pickle.load(f)
                    self.pattern_cache = data.get('patterns', {})
                    self.strategy_performance = data.get('strategy_performance', defaultdict(list))
                    self.engine_performance = data.get('engine_performance', defaultdict(dict))
                    self.performance_metrics = data.get('performance_metrics', self.performance_metrics)
                
                logger.info(f"📚 تم تحميل {len(self.pattern_cache)} نمط تعلم")
            else:
                logger.info("📝 بدء نظام تعلم جديد")
                
        except Exception as e:
            logger.error(f"❌ فشل في تحميل أنماط التعلم: {e}")
    
    def _save_learning_patterns(self):
        """حفظ أنماط التعلم"""
        try:
            os.makedirs(os.path.dirname(self.patterns_path), exist_ok=True)
            
            data = {
                'patterns': dict(self.pattern_cache),
                'strategy_performance': dict(self.strategy_performance),
                'engine_performance': dict(self.engine_performance),
                'performance_metrics': self.performance_metrics,
                'last_save': datetime.now().isoformat()
            }
            
            with open(self.patterns_path, 'wb') as f:
                pickle.dump(data, f)
                
            logger.debug("💾 تم حفظ أنماط التعلم")
            
        except Exception as e:
            logger.error(f"❌ فشل في حفظ أنماط التعلم: {e}")
    
    async def record_learning_event(self, 
                                  query: str,
                                  strategy_used: str,
                                  engine_used: str,
                                  results: List[Dict],
                                  context: Dict[str, Any],
                                  feedback_score: Optional[float] = None) -> LearningEvent:
        """تسجيل حدث تعلم جديد"""
        try:
            # حساب مقاييس النجاح
            success_metrics = self._calculate_success_metrics(results, context)
            
            # حساب نقاط التغذية الراجعة
            if feedback_score is None:
                feedback_score = self._estimate_feedback_score(success_metrics)
            
            # حساب قيمة التعلم
            learning_value = self._calculate_learning_value(success_metrics, feedback_score, context)
            
            # إنشاء حدث التعلم
            learning_event = LearningEvent(
                timestamp=datetime.now(),
                query=query,
                strategy_used=strategy_used,
                engine_used=engine_used,
                results_count=len(results),
                success_metrics=success_metrics,
                context=context,
                feedback_score=feedback_score,
                learning_value=learning_value
            )
            
            # إضافة للذاكرة
            self.learning_memory.append(learning_event)
            
            # حفظ في قاعدة البيانات
            await self._save_learning_event(learning_event)
            
            # تحديث الإحصائيات
            self.performance_metrics['total_learning_events'] += 1
            
            # تحليل الأنماط الجديدة
            await self._analyze_for_new_patterns()
            
            # تحديث أداء الاستراتيجيات
            self._update_strategy_performance(strategy_used, learning_value, context)
            
            logger.debug(f"📝 تم تسجيل حدث تعلم: {strategy_used} | {learning_value:.3f}")
            
            return learning_event
            
        except Exception as e:
            logger.error(f"❌ فشل في تسجيل حدث التعلم: {e}")
            return None
    
    def _calculate_success_metrics(self, results: List[Dict], context: Dict[str, Any]) -> Dict[str, float]:
        """حساب مقاييس النجاح"""
        metrics = {}
        
        if not results:
            return {metric.value: 0.0 for metric in LearningMetric}
        
        # متوسط نقاط الصلة
        relevance_scores = [r.get('relevance_score', 0.5) for r in results]
        metrics[LearningMetric.RELEVANCE_SCORE.value] = sum(relevance_scores) / len(relevance_scores)
        
        # متوسط نقاط الجودة
        quality_scores = [r.get('quality_score', 0.5) for r in results]
        metrics[LearningMetric.QUALITY_SCORE.value] = sum(quality_scores) / len(quality_scores)
        
        # معدل النجاح (نسبة النتائج عالية الجودة)
        high_quality_results = sum(1 for r in results if r.get('final_score', 0) > 0.7)
        metrics[LearningMetric.SUCCESS_RATE.value] = high_quality_results / len(results)
        
        # وقت الحصول على النتائج
        execution_time = context.get('execution_time', 1.0)
        metrics[LearningMetric.TIME_TO_RESULT.value] = max(0, 1.0 - (execution_time / 30.0))  # تطبيع لـ 30 ثانية
        
        # تقدير التفاعل (بناءً على جودة النتائج)
        metrics[LearningMetric.USER_ENGAGEMENT.value] = metrics[LearningMetric.QUALITY_SCORE.value]
        
        # تقدير معدل النقر (بناءً على الصلة)
        metrics[LearningMetric.CLICK_THROUGH_RATE.value] = metrics[LearningMetric.RELEVANCE_SCORE.value]
        
        return metrics
    
    def _estimate_feedback_score(self, success_metrics: Dict[str, float]) -> float:
        """تقدير نقاط التغذية الراجعة"""
        # حساب متوسط مرجح للمقاييس
        weights = {
            LearningMetric.RELEVANCE_SCORE.value: 0.3,
            LearningMetric.QUALITY_SCORE.value: 0.3,
            LearningMetric.SUCCESS_RATE.value: 0.2,
            LearningMetric.TIME_TO_RESULT.value: 0.1,
            LearningMetric.USER_ENGAGEMENT.value: 0.1
        }
        
        weighted_score = 0.0
        for metric, weight in weights.items():
            weighted_score += success_metrics.get(metric, 0.0) * weight
        
        return weighted_score
    
    def _calculate_learning_value(self, success_metrics: Dict[str, float], 
                                feedback_score: float, context: Dict[str, Any]) -> float:
        """حساب قيمة التعلم من الحدث"""
        # القيمة الأساسية من التغذية الراجعة
        base_value = feedback_score
        
        # مكافأة للنتائج المتنوعة
        diversity_bonus = context.get('result_diversity', 0.0) * 0.1
        
        # مكافأة للسرعة
        speed_bonus = success_metrics.get(LearningMetric.TIME_TO_RESULT.value, 0.0) * 0.1
        
        # مكافأة للجودة العالية
        quality_bonus = max(0, success_metrics.get(LearningMetric.QUALITY_SCORE.value, 0.0) - 0.7) * 0.2
        
        total_value = base_value + diversity_bonus + speed_bonus + quality_bonus
        
        return min(total_value, 1.0)  # حد أقصى 1.0

    async def _save_learning_event(self, event: LearningEvent):
        """حفظ حدث التعلم في قاعدة البيانات"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute('''
                    INSERT INTO learning_events
                    (timestamp, query, strategy_used, engine_used, results_count,
                     success_metrics, context, feedback_score, learning_value)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    event.timestamp.timestamp(),
                    event.query,
                    event.strategy_used,
                    event.engine_used,
                    event.results_count,
                    json.dumps(event.success_metrics),
                    json.dumps(event.context),
                    event.feedback_score,
                    event.learning_value
                ))

        except Exception as e:
            logger.error(f"❌ فشل في حفظ حدث التعلم: {e}")

    async def _analyze_for_new_patterns(self):
        """تحليل الأحداث الأخيرة لاكتشاف أنماط جديدة"""
        try:
            if len(self.learning_memory) < self.learning_config['min_events_for_pattern']:
                return

            # تحليل أنماط الاستراتيجيات الناجحة
            await self._discover_strategy_patterns()

            # تحليل أنماط المحركات الفعالة
            await self._discover_engine_patterns()

            # تحليل أنماط الاستعلامات
            await self._discover_query_patterns()

            # تنظيف الأنماط القديمة
            self._cleanup_old_patterns()

        except Exception as e:
            logger.error(f"❌ فشل في تحليل الأنماط: {e}")

    async def _discover_strategy_patterns(self):
        """اكتشاف أنماط الاستراتيجيات الناجحة"""
        try:
            # تجميع الأحداث حسب السياق
            context_groups = defaultdict(list)
            for event in list(self.learning_memory)[-50:]:  # آخر 50 حدث
                context_key = self._extract_context_key(event.context)
                context_groups[context_key].append(event)

            # تحليل كل مجموعة سياق
            for context_key, events in context_groups.items():
                if len(events) >= self.learning_config['min_events_for_pattern']:
                    # حساب أداء كل استراتيجية في هذا السياق
                    strategy_performance = defaultdict(list)
                    for event in events:
                        strategy_performance[event.strategy_used].append(event.learning_value)

                    # العثور على أفضل استراتيجية
                    best_strategy = None
                    best_score = 0
                    for strategy, scores in strategy_performance.items():
                        if len(scores) >= 3:  # عينة كافية
                            avg_score = sum(scores) / len(scores)
                            if avg_score > best_score:
                                best_score = avg_score
                                best_strategy = strategy

                    # إنشاء نمط إذا كان الأداء جيد
                    if best_strategy and best_score > 0.7:
                        pattern = LearningPattern(
                            pattern_id=f"strategy_{context_key}_{best_strategy}",
                            pattern_type="strategy_selection",
                            conditions={"context": context_key},
                            actions={"preferred_strategy": best_strategy},
                            success_rate=best_score,
                            confidence=min(len(strategy_performance[best_strategy]) / 10, 1.0),
                            usage_count=0,
                            last_updated=datetime.now()
                        )

                        self.pattern_cache[pattern.pattern_id] = pattern
                        self.performance_metrics['patterns_discovered'] += 1

                        logger.info(f"🔍 اكتشاف نمط استراتيجية: {best_strategy} للسياق {context_key}")

        except Exception as e:
            logger.error(f"❌ فشل في اكتشاف أنماط الاستراتيجيات: {e}")

    async def _discover_engine_patterns(self):
        """اكتشاف أنماط المحركات الفعالة"""
        try:
            # تجميع الأحداث حسب نوع الاستعلام
            query_type_groups = defaultdict(list)
            for event in list(self.learning_memory)[-50:]:
                query_type = self._classify_query_type(event.query)
                query_type_groups[query_type].append(event)

            # تحليل كل نوع استعلام
            for query_type, events in query_type_groups.items():
                if len(events) >= self.learning_config['min_events_for_pattern']:
                    # حساب أداء كل محرك لهذا النوع
                    engine_performance = defaultdict(list)
                    for event in events:
                        relevance_score = event.success_metrics.get('relevance_score', 0)
                        engine_performance[event.engine_used].append(relevance_score)

                    # العثور على أفضل محرك
                    best_engine = None
                    best_score = 0
                    for engine, scores in engine_performance.items():
                        if len(scores) >= 3:
                            avg_score = sum(scores) / len(scores)
                            if avg_score > best_score:
                                best_score = avg_score
                                best_engine = engine

                    # إنشاء نمط إذا كان الأداء جيد
                    if best_engine and best_score > 0.7:
                        pattern = LearningPattern(
                            pattern_id=f"engine_{query_type}_{best_engine}",
                            pattern_type="engine_selection",
                            conditions={"query_type": query_type},
                            actions={"preferred_engine": best_engine},
                            success_rate=best_score,
                            confidence=min(len(engine_performance[best_engine]) / 10, 1.0),
                            usage_count=0,
                            last_updated=datetime.now()
                        )

                        self.pattern_cache[pattern.pattern_id] = pattern
                        logger.info(f"🔍 اكتشاف نمط محرك: {best_engine} لنوع {query_type}")

        except Exception as e:
            logger.error(f"❌ فشل في اكتشاف أنماط المحركات: {e}")

    async def _discover_query_patterns(self):
        """اكتشاف أنماط الاستعلامات الناجحة"""
        try:
            # تحليل الاستعلامات عالية الأداء
            high_performance_events = [
                event for event in self.learning_memory
                if event.learning_value > 0.8
            ]

            if len(high_performance_events) >= 5:
                # تحليل الخصائص المشتركة
                common_patterns = self._extract_query_commonalities(high_performance_events)

                for pattern_data in common_patterns:
                    pattern = LearningPattern(
                        pattern_id=f"query_{pattern_data['type']}_{hash(str(pattern_data))}",
                        pattern_type="query_optimization",
                        conditions=pattern_data['conditions'],
                        actions=pattern_data['actions'],
                        success_rate=pattern_data['success_rate'],
                        confidence=pattern_data['confidence'],
                        usage_count=0,
                        last_updated=datetime.now()
                    )

                    self.pattern_cache[pattern.pattern_id] = pattern
                    logger.info(f"🔍 اكتشاف نمط استعلام: {pattern_data['type']}")

        except Exception as e:
            logger.error(f"❌ فشل في اكتشاف أنماط الاستعلامات: {e}")

    def _extract_context_key(self, context: Dict[str, Any]) -> str:
        """استخراج مفتاح السياق"""
        key_elements = []

        # إضافة عناصر السياق المهمة
        if 'intent' in context:
            key_elements.append(f"intent:{context['intent']}")
        if 'complexity' in context:
            key_elements.append(f"complexity:{context['complexity']}")
        if 'urgency' in context:
            key_elements.append(f"urgency:{context['urgency']}")

        return "_".join(key_elements) if key_elements else "general"

    def _classify_query_type(self, query: str) -> str:
        """تصنيف نوع الاستعلام"""
        query_lower = query.lower()

        if any(word in query_lower for word in ['news', 'breaking', 'announcement']):
            return 'news'
        elif any(word in query_lower for word in ['review', 'rating', 'opinion']):
            return 'review'
        elif any(word in query_lower for word in ['guide', 'tutorial', 'how to']):
            return 'guide'
        elif any(word in query_lower for word in ['release', 'launch', 'coming']):
            return 'release'
        else:
            return 'general'

    def _extract_query_commonalities(self, events: List[LearningEvent]) -> List[Dict]:
        """استخراج الخصائص المشتركة للاستعلامات الناجحة"""
        commonalities = []

        # تحليل طول الاستعلامات
        query_lengths = [len(event.query.split()) for event in events]
        avg_length = sum(query_lengths) / len(query_lengths)

        if avg_length > 3:
            commonalities.append({
                'type': 'optimal_length',
                'conditions': {'min_query_length': 3},
                'actions': {'suggest_longer_query': True},
                'success_rate': 0.8,
                'confidence': 0.7
            })

        # تحليل الكلمات المفتاحية الشائعة
        all_words = []
        for event in events:
            all_words.extend(event.query.lower().split())

        word_freq = defaultdict(int)
        for word in all_words:
            word_freq[word] += 1

        # العثور على الكلمات الأكثر شيوعاً
        common_words = [word for word, freq in word_freq.items() if freq >= len(events) * 0.5]

        if common_words:
            commonalities.append({
                'type': 'successful_keywords',
                'conditions': {'contains_keywords': common_words},
                'actions': {'boost_relevance': True},
                'success_rate': 0.75,
                'confidence': 0.8
            })

        return commonalities

    def _cleanup_old_patterns(self):
        """تنظيف الأنماط القديمة وغير الفعالة"""
        try:
            current_time = datetime.now()
            patterns_to_remove = []

            for pattern_id, pattern in self.pattern_cache.items():
                # إزالة الأنماط القديمة (أكثر من 30 يوم)
                age_days = (current_time - pattern.last_updated).days
                if age_days > 30:
                    patterns_to_remove.append(pattern_id)
                    continue

                # إزالة الأنماط منخفضة الأداء
                if pattern.success_rate < 0.5 and pattern.usage_count > 10:
                    patterns_to_remove.append(pattern_id)
                    continue

                # تقليل الثقة للأنماط غير المستخدمة
                if pattern.usage_count == 0 and age_days > 7:
                    pattern.confidence *= (1 - self.learning_config['pattern_decay_rate'])
                    if pattern.confidence < 0.3:
                        patterns_to_remove.append(pattern_id)

            # إزالة الأنماط المحددة
            for pattern_id in patterns_to_remove:
                del self.pattern_cache[pattern_id]

            if patterns_to_remove:
                logger.info(f"🧹 تم تنظيف {len(patterns_to_remove)} نمط قديم")

            # الحد من عدد الأنماط
            if len(self.pattern_cache) > self.learning_config['max_patterns']:
                # ترتيب حسب الأداء والاستخدام
                sorted_patterns = sorted(
                    self.pattern_cache.items(),
                    key=lambda x: x[1].success_rate * x[1].confidence * (x[1].usage_count + 1),
                    reverse=True
                )

                # الاحتفاظ بأفضل الأنماط فقط
                keep_count = self.learning_config['max_patterns']
                self.pattern_cache = dict(sorted_patterns[:keep_count])

                logger.info(f"🎯 تم الاحتفاظ بأفضل {keep_count} نمط")

        except Exception as e:
            logger.error(f"❌ فشل في تنظيف الأنماط: {e}")

    def _update_strategy_performance(self, strategy: str, learning_value: float, context: Dict[str, Any]):
        """تحديث أداء الاستراتيجية"""
        try:
            context_key = self._extract_context_key(context)

            # تحديث الأداء العام
            self.strategy_performance[strategy].append(learning_value)

            # تحديث الأداء حسب السياق
            if context_key not in self.engine_performance[strategy]:
                self.engine_performance[strategy][context_key] = []

            self.engine_performance[strategy][context_key].append(learning_value)

            # الاحتفاظ بآخر 50 نتيجة فقط
            if len(self.strategy_performance[strategy]) > 50:
                self.strategy_performance[strategy] = self.strategy_performance[strategy][-50:]

            if len(self.engine_performance[strategy][context_key]) > 20:
                self.engine_performance[strategy][context_key] = self.engine_performance[strategy][context_key][-20:]

        except Exception as e:
            logger.error(f"❌ فشل في تحديث أداء الاستراتيجية: {e}")

    async def get_optimal_strategy(self, context: Dict[str, Any]) -> Tuple[str, float]:
        """الحصول على الاستراتيجية المثلى للسياق المحدد"""
        try:
            context_key = self._extract_context_key(context)

            # البحث في الأنماط المكتشفة
            for pattern in self.pattern_cache.values():
                if (pattern.pattern_type == "strategy_selection" and
                    pattern.conditions.get("context") == context_key and
                    pattern.confidence > self.learning_config['confidence_threshold']):

                    # تحديث استخدام النمط
                    pattern.usage_count += 1

                    strategy = pattern.actions.get("preferred_strategy")
                    confidence = pattern.confidence

                    logger.debug(f"🎯 استخدام نمط مكتشف: {strategy} (ثقة: {confidence:.2f})")
                    return strategy, confidence

            # إذا لم يوجد نمط، استخدم الأداء التاريخي
            best_strategy = "hybrid"  # افتراضي
            best_score = 0.5

            for strategy, performances in self.strategy_performance.items():
                if len(performances) >= 3:  # عينة كافية
                    avg_performance = sum(performances) / len(performances)
                    if avg_performance > best_score:
                        best_score = avg_performance
                        best_strategy = strategy

            return best_strategy, best_score

        except Exception as e:
            logger.error(f"❌ فشل في الحصول على الاستراتيجية المثلى: {e}")
            return "hybrid", 0.5

    async def get_optimal_engine(self, query_type: str) -> Tuple[str, float]:
        """الحصول على المحرك الأمثل لنوع الاستعلام"""
        try:
            # البحث في الأنماط المكتشفة
            for pattern in self.pattern_cache.values():
                if (pattern.pattern_type == "engine_selection" and
                    pattern.conditions.get("query_type") == query_type and
                    pattern.confidence > self.learning_config['confidence_threshold']):

                    pattern.usage_count += 1
                    engine = pattern.actions.get("preferred_engine")
                    confidence = pattern.confidence

                    logger.debug(f"🎯 استخدام محرك مكتشف: {engine} (ثقة: {confidence:.2f})")
                    return engine, confidence

            # إذا لم يوجد نمط، استخدم الترتيب الافتراضي
            default_engines = {
                'news': ('tavily', 0.8),
                'review': ('serpapi', 0.7),
                'guide': ('enhanced', 0.6),
                'release': ('tavily', 0.8),
                'general': ('hybrid', 0.5)
            }

            return default_engines.get(query_type, ('tavily', 0.7))

        except Exception as e:
            logger.error(f"❌ فشل في الحصول على المحرك الأمثل: {e}")
            return "tavily", 0.7

    def should_explore(self) -> bool:
        """تحديد ما إذا كان يجب استكشاف استراتيجيات جديدة"""
        # استكشاف بناءً على معدل الاستكشاف المحدد
        import random
        return random.random() < self.learning_config['exploration_rate']

    def get_learning_insights(self) -> Dict[str, Any]:
        """الحصول على رؤى التعلم"""
        insights = {
            'performance_metrics': self.performance_metrics.copy(),
            'total_patterns': len(self.pattern_cache),
            'active_patterns': len([p for p in self.pattern_cache.values() if p.usage_count > 0]),
            'strategy_rankings': {},
            'engine_rankings': {},
            'recent_discoveries': []
        }

        # ترتيب الاستراتيجيات
        for strategy, performances in self.strategy_performance.items():
            if performances:
                insights['strategy_rankings'][strategy] = {
                    'average_performance': sum(performances) / len(performances),
                    'usage_count': len(performances),
                    'trend': 'improving' if len(performances) > 5 and
                            sum(performances[-5:]) / 5 > sum(performances[:-5]) / max(len(performances) - 5, 1)
                            else 'stable'
                }

        # الاكتشافات الحديثة
        recent_patterns = [
            p for p in self.pattern_cache.values()
            if (datetime.now() - p.last_updated).days <= 7
        ]

        insights['recent_discoveries'] = [
            {
                'type': p.pattern_type,
                'success_rate': p.success_rate,
                'confidence': p.confidence,
                'age_days': (datetime.now() - p.last_updated).days
            }
            for p in recent_patterns
        ]

        return insights

    async def optimize_learning_parameters(self):
        """تحسين معاملات التعلم بناءً على الأداء"""
        try:
            insights = self.get_learning_insights()

            # تحسين معدل الاستكشاف
            if insights['performance_metrics']['successful_adaptations'] > insights['performance_metrics']['failed_adaptations']:
                # إذا كانت التكيفات ناجحة، قلل الاستكشاف
                self.learning_config['exploration_rate'] *= 0.95
            else:
                # إذا كانت التكيفات فاشلة، زد الاستكشاف
                self.learning_config['exploration_rate'] *= 1.05

            # حدود الاستكشاف
            self.learning_config['exploration_rate'] = max(0.1, min(0.4, self.learning_config['exploration_rate']))

            # تحسين عتبة الثقة
            active_patterns_ratio = insights['active_patterns'] / max(insights['total_patterns'], 1)
            if active_patterns_ratio < 0.5:
                # إذا كانت الأنماط غير مستخدمة، قلل عتبة الثقة
                self.learning_config['confidence_threshold'] *= 0.95
            else:
                # إذا كانت الأنماط مستخدمة، زد عتبة الثقة
                self.learning_config['confidence_threshold'] *= 1.02

            # حدود عتبة الثقة
            self.learning_config['confidence_threshold'] = max(0.5, min(0.9, self.learning_config['confidence_threshold']))

            logger.info(f"🔧 تم تحسين معاملات التعلم: استكشاف={self.learning_config['exploration_rate']:.3f}, ثقة={self.learning_config['confidence_threshold']:.3f}")

        except Exception as e:
            logger.error(f"❌ فشل في تحسين معاملات التعلم: {e}")

# إنشاء مثيل عام
adaptive_learning_system = AdaptiveLearningSystem()
