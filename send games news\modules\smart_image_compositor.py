"""
نظام دمج الصور الذكي
يدمج خلفيات الذكاء الاصطناعي مع صور الألعاب المرخصة ويضيف عناوين احترافية
"""

import os
import asyncio
import hashlib
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Union
from PIL import Image, ImageDraw, ImageFont, ImageFilter, ImageEnhance, ImageOps
import aiohttp
import numpy as np

from modules.logger import logger
from modules.licensed_image_manager import licensed_image_manager
from config.settings import BotConfig


class SmartImageCompositor:
    """مركب الصور الذكي"""
    
    def __init__(self):
        self.output_dir = "images/smart_composite"
        self.temp_dir = "temp/image_processing"
        self.fonts_dir = "font"
        self.image_size = (1200, 630)
        
        # إنشاء المجلدات
        os.makedirs(self.output_dir, exist_ok=True)
        os.makedirs(self.temp_dir, exist_ok=True)
        
        # إعدادات التصميم
        self.design_templates = {
            'modern': {
                'overlay_opacity': 0.7,
                'text_shadow': True,
                'gradient_overlay': True,
                'border_style': 'rounded',
                'color_scheme': ['#FF6B35', '#004E89', '#FFD23F']
            },
            'classic': {
                'overlay_opacity': 0.6,
                'text_shadow': False,
                'gradient_overlay': False,
                'border_style': 'square',
                'color_scheme': ['#2C3E50', '#E74C3C', '#F39C12']
            },
            'futuristic': {
                'overlay_opacity': 0.8,
                'text_shadow': True,
                'gradient_overlay': True,
                'border_style': 'hexagon',
                'color_scheme': ['#00D4FF', '#1A1A2E', '#16213E']
            }
        }
        
        logger.info("🎨 تم تهيئة مركب الصور الذكي")

    async def create_smart_composite(self, article: Dict, game_name: str = None) -> Optional[Dict]:
        """إنشاء تركيب ذكي للصورة"""
        try:
            title = article.get('title', '')
            logger.info(f"🎨 بدء التركيب الذكي للصورة: {title[:50]}...")
            
            # تحديد نوع التركيب المطلوب
            composition_type = self._determine_composition_type(article, game_name)
            
            if composition_type == 'single_game':
                return await self._create_single_game_composite(article, game_name)
            elif composition_type == 'multi_game':
                return await self._create_multi_game_composite(article)
            else:
                return await self._create_general_composite(article)
                
        except Exception as e:
            logger.error(f"❌ خطأ في التركيب الذكي: {e}")
            return None

    def _determine_composition_type(self, article: Dict, game_name: str = None) -> str:
        """تحديد نوع التركيب المطلوب"""
        try:
            title = article.get('title', '').lower()
            content = article.get('content', '').lower()
            
            if game_name:
                return 'single_game'
            elif any(keyword in title for keyword in ['أفضل', 'best', 'top', 'قائمة', 'list']):
                return 'multi_game'
            else:
                return 'general'
                
        except Exception as e:
            logger.warning(f"خطأ في تحديد نوع التركيب: {e}")
            return 'general'

    async def _create_single_game_composite(self, article: Dict, game_name: str) -> Optional[Dict]:
        """إنشاء تركيب للعبة واحدة"""
        try:
            logger.info(f"🎮 إنشاء تركيب للعبة: {game_name}")
            
            # الحصول على صور اللعبة المرخصة
            game_images = await licensed_image_manager.get_licensed_images_for_game(game_name, 3)
            
            if not game_images:
                logger.warning(f"لم يتم العثور على صور مرخصة لـ {game_name}")
                return None
            
            # اختيار أفضل صورة كخلفية
            main_image = game_images[0]
            
            # إنشاء خلفية AI مكملة
            ai_background = await self._generate_complementary_background(article, game_name)
            
            # دمج الصور
            composite = await self._blend_images_intelligently(
                main_image.url, ai_background, article.get('title', ''), 'single_game'
            )
            
            return composite
            
        except Exception as e:
            logger.error(f"❌ خطأ في تركيب اللعبة الواحدة: {e}")
            return None

    async def _create_multi_game_composite(self, article: Dict) -> Optional[Dict]:
        """إنشاء تركيب متعدد الألعاب"""
        try:
            logger.info("🎮 إنشاء تركيب متعدد الألعاب")
            
            # استخراج أسماء الألعاب من المقال
            game_names = self._extract_game_names_from_article(article)
            
            if not game_names:
                return await self._create_general_composite(article)
            
            # الحصول على صور الألعاب
            game_images = []
            for game_name in game_names[:5]:  # أقصى 5 ألعاب
                images = await licensed_image_manager.get_licensed_images_for_game(game_name, 1)
                if images:
                    game_images.append({
                        'game': game_name,
                        'image': images[0]
                    })
            
            if not game_images:
                return await self._create_general_composite(article)
            
            # إنشاء خلفية AI للقائمة
            ai_background = await self._generate_list_background(article, len(game_images))
            
            # دمج الصور في تخطيط شبكي
            composite = await self._create_grid_composite(
                game_images, ai_background, article.get('title', '')
            )
            
            return composite
            
        except Exception as e:
            logger.error(f"❌ خطأ في التركيب متعدد الألعاب: {e}")
            return None

    async def _create_general_composite(self, article: Dict) -> Optional[Dict]:
        """إنشاء تركيب عام"""
        try:
            logger.info("🎨 إنشاء تركيب عام")
            
            # إنشاء خلفية AI عامة
            ai_background = await self._generate_general_background(article)
            
            if not ai_background:
                return None
            
            # إضافة النص والتحسينات
            composite = await self._enhance_background_with_professional_text(
                ai_background, article.get('title', ''), 'general'
            )
            
            return composite
            
        except Exception as e:
            logger.error(f"❌ خطأ في التركيب العام: {e}")
            return None

    def _extract_game_names_from_article(self, article: Dict) -> List[str]:
        """استخراج أسماء الألعاب من المقال"""
        try:
            title = article.get('title', '')
            content = article.get('content', '')
            text = f"{title} {content}".lower()
            
            # قاعدة بيانات الألعاب الشهيرة
            popular_games = [
                'Fortnite', 'Minecraft', 'Call of Duty', 'FIFA', 'GTA', 'Grand Theft Auto',
                'Cyberpunk 2077', 'The Witcher 3', 'Overwatch 2', 'League of Legends', 'Valorant',
                'Apex Legends', 'Destiny 2', 'Halo Infinite', 'God of War', 'Spider-Man', 'Horizon',
                'The Last of Us', 'Uncharted', 'Zelda', 'Mario', 'Pokemon', 'Genshin Impact',
                'Elden Ring', 'Dark Souls', 'Sekiro', 'Bloodborne', 'Red Dead Redemption',
                'Assassin\'s Creed', 'Far Cry', 'Watch Dogs', 'Rainbow Six Siege', 'Battlefield',
                'Counter-Strike', 'Dota 2', 'World of Warcraft', 'Diablo', 'StarCraft'
            ]
            
            found_games = []
            for game in popular_games:
                if game.lower() in text:
                    found_games.append(game)
            
            return found_games[:5]  # أقصى 5 ألعاب
            
        except Exception as e:
            logger.warning(f"خطأ في استخراج أسماء الألعاب: {e}")
            return []

    async def _generate_complementary_background(self, article: Dict, game_name: str) -> Optional[str]:
        """إنشاء خلفية مكملة للعبة"""
        try:
            title = article.get('title', '')
            
            # إنشاء prompt مخصص للعبة
            prompt = f"Professional gaming background for {game_name}, atmospheric lighting, gaming setup, modern design, complementary colors, high quality, 4K"
            
            return await self._generate_ai_image(prompt)
            
        except Exception as e:
            logger.warning(f"فشل في إنشاء خلفية مكملة: {e}")
            return None

    async def _generate_list_background(self, article: Dict, num_games: int) -> Optional[str]:
        """إنشاء خلفية لقائمة الألعاب"""
        try:
            title = article.get('title', '')
            
            prompt = f"Professional gaming list background, top {num_games} games layout, modern gaming interface, clean design, professional esports theme, high quality"
            
            return await self._generate_ai_image(prompt)
            
        except Exception as e:
            logger.warning(f"فشل في إنشاء خلفية القائمة: {e}")
            return None

    async def _generate_general_background(self, article: Dict) -> Optional[str]:
        """إنشاء خلفية عامة"""
        try:
            title = article.get('title', '')
            
            prompt = f"Professional gaming news background, modern gaming theme, technology elements, clean interface design, high quality, 4K"
            
            return await self._generate_ai_image(prompt)
            
        except Exception as e:
            logger.warning(f"فشل في إنشاء خلفية عامة: {e}")
            return None

    async def _generate_ai_image(self, prompt: str) -> Optional[str]:
        """إنشاء صورة بالذكاء الاصطناعي"""
        try:
            import urllib.parse
            
            # تحسين الـ prompt
            enhanced_prompt = f"{prompt}, professional, high quality, 4K, detailed, gaming aesthetic, modern design"
            encoded_prompt = urllib.parse.quote(enhanced_prompt)
            
            # استخدام Pollinations.AI
            image_url = f"https://image.pollinations.ai/prompt/{encoded_prompt}"
            params = "?width=1200&height=630&model=flux&enhance=true"
            full_url = image_url + params
            
            # التحقق من نجاح الإنشاء
            async with aiohttp.ClientSession() as session:
                async with session.get(full_url) as response:
                    if response.status == 200:
                        return full_url
            
            return None
            
        except Exception as e:
            logger.warning(f"فشل في إنشاء صورة AI: {e}")
            return None

    async def _blend_images_intelligently(self, main_image_url: str, background_url: str, title: str, composition_type: str) -> Optional[Dict]:
        """دمج الصور بذكاء"""
        try:
            if not background_url:
                return None
            
            # تحميل الصور
            main_img_data = await self._download_image(main_image_url)
            bg_img_data = await self._download_image(background_url)
            
            if not main_img_data or not bg_img_data:
                return None
            
            # معالجة الصور
            with Image.open(main_img_data) as main_img, Image.open(bg_img_data) as bg_img:
                # تحضير الصور
                main_img = main_img.convert('RGBA')
                bg_img = bg_img.convert('RGBA')
                
                # تغيير الأحجام
                bg_img = bg_img.resize(self.image_size, Image.Resampling.LANCZOS)
                
                # دمج ذكي
                blended = self._smart_blend(main_img, bg_img, composition_type)
                
                # إضافة النص
                final_img = self._add_professional_text(blended, title, composition_type)
                
                # حفظ النتيجة
                return await self._save_composite_image(final_img, title, composition_type)
            
        except Exception as e:
            logger.error(f"❌ خطأ في الدمج الذكي: {e}")
            return None

    def _smart_blend(self, main_img: Image.Image, bg_img: Image.Image, composition_type: str) -> Image.Image:
        """دمج ذكي للصور"""
        try:
            if composition_type == 'single_game':
                # للعبة واحدة: الصورة الرئيسية في المقدمة مع خلفية مضببة
                
                # تضبيب الخلفية
                blurred_bg = bg_img.filter(ImageFilter.GaussianBlur(radius=3))
                
                # تحسين الصورة الرئيسية
                main_resized = main_img.resize((400, 300), Image.Resampling.LANCZOS)
                
                # إضافة ظل للصورة الرئيسية
                shadow = self._create_shadow(main_resized)
                
                # دمج الطبقات
                result = blurred_bg.copy()
                
                # موضع الصورة الرئيسية (يمين الوسط)
                main_x = self.image_size[0] - main_resized.width - 50
                main_y = (self.image_size[1] - main_resized.height) // 2
                
                # لصق الظل أولاً
                result.paste(shadow, (main_x + 5, main_y + 5), shadow)
                # ثم الصورة الرئيسية
                result.paste(main_resized, (main_x, main_y), main_resized)
                
                return result
            
            else:
                # للأنواع الأخرى: دمج بسيط مع شفافية
                overlay = Image.new('RGBA', self.image_size, (0, 0, 0, 100))
                result = Image.alpha_composite(bg_img, overlay)
                return result
                
        except Exception as e:
            logger.warning(f"خطأ في الدمج الذكي: {e}")
            return bg_img

    def _create_shadow(self, image: Image.Image) -> Image.Image:
        """إنشاء ظل للصورة"""
        try:
            # إنشاء ظل أسود
            shadow = Image.new('RGBA', image.size, (0, 0, 0, 0))
            shadow_draw = ImageDraw.Draw(shadow)
            
            # رسم مستطيل أسود بشفافية
            shadow_draw.rectangle([0, 0, image.width, image.height], fill=(0, 0, 0, 80))
            
            # تضبيب الظل
            shadow = shadow.filter(ImageFilter.GaussianBlur(radius=2))
            
            return shadow
            
        except Exception as e:
            logger.warning(f"فشل في إنشاء الظل: {e}")
            return Image.new('RGBA', image.size, (0, 0, 0, 0))

    async def _download_image(self, url: str) -> Optional[str]:
        """تحميل صورة من URL"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    if response.status == 200:
                        # حفظ مؤقت
                        filename = f"temp_{hashlib.md5(url.encode()).hexdigest()[:8]}.jpg"
                        filepath = os.path.join(self.temp_dir, filename)

                        with open(filepath, 'wb') as f:
                            f.write(await response.read())

                        return filepath

            return None

        except Exception as e:
            logger.warning(f"فشل في تحميل الصورة: {e}")
            return None

    def _add_professional_text(self, image: Image.Image, title: str, composition_type: str) -> Image.Image:
        """إضافة نص احترافي للصورة"""
        try:
            draw = ImageDraw.Draw(image)

            # اختيار قالب التصميم
            template = self.design_templates.get('modern', self.design_templates['modern'])

            # تحضير النص
            display_title = self._prepare_title_for_display(title)

            # تحديد منطقة النص
            if composition_type == 'single_game':
                # النص في الجانب الأيسر
                text_area = (50, 50, 600, 400)
            else:
                # النص في الوسط السفلي
                text_area = (50, 400, self.image_size[0] - 50, self.image_size[1] - 50)

            # رسم النص
            self._draw_styled_text(draw, display_title, text_area, template)

            return image

        except Exception as e:
            logger.warning(f"فشل في إضافة النص: {e}")
            return image

    def _prepare_title_for_display(self, title: str) -> str:
        """تحضير العنوان للعرض"""
        # تقصير العنوان إذا كان طويلاً
        if len(title) > 80:
            title = title[:77] + "..."

        # تقسيم العنوان لأسطر متعددة إذا لزم الأمر
        words = title.split()
        if len(words) > 8:
            mid = len(words) // 2
            line1 = ' '.join(words[:mid])
            line2 = ' '.join(words[mid:])
            return f"{line1}\n{line2}"

        return title

    def _draw_styled_text(self, draw: ImageDraw.Draw, text: str, text_area: Tuple[int, int, int, int], template: Dict):
        """رسم نص بأسلوب احترافي"""
        try:
            x1, y1, x2, y2 = text_area

            # محاولة تحميل خط مناسب
            try:
                font_size = 42
                font = ImageFont.truetype(os.path.join(self.fonts_dir, "arabic/NotoSansArabic-Bold.ttf"), font_size)
            except:
                try:
                    font = ImageFont.truetype("arial.ttf", 42)
                except:
                    font = ImageFont.load_default()

            # حساب موضع النص
            text_bbox = draw.textbbox((0, 0), text, font=font)
            text_width = text_bbox[2] - text_bbox[0]
            text_height = text_bbox[3] - text_bbox[1]

            text_x = x1 + 20
            text_y = y1 + (y2 - y1 - text_height) // 2

            # رسم خلفية للنص إذا لزم الأمر
            if template.get('text_background', True):
                bg_padding = 20
                bg_rect = [
                    text_x - bg_padding,
                    text_y - bg_padding,
                    text_x + text_width + bg_padding,
                    text_y + text_height + bg_padding
                ]
                draw.rounded_rectangle(bg_rect, radius=10, fill=(0, 0, 0, 150))

            # رسم الظل إذا كان مطلوباً
            if template.get('text_shadow', True):
                shadow_offset = 3
                draw.text((text_x + shadow_offset, text_y + shadow_offset), text, font=font, fill=(0, 0, 0, 180))

            # رسم النص الأساسي
            text_color = template['color_scheme'][0] if template['color_scheme'] else '#FFFFFF'
            draw.text((text_x, text_y), text, font=font, fill=text_color)

        except Exception as e:
            logger.warning(f"فشل في رسم النص المنسق: {e}")

    async def _save_composite_image(self, image: Image.Image, title: str, composition_type: str) -> Dict:
        """حفظ الصورة المركبة"""
        try:
            # إنشاء اسم الملف
            filename = f"smart_composite_{composition_type}_{hashlib.md5(title.encode()).hexdigest()[:8]}.png"
            filepath = os.path.join(self.output_dir, filename)

            # حفظ الصورة
            image.save(filepath, 'PNG', quality=95)

            # تنظيف الملفات المؤقتة
            self._cleanup_temp_files()

            return {
                'url': f"file://{os.path.abspath(filepath)}",
                'local_path': filepath,
                'filename': filename,
                'description': f'Smart composite image for: {title[:50]}...',
                'source': 'Smart Image Compositor',
                'license': 'Generated Content',
                'attribution': f'Created by {getattr(BotConfig, "WEBSITE_NAME", "Gaming News")}',
                'width': self.image_size[0],
                'height': self.image_size[1],
                'format': 'PNG',
                'generation_method': f'smart_composite_{composition_type}',
                'creation_date': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"❌ خطأ في حفظ الصورة المركبة: {e}")
            return {}

    def _cleanup_temp_files(self):
        """تنظيف الملفات المؤقتة"""
        try:
            for filename in os.listdir(self.temp_dir):
                if filename.startswith('temp_'):
                    filepath = os.path.join(self.temp_dir, filename)
                    try:
                        os.remove(filepath)
                    except:
                        pass
        except Exception as e:
            logger.debug(f"تنظيف الملفات المؤقتة: {e}")


# إنشاء كائن مركب الصور الذكي
smart_image_compositor = SmartImageCompositor()
