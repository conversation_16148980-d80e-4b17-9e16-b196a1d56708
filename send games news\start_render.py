#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل محسن للبوت في بيئة Render
يحل مشاكل الشبكة والاتصال تلقائياً
"""

import os
import sys
import logging
import asyncio
import signal
from pathlib import Path

# إعداد نظام السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def setup_environment():
    """إعداد البيئة للتشغيل في Render"""
    logger.info("🚀 إعداد البيئة لـ Render...")
    
    # تعيين متغيرات البيئة المطلوبة
    os.environ.setdefault('RENDER', 'true')
    os.environ.setdefault('PYTHONUNBUFFERED', '1')
    os.environ.setdefault('PYTHONIOENCODING', 'utf-8')
    
    # إنشاء المجلدات المطلوبة
    required_dirs = [
        'logs',
        'temp',
        'cache',
        'data',
        'images',
        'background',
        'assets/backgrounds',
        'assets/fonts'
    ]
    
    for dir_path in required_dirs:
        Path(dir_path).mkdir(parents=True, exist_ok=True)
        logger.info(f"✅ مجلد: {dir_path}")
    
    # إنشاء الملفات المطلوبة إذا لم تكن موجودة
    required_files = [
        'data/bot_state.json',
        'data/system_state.json'
    ]
    
    for file_path in required_files:
        if not Path(file_path).exists():
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write('{}')
            logger.info(f"✅ ملف: {file_path}")

def apply_network_fixes():
    """تطبيق إصلاحات الشبكة لبيئة Render"""
    logger.info("🔧 تطبيق إصلاحات الشبكة...")
    
    try:
        # تشغيل أداة إصلاح الشبكة
        from render_network_fix import RenderNetworkFixer
        
        fixer = RenderNetworkFixer()
        results = fixer.run_full_diagnosis()
        
        if results['basic_connectivity']:
            logger.info("✅ إصلاحات الشبكة تمت بنجاح")
            return True
        else:
            logger.warning("⚠️ بعض مشاكل الشبكة لم تحل، لكن سيتم المتابعة")
            return True  # نتابع حتى لو كانت هناك مشاكل
            
    except Exception as e:
        logger.warning(f"⚠️ فشل في تطبيق إصلاحات الشبكة: {e}")
        return True  # نتابع حتى لو فشلت الإصلاحات

def check_required_env_vars():
    """فحص متغيرات البيئة المطلوبة مع إصلاح تلقائي"""
    logger.info("🔍 فحص متغيرات البيئة...")

    # طباعة جميع متغيرات البيئة للتشخيص
    logger.info("🔍 متغيرات البيئة المتاحة:")
    env_vars = ['BOT_TOKEN', 'TELEGRAM_BOT_TOKEN', 'SUPABASE_URL', 'SUPABASE_KEY', 'RENDER']
    for var in env_vars:
        value = os.getenv(var)
        if value:
            # إخفاء جزء من القيمة للأمان
            masked_value = value[:10] + "..." if len(value) > 10 else value
            logger.info(f"   {var}: {masked_value}")
        else:
            logger.warning(f"   {var}: غير موجود")

    # إصلاح تلقائي لمتغيرات البيئة
    fixes_applied = []

    # إصلاح TELEGRAM_BOT_TOKEN
    if not os.getenv('TELEGRAM_BOT_TOKEN'):
        if os.getenv('BOT_TOKEN'):
            os.environ['TELEGRAM_BOT_TOKEN'] = os.getenv('BOT_TOKEN')
            fixes_applied.append("TELEGRAM_BOT_TOKEN ← BOT_TOKEN")
        else:
            # محاولة استخدام القيمة المباشرة من render.yaml
            hardcoded_token = "7605181405:AAEBua03X_QdD6GkCqKWR1P02EtXIbnSkx4"
            os.environ['TELEGRAM_BOT_TOKEN'] = hardcoded_token
            os.environ['BOT_TOKEN'] = hardcoded_token
            fixes_applied.append("TELEGRAM_BOT_TOKEN ← قيمة مباشرة")

    # إصلاح SUPABASE_URL
    if not os.getenv('SUPABASE_URL'):
        hardcoded_url = "https://ytqxxodyecdeosnqoure.supabase.co"
        os.environ['SUPABASE_URL'] = hardcoded_url
        fixes_applied.append("SUPABASE_URL ← قيمة مباشرة")

    # إصلاح SUPABASE_KEY
    if not os.getenv('SUPABASE_KEY'):
        hardcoded_key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4"
        os.environ['SUPABASE_KEY'] = hardcoded_key
        fixes_applied.append("SUPABASE_KEY ← قيمة مباشرة")

    if fixes_applied:
        logger.info("🔧 تم تطبيق الإصلاحات التالية:")
        for fix in fixes_applied:
            logger.info(f"   ✅ {fix}")

    # التحقق النهائي
    required_vars = ['TELEGRAM_BOT_TOKEN', 'SUPABASE_URL', 'SUPABASE_KEY']
    missing_vars = []

    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)

    if missing_vars:
        logger.error(f"❌ متغيرات البيئة المفقودة: {', '.join(missing_vars)}")
        return False

    logger.info("✅ جميع متغيرات البيئة موجودة")
    return True

def setup_signal_handlers():
    """إعداد معالجات الإشارات للإغلاق الآمن"""
    def signal_handler(signum, frame):
        logger.info(f"📡 تم استلام إشارة {signum}، إغلاق آمن...")
        sys.exit(0)
    
    signal.signal(signal.SIGTERM, signal_handler)
    signal.signal(signal.SIGINT, signal_handler)

def start_bot():
    """تشغيل البوت الرئيسي"""
    logger.info("🤖 بدء تشغيل البوت...")
    
    try:
        # استيراد وتشغيل البوت الرئيسي
        import main
        
        # تشغيل البوت
        if hasattr(main, 'main') and callable(main.main):
            # إذا كان main.py يحتوي على دالة main
            if asyncio.iscoroutinefunction(main.main):
                asyncio.run(main.main())
            else:
                main.main()
        else:
            # إذا كان main.py يعمل مباشرة
            logger.info("✅ تم تشغيل البوت بنجاح")
        
    except KeyboardInterrupt:
        logger.info("⏹️ تم إيقاف البوت بواسطة المستخدم")
    except Exception as e:
        logger.error(f"❌ خطأ في تشغيل البوت: {e}")
        raise

def run_web_server():
    """تشغيل خادم الويب للحفاظ على الخدمة نشطة"""
    try:
        # محاولة استيراد خادم الويب
        try:
            from web_api import app
            port = int(os.getenv('PORT', 10000))
            logger.info(f"🌐 تشغيل خادم الويب على المنفذ {port}...")
            app.run(host='0.0.0.0', port=port)
        except ImportError:
            # إنشاء خادم ويب بسيط
            from http.server import HTTPServer, BaseHTTPRequestHandler
            
            class HealthHandler(BaseHTTPRequestHandler):
                def do_GET(self):
                    if self.path == '/health':
                        self.send_response(200)
                        self.send_header('Content-type', 'text/plain')
                        self.end_headers()
                        self.wfile.write(b'OK')
                    else:
                        self.send_response(404)
                        self.end_headers()
                
                def log_message(self, format, *args):
                    pass  # تعطيل السجلات
            
            port = int(os.getenv('PORT', 10000))
            server = HTTPServer(('0.0.0.0', port), HealthHandler)
            logger.info(f"🌐 تشغيل خادم ويب بسيط على المنفذ {port}...")
            server.serve_forever()
            
    except Exception as e:
        logger.error(f"❌ خطأ في تشغيل خادم الويب: {e}")

def main():
    """الدالة الرئيسية"""
    logger.info("🚀 بدء تشغيل البوت في بيئة Render")
    logger.info("=" * 50)
    
    try:
        # إعداد معالجات الإشارات
        setup_signal_handlers()
        
        # إعداد البيئة
        setup_environment()
        
        # تطبيق إصلاحات الشبكة
        network_ok = apply_network_fixes()
        if not network_ok:
            logger.warning("⚠️ مشاكل في الشبكة، لكن سيتم المتابعة")
        
        # فحص متغيرات البيئة
        env_ok = check_required_env_vars()
        if not env_ok:
            logger.error("❌ متغيرات البيئة مفقودة، لا يمكن تشغيل البوت")
            sys.exit(1)
        
        # تحديد نوع الخدمة
        service_type = os.getenv('RENDER_SERVICE_TYPE', 'web')
        logger.info(f"📋 نوع الخدمة: {service_type}")
        
        if service_type == 'web':
            # إذا كانت خدمة ويب، نشغل خادم الويب مع البوت
            logger.info("🌐 تشغيل كخدمة ويب...")
            
            # تشغيل البوت في خيط منفصل
            import threading
            bot_thread = threading.Thread(target=start_bot, daemon=True)
            bot_thread.start()
            
            # تشغيل خادم الويب في الخيط الرئيسي
            run_web_server()
            
        else:
            # إذا كانت خدمة عامل، نشغل البوت فقط
            logger.info("⚙️ تشغيل كخدمة عامل...")
            start_bot()
            
    except KeyboardInterrupt:
        logger.info("⏹️ تم إيقاف البوت بواسطة المستخدم")
    except Exception as e:
        logger.error(f"❌ خطأ في التشغيل: {e}")
        sys.exit(1)

if __name__ == "__main__":
    # تشغيل البوت
    try:
        main()
    except KeyboardInterrupt:
        logger.info("⏹️ تم إيقاف البوت")
    except Exception as e:
        logger.error(f"❌ خطأ في التشغيل: {e}")
        sys.exit(1)
