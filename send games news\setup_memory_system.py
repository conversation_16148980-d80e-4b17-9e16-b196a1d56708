# إعداد نظام الذاكرة المتقدم
import asyncio
import os
import sys
from pathlib import Path

# إضافة المسار الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def install_requirements():
    """تثبيت المتطلبات اللازمة"""
    print("📦 تثبيت المتطلبات اللازمة...")
    
    requirements = [
        "pinecone-client",
        "sentence-transformers",
        "chromadb",
        "langchain-community",
        "langchain-pinecone",
        "faiss-cpu",
        "transformers",
        "torch"
    ]
    
    for req in requirements:
        try:
            print(f"   تثبيت {req}...")
            os.system(f"pip install {req}")
            print(f"   ✅ تم تثبيت {req}")
        except Exception as e:
            print(f"   ❌ فشل تثبيت {req}: {e}")
    
    print("✅ تم تثبيت جميع المتطلبات")

def setup_environment_variables():
    """إعداد متغيرات البيئة"""
    print("\n🔧 إعداد متغيرات البيئة...")
    
    env_file = Path(".env")
    env_content = ""
    
    if env_file.exists():
        with open(env_file, 'r', encoding='utf-8') as f:
            env_content = f.read()
    
    # إضافة متغيرات نظام الذاكرة
    memory_vars = """
# نظام الذاكرة المتقدم
PINECONE_API_KEY=your_pinecone_api_key_here
PINECONE_ENVIRONMENT=us-west1-gcp
MEMORY_SYSTEM_ENABLED=true
MEMORY_CLEANUP_INTERVAL=24  # ساعات
"""
    
    # فحص إذا كانت المتغيرات موجودة
    if "PINECONE_API_KEY" not in env_content:
        with open(env_file, 'a', encoding='utf-8') as f:
            f.write(memory_vars)
        print("✅ تم إضافة متغيرات نظام الذاكرة إلى .env")
    else:
        print("ℹ️ متغيرات نظام الذاكرة موجودة بالفعل")
    
    print("\n💡 لا تنس إضافة مفتاح Pinecone الخاص بك في ملف .env")
    print("   يمكنك الحصول على مفتاح مجاني من: https://www.pinecone.io/")

async def test_memory_system():
    """اختبار نظام الذاكرة"""
    print("\n🧪 اختبار نظام الذاكرة...")
    
    try:
        from modules.memory_system import memory_system, MemoryType, MemoryImportance
        
        # اختبار حفظ ذاكرة
        print("   💾 اختبار حفظ ذاكرة...")
        memory_id = await memory_system.store_memory(
            content="هذا اختبار لنظام الذاكرة المتقدم",
            memory_type=MemoryType.ARTICLE,
            importance=MemoryImportance.MEDIUM,
            metadata={"test": True, "category": "gaming"},
            tags=["test", "memory", "gaming"]
        )
        
        if memory_id:
            print(f"   ✅ تم حفظ الذاكرة بنجاح: {memory_id}")
        else:
            print("   ❌ فشل في حفظ الذاكرة")
            return False
        
        # اختبار استرجاع الذاكرة
        print("   🔍 اختبار استرجاع الذاكرة...")
        memories = await memory_system.retrieve_memories(
            query="اختبار نظام الذاكرة",
            memory_types=[MemoryType.ARTICLE],
            limit=5
        )
        
        if memories:
            print(f"   ✅ تم استرجاع {len(memories)} ذاكرة")
            for memory in memories:
                print(f"      - {memory.content[:50]}...")
        else:
            print("   ⚠️ لم يتم العثور على ذكريات")
        
        # عرض الإحصائيات
        print("   📊 إحصائيات النظام:")
        stats = memory_system.get_memory_stats()
        print(f"      إجمالي الذكريات: {stats['total_memories']}")
        print(f"      Pinecone متوفر: {stats['pinecone_available']}")
        print(f"      نموذج التضمين محمل: {stats['embedding_model_loaded']}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار نظام الذاكرة: {e}")
        return False

def create_memory_integration_example():
    """إنشاء مثال على التكامل مع النظام الحالي"""
    print("\n📝 إنشاء مثال التكامل...")
    
    integration_code = '''# مثال على تكامل نظام الذاكرة مع الوكيل الحالي

from modules.memory_system import memory_system, MemoryType, MemoryImportance

class EnhancedGamingNewsBot:
    """وكيل أخبار الألعاب المحسن مع نظام الذاكرة"""
    
    def __init__(self):
        self.memory = memory_system
    
    async def process_article_with_memory(self, article_data):
        """معالجة مقال مع حفظ في الذاكرة"""
        
        # حفظ المقال في الذاكرة
        await self.memory.store_memory(
            content=f"{article_data['title']} - {article_data['content'][:500]}",
            memory_type=MemoryType.ARTICLE,
            importance=MemoryImportance.MEDIUM,
            metadata={
                "title": article_data['title'],
                "source": article_data.get('source', ''),
                "category": article_data.get('category', ''),
                "performance_score": article_data.get('quality_score', 0)
            },
            tags=article_data.get('keywords', [])
        )
        
        # البحث عن مقالات مشابهة في الذاكرة
        similar_memories = await self.memory.retrieve_memories(
            query=article_data['title'],
            memory_types=[MemoryType.ARTICLE],
            limit=5
        )
        
        # تجنب التكرار
        if len(similar_memories) > 2:
            print(f"⚠️ تم العثور على {len(similar_memories)} مقال مشابه")
            return False  # تخطي المقال المكرر
        
        return True  # متابعة معالجة المقال
    
    async def get_content_suggestions(self, topic):
        """الحصول على اقتراحات محتوى من الذاكرة"""
        
        # البحث في الذاكرة عن مواضيع مشابهة
        related_memories = await self.memory.retrieve_memories(
            query=topic,
            memory_types=[MemoryType.ARTICLE, MemoryType.TREND],
            limit=10
        )
        
        suggestions = []
        for memory in related_memories:
            suggestions.append({
                "title": memory.metadata.get('title', ''),
                "relevance": memory.access_count,
                "last_accessed": memory.last_accessed
            })
        
        return suggestions
    
    async def learn_from_performance(self, article_id, performance_data):
        """التعلم من أداء المقالات"""
        
        # حفظ بيانات الأداء في الذاكرة
        await self.memory.store_memory(
            content=f"أداء المقال {article_id}: {performance_data}",
            memory_type=MemoryType.PERFORMANCE,
            importance=MemoryImportance.HIGH,
            metadata={
                "article_id": article_id,
                "views": performance_data.get('views', 0),
                "engagement": performance_data.get('engagement', 0),
                "shares": performance_data.get('shares', 0)
            }
        )

# استخدام النظام المحسن
bot = EnhancedGamingNewsBot()
'''
    
    with open("memory_integration_example.py", 'w', encoding='utf-8') as f:
        f.write(integration_code)
    
    print("✅ تم إنشاء ملف memory_integration_example.py")

def show_next_steps():
    """عرض الخطوات التالية"""
    print("\n🎯 الخطوات التالية:")
    print("=" * 50)
    
    steps = [
        "1. احصل على مفتاح Pinecone مجاني من https://www.pinecone.io/",
        "2. أضف المفتاح إلى ملف .env",
        "3. شغل الاختبار مرة أخرى للتأكد من عمل النظام",
        "4. ادمج نظام الذاكرة مع الوكيل الحالي",
        "5. راقب الأداء والإحصائيات"
    ]
    
    for step in steps:
        print(f"   {step}")
    
    print("\n📚 ملفات مفيدة:")
    print("   • modules/memory_system.py - النظام الأساسي")
    print("   • memory_integration_example.py - مثال التكامل")
    print("   • PRIORITY_IMPROVEMENTS_IMPLEMENTATION_PLAN.md - خطة التنفيذ")
    
    print("\n🔧 أوامر مفيدة:")
    print("   python setup_memory_system.py  # إعادة تشغيل الإعداد")
    print("   python test_memory_system.py   # اختبار شامل للنظام")

async def main():
    """الوظيفة الرئيسية"""
    print("🧠 إعداد نظام الذاكرة المتقدم للوكيل الذكي")
    print("=" * 60)
    
    try:
        # 1. تثبيت المتطلبات
        install_requirements()
        
        # 2. إعداد متغيرات البيئة
        setup_environment_variables()
        
        # 3. اختبار النظام
        success = await test_memory_system()
        
        # 4. إنشاء مثال التكامل
        create_memory_integration_example()
        
        # 5. عرض الخطوات التالية
        show_next_steps()
        
        if success:
            print("\n🎉 تم إعداد نظام الذاكرة بنجاح!")
        else:
            print("\n⚠️ تم الإعداد مع بعض التحذيرات")
            print("   تأكد من إضافة مفتاح Pinecone للحصول على الأداء الأمثل")
        
    except Exception as e:
        print(f"\n❌ خطأ في الإعداد: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
