#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل البوت النهائي
يحمل متغيرات البيئة بشكل صحيح ويشغل البوت
"""

import os
import sys
import time
import logging
import requests
from pathlib import Path

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

def load_env_file():
    """تحميل متغيرات البيئة من ملف .env"""
    env_file = Path('.env')
    if not env_file.exists():
        logger.error("❌ ملف .env غير موجود")
        return False
    
    logger.info("🔧 تحميل متغيرات البيئة من ملف .env...")
    
    try:
        with open(env_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    key = key.strip()
                    value = value.strip()
                    os.environ[key] = value
                    logger.info(f"✅ تم تحميل {key}")
        
        return True
    except Exception as e:
        logger.error(f"❌ خطأ في تحميل ملف .env: {e}")
        return False

def clear_telegram_conflicts():
    """مسح تضارب Telegram"""
    try:
        bot_token = os.getenv('BOT_TOKEN')
        if not bot_token:
            logger.error("❌ لم يتم العثور على BOT_TOKEN")
            return False
        
        logger.info("🔧 مسح تضارب Telegram...")
        
        # مسح webhook
        webhook_url = f"https://api.telegram.org/bot{bot_token}/deleteWebhook"
        try:
            response = requests.post(webhook_url, json={"drop_pending_updates": True}, timeout=10)
            if response.status_code == 200:
                logger.info("✅ تم مسح webhook")
        except Exception as e:
            logger.warning(f"تحذير في مسح webhook: {e}")
        
        # مسح التحديثات المعلقة
        updates_url = f"https://api.telegram.org/bot{bot_token}/getUpdates"
        try:
            for attempt in range(3):
                response = requests.get(updates_url, params={"timeout": 1, "limit": 100}, timeout=15)
                if response.status_code == 200:
                    result = response.json()
                    if result.get('ok') and result.get('result'):
                        updates = result['result']
                        if updates:
                            last_id = updates[-1]['update_id']
                            requests.get(updates_url, params={"offset": last_id + 1, "timeout": 1}, timeout=10)
                            logger.info(f"✅ تم مسح {len(updates)} تحديث معلق")
                        else:
                            logger.info("✅ لا توجد تحديثات معلقة")
                            break
                    else:
                        break
                else:
                    logger.warning(f"فشل في جلب التحديثات: {response.status_code}")
                    break
        except Exception as e:
            logger.warning(f"تحذير في مسح التحديثات: {e}")
        
        time.sleep(2)
        logger.info("✅ تم مسح جميع تضارب Telegram")
        return True
        
    except Exception as e:
        logger.error(f"❌ خطأ في مسح تضارب Telegram: {e}")
        return False

def test_bot_token():
    """اختبار صحة token البوت"""
    try:
        bot_token = os.getenv('BOT_TOKEN')
        if not bot_token:
            logger.error("❌ BOT_TOKEN غير موجود")
            return False
        
        logger.info("🔍 اختبار صحة token البوت...")
        
        response = requests.get(f"https://api.telegram.org/bot{bot_token}/getMe", timeout=10)
        if response.status_code == 200:
            result = response.json()
            if result.get('ok'):
                bot_info = result.get('result', {})
                logger.info(f"✅ البوت متصل: {bot_info.get('first_name', 'Unknown')}")
                return True
        
        logger.error(f"❌ فشل في الاتصال بالبوت: {response.status_code}")
        return False
        
    except Exception as e:
        logger.error(f"❌ خطأ في اختبار البوت: {e}")
        return False

def check_environment():
    """فحص متغيرات البيئة"""
    logger.info("🔍 فحص متغيرات البيئة...")
    
    required_vars = ['BOT_TOKEN', 'ADMIN_CHAT_ID', 'SUPABASE_URL', 'SUPABASE_KEY']
    missing_vars = []
    
    for var in required_vars:
        value = os.getenv(var)
        if value:
            logger.info(f"✅ {var}: موجود")
        else:
            missing_vars.append(var)
            logger.error(f"❌ {var}: مفقود")
    
    if missing_vars:
        logger.error(f"❌ متغيرات البيئة المفقودة: {', '.join(missing_vars)}")
        return False
    
    logger.info("✅ جميع متغيرات البيئة متوفرة")
    return True

def main():
    """الدالة الرئيسية"""
    logger.info("🤖 بدء تشغيل البوت النهائي...")
    logger.info("=" * 60)
    
    # 1. تحميل متغيرات البيئة
    if not load_env_file():
        logger.error("❌ فشل في تحميل متغيرات البيئة")
        return
    
    # 2. فحص متغيرات البيئة
    if not check_environment():
        logger.error("❌ فشل في فحص متغيرات البيئة")
        return
    
    # 3. اختبار token البوت
    if not test_bot_token():
        logger.error("❌ فشل في اختبار البوت")
        return
    
    # 4. مسح تضارب Telegram
    clear_telegram_conflicts()
    
    # 5. انتظار قصير
    logger.info("⏳ انتظار 3 ثوانِ قبل التشغيل...")
    time.sleep(3)
    
    # 6. تشغيل البوت
    logger.info("🚀 تشغيل البوت الآن...")
    logger.info("=" * 60)
    
    try:
        # استيراد وتشغيل البوت
        import main
        
    except KeyboardInterrupt:
        logger.info("🛑 تم إيقاف البوت بواسطة المستخدم")
    except Exception as e:
        logger.error(f"❌ خطأ في تشغيل البوت: {e}")
        
        # في حالة خطأ Telegram Conflict، محاولة الإصلاح
        if "Conflict" in str(e) or "getUpdates" in str(e):
            logger.info("🔧 محاولة إصلاح مشكلة Telegram Conflict...")
            clear_telegram_conflicts()
            time.sleep(5)
            logger.info("🔄 إعادة محاولة تشغيل البوت...")
            try:
                import importlib
                importlib.reload(main)
            except Exception as e2:
                logger.error(f"❌ فشل في إعادة التشغيل: {e2}")

if __name__ == "__main__":
    main()
