# نظام إدارة حالة الوكيل الذكي
import os
import json
import sqlite3
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from enum import Enum
from dataclasses import dataclass, asdict
from .logger import logger
from .database import db

class AgentState(Enum):
    """حالات الوكيل المختلفة"""
    STOPPED = "stopped"
    STARTING = "starting"
    RUNNING = "running"
    PAUSING = "pausing"
    PAUSED = "paused"
    STOPPING = "stopping"
    ERROR = "error"
    MAINTENANCE = "maintenance"

class OperationState(Enum):
    """حالات العمليات"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    PAUSED = "paused"

@dataclass
class AgentStateInfo:
    """معلومات حالة الوكيل"""
    state: AgentState
    timestamp: datetime
    uptime_seconds: float
    processed_articles: int
    published_articles: int
    current_operation: Optional[str]
    last_error: Optional[str]
    memory_usage_mb: float
    cpu_usage_percent: float
    active_operations: List[str]
    
    def to_dict(self) -> Dict:
        """تحويل إلى قاموس"""
        data = asdict(self)
        data['state'] = self.state.value
        data['timestamp'] = self.timestamp.isoformat()
        return data
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'AgentStateInfo':
        """إنشاء من قاموس"""
        data['state'] = AgentState(data['state'])
        data['timestamp'] = datetime.fromisoformat(data['timestamp'])
        return cls(**data)

@dataclass
class OperationInfo:
    """معلومات العملية"""
    operation_id: str
    operation_type: str
    state: OperationState
    start_time: datetime
    end_time: Optional[datetime]
    progress_percent: float
    details: Dict[str, Any]
    error_message: Optional[str]
    
    def to_dict(self) -> Dict:
        """تحويل إلى قاموس"""
        data = asdict(self)
        data['state'] = self.state.value
        data['start_time'] = self.start_time.isoformat()
        data['end_time'] = self.end_time.isoformat() if self.end_time else None
        return data
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'OperationInfo':
        """إنشاء من قاموس"""
        data['state'] = OperationState(data['state'])
        data['start_time'] = datetime.fromisoformat(data['start_time'])
        data['end_time'] = datetime.fromisoformat(data['end_time']) if data['end_time'] else None
        return cls(**data)

class DatabaseStateChecker:
    """فاحص حالة قاعدة البيانات"""
    
    def __init__(self, db_path: str):
        self.db_path = db_path
        self._lock = threading.Lock()
    
    def check_database_integrity(self) -> Tuple[bool, List[str]]:
        """فحص سلامة قاعدة البيانات"""
        issues = []
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # فحص وجود الجداول الأساسية
                required_tables = [
                    'published_articles', 'monitored_sources', 'error_log',
                    'performance_stats', 'processed_videos', 'video_approvals'
                ]
                
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                existing_tables = [row[0] for row in cursor.fetchall()]
                
                for table in required_tables:
                    if table not in existing_tables:
                        issues.append(f"الجدول المطلوب '{table}' غير موجود")
                
                # فحص بنية الجداول
                for table in existing_tables:
                    try:
                        cursor.execute(f"PRAGMA table_info({table})")
                        columns = cursor.fetchall()
                        if not columns:
                            issues.append(f"الجدول '{table}' فارغ أو تالف")
                    except Exception as e:
                        issues.append(f"خطأ في فحص الجدول '{table}': {str(e)}")
                
                # فحص الفهارس
                cursor.execute("PRAGMA integrity_check")
                integrity_result = cursor.fetchone()
                if integrity_result[0] != "ok":
                    issues.append(f"مشكلة في سلامة قاعدة البيانات: {integrity_result[0]}")
                
        except Exception as e:
            issues.append(f"خطأ في الاتصال بقاعدة البيانات: {str(e)}")
            return False, issues
        
        return len(issues) == 0, issues
    
    def get_database_stats(self) -> Dict[str, Any]:
        """الحصول على إحصائيات قاعدة البيانات"""
        stats = {
            'file_size_mb': 0,
            'total_tables': 0,
            'total_records': 0,
            'last_modified': None,
            'vacuum_needed': False
        }
        
        try:
            # حجم الملف
            if os.path.exists(self.db_path):
                stats['file_size_mb'] = os.path.getsize(self.db_path) / (1024 * 1024)
                stats['last_modified'] = datetime.fromtimestamp(
                    os.path.getmtime(self.db_path)
                ).isoformat()
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # عدد الجداول
                cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table'")
                stats['total_tables'] = cursor.fetchone()[0]
                
                # عدد السجلات الإجمالي
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = [row[0] for row in cursor.fetchall()]
                
                total_records = 0
                for table in tables:
                    try:
                        cursor.execute(f"SELECT COUNT(*) FROM {table}")
                        total_records += cursor.fetchone()[0]
                    except:
                        pass
                
                stats['total_records'] = total_records
                
                # فحص الحاجة للتنظيف
                cursor.execute("PRAGMA freelist_count")
                free_pages = cursor.fetchone()[0]
                cursor.execute("PRAGMA page_count")
                total_pages = cursor.fetchone()[0]
                
                if total_pages > 0 and (free_pages / total_pages) > 0.1:
                    stats['vacuum_needed'] = True
                
        except Exception as e:
            logger.warning(f"خطأ في الحصول على إحصائيات قاعدة البيانات: {e}")
        
        return stats

class AgentStateManager:
    """مدير حالة الوكيل الذكي"""
    
    def __init__(self, state_file: str = "agent_state.json"):
        self.state_file = state_file
        self.db_checker = DatabaseStateChecker(db.db_path)
        self._lock = threading.Lock()
        self._current_state = AgentState.STOPPED
        self._operations: Dict[str, OperationInfo] = {}
        self._state_history: List[AgentStateInfo] = []
        self._startup_time = datetime.now()
        
        # إنشاء جدول حالة الوكيل في قاعدة البيانات
        self._create_state_tables()
        
        # تحميل الحالة المحفوظة
        self._load_saved_state()
    
    def _create_state_tables(self):
        """إنشاء جداول حالة الوكيل"""
        try:
            with sqlite3.connect(db.db_path) as conn:
                cursor = conn.cursor()
                
                # جدول حالة الوكيل
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS agent_state_history (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        state TEXT NOT NULL,
                        timestamp TEXT NOT NULL,
                        uptime_seconds REAL,
                        processed_articles INTEGER,
                        published_articles INTEGER,
                        current_operation TEXT,
                        last_error TEXT,
                        memory_usage_mb REAL,
                        cpu_usage_percent REAL,
                        active_operations TEXT,
                        details TEXT
                    )
                ''')
                
                # جدول العمليات
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS agent_operations (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        operation_id TEXT UNIQUE NOT NULL,
                        operation_type TEXT NOT NULL,
                        state TEXT NOT NULL,
                        start_time TEXT NOT NULL,
                        end_time TEXT,
                        progress_percent REAL DEFAULT 0,
                        details TEXT,
                        error_message TEXT,
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # جدول إعدادات الوكيل
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS agent_settings (
                        key TEXT PRIMARY KEY,
                        value TEXT NOT NULL,
                        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                conn.commit()
                logger.info("✅ تم إنشاء جداول حالة الوكيل")
                
        except Exception as e:
            logger.error(f"❌ فشل في إنشاء جداول حالة الوكيل: {e}")

    def _load_saved_state(self):
        """تحميل الحالة المحفوظة"""
        try:
            # تحميل من ملف JSON
            if os.path.exists(self.state_file):
                with open(self.state_file, 'r', encoding='utf-8') as f:
                    saved_data = json.load(f)

                    if 'current_state' in saved_data:
                        self._current_state = AgentState(saved_data['current_state'])

                    if 'operations' in saved_data:
                        for op_data in saved_data['operations']:
                            op_info = OperationInfo.from_dict(op_data)
                            self._operations[op_info.operation_id] = op_info

                    logger.info(f"✅ تم تحميل الحالة المحفوظة: {self._current_state.value}")

            # تحميل من قاعدة البيانات
            self._load_operations_from_db()

        except Exception as e:
            logger.warning(f"⚠️ فشل في تحميل الحالة المحفوظة: {e}")
            self._current_state = AgentState.STOPPED

    def _load_operations_from_db(self):
        """تحميل العمليات من قاعدة البيانات"""
        try:
            with sqlite3.connect(db.db_path) as conn:
                cursor = conn.cursor()

                # تحميل العمليات النشطة
                cursor.execute('''
                    SELECT operation_id, operation_type, state, start_time, end_time,
                           progress_percent, details, error_message
                    FROM agent_operations
                    WHERE state IN ('pending', 'running', 'paused')
                    ORDER BY start_time DESC
                    LIMIT 50
                ''')

                for row in cursor.fetchall():
                    op_data = {
                        'operation_id': row[0],
                        'operation_type': row[1],
                        'state': row[2],
                        'start_time': row[3],
                        'end_time': row[4],
                        'progress_percent': row[5],
                        'details': json.loads(row[6]) if row[6] else {},
                        'error_message': row[7]
                    }

                    op_info = OperationInfo.from_dict(op_data)
                    self._operations[op_info.operation_id] = op_info

                logger.info(f"✅ تم تحميل {len(self._operations)} عملية من قاعدة البيانات")

        except Exception as e:
            logger.warning(f"⚠️ فشل في تحميل العمليات من قاعدة البيانات: {e}")

    def save_state(self):
        """حفظ الحالة الحالية"""
        try:
            with self._lock:
                # حفظ في ملف JSON
                state_data = {
                    'current_state': self._current_state.value,
                    'timestamp': datetime.now().isoformat(),
                    'operations': [op.to_dict() for op in self._operations.values()],
                    'startup_time': self._startup_time.isoformat()
                }

                with open(self.state_file, 'w', encoding='utf-8') as f:
                    json.dump(state_data, f, ensure_ascii=False, indent=2)

                # حفظ في قاعدة البيانات
                self._save_state_to_db()

                logger.debug("✅ تم حفظ حالة الوكيل")

        except Exception as e:
            logger.error(f"❌ فشل في حفظ حالة الوكيل: {e}")

    def _save_state_to_db(self):
        """حفظ الحالة في قاعدة البيانات"""
        try:
            current_info = self.get_current_state_info()

            with sqlite3.connect(db.db_path) as conn:
                cursor = conn.cursor()

                # حفظ حالة الوكيل
                cursor.execute('''
                    INSERT INTO agent_state_history (
                        state, timestamp, uptime_seconds, processed_articles,
                        published_articles, current_operation, last_error,
                        memory_usage_mb, cpu_usage_percent, active_operations, details
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    current_info.state.value,
                    current_info.timestamp.isoformat(),
                    current_info.uptime_seconds,
                    current_info.processed_articles,
                    current_info.published_articles,
                    current_info.current_operation,
                    current_info.last_error,
                    current_info.memory_usage_mb,
                    current_info.cpu_usage_percent,
                    json.dumps(current_info.active_operations),
                    json.dumps({})
                ))

                # حفظ العمليات
                for operation in self._operations.values():
                    cursor.execute('''
                        INSERT OR REPLACE INTO agent_operations (
                            operation_id, operation_type, state, start_time, end_time,
                            progress_percent, details, error_message
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        operation.operation_id,
                        operation.operation_type,
                        operation.state.value,
                        operation.start_time.isoformat(),
                        operation.end_time.isoformat() if operation.end_time else None,
                        operation.progress_percent,
                        json.dumps(operation.details),
                        operation.error_message
                    ))

                conn.commit()

        except Exception as e:
            logger.error(f"❌ فشل في حفظ الحالة في قاعدة البيانات: {e}")

    def set_state(self, new_state: AgentState, operation: Optional[str] = None, error: Optional[str] = None):
        """تعيين حالة جديدة للوكيل"""
        with self._lock:
            old_state = self._current_state
            self._current_state = new_state

            logger.info(f"🔄 تغيير حالة الوكيل: {old_state.value} → {new_state.value}")

            if operation:
                logger.info(f"📋 العملية الحالية: {operation}")

            if error:
                logger.error(f"❌ خطأ: {error}")

            # حفظ الحالة
            self.save_state()

    def get_current_state(self) -> AgentState:
        """الحصول على الحالة الحالية"""
        return self._current_state

    def get_current_state_info(self) -> AgentStateInfo:
        """الحصول على معلومات الحالة الحالية"""
        import psutil

        try:
            process = psutil.Process()
            memory_usage = process.memory_info().rss / (1024 * 1024)  # MB
            cpu_usage = process.cpu_percent()
        except:
            memory_usage = 0
            cpu_usage = 0

        uptime = (datetime.now() - self._startup_time).total_seconds()

        # الحصول على إحصائيات من قاعدة البيانات
        try:
            stats = db.get_stats_summary(1)
            processed_articles = stats.get('articles_processed', 0)
            published_articles = stats.get('articles_published', 0)
        except:
            processed_articles = 0
            published_articles = 0

        active_operations = [
            op.operation_id for op in self._operations.values()
            if op.state in [OperationState.RUNNING, OperationState.PENDING]
        ]

        current_operation = None
        if active_operations:
            # أحدث عملية نشطة
            running_ops = [
                op for op in self._operations.values()
                if op.state == OperationState.RUNNING
            ]
            if running_ops:
                current_operation = max(running_ops, key=lambda x: x.start_time).operation_type

        # آخر خطأ
        last_error = None
        error_ops = [
            op for op in self._operations.values()
            if op.state == OperationState.FAILED and op.error_message
        ]
        if error_ops:
            last_error = max(error_ops, key=lambda x: x.start_time).error_message

        return AgentStateInfo(
            state=self._current_state,
            timestamp=datetime.now(),
            uptime_seconds=uptime,
            processed_articles=processed_articles,
            published_articles=published_articles,
            current_operation=current_operation,
            last_error=last_error,
            memory_usage_mb=memory_usage,
            cpu_usage_percent=cpu_usage,
            active_operations=active_operations
        )

    def start_operation(self, operation_type: str, details: Dict[str, Any] = None) -> str:
        """بدء عملية جديدة"""
        operation_id = f"{operation_type}_{int(time.time() * 1000)}"

        operation = OperationInfo(
            operation_id=operation_id,
            operation_type=operation_type,
            state=OperationState.RUNNING,
            start_time=datetime.now(),
            end_time=None,
            progress_percent=0.0,
            details=details or {},
            error_message=None
        )

        with self._lock:
            self._operations[operation_id] = operation

        logger.info(f"🚀 بدء عملية جديدة: {operation_type} (ID: {operation_id})")
        self.save_state()

        return operation_id

    def update_operation_progress(self, operation_id: str, progress: float, details: Dict[str, Any] = None):
        """تحديث تقدم العملية"""
        with self._lock:
            if operation_id in self._operations:
                self._operations[operation_id].progress_percent = progress
                if details:
                    self._operations[operation_id].details.update(details)

                logger.debug(f"📊 تحديث تقدم العملية {operation_id}: {progress:.1f}%")

    def complete_operation(self, operation_id: str, success: bool = True, error_message: str = None):
        """إنهاء العملية"""
        with self._lock:
            if operation_id in self._operations:
                operation = self._operations[operation_id]
                operation.end_time = datetime.now()
                operation.progress_percent = 100.0

                if success:
                    operation.state = OperationState.COMPLETED
                    logger.info(f"✅ اكتملت العملية بنجاح: {operation.operation_type} (ID: {operation_id})")
                else:
                    operation.state = OperationState.FAILED
                    operation.error_message = error_message
                    logger.error(f"❌ فشلت العملية: {operation.operation_type} (ID: {operation_id}) - {error_message}")

                self.save_state()

    def pause_operation(self, operation_id: str):
        """إيقاف العملية مؤقتاً"""
        with self._lock:
            if operation_id in self._operations:
                self._operations[operation_id].state = OperationState.PAUSED
                logger.info(f"⏸️ تم إيقاف العملية مؤقتاً: {operation_id}")
                self.save_state()

    def resume_operation(self, operation_id: str):
        """استئناف العملية"""
        with self._lock:
            if operation_id in self._operations:
                self._operations[operation_id].state = OperationState.RUNNING
                logger.info(f"▶️ تم استئناف العملية: {operation_id}")
                self.save_state()

    def cancel_operation(self, operation_id: str):
        """إلغاء العملية"""
        with self._lock:
            if operation_id in self._operations:
                self._operations[operation_id].state = OperationState.CANCELLED
                self._operations[operation_id].end_time = datetime.now()
                logger.info(f"🚫 تم إلغاء العملية: {operation_id}")
                self.save_state()

    def get_active_operations(self) -> List[OperationInfo]:
        """الحصول على العمليات النشطة"""
        return [
            op for op in self._operations.values()
            if op.state in [OperationState.RUNNING, OperationState.PENDING, OperationState.PAUSED]
        ]

    def get_operation_info(self, operation_id: str) -> Optional[OperationInfo]:
        """الحصول على معلومات عملية محددة"""
        return self._operations.get(operation_id)

    def cleanup_old_operations(self, days: int = 7):
        """تنظيف العمليات القديمة"""
        cutoff_date = datetime.now() - timedelta(days=days)

        with self._lock:
            operations_to_remove = []

            for op_id, operation in self._operations.items():
                if (operation.state in [OperationState.COMPLETED, OperationState.FAILED, OperationState.CANCELLED] and
                    operation.end_time and operation.end_time < cutoff_date):
                    operations_to_remove.append(op_id)

            for op_id in operations_to_remove:
                del self._operations[op_id]

            if operations_to_remove:
                logger.info(f"🧹 تم تنظيف {len(operations_to_remove)} عملية قديمة")
                self.save_state()

    def check_database_state(self) -> Dict[str, Any]:
        """فحص حالة قاعدة البيانات"""
        logger.info("🔍 فحص حالة قاعدة البيانات...")

        # فحص سلامة قاعدة البيانات
        is_healthy, issues = self.db_checker.check_database_integrity()

        # الحصول على الإحصائيات
        stats = self.db_checker.get_database_stats()

        result = {
            'healthy': is_healthy,
            'issues': issues,
            'stats': stats,
            'recommendations': []
        }

        # إضافة توصيات
        if not is_healthy:
            result['recommendations'].append("إصلاح مشاكل قاعدة البيانات")

        if stats.get('vacuum_needed', False):
            result['recommendations'].append("تنظيف قاعدة البيانات (VACUUM)")

        if stats.get('file_size_mb', 0) > 100:
            result['recommendations'].append("أرشفة البيانات القديمة")

        logger.info(f"📊 نتيجة فحص قاعدة البيانات: {'صحية' if is_healthy else 'تحتاج إصلاح'}")

        return result

    def prepare_for_shutdown(self) -> bool:
        """التحضير للإيقاف الآمن"""
        logger.info("🛑 التحضير للإيقاف الآمن...")

        self.set_state(AgentState.STOPPING, "إيقاف الوكيل")

        # إيقاف العمليات النشطة
        active_operations = self.get_active_operations()

        if active_operations:
            logger.info(f"⏸️ إيقاف {len(active_operations)} عملية نشطة...")

            for operation in active_operations:
                if operation.state == OperationState.RUNNING:
                    self.pause_operation(operation.operation_id)

        # حفظ الحالة النهائية
        self.save_state()

        # تنظيف العمليات القديمة
        self.cleanup_old_operations()

        logger.info("✅ تم التحضير للإيقاف الآمن")
        return True

    def prepare_for_startup(self) -> bool:
        """التحضير لبدء التشغيل"""
        logger.info("🚀 التحضير لبدء التشغيل...")

        self.set_state(AgentState.STARTING, "بدء تشغيل الوكيل")

        # فحص حالة قاعدة البيانات
        db_state = self.check_database_state()

        if not db_state['healthy']:
            logger.warning("⚠️ قاعدة البيانات تحتاج إصلاح")
            for issue in db_state['issues']:
                logger.warning(f"   • {issue}")

            # محاولة إصلاح المشاكل البسيطة
            try:
                self._auto_fix_database_issues(db_state['issues'])
            except Exception as e:
                logger.error(f"❌ فشل في إصلاح قاعدة البيانات: {e}")
                return False

        # استئناف العمليات المتوقفة
        paused_operations = [
            op for op in self._operations.values()
            if op.state == OperationState.PAUSED
        ]

        if paused_operations:
            logger.info(f"▶️ استئناف {len(paused_operations)} عملية متوقفة...")
            for operation in paused_operations:
                self.resume_operation(operation.operation_id)

        self.set_state(AgentState.RUNNING, "الوكيل جاهز للعمل")

        logger.info("✅ تم التحضير لبدء التشغيل بنجاح")
        return True

    def _auto_fix_database_issues(self, issues: List[str]):
        """إصلاح تلقائي لمشاكل قاعدة البيانات"""
        logger.info("🔧 محاولة إصلاح مشاكل قاعدة البيانات...")

        for issue in issues:
            if "غير موجود" in issue and "جدول" in issue:
                # إعادة إنشاء الجداول المفقودة
                logger.info(f"🔨 إعادة إنشاء الجداول المفقودة...")
                db.create_tables()
                break

        # تنظيف قاعدة البيانات إذا لزم الأمر
        try:
            with sqlite3.connect(db.db_path) as conn:
                conn.execute("VACUUM")
                logger.info("🧹 تم تنظيف قاعدة البيانات")
        except Exception as e:
            logger.warning(f"⚠️ فشل في تنظيف قاعدة البيانات: {e}")

# إنشاء مثيل مدير حالة الوكيل
agent_state_manager = AgentStateManager()
