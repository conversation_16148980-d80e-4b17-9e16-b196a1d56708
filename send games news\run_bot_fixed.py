#!/usr/bin/env python3
"""
سكريبت تشغيل البوت مع الإصلاحات المطبقة
يتضمن فحص أولي وتطبيق الإصلاحات عند الحاجة
"""

import sys
import os
import subprocess
import logging
from datetime import datetime

# إعداد التسجيل
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_and_fix_dependencies():
    """فحص وإصلاح التبعيات"""
    logger.info("🔍 فحص التبعيات...")
    
    try:
        # فحص python-telegram-bot
        import telegram
        version = telegram.__version__
        
        if not version.startswith("20.6"):
            logger.warning(f"⚠️ إصدار python-telegram-bot غير صحيح: {version}")
            logger.info("🔧 تطبيق إصلاح Telegram Bot...")
            
            subprocess.run("pip uninstall python-telegram-bot -y", shell=True)
            subprocess.run("pip install python-telegram-bot==20.6", shell=True)
            
            logger.info("✅ تم إصلاح python-telegram-bot")
        else:
            logger.info("✅ python-telegram-bot بالإصدار الصحيح")
            
    except ImportError:
        logger.error("❌ python-telegram-bot غير مثبت")
        logger.info("🔧 تثبيت python-telegram-bot...")
        subprocess.run("pip install python-telegram-bot==20.6", shell=True)
    
    # فحص yt-dlp
    try:
        import yt_dlp
        logger.info("✅ yt-dlp متوفر")
    except ImportError:
        logger.warning("⚠️ yt-dlp غير مثبت")
        logger.info("🔧 تثبيت yt-dlp...")
        subprocess.run("pip install yt-dlp>=2023.12.30", shell=True)
    
    # فحص ffmpeg-python
    try:
        import ffmpeg
        logger.info("✅ ffmpeg-python متوفر")
    except ImportError:
        logger.warning("⚠️ ffmpeg-python غير مثبت")
        logger.info("🔧 تثبيت ffmpeg-python...")
        subprocess.run("pip install ffmpeg-python>=0.2.0", shell=True)

def check_ffmpeg():
    """فحص FFmpeg"""
    try:
        result = subprocess.run("ffmpeg -version", shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            logger.info("✅ FFmpeg متوفر")
            return True
        else:
            logger.warning("⚠️ FFmpeg غير متوفر")
            logger.info("💡 يرجى تثبيت FFmpeg من: https://ffmpeg.org/download.html")
            return False
    except Exception:
        logger.warning("⚠️ لا يمكن فحص FFmpeg")
        return False

def check_environment():
    """فحص متغيرات البيئة"""
    logger.info("🔑 فحص متغيرات البيئة...")
    
    required_vars = [
        "GEMINI_API_KEY",
        "TELEGRAM_BOT_TOKEN",
        "BLOGGER_CLIENT_ID",
        "BLOGGER_CLIENT_SECRET",
        "BLOGGER_BLOG_ID"
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        logger.error(f"❌ متغيرات البيئة المفقودة: {', '.join(missing_vars)}")
        logger.info("💡 يرجى إعداد ملف .env مع المتغيرات المطلوبة")
        return False
    else:
        logger.info("✅ جميع متغيرات البيئة متوفرة")
        return True

def run_bot():
    """تشغيل البوت"""
    logger.info("🚀 بدء تشغيل البوت...")
    
    try:
        # إضافة المسار الحالي
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        # استيراد وتشغيل البوت
        from main import GamingNewsBot
        import asyncio
        
        bot = GamingNewsBot()
        asyncio.run(bot.run())
        
    except KeyboardInterrupt:
        logger.info("⏹️ تم إيقاف البوت بواسطة المستخدم")
    except Exception as e:
        logger.error(f"❌ خطأ في تشغيل البوت: {e}")
        logger.info("💡 يرجى فحص السجلات للمزيد من التفاصيل")

def main():
    """الدالة الرئيسية"""
    logger.info("=" * 60)
    logger.info("🤖 وكيل أخبار الألعاب - الإصدار المحسن")
    logger.info(f"⏰ وقت البدء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info("=" * 60)
    
    # 1. فحص وإصلاح التبعيات
    check_and_fix_dependencies()
    
    # 2. فحص FFmpeg
    ffmpeg_available = check_ffmpeg()
    if not ffmpeg_available:
        logger.warning("⚠️ ميزة Whisper قد لا تعمل بدون FFmpeg")
    
    # 3. فحص متغيرات البيئة
    env_ok = check_environment()
    if not env_ok:
        logger.error("❌ لا يمكن تشغيل البوت بدون متغيرات البيئة المطلوبة")
        return
    
    # 4. تشغيل البوت
    logger.info("✅ جميع الفحوصات اكتملت بنجاح")
    logger.info("🚀 بدء تشغيل البوت...")
    
    run_bot()

if __name__ == "__main__":
    main()
