#!/usr/bin/env python3
"""
إصلاح مشكلة المنافذ في Render
Port Configuration Fix for Render
"""

import os
import logging

logger = logging.getLogger(__name__)

def fix_port_conflicts():
    """إصلاح تعارض المنافذ"""
    logger.info("🔧 إصلاح تعارض المنافذ...")
    
    # الحصول على المنفذ الرئيسي من Render
    main_port = os.environ.get('PORT', '10000')
    
    # تعيين منافذ مختلفة لكل خدمة
    port_config = {
        'MAIN_PORT': main_port,
        'WEB_SERVER_PORT': main_port,  # استخدام نفس المنفذ للخدمة الرئيسية
        'TELEGRAM_WEB_PORT': str(int(main_port) + 1),
        'HEALTH_CHECK_PORT': str(int(main_port) + 2)
    }
    
    for key, value in port_config.items():
        os.environ[key] = value
        logger.info(f"✅ {key} = {value}")
    
    # تعطيل الخدمات الإضافية التي تسبب تعارض
    os.environ['DISABLE_MULTIPLE_SERVERS'] = 'true'
    os.environ['SINGLE_PORT_MODE'] = 'true'
    
    logger.info("✅ تم إصلاح تعارض المنافذ")
    return main_port

def create_single_server_config():
    """إنشاء تكوين خادم واحد"""
    logger.info("🔧 إنشاء تكوين خادم واحد...")
    
    config = {
        "server_mode": "single",
        "primary_port": os.environ.get('PORT', '10000'),
        "services": {
            "web_server": True,
            "telegram_web": False,  # تعطيل مؤقت
            "ngrok": False,
            "health_check": True
        }
    }
    
    # حفظ التكوين في متغيرات البيئة
    for key, value in config.items():
        if isinstance(value, dict):
            for sub_key, sub_value in value.items():
                env_key = f"{key.upper()}_{sub_key.upper()}"
                os.environ[env_key] = str(sub_value)
        else:
            os.environ[key.upper()] = str(value)
    
    logger.info("✅ تم إنشاء تكوين خادم واحد")

if __name__ == "__main__":
    fix_port_conflicts()
    create_single_server_config()
