#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل محسن للبوت في بيئة Render
يحل مشاكل الشبكة والاتصال تلقائياً
"""

import os
import sys
import logging
import asyncio
import signal
from pathlib import Path
import requests
from requests.adapters import HTTPAdapter
try:
    from urllib3.util.retry import Retry
except Exception:
    Retry = None

# استيراد تكوينات الشبكة المساعدة
try:
    from network_config import (
        apply_network_fixes as apply_nc_fixes,
        log_network_info,
        get_proxy_url,
        test_telegram_api
    )
except Exception:
    apply_nc_fixes = None
    log_network_info = None
    get_proxy_url = lambda: None
    async def test_telegram_api(token: str) -> bool:
        return False

# إعداد نظام السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def setup_environment():
    """إعداد البيئة للتشغيل في Render"""
    logger.info("🚀 إعداد البيئة لـ Render...")

    # تعيين متغيرات البيئة المطلوبة
    os.environ.setdefault('RENDER', 'true')
    os.environ.setdefault('PYTHONUNBUFFERED', '1')
    os.environ.setdefault('PYTHONIOENCODING', 'utf-8')

    # إنشاء المجلدات المطلوبة
    required_dirs = [
        'logs',
        'temp',
        'cache',
        'security/logs',
        'security/quarantine',
        'security_logs'
    ]

    for dir_path in required_dirs:
        Path(dir_path).mkdir(parents=True, exist_ok=True)
        logger.info(f"✅ مجلد: {dir_path}")

    # إنشاء الملفات المطلوبة
    required_files = [
        'user_channels.json',
        'all_users.json',
        'user_feedback.json',
        'user_mods_status.json',
        'user_blocked_mods.json',
        'user_invitations.json',
        'user_subscriptions.json',
        'user_feature_activation.json',
        'admin_settings.json',
        'admin_processed_mods.json',
        'pending_publication.json'
    ]

    for file_path in required_files:
        if not Path(file_path).exists():
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write('{}')
            logger.info(f"✅ ملف: {file_path}")


def create_resilient_session() -> requests.Session:
    """إنشاء جلسة Requests مع إعدادات إعادة محاولة وبروكسي إن وجد"""
    session = requests.Session()
    adapter_kwargs = {
        'pool_connections': 50,
        'pool_maxsize': 50,
        'max_retries': Retry(
            total=8,
            connect=8,
            read=8,
            backoff_factor=1.5,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["HEAD", "GET", "OPTIONS", "POST"],
            raise_on_status=False,
        ) if Retry else 0,
    }
    adapter = HTTPAdapter(**adapter_kwargs)
    session.mount('http://', adapter)
    session.mount('https://', adapter)

    # بروكسي إن وجد
    proxy_url = get_proxy_url() if callable(get_proxy_url) else None
    if proxy_url:
        session.proxies.update({'http': proxy_url, 'https': proxy_url})

    session.headers.update({'User-Agent': 'RenderBot/1.0'})
    return session

RESILIENT_SESSION = None


async def clear_telegram_conflicts():
    """مسح تعارضات Telegram بطريقة شاملة"""
    logger.info("🔧 مسح تعارضات Telegram...")

    try:
        bot_token = os.getenv('BOT_TOKEN') or os.getenv('TELEGRAM_BOT_TOKEN')
        if not bot_token:
            logger.warning("⚠️ لم يتم العثور على BOT_TOKEN")
            return False

        # مسح webhook مع إعدادات شاملة
        try:
            response = requests.post(
                f"https://api.telegram.org/bot{bot_token}/deleteWebhook",
                json={"drop_pending_updates": True},
                timeout=20
            )
            if response.status_code == 200 and response.json().get('ok'):
                logger.info("✅ تم مسح webhook")
        except Exception as e:
            logger.warning(f"⚠️ تحذير في مسح webhook: {e}")

        # مسح جميع التحديثات المعلقة بطريقة متقدمة
        try:
            for attempt in range(5):  # 5 محاولات
                response = requests.get(
                    f"https://api.telegram.org/bot{bot_token}/getUpdates",
                    params={"timeout": 1, "limit": 100},
                    timeout=20
                )

                if response.status_code == 200:
                    result = response.json()
                    if result.get('ok') and result.get('result'):
                        updates = result['result']
                        if updates:
                            last_update_id = updates[-1]['update_id']
                            # مسح التحديثات
                            requests.get(
                                f"https://api.telegram.org/bot{bot_token}/getUpdates",
                                params={"offset": last_update_id + 1, "timeout": 1},
                                timeout=15
                            )
                            logger.info(f"✅ تم مسح {len(updates)} تحديث معلق - المحاولة {attempt + 1}")
                        else:
                            logger.info("✅ لا توجد تحديثات معلقة")
                            break
                    else:
                        break
                else:
                    logger.warning(f"⚠️ فشل في جلب التحديثات: {response.status_code}")
                    break

                # انتظار قصير بين المحاولات
                await asyncio.sleep(2)

        except Exception as e:
            logger.warning(f"⚠️ تحذير في مسح التحديثات: {e}")

        # انتظار إضافي للتأكد من تطبيق التغييرات
        await asyncio.sleep(5)

        logger.info("✅ تم مسح تعارضات Telegram")
        return True

    except Exception as e:
        logger.error(f"❌ خطأ في مسح تعارضات Telegram: {e}")
        return False

async def ensure_telegram_connectivity(token: str) -> bool:
    """التأكد من إمكانية الاتصال بـ Telegram مع إعادة محاولة تدريجية"""
    # محاولة سريعة عبر دالة الاختبار غير المتزامنة إن توفرت
    try:
        if callable(test_telegram_api):
            ok = await test_telegram_api(token)
            if ok:
                return True
    except Exception:
        pass

    # محاولة مباشرة عبر Requests
    global RESILIENT_SESSION
    RESILIENT_SESSION = RESILIENT_SESSION or create_resilient_session()

    url = f"https://api.telegram.org/bot{token}/getMe"
    delays = [2, 5, 10, 20]
    for attempt, delay in enumerate(delays, start=1):
        try:
            resp = RESILIENT_SESSION.get(url, timeout=30)
            if resp.status_code == 200 and resp.json().get('ok'):
                logger.info("✅ اتصال Telegram جاهز")
                return True
            else:
                logger.warning(f"⚠️ محاولة {attempt}: كود {resp.status_code}")
        except Exception as e:
            logger.warning(f"⚠️ محاولة {attempt}: فشل الاتصال بـ Telegram: {e}")
        await asyncio.sleep(delay)

    logger.error("❌ تعذر التأكد من اتصال Telegram بعد عدة محاولات")
    return False

def apply_network_fixes():
    """تطبيق إصلاحات الشبكة لبيئة Render"""
    logger.info("🔧 تطبيق إصلاحات الشبكة...")

    try:
        # تشغيل أداة إصلاح الشبكة
        from render_network_fix import RenderNetworkFixer

        fixer = RenderNetworkFixer()
        results = fixer.run_full_diagnosis()

        if results['basic_connectivity']:
            logger.info("✅ إصلاحات الشبكة تمت بنجاح")
            return True
        else:
            logger.warning("⚠️ بعض مشاكل الشبكة لم تحل، لكن سيتم المتابعة")
            return True  # نتابع حتى لو كانت هناك مشاكل

    except Exception as e:
        logger.warning(f"⚠️ فشل في تطبيق إصلاحات الشبكة: {e}")
        return True  # نتابع حتى لو فشلت الإصلاحات

def check_required_env_vars():
    """فحص متغيرات البيئة المطلوبة مع إصلاح تلقائي آمن"""
    logger.info("🔍 فحص متغيرات البيئة...")

    # تحميل ملف .env إن وجد
    try:
        from dotenv import load_dotenv
        load_dotenv()
    except Exception:
        pass

    fixes_applied: list[str] = []

    # توحيد اسم التوكن: إذا كان BOT_TOKEN موجوداً وليس TELEGRAM_BOT_TOKEN
    bot_token = os.getenv('BOT_TOKEN')
    tg_token = os.getenv('TELEGRAM_BOT_TOKEN')
    if not tg_token and bot_token:
        os.environ['TELEGRAM_BOT_TOKEN'] = bot_token
        fixes_applied.append("TELEGRAM_BOT_TOKEN 9 BOT_TOKEN")

    # التحقق النهائي
    missing_vars: list[str] = []

    # تحقق من وجود أي من التوكنين
    final_token = os.getenv('TELEGRAM_BOT_TOKEN') or os.getenv('BOT_TOKEN')
    if not final_token:
        missing_vars.append('BOT_TOKEN/TELEGRAM_BOT_TOKEN')

    for var in ['SUPABASE_URL', 'SUPABASE_KEY']:
        if not os.getenv(var):
            missing_vars.append(var)
        else:
            # إخفاء جزء من القيمة للأمان
            value = os.getenv(var)
            masked_value = value[:10] + "..." if len(value) > 10 else value
            logger.info(f"✅ {var}: {masked_value}")

    if fixes_applied:
        logger.info(f"🔧 تم تطبيق إصلاحات: {', '.join(fixes_applied)}")

    if missing_vars:
        logger.error(f"❌ متغيرات البيئة المفقودة: {', '.join(missing_vars)}")
        return False

    logger.info("✅ جميع متغيرات البيئة الأساسية موجودة")
    return True

def setup_signal_handlers():
    """إعداد معالجات الإشارات للإغلاق الآمن"""
    def signal_handler(signum, frame):
        logger.info(f"📡 تم استلام إشارة {signum}، إغلاق آمن...")
        sys.exit(0)

    signal.signal(signal.SIGTERM, signal_handler)
    signal.signal(signal.SIGINT, signal_handler)

async def start_bot():
    """تشغيل البوت الرئيسي"""
    logger.info("🤖 بدء تشغيل البوت...")

    try:
        # استيراد وتشغيل البوت الرئيسي
        import main

        # تشغيل البوت
        await main.main()

    except KeyboardInterrupt:
        logger.info("⏹️ تم إيقاف البوت بواسطة المستخدم")
    except Exception as e:
        logger.error(f"❌ خطأ في تشغيل البوت: {e}")
        raise

def run_web_server():
    """تشغيل خادم الويب للحفاظ على الخدمة نشطة"""
    try:
        from web_server import run_web_server as start_web_server
        logger.info("🌐 تشغيل خادم الويب...")
        start_web_server()
    except ImportError:
        logger.warning("⚠️ خادم الويب غير متوفر")
    except Exception as e:
        logger.error(f"❌ خطأ في تشغيل خادم الويب: {e}")

async def main():
    """الدالة الرئيسية"""
    logger.info("🚀 بدء تشغيل البوت في بيئة Render")
    logger.info("=" * 50)

    try:
        # إعداد معالجات الإشارات
        setup_signal_handlers()

        # تطبيق إصلاحات Telegram أولاً لتجنب التعارضات
        await clear_telegram_conflicts()

        # إعداد البيئة
        setup_environment()

        # تطبيق تحسينات الشبكة الإضافية إن توفرت
        if apply_nc_fixes:
            try:
                apply_nc_fixes()
            except Exception:
                pass
        if log_network_info:
            try:
                log_network_info()
            except Exception:
                pass

        # تأكيد اتصال Telegram قبل بدء البوت لتجنب توقفات لاحقة
        final_token = os.getenv('TELEGRAM_BOT_TOKEN') or os.getenv('BOT_TOKEN')
        if not await ensure_telegram_connectivity(final_token):
            logger.warning("⚠️ لم يتم التأكد من اتصال Telegram، سنواصل التشغيل وقد يستغرق الاتصال وقتاً أطول")

        # تطبيق إصلاحات الشبكة
        network_ok = apply_network_fixes()
        if not network_ok:
            logger.warning("⚠️ مشاكل في الشبكة، لكن سيتم المتابعة")

        # فحص متغيرات البيئة
        env_ok = check_required_env_vars()
        if not env_ok:
            logger.error("❌ متغيرات البيئة مفقودة، لا يمكن تشغيل البوت")
            sys.exit(1)

        # تحديد نوع الخدمة
        service_type = os.getenv('RENDER_SERVICE_TYPE', 'worker')
        logger.info(f"📋 نوع الخدمة: {service_type}")

        if service_type == 'web':
            # إذا كانت خدمة ويب، نشغل خادم الويب مع البوت
            logger.info("🌐 تشغيل كخدمة ويب...")

            # تشغيل البوت في مهمة منفصلة
            bot_task = asyncio.create_task(start_bot())

            # تشغيل خادم الويب في خيط منفصل
            import threading
            web_thread = threading.Thread(target=run_web_server, daemon=True)
            web_thread.start()

            # انتظار البوت
            await bot_task

        else:
            # إذا كانت خدمة عامل، نشغل البوت فقط
            logger.info("⚙️ تشغيل كخدمة عامل...")
            await start_bot()

    except KeyboardInterrupt:
        logger.info("⏹️ تم إيقاف البوت بواسطة المستخدم")
    except Exception as e:
        logger.error(f"❌ خطأ في التشغيل: {e}")
        sys.exit(1)

if __name__ == "__main__":
    # تشغيل البوت
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("⏹️ تم إيقاف البوت")
    except Exception as e:
        logger.error(f"❌ خطأ في التشغيل: {e}")
        sys.exit(1)
