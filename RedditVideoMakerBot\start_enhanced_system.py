#!/usr/bin/env python3
"""
تشغيل النظام المحسن مع البوت التفاعلي
يوفر تحكم كامل عبر التلغرام مع أزرار تشغيل وإيقاف
"""

import asyncio
import logging
import sys
import threading
import time
from pathlib import Path

# إعداد السجلات
Path("logs").mkdir(exist_ok=True)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/enhanced_system.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

def display_welcome():
    """عرض رسالة الترحيب"""
    print("""
    🤖 نظام إنشاء الفيديوهات التلقائي المحسن
    ==========================================
    
    ✅ بوت تلغرام تفاعلي مع أزرار
    ✅ تحكم كامل في النظام (تشغيل/إيقاف)
    ✅ مراقبة مستمرة وإشعارات فورية
    ✅ نقل جميع الرسائل عبر التلغرام
    ✅ واجهة سهلة الاستخدام
    
    المميزات:
    • إنشاء فيديوهات تلقائي من Reddit
    • نشر على YouTube Shorts
    • جدولة ذكية كل 10 ساعات
    • إشعارات مفصلة لكل خطوة
    • عرض السجلات والإحصائيات
    
    الأوامر المتاحة في التلغرام:
    /start - القائمة الرئيسية مع الأزرار
    /run - تشغيل النظام
    /stop - إيقاف النظام
    /status - حالة النظام التفصيلية
    /logs - عرض آخر السجلات
    /help - المساعدة الكاملة
    
    """)

async def start_telegram_bot():
    """بدء البوت التفاعلي"""
    try:
        from automation.telegram_bot import EnhancedTelegramBot
        
        logger.info("🚀 بدء تشغيل البوت التفاعلي...")
        
        # إنشاء البوت
        bot = EnhancedTelegramBot()
        
        # إرسال رسالة بدء التشغيل
        bot.send_startup_message()
        
        # بدء البوت التفاعلي
        await bot.start_interactive_bot()
        
    except Exception as e:
        logger.error(f"❌ فشل في تشغيل البوت: {e}")
        raise

def start_dashboard():
    """بدء واجهة المراقبة في خيط منفصل"""
    try:
        logger.info("🌐 بدء تشغيل واجهة المراقبة...")
        from automation.dashboard import start_dashboard_thread
        dashboard_thread = start_dashboard_thread()
        logger.info("✅ تم تشغيل واجهة المراقبة على http://localhost:5000")
        return dashboard_thread
    except Exception as e:
        logger.error(f"❌ فشل في تشغيل واجهة المراقبة: {e}")
        return None

def check_requirements():
    """فحص المتطلبات الأساسية"""
    logger.info("🔍 فحص المتطلبات الأساسية...")
    
    # فحص ملفات الإعدادات
    required_files = ['config.toml']
    missing_files = []
    
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)
    
    if missing_files:
        logger.error(f"❌ ملفات مطلوبة مفقودة: {', '.join(missing_files)}")
        return False
    
    # إنشاء المجلدات المطلوبة
    required_dirs = ['logs', 'results', 'assets']
    for dir_name in required_dirs:
        Path(dir_name).mkdir(exist_ok=True)
    
    logger.info("✅ جميع المتطلبات متوفرة")
    return True

def test_integrations():
    """اختبار التكاملات الأساسية"""
    logger.info("🧪 اختبار التكاملات...")
    
    # اختبار نظام الرسائل
    try:
        from automation.message_router import send_telegram_message
        send_telegram_message("🧪 اختبار نظام الرسائل - النظام يعمل!")
        logger.info("✅ نظام الرسائل يعمل")
    except Exception as e:
        logger.warning(f"⚠️ مشكلة في نظام الرسائل: {e}")
    
    # اختبار Gemini AI
    try:
        from automation.gemini_content_generator import GeminiContentGenerator
        generator = GeminiContentGenerator()
        test_result = generator.generate_content(
            "Test Reddit Post", 
            "This is a test content for AI generation",
            "AskReddit"
        )
        if test_result and test_result.get('title'):
            logger.info("✅ Gemini AI يعمل")
        else:
            logger.warning("⚠️ مشكلة في Gemini AI")
    except Exception as e:
        logger.warning(f"⚠️ مشكلة في Gemini AI: {e}")

async def main():
    """الدالة الرئيسية"""
    try:
        # عرض رسالة الترحيب
        display_welcome()
        
        # فحص المتطلبات
        if not check_requirements():
            logger.error("❌ فشل في فحص المتطلبات")
            sys.exit(1)
        
        # اختبار التكاملات
        test_integrations()
        
        # بدء واجهة المراقبة في خيط منفصل
        dashboard_thread = threading.Thread(target=start_dashboard, daemon=True)
        dashboard_thread.start()
        
        # انتظار قصير لبدء واجهة المراقبة
        await asyncio.sleep(2)
        
        logger.info("🎉 تم تشغيل النظام الكامل بنجاح!")
        logger.info("💡 استخدم البوت في التلغرام للتحكم في النظام")
        logger.info("🌐 واجهة المراقبة: http://localhost:5000")
        
        # بدء البوت التفاعلي (هذا سيبقي البرنامج يعمل)
        await start_telegram_bot()
        
    except KeyboardInterrupt:
        logger.info("⏹️ تم إيقاف النظام بواسطة المستخدم")
    except Exception as e:
        logger.error(f"❌ خطأ في النظام الرئيسي: {e}")
        
        # إرسال إشعار خطأ حرج
        try:
            from automation.message_router import send_telegram_error
            send_telegram_error("خطأ حرج في النظام الرئيسي", str(e))
        except:
            pass
            
        sys.exit(1)

if __name__ == "__main__":
    # التحقق من إصدار Python
    if sys.version_info.major != 3 or sys.version_info.minor < 10:
        print("❌ يتطلب Python 3.10 أو أحدث")
        sys.exit(1)
    
    # تشغيل النظام الكامل
    asyncio.run(main())
