# ملف تكوين Render لوكيل أخبار الألعاب
services:
  - type: web
    name: gaming-news-agent
    env: python
    plan: free
    buildCommand: |
      pip install --upgrade pip
      pip install -r requirements.txt
      python setup_env.py
      python render_network_fix.py
      python -c "print('✅ Build completed successfully')"
    startCommand: python start_render_safe.py
    envVars:
      - key: BOT_TOKEN
        value: **********************************************
      - key: TELEGRAM_BOT_TOKEN
        value: **********************************************
      - key: ADMIN_CHAT_ID
        value: 7513880877
      - key: SUPABASE_URL
        value: https://ytqxxodyecdeosnqoure.supabase.co
      - key: SUPABASE_KEY
        value: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4
      - key: ENVIRONMENT
        value: production
      - key: DEBUG
        value: false
      - key: LOG_LEVEL
        value: INFO
      - key: PYTHON_VERSION
        value: 3.11.0
      - key: RENDER
        value: true
      - key: RENDER_SERVICE_TYPE
        value: web
      - key: PYTHONUNBUFFERED
        value: 1
      - key: PYTHONIOENCODING
        value: utf-8
      - key: PORT
        value: 10000
    healthCheckPath: /health
    autoDeploy: true
