#!/usr/bin/env python3
"""
مولد المحتوى باستخدام Gemini AI
ينشئ عناوين وأوصاف جذابة بطابع شبابي للفيديوهات
"""

import google.generativeai as genai
import logging
import json
import re
from typing import Dict, List, Optional, Tuple
import random
import time

# إعداد السجلات
logger = logging.getLogger(__name__)

class GeminiContentGenerator:
    def __init__(self, api_key: str = "AIzaSyAnnYx15P89izzx_rJy9en4kaLVJ_Nuk40"):
        """
        إعداد مولد المحتوى باستخدام Gemini
        
        Args:
            api_key: مفتاح API الخاص بـ Gemini
        """
        self.api_key = api_key
        self.model = None
        self._setup_gemini()
        
        # قوالب العناوين الشبابية بالإنجليزية
        self.title_templates = [
            "This Person Did Something UNBELIEVABLE! 😱",
            "Crazy Story That Actually Happened! 🤯",
            "You Won't Believe What Happened Next! 😲",
            "The Weirdest Story You'll Hear Today! 🔥",
            "This Is What Happened When... 💀",
            "Story That Will Make You Rethink Everything! 🧠",
            "Wait... This Actually Happened?! 😳",
            "The Most Shocking Story You'll Ever Hear! ⚡",
            "This Person Changed Their Life In A CRAZY Way! 🚀",
            "Story That Will Stay With You Forever! 💭"
        ]
        
        # هاشتاغات شائعة بالإنجليزية
        self.popular_hashtags = [
            "#Shorts", "#Reddit", "#Stories", "#Viral", "#Entertainment", "#English",
            "#TrueStory", "#Crazy", "#Unbelievable", "#ShortVideo", "#Content",
            "#RealStory", "#Amazing", "#Shocking", "#Fun", "#Entertainment",
            "#Tales", "#Experiences", "#Real", "#Share", "#Explore"
        ]
        
    def _setup_gemini(self):
        """إعداد نموذج Gemini"""
        try:
            genai.configure(api_key=self.api_key)
            
            # إعداد النموذج مع معاملات محسنة
            generation_config = {
                "temperature": 0.8,
                "top_p": 0.9,
                "top_k": 40,
                "max_output_tokens": 1000,
            }
            
            safety_settings = [
                {"category": "HARM_CATEGORY_HARASSMENT", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
                {"category": "HARM_CATEGORY_HATE_SPEECH", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
                {"category": "HARM_CATEGORY_SEXUALLY_EXPLICIT", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
                {"category": "HARM_CATEGORY_DANGEROUS_CONTENT", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
            ]
            
            self.model = genai.GenerativeModel(
                model_name="gemini-2.0-flash-exp",
                generation_config=generation_config,
                safety_settings=safety_settings
            )
            
            logger.info("✅ تم إعداد Gemini AI بنجاح")
            
        except Exception as e:
            logger.error(f"❌ فشل في إعداد Gemini AI: {e}")
            raise
            
    def generate_content(self, reddit_title: str, reddit_content: str, 
                        subreddit: str = "AskReddit") -> Dict[str, any]:
        """
        إنشاء عنوان ووصف للفيديو بناءً على محتوى Reddit
        
        Args:
            reddit_title: عنوان المنشور في Reddit
            reddit_content: محتوى المنشور والتعليقات
            subreddit: اسم الـ subreddit
            
        Returns:
            قاموس يحتوي على العنوان والوصف والهاشتاغات
        """
        try:
            # إنشاء prompt محسن
            prompt = self._create_prompt(reddit_title, reddit_content, subreddit)
            
            # إرسال الطلب لـ Gemini
            response = self.model.generate_content(prompt)
            
            if response.text:
                # تحليل الاستجابة
                content = self._parse_response(response.text)
                
                # إضافة هاشتاغات إضافية
                content['hashtags'].extend(self._get_random_hashtags())
                content['hashtags'] = list(set(content['hashtags']))  # إزالة المكررات
                
                logger.info(f"✅ تم إنشاء المحتوى بنجاح: {content['title'][:50]}...")
                return content
            else:
                logger.warning("⚠️ استجابة فارغة من Gemini، استخدام المحتوى الافتراضي")
                return self._get_fallback_content(reddit_title)
                
        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء المحتوى: {e}")
            return self._get_fallback_content(reddit_title)
            
    def _create_prompt(self, reddit_title: str, reddit_content: str, subreddit: str) -> str:
        """إنشاء prompt محسن لـ Gemini"""
        
        # تقصير المحتوى إذا كان طويلاً
        content_preview = reddit_content[:1000] + "..." if len(reddit_content) > 1000 else reddit_content
        
        prompt = f"""
You are an expert in creating engaging content for young audiences on social media platforms, especially YouTube Shorts.

Your task: Create an engaging title and description for a short video based on a Reddit story.

Story Information:
- Source: r/{subreddit}
- Original Title: {reddit_title}
- Content: {content_preview}

Requirements:
1. Catchy title (50-80 characters) with a youthful and curiosity-inducing style
2. Detailed description (200-400 words) that summarizes the story in an engaging way
3. 10-15 diverse and appropriate hashtags

Guidelines:
- Use simple and understandable English
- Make the title spark curiosity without exaggeration
- Add appropriate emojis
- Make the description tell the story in an engaging way
- Avoid offensive or sensitive content
- Focus on YouTube Shorts format
- Use trending language and phrases

Response format:
```json
{{
    "title": "Title here",
    "description": "Detailed description here",
    "hashtags": ["hashtag1", "hashtag2", "..."]
}}
```
"""
        return prompt
        
    def _parse_response(self, response_text: str) -> Dict[str, any]:
        """تحليل استجابة Gemini واستخراج المحتوى"""
        try:
            # البحث عن JSON في الاستجابة
            json_match = re.search(r'```json\s*(\{.*?\})\s*```', response_text, re.DOTALL)
            if json_match:
                json_str = json_match.group(1)
                content = json.loads(json_str)
                
                # التحقق من وجود الحقول المطلوبة
                if all(key in content for key in ['title', 'description', 'hashtags']):
                    return {
                        'title': content['title'][:100],  # حد أقصى 100 حرف
                        'description': content['description'][:4000],  # حد أقصى 4000 حرف
                        'hashtags': content['hashtags'][:20]  # حد أقصى 20 هاشتاغ
                    }
                    
            # إذا فشل تحليل JSON، حاول استخراج المحتوى يدوياً
            return self._manual_parse(response_text)
            
        except Exception as e:
            logger.error(f"خطأ في تحليل استجابة Gemini: {e}")
            raise
            
    def _manual_parse(self, text: str) -> Dict[str, any]:
        """تحليل يدوي للاستجابة إذا فشل تحليل JSON"""
        lines = text.split('\n')
        title = ""
        description = ""
        hashtags = []
        
        current_section = None
        
        for line in lines:
            line = line.strip()
            if 'عنوان' in line or 'title' in line.lower():
                current_section = 'title'
            elif 'وصف' in line or 'description' in line.lower():
                current_section = 'description'
            elif 'هاشتاغ' in line or 'hashtag' in line.lower():
                current_section = 'hashtags'
            elif line and current_section:
                if current_section == 'title' and not title:
                    title = line
                elif current_section == 'description':
                    description += line + " "
                elif current_section == 'hashtags' and line.startswith('#'):
                    hashtags.extend(line.split())
                    
        return {
            'title': title[:100] if title else "Crazy Story From Reddit! 🔥",
            'description': description[:4000] if description else "Amazing story from Reddit",
            'hashtags': hashtags[:20] if hashtags else self._get_random_hashtags()
        }
        
    def _get_fallback_content(self, reddit_title: str) -> Dict[str, any]:
        """محتوى احتياطي في حالة فشل Gemini"""
        # اختيار عنوان عشوائي من القوالب
        template = random.choice(self.title_templates)
        
        # إنشاء وصف بسيط بالإنجليزية
        description = f"""
Crazy story from Reddit!

Original Title: {reddit_title}

This is a true story shared by a user on Reddit platform.
Stories like this show us different experiences and diverse perspectives from people around the world.

Watch the full video to know all the details!

#Shorts #Reddit #Stories #Content
        """.strip()
        
        return {
            'title': template,
            'description': description,
            'hashtags': self._get_random_hashtags()
        }
        
    def _get_random_hashtags(self, count: int = 10) -> List[str]:
        """الحصول على هاشتاغات عشوائية"""
        return random.sample(self.popular_hashtags, min(count, len(self.popular_hashtags)))
        
    def test_generation(self):
        """اختبار مولد المحتوى"""
        test_title = "What's the most embarrassing thing that happened to you?"
        test_content = "I was at a job interview and accidentally called the interviewer 'mom'. The silence was deafening."
        
        result = self.generate_content(test_title, test_content)
        
        print("=== اختبار مولد المحتوى ===")
        print(f"العنوان: {result['title']}")
        print(f"الوصف: {result['description'][:200]}...")
        print(f"الهاشتاغات: {', '.join(result['hashtags'][:10])}")
        
        return result

# دالة مساعدة للاستخدام السهل
def generate_video_content(reddit_title: str, reddit_content: str, 
                          subreddit: str = "AskReddit") -> Dict[str, any]:
    """
    دالة مساعدة لإنشاء محتوى الفيديو
    
    Args:
        reddit_title: عنوان المنشور
        reddit_content: محتوى المنشور
        subreddit: اسم الـ subreddit
        
    Returns:
        قاموس يحتوي على العنوان والوصف والهاشتاغات
    """
    try:
        generator = GeminiContentGenerator()
        return generator.generate_content(reddit_title, reddit_content, subreddit)
    except Exception as e:
        logger.error(f"خطأ في إنشاء محتوى الفيديو: {e}")
        # إرجاع محتوى افتراضي بالإنجليزية
        return {
            'title': "Crazy Story From Reddit! 🔥",
            'description': f"Amazing story: {reddit_title}\n\n#Shorts #Reddit #Stories",
            'hashtags': ["#Shorts", "#Reddit", "#Stories", "#Viral", "#Entertainment"]
        }

if __name__ == "__main__":
    # اختبار الوحدة
    generator = GeminiContentGenerator()
    generator.test_generation()
