-- ===================================================================
-- Fix for ERROR: 42703: column "is_active" does not exist
-- This script adds the "is_active" column to tables where it might be missing.
-- Run this script BEFORE running the main table creation script.
-- ===================================================================

-- Add 'is_active' to 'mods' table if it doesn't exist
ALTER TABLE public.mods ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT true;

-- Add 'is_active' to 'saved_notifications' table if it doesn't exist
ALTER TABLE public.saved_notifications ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT true;

-- Add 'is_active' to 'tasks' table if it doesn't exist
ALTER TABLE public.tasks ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT true;

-- ===================================================================
-- End of fix script
-- ===================================================================
