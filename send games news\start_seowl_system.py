#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل نظام SEOwl الذكي للفهرسة
Start SEOwl Intelligent Indexing System
"""

import asyncio
import sys
import os
import threading
import time
from datetime import datetime

# إضافة المسار الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.logger import logger
from modules.seowl_integration import seowl_integration


def start_seowl_web_interface():
    """تشغيل واجهة الويب لـ SEOwl"""
    try:
        logger.info("🌐 بدء واجهة الويب لـ SEOwl...")
        
        # تشغيل واجهة الويب
        os.system("python seowl_web_interface.py")
        
    except Exception as e:
        logger.error(f"❌ خطأ في تشغيل واجهة الويب: {e}")


def start_seowl_integration():
    """تشغيل تكامل SEOwl مع الوكيل"""
    try:
        logger.info("🔗 بدء تكامل SEOwl مع الوكيل...")
        
        # تفعيل التكامل
        seowl_integration.start_integration()
        
        logger.info("✅ تم تفعيل تكامل SEOwl بنجاح")
        
        # إبقاء النظام يعمل
        while seowl_integration.integration_active:
            time.sleep(60)  # فحص كل دقيقة
            
    except KeyboardInterrupt:
        logger.info("⏹️ تم إيقاف التكامل بواسطة المستخدم")
        seowl_integration.stop_integration()
    except Exception as e:
        logger.error(f"❌ خطأ في تشغيل التكامل: {e}")


def run_seowl_test():
    """تشغيل اختبار سريع لنظام SEOwl"""
    try:
        print("🧪 بدء اختبار نظام SEOwl...")
        print("=" * 60)
        print()
        
        # اختبار استيراد الوحدات
        print("📦 اختبار استيراد الوحدات...")
        
        try:
            from modules.seowl_indexing_checker import seowl_checker
            print("✅ تم استيراد SEOwl Checker بنجاح")
        except Exception as e:
            print(f"❌ فشل استيراد SEOwl Checker: {e}")
            return False
        
        try:
            from modules.seowl_integration import seowl_integration
            print("✅ تم استيراد SEOwl Integration بنجاح")
        except Exception as e:
            print(f"❌ فشل استيراد SEOwl Integration: {e}")
            return False
        
        try:
            from seowl_web_interface import app
            print("✅ تم استيراد واجهة الويب بنجاح")
        except Exception as e:
            print(f"❌ فشل استيراد واجهة الويب: {e}")
            return False
        
        # اختبار إنشاء الكائنات
        print("\n🔧 اختبار إنشاء الكائنات...")
        
        try:
            stats = seowl_checker.get_stats()
            print(f"✅ إحصائيات SEOwl Checker: {stats}")
        except Exception as e:
            print(f"❌ خطأ في إحصائيات SEOwl Checker: {e}")
            return False
        
        try:
            status = seowl_integration.get_integration_status()
            print(f"✅ حالة التكامل: نشط={status.get('integration_active', False)}")
        except Exception as e:
            print(f"❌ خطأ في حالة التكامل: {e}")
            return False
        
        # اختبار واجهة الويب
        print("\n🌐 اختبار واجهة الويب...")
        
        try:
            with app.test_client() as client:
                response = client.get('/')
                
                if response.status_code == 200:
                    print("✅ واجهة الويب تعمل بشكل صحيح")
                else:
                    print(f"⚠️ مشكلة في واجهة الويب: كود {response.status_code}")
                    
        except Exception as e:
            print(f"❌ خطأ في اختبار واجهة الويب: {e}")
            return False
        
        # اختبار API
        print("\n🔌 اختبار API...")
        
        try:
            with app.test_client() as client:
                response = client.get('/api/seowl-status')
                
                if response.status_code == 200:
                    data = response.get_json()
                    if data and data.get('success'):
                        print("✅ API يعمل بشكل صحيح")
                    else:
                        print(f"⚠️ مشكلة في API: {data}")
                else:
                    print(f"⚠️ مشكلة في API: كود {response.status_code}")
                    
        except Exception as e:
            print(f"❌ خطأ في اختبار API: {e}")
            return False
        
        print("\n" + "=" * 60)
        print("🎉 تم إكمال اختبار SEOwl بنجاح!")
        print()
        print("📋 ملخص النتائج:")
        print("✅ جميع الوحدات تعمل بشكل صحيح")
        print("✅ نظام SEOwl جاهز للاستخدام")
        print("✅ واجهة الويب جاهزة")
        print("✅ API يعمل بشكل صحيح")
        print()
        print("🚀 يمكنك الآن تشغيل النظام الكامل!")
        print("🌐 واجهة الإدارة ستكون متاحة على: http://localhost:5003")
        print()
        
        return True
        
    except Exception as e:
        logger.error(f"❌ خطأ في اختبار SEOwl: {e}")
        print(f"❌ خطأ في الاختبار: {e}")
        return False


def show_system_info():
    """عرض معلومات النظام"""
    print("🔍 نظام SEOwl للفهرسة الذكية")
    print("=" * 60)
    print()
    print("📋 الميزات الرئيسية:")
    print("• فحص تلقائي للمقالات بعد 3 ساعات من النشر")
    print("• استخدام SEOwl API لفحص شامل للفهرسة")
    print("• إصلاح تلقائي لمشاكل SEO والأداء")
    print("• واجهة ويب متقدمة للإدارة والمراقبة")
    print("• تكامل كامل مع الوكيل الحالي")
    print()
    print("🌐 الواجهات المتاحة:")
    print("• واجهة SEOwl: http://localhost:5003")
    print("• واجهة Whisper: http://localhost:5001")
    print("• واجهة الفهرسة العامة: http://localhost:5002")
    print()
    print("🔑 مفتاح SEOwl API:")
    print("V0P8IMfg8yDLL2buW3ZNijYnVXQIJICFlWCdBRsmbm1KZ1nA47PlgDhS2zje")
    print()


def main():
    """الدالة الرئيسية"""
    try:
        show_system_info()
        
        print("اختر طريقة التشغيل:")
        print("1. تشغيل واجهة الويب فقط")
        print("2. تشغيل التكامل مع الوكيل")
        print("3. تشغيل النظام الكامل (واجهة + تكامل)")
        print("4. اختبار سريع للنظام")
        print("5. عرض معلومات النظام")
        print()
        
        choice = input("أدخل اختيارك (1-5): ").strip()
        
        if choice == "1":
            print("🌐 تشغيل واجهة الويب...")
            start_seowl_web_interface()
            
        elif choice == "2":
            print("🔗 تشغيل التكامل...")
            start_seowl_integration()
            
        elif choice == "3":
            print("🚀 تشغيل النظام الكامل...")
            
            # تشغيل واجهة الويب في خيط منفصل
            web_thread = threading.Thread(target=start_seowl_web_interface, daemon=True)
            web_thread.start()
            
            # انتظار قليل ثم تشغيل التكامل
            time.sleep(3)
            print("✅ تم تشغيل واجهة الويب")
            print("🌐 متاحة على: http://localhost:5003")
            print()
            
            start_seowl_integration()
            
        elif choice == "4":
            print("🧪 تشغيل اختبار سريع...")
            success = run_seowl_test()
            
            if success:
                start_system = input("\nهل تريد تشغيل النظام الآن؟ (y/n): ").strip().lower()
                
                if start_system in ['y', 'yes', 'نعم', 'ن']:
                    print("🚀 بدء تشغيل النظام...")
                    
                    # تشغيل واجهة الويب في خيط منفصل
                    web_thread = threading.Thread(target=start_seowl_web_interface, daemon=True)
                    web_thread.start()
                    
                    print("✅ تم تشغيل واجهة الويب")
                    print("🌐 يمكنك الوصول إليها على: http://localhost:5003")
                    print()
                    print("اضغط Ctrl+C لإيقاف النظام")
                    
                    # إبقاء النظام يعمل
                    try:
                        while True:
                            time.sleep(1)
                    except KeyboardInterrupt:
                        print("\n⏹️ تم إيقاف النظام")
            
        elif choice == "5":
            show_system_info()
            
        else:
            print("❌ اختيار غير صحيح")
            print("تشغيل الاختبار السريع بدلاً من ذلك...")
            run_seowl_test()
        
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف النظام بواسطة المستخدم")
    except Exception as e:
        logger.error(f"❌ خطأ في التشغيل: {e}")
        print(f"❌ خطأ: {e}")


if __name__ == "__main__":
    main()
