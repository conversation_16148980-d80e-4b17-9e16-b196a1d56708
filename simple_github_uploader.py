#!/usr/bin/env python3
"""
أداة رفع مبسطة للمشاريع على GitHub
Simple GitHub Project Uploader
"""

import os
import subprocess
import requests
import json
from pathlib import Path
import shutil

class SimpleGitHubUploader:
    def __init__(self, token):
        self.token = token
        self.headers = {
            'Authorization': f'token {token}',
            'Accept': 'application/vnd.github.v3+json'
        }
        self.base_url = 'https://api.github.com'
    
    def create_repository(self, name, description="", private=True):
        """إنشاء مستودع جديد على GitHub"""
        data = {
            'name': name,
            'description': description,
            'private': private,
            'auto_init': False
        }
        
        response = requests.post(
            f'{self.base_url}/user/repos',
            headers=self.headers,
            json=data
        )
        
        if response.status_code == 201:
            repo_data = response.json()
            print(f"✅ تم إنشاء المستودع: {repo_data['html_url']}")
            return repo_data
        else:
            print(f"❌ فشل في إنشاء المستودع: {response.json()}")
            return None
    
    def init_and_upload(self, project_path, repo_name, commit_message="Initial commit"):
        """تهيئة Git ورفع المشروع"""
        original_dir = os.getcwd()
        
        try:
            # الانتقال لمجلد المشروع
            os.chdir(project_path)
            
            # تهيئة Git
            subprocess.run(['git', 'init'], check=True, capture_output=True)
            print("✅ تم تهيئة Git")
            
            # إنشاء .gitignore إذا لم يكن موجوداً
            gitignore_path = Path('.gitignore')
            if not gitignore_path.exists():
                gitignore_content = """# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Logs
*.log
logs/

# Cache
cache/
.cache/

# Temporary files
temp/
tmp/
*.tmp

# API Keys and secrets
*.json
!requirements.json
!package.json
client_secret.json
service_account.json
.env
config.ini

# Database
*.db
*.sqlite
*.sqlite3

# Images (large files)
*.jpg
*.jpeg
*.png
*.gif
*.bmp
*.tiff
*.webp

# Audio/Video
*.mp3
*.mp4
*.wav
*.avi
*.mov

# Archives
*.zip
*.rar
*.tar.gz
*.7z
"""
                with open(gitignore_path, 'w', encoding='utf-8') as f:
                    f.write(gitignore_content)
                print("✅ تم إنشاء .gitignore")
            
            # إضافة جميع الملفات
            subprocess.run(['git', 'add', '.'], check=True, capture_output=True)
            print("✅ تم إضافة الملفات")
            
            # عمل commit
            subprocess.run(['git', 'commit', '-m', commit_message], check=True, capture_output=True)
            print("✅ تم عمل commit")
            
            # تحديد الفرع الرئيسي
            subprocess.run(['git', 'branch', '-M', 'main'], check=True, capture_output=True)
            
            # إضافة remote origin
            repo_url = f"https://github.com/{self.get_username()}/{repo_name}.git"
            subprocess.run(['git', 'remote', 'add', 'origin', repo_url], check=True, capture_output=True)
            print("✅ تم إضافة remote origin")
            
            # Push للمستودع
            subprocess.run(['git', 'push', '-u', 'origin', 'main'], check=True, capture_output=True)
            print("✅ تم رفع المشروع على GitHub")
            
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ خطأ في Git: {e}")
            return False
        except Exception as e:
            print(f"❌ خطأ عام: {e}")
            return False
        finally:
            os.chdir(original_dir)
    
    def get_username(self):
        """الحصول على اسم المستخدم من GitHub"""
        response = requests.get(f'{self.base_url}/user', headers=self.headers)
        if response.status_code == 200:
            return response.json()['login']
        return None
    
    def upload_project(self, project_path, repo_name, description="", private=True):
        """رفع مشروع كامل"""
        print(f"🚀 بدء رفع المشروع: {repo_name}")
        
        # التحقق من وجود المجلد
        if not os.path.exists(project_path):
            print(f"❌ المجلد غير موجود: {project_path}")
            return False
        
        # إنشاء المستودع
        repo_data = self.create_repository(repo_name, description, private)
        if not repo_data:
            return False
        
        # رفع المشروع
        success = self.init_and_upload(project_path, repo_name)
        
        if success:
            print(f"🎉 تم رفع المشروع بنجاح!")
            print(f"🔗 رابط المستودع: {repo_data['html_url']}")
            print(f"🔒 المستودع {'خاص' if private else 'عام'}")
            return True
        else:
            print("❌ فشل في رفع المشروع")
            return False

def main():
    """الدالة الرئيسية"""
    print("🚀 أداة رفع المشاريع على GitHub - نسخة مبسطة")
    print("=" * 50)
    
    # طلب GitHub Token
    token = input("أدخل GitHub Personal Access Token: ").strip()
    if not token:
        print("❌ يجب إدخال GitHub Token")
        return
    
    # إنشاء كائن الأداة
    uploader = SimpleGitHubUploader(token)
    
    # التحقق من صحة الـ token
    username = uploader.get_username()
    if not username:
        print("❌ GitHub Token غير صحيح")
        return
    
    print(f"✅ مرحباً {username}!")
    
    # رفع مشروع "send games news"
    project_path = "./send games news"
    repo_name = "games-news-telegram-bot"
    description = "بوت Telegram متقدم لأخبار الألعاب مع ذكاء اصطناعي"
    
    print(f"\n📁 المشروع المحدد: {project_path}")
    print(f"📝 اسم المستودع: {repo_name}")
    print(f"📄 الوصف: {description}")
    print(f"🔒 نوع المستودع: خاص")
    
    confirm = input("\nهل تريد المتابعة؟ (y/n): ").strip().lower()
    if confirm not in ['y', 'yes', 'نعم']:
        print("❌ تم إلغاء العملية")
        return
    
    # رفع المشروع
    success = uploader.upload_project(
        project_path=project_path,
        repo_name=repo_name,
        description=description,
        private=True  # مستودع خاص
    )
    
    if success:
        print("\n🎉 تم رفع المشروع بنجاح!")
        print("🔒 المستودع خاص ومحمي")
    else:
        print("\n❌ فشل في رفع المشروع")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 تم إيقاف الأداة")
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")
