# نظام تكامل APIs المتقدم
import asyncio
import aiohttp
import json
import time
import random
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from urllib.parse import quote_plus
import sqlite3
from .logger import logger

class APIIntegrationManager:
    """مدير تكامل APIs المتقدم"""
    
    def __init__(self):
        self.api_keys = self._load_api_keys()
        self.rate_limits = self._init_rate_limits()
        self.cache = {}
        self.session = None
        
    def _load_api_keys(self) -> Dict:
        """تحميل مفاتيح APIs"""
        return {
            # APIs أساسية (موجودة)
            'google_api_key': '',  # Google APIs
            'gemini_api_key': '',  # Gemini AI
            
            # APIs متقدمة للـ SEO والتحليل
            'google_search_console_key': '',  # Google Search Console
            'google_analytics_key': '',  # Google Analytics 4
            'google_pagespeed_key': '',  # PageSpeed Insights
            'semrush_api_key': '',  # SEMrush
            'ahrefs_api_key': '',  # Ahrefs
            'moz_api_key': '',  # Moz
            
            # APIs التحليل والمراقبة
            'hotjar_api_key': '',  # Hotjar
            'mixpanel_api_key': '',  # Mixpanel
            'facebook_pixel_id': '',  # Facebook Pixel
            'google_trends_key': '',  # Google Trends
            
            # APIs وسائل التواصل
            'twitter_api_key': '',  # Twitter API v2
            'facebook_api_key': '',  # Facebook Graph API
            'instagram_api_key': '',  # Instagram Basic Display
            'youtube_analytics_key': '',  # YouTube Analytics
            
            # APIs المحتوى والتحليل
            'buzzsumo_api_key': '',  # BuzzSumo
            'clearbit_api_key': '',  # Clearbit
            'hunter_api_key': '',  # Hunter.io
        }
    
    def _init_rate_limits(self) -> Dict:
        """تهيئة حدود معدل الطلبات"""
        return {
            'google_apis': {'requests_per_minute': 100, 'last_request': 0},
            'semrush': {'requests_per_minute': 10, 'last_request': 0},
            'ahrefs': {'requests_per_minute': 20, 'last_request': 0},
            'social_apis': {'requests_per_minute': 50, 'last_request': 0},
            'analytics_apis': {'requests_per_minute': 30, 'last_request': 0}
        }
    
    async def __aenter__(self):
        """إنشاء جلسة HTTP"""
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """إغلاق جلسة HTTP"""
        if self.session:
            await self.session.close()

class CoreWebVitalsAnalyzer:
    """محلل Core Web Vitals المتقدم"""
    
    def __init__(self, api_manager: APIIntegrationManager):
        self.api_manager = api_manager
        
    async def analyze_page_performance(self, url: str) -> Dict:
        """تحليل أداء الصفحة مع Core Web Vitals"""
        try:
            # استخدام PageSpeed Insights API
            pagespeed_data = await self._get_pagespeed_insights(url)
            
            # تحليل Core Web Vitals
            core_vitals = self._extract_core_web_vitals(pagespeed_data)
            
            # تحليل الأداء العام
            performance_analysis = self._analyze_performance_metrics(pagespeed_data)
            
            # توليد توصيات التحسين
            optimization_recommendations = self._generate_optimization_recommendations(
                core_vitals, performance_analysis
            )
            
            return {
                'url': url,
                'timestamp': datetime.now().isoformat(),
                'core_web_vitals': core_vitals,
                'performance_metrics': performance_analysis,
                'recommendations': optimization_recommendations,
                'overall_score': self._calculate_overall_score(core_vitals, performance_analysis)
            }
            
        except Exception as e:
            logger.error(f"❌ فشل في تحليل Core Web Vitals للصفحة {url}", e)
            return {}
    
    async def _get_pagespeed_insights(self, url: str) -> Dict:
        """الحصول على بيانات PageSpeed Insights"""
        try:
            api_key = self.api_manager.api_keys.get('google_pagespeed_key')
            if not api_key:
                logger.warning("⚠️ مفتاح PageSpeed Insights غير متوفر - استخدام بيانات محاكاة")
                return self._simulate_pagespeed_data(url)
            
            # طلب API حقيقي
            api_url = f"https://www.googleapis.com/pagespeedonline/v5/runPagespeed"
            params = {
                'url': url,
                'key': api_key,
                'category': 'performance',
                'strategy': 'mobile'
            }
            
            async with self.api_manager.session.get(api_url, params=params) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    logger.warning(f"⚠️ فشل في طلب PageSpeed API: {response.status}")
                    return self._simulate_pagespeed_data(url)
                    
        except Exception as e:
            logger.error("❌ خطأ في طلب PageSpeed Insights", e)
            return self._simulate_pagespeed_data(url)
    
    def _simulate_pagespeed_data(self, url: str) -> Dict:
        """محاكاة بيانات PageSpeed للاختبار"""
        
        return {
            'lighthouseResult': {
                'audits': {
                    'largest-contentful-paint': {
                        'numericValue': random.uniform(1500, 4000),
                        'score': random.uniform(0.5, 1.0)
                    },
                    'first-input-delay': {
                        'numericValue': random.uniform(50, 200),
                        'score': random.uniform(0.6, 1.0)
                    },
                    'cumulative-layout-shift': {
                        'numericValue': random.uniform(0.05, 0.25),
                        'score': random.uniform(0.4, 1.0)
                    },
                    'speed-index': {
                        'numericValue': random.uniform(2000, 6000),
                        'score': random.uniform(0.5, 0.9)
                    },
                    'total-blocking-time': {
                        'numericValue': random.uniform(100, 500),
                        'score': random.uniform(0.6, 0.9)
                    }
                },
                'categories': {
                    'performance': {
                        'score': random.uniform(0.6, 0.95)
                    }
                }
            }
        }
    
    def _extract_core_web_vitals(self, pagespeed_data: Dict) -> Dict:
        """استخراج Core Web Vitals من البيانات"""
        try:
            audits = pagespeed_data.get('lighthouseResult', {}).get('audits', {})
            
            # Largest Contentful Paint (LCP)
            lcp_data = audits.get('largest-contentful-paint', {})
            lcp_value = lcp_data.get('numericValue', 0) / 1000  # تحويل لثواني
            lcp_score = lcp_data.get('score', 0) * 100
            lcp_status = 'جيد' if lcp_value <= 2.5 else 'يحتاج تحسين' if lcp_value <= 4.0 else 'ضعيف'
            
            # First Input Delay (FID) - نستخدم Total Blocking Time كبديل
            tbt_data = audits.get('total-blocking-time', {})
            fid_value = tbt_data.get('numericValue', 0)  # مللي ثانية
            fid_score = tbt_data.get('score', 0) * 100
            fid_status = 'جيد' if fid_value <= 100 else 'يحتاج تحسين' if fid_value <= 300 else 'ضعيف'
            
            # Cumulative Layout Shift (CLS)
            cls_data = audits.get('cumulative-layout-shift', {})
            cls_value = cls_data.get('numericValue', 0)
            cls_score = cls_data.get('score', 0) * 100
            cls_status = 'جيد' if cls_value <= 0.1 else 'يحتاج تحسين' if cls_value <= 0.25 else 'ضعيف'
            
            return {
                'lcp': {
                    'value': round(lcp_value, 2),
                    'unit': 'ثانية',
                    'score': round(lcp_score, 1),
                    'status': lcp_status,
                    'threshold': '≤ 2.5 ثانية'
                },
                'fid': {
                    'value': round(fid_value, 0),
                    'unit': 'مللي ثانية',
                    'score': round(fid_score, 1),
                    'status': fid_status,
                    'threshold': '≤ 100 مللي ثانية'
                },
                'cls': {
                    'value': round(cls_value, 3),
                    'unit': 'نقاط',
                    'score': round(cls_score, 1),
                    'status': cls_status,
                    'threshold': '≤ 0.1'
                }
            }
            
        except Exception as e:
            logger.error("❌ فشل في استخراج Core Web Vitals", e)
            return {}
    
    def _analyze_performance_metrics(self, pagespeed_data: Dict) -> Dict:
        """تحليل مقاييس الأداء الإضافية"""
        try:
            audits = pagespeed_data.get('lighthouseResult', {}).get('audits', {})
            performance_score = pagespeed_data.get('lighthouseResult', {}).get('categories', {}).get('performance', {}).get('score', 0) * 100
            
            # Speed Index
            speed_index = audits.get('speed-index', {}).get('numericValue', 0) / 1000
            
            # Time to Interactive
            tti = audits.get('interactive', {}).get('numericValue', 0) / 1000
            
            # First Contentful Paint
            fcp = audits.get('first-contentful-paint', {}).get('numericValue', 0) / 1000
            
            return {
                'performance_score': round(performance_score, 1),
                'speed_index': round(speed_index, 2),
                'time_to_interactive': round(tti, 2),
                'first_contentful_paint': round(fcp, 2),
                'grade': self._get_performance_grade(performance_score)
            }
            
        except Exception as e:
            logger.error("❌ فشل في تحليل مقاييس الأداء", e)
            return {}
    
    def _get_performance_grade(self, score: float) -> str:
        """تحديد درجة الأداء"""
        if score >= 90:
            return 'A+ ممتاز'
        elif score >= 80:
            return 'A جيد جداً'
        elif score >= 70:
            return 'B جيد'
        elif score >= 60:
            return 'C مقبول'
        else:
            return 'D يحتاج تحسين'
    
    def _generate_optimization_recommendations(self, core_vitals: Dict, performance: Dict) -> List[Dict]:
        """توليد توصيات التحسين"""
        recommendations = []
        
        # توصيات LCP
        lcp_status = core_vitals.get('lcp', {}).get('status')
        if lcp_status in ['يحتاج تحسين', 'ضعيف']:
            recommendations.append({
                'metric': 'LCP',
                'priority': 'عالية',
                'title': 'تحسين Largest Contentful Paint',
                'actions': [
                    'تحسين سرعة الخادم واستخدام CDN',
                    'ضغط وتحسين الصور الكبيرة',
                    'إزالة JavaScript و CSS غير المستخدم',
                    'استخدام preload للموارد المهمة'
                ]
            })
        
        # توصيات FID
        fid_status = core_vitals.get('fid', {}).get('status')
        if fid_status in ['يحتاج تحسين', 'ضعيف']:
            recommendations.append({
                'metric': 'FID',
                'priority': 'متوسطة',
                'title': 'تحسين First Input Delay',
                'actions': [
                    'تقليل وقت تنفيذ JavaScript',
                    'تقسيم الأكواد الكبيرة',
                    'استخدام Web Workers للمهام الثقيلة',
                    'تأجيل تحميل JavaScript غير الضروري'
                ]
            })
        
        # توصيات CLS
        cls_status = core_vitals.get('cls', {}).get('status')
        if cls_status in ['يحتاج تحسين', 'ضعيف']:
            recommendations.append({
                'metric': 'CLS',
                'priority': 'عالية',
                'title': 'تحسين Cumulative Layout Shift',
                'actions': [
                    'تحديد أبعاد الصور والفيديوهات',
                    'حجز مساحة للإعلانات والمحتوى الديناميكي',
                    'تجنب إدراج محتوى فوق المحتوى الموجود',
                    'استخدام font-display: swap للخطوط'
                ]
            })
        
        # توصيات الأداء العام
        performance_score = performance.get('performance_score', 0)
        if performance_score < 70:
            recommendations.append({
                'metric': 'Performance',
                'priority': 'عالية',
                'title': 'تحسين الأداء العام',
                'actions': [
                    'تفعيل ضغط Gzip/Brotli',
                    'تحسين التخزين المؤقت',
                    'تقليل عدد طلبات HTTP',
                    'استخدام تنسيقات صور حديثة (WebP, AVIF)'
                ]
            })
        
        return recommendations
    
    def _calculate_overall_score(self, core_vitals: Dict, performance: Dict) -> float:
        """حساب النقاط الإجمالية"""
        try:
            lcp_score = core_vitals.get('lcp', {}).get('score', 0)
            fid_score = core_vitals.get('fid', {}).get('score', 0)
            cls_score = core_vitals.get('cls', {}).get('score', 0)
            performance_score = performance.get('performance_score', 0)
            
            # متوسط مرجح
            overall_score = (lcp_score * 0.25 + fid_score * 0.25 + cls_score * 0.25 + performance_score * 0.25)
            
            return round(overall_score, 1)
            
        except Exception as e:
            logger.error("❌ فشل في حساب النقاط الإجمالية", e)
            return 0.0

class KeywordResearchAPI:
    """API بحث الكلمات المفتاحية المتقدم"""
    
    def __init__(self, api_manager: APIIntegrationManager):
        self.api_manager = api_manager
    
    async def comprehensive_keyword_research(self, seed_keywords: List[str], 
                                           target_language: str = 'ar') -> Dict:
        """بحث شامل عن الكلمات المفتاحية"""
        try:
            results = {
                'seed_keywords': seed_keywords,
                'keyword_opportunities': [],
                'competitor_keywords': [],
                'trending_keywords': [],
                'long_tail_suggestions': [],
                'search_volume_data': {},
                'difficulty_analysis': {},
                'content_gaps': []
            }
            
            # بحث Google Keyword Planner
            google_data = await self._get_google_keyword_data(seed_keywords)
            results['search_volume_data'].update(google_data)
            
            # بحث SEMrush (إذا متوفر)
            semrush_data = await self._get_semrush_data(seed_keywords)
            results['competitor_keywords'].extend(semrush_data.get('competitor_keywords', []))
            
            # بحث Ahrefs (إذا متوفر)
            ahrefs_data = await self._get_ahrefs_data(seed_keywords)
            results['difficulty_analysis'].update(ahrefs_data.get('difficulty_data', {}))
            
            # Google Trends
            trending_data = await self._get_google_trends_data(seed_keywords)
            results['trending_keywords'].extend(trending_data)
            
            # توليد اقتراحات Long-tail
            long_tail = await self._generate_long_tail_suggestions(seed_keywords)
            results['long_tail_suggestions'].extend(long_tail)
            
            # تحليل فجوات المحتوى
            content_gaps = await self._analyze_content_gaps(seed_keywords)
            results['content_gaps'].extend(content_gaps)
            
            logger.info(f"🔍 تم إجراء بحث شامل للكلمات المفتاحية: {len(seed_keywords)} كلمة أساسية")
            
            return results
            
        except Exception as e:
            logger.error("❌ فشل في البحث الشامل للكلمات المفتاحية", e)
            return {}
    
    async def _get_google_keyword_data(self, keywords: List[str]) -> Dict:
        """الحصول على بيانات Google Keyword Planner"""
        try:
            # محاكاة بيانات Google Keyword Planner
            # في التطبيق الحقيقي، استخدم Google Ads API
            
            keyword_data = {}
            for keyword in keywords:
                keyword_data[keyword] = {
                    'avg_monthly_searches': self._estimate_search_volume(keyword),
                    'competition': self._estimate_competition(keyword),
                    'suggested_bid': self._estimate_bid(keyword)
                }
            
            return keyword_data
            
        except Exception as e:
            logger.error("❌ فشل في الحصول على بيانات Google", e)
            return {}
    
    def _estimate_search_volume(self, keyword: str) -> int:
        """تقدير حجم البحث"""
        base_volume = 1000
        
        # تعديل بناءً على طول الكلمة
        word_count = len(keyword.split())
        if word_count == 1:
            base_volume *= 3
        elif word_count == 2:
            base_volume *= 2
        
        # تعديل بناءً على نوع الكلمة
        if any(word in keyword.lower() for word in ['game', 'لعبة', 'gaming']):
            base_volume *= 1.5
        
        return random.randint(int(base_volume * 0.5), int(base_volume * 2))
    
    def _estimate_competition(self, keyword: str) -> str:
        """تقدير مستوى المنافسة"""
        
        word_count = len(keyword.split())
        if word_count == 1:
            return random.choice(['عالية', 'متوسطة'])
        elif word_count >= 4:
            return random.choice(['منخفضة', 'متوسطة'])
        else:
            return random.choice(['متوسطة', 'عالية'])
    
    def _estimate_bid(self, keyword: str) -> float:
        """تقدير سعر النقرة"""
        
        base_bid = 0.5
        if 'gaming' in keyword.lower():
            base_bid *= 1.5
        
        return round(random.uniform(base_bid * 0.5, base_bid * 2), 2)
    
    async def _get_semrush_data(self, keywords: List[str]) -> Dict:
        """الحصول على بيانات SEMrush"""
        try:
            api_key = self.api_manager.api_keys.get('semrush_api_key')
            if not api_key:
                return self._simulate_semrush_data(keywords)
            
            # طلب API حقيقي لـ SEMrush
            # يتطلب تنفيذ طلبات HTTP للـ SEMrush API
            
            return self._simulate_semrush_data(keywords)
            
        except Exception as e:
            logger.error("❌ فشل في الحصول على بيانات SEMrush", e)
            return {}
    
    def _simulate_semrush_data(self, keywords: List[str]) -> Dict:
        """محاكاة بيانات SEMrush"""
        competitor_keywords = []
        
        for keyword in keywords:
            # إضافة كلمات منافسة محاكاة
            competitor_keywords.extend([
                f"{keyword} review",
                f"{keyword} guide",
                f"best {keyword}",
                f"{keyword} tips",
                f"{keyword} 2025"
            ])
        
        return {
            'competitor_keywords': competitor_keywords[:20],  # أفضل 20
            'organic_competitors': ['gamespot.com', 'ign.com', 'polygon.com']
        }
    
    async def _get_ahrefs_data(self, keywords: List[str]) -> Dict:
        """الحصول على بيانات Ahrefs"""
        try:
            # محاكاة بيانات Ahrefs
            difficulty_data = {}
            
            for keyword in keywords:
                difficulty_data[keyword] = {
                    'keyword_difficulty': self._calculate_keyword_difficulty(keyword),
                    'search_volume': self._estimate_search_volume(keyword),
                    'clicks': int(self._estimate_search_volume(keyword) * 0.3),
                    'cpc': self._estimate_bid(keyword)
                }
            
            return {'difficulty_data': difficulty_data}
            
        except Exception as e:
            logger.error("❌ فشل في الحصول على بيانات Ahrefs", e)
            return {}
    
    def _calculate_keyword_difficulty(self, keyword: str) -> int:
        """حساب صعوبة الكلمة المفتاحية"""
        
        base_difficulty = 50
        
        # كلمة واحدة = أصعب
        if len(keyword.split()) == 1:
            base_difficulty += 20
        
        # كلمات طويلة = أسهل
        elif len(keyword.split()) >= 4:
            base_difficulty -= 15
        
        # كلمات تجارية = أصعب
        if any(word in keyword.lower() for word in ['best', 'buy', 'review']):
            base_difficulty += 10
        
        return max(10, min(90, base_difficulty + random.randint(-10, 10)))
    
    async def _get_google_trends_data(self, keywords: List[str]) -> List[Dict]:
        """الحصول على بيانات Google Trends"""
        try:
            trending_keywords = []
            
            for keyword in keywords:
                # محاكاة بيانات الاتجاهات
                trending_keywords.append({
                    'keyword': keyword,
                    'trend_score': random.randint(60, 100),
                    'growth_rate': random.randint(-20, 50),
                    'seasonality': random.choice(['stable', 'seasonal', 'trending'])
                })
            
            return trending_keywords
            
        except Exception as e:
            logger.error("❌ فشل في الحصول على بيانات Google Trends", e)
            return []
    
    async def _generate_long_tail_suggestions(self, keywords: List[str]) -> List[str]:
        """توليد اقتراحات الكلمات الطويلة"""
        long_tail_templates = [
            "how to {keyword}",
            "best {keyword} for beginners",
            "{keyword} vs {keyword}",
            "{keyword} guide 2025",
            "free {keyword} tools",
            "{keyword} tips and tricks",
            "why {keyword} is important",
            "{keyword} for mobile"
        ]
        
        suggestions = []
        for keyword in keywords:
            for template in long_tail_templates:
                suggestion = template.format(keyword=keyword)
                suggestions.append(suggestion)
        
        return suggestions[:50]  # أفضل 50 اقتراح
    
    async def _analyze_content_gaps(self, keywords: List[str]) -> List[Dict]:
        """تحليل فجوات المحتوى"""
        content_gaps = []
        
        for keyword in keywords:
            gap = {
                'keyword': keyword,
                'gap_type': random.choice(['missing_content', 'outdated_content', 'shallow_content']),
                'opportunity_score': random.randint(60, 95),
                'recommended_content_type': random.choice(['guide', 'review', 'comparison', 'tutorial']),
                'estimated_traffic_potential': random.randint(500, 5000)
            }
            content_gaps.append(gap)
        
        return content_gaps

# إنشاء مثيل عام لمدير APIs
api_manager = APIIntegrationManager()
