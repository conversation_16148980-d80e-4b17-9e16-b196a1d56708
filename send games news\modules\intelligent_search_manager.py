# مدير البحث الذكي المتقدم - الجيل الجديد
import asyncio
import json
import time
import hashlib
import sqlite3
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any, Union
from dataclasses import dataclass, asdict
from enum import Enum
import aiohttp
import os
from collections import defaultdict, deque
import pickle
import logging

from .logger import logger
from .database import db
from .tavily_search import tavily_search
from .serpapi_search import serpapi_search
from .smart_search_manager import smart_search_manager
from .enhanced_search_manager import enhanced_search_manager
from .fallback_ai_manager import fallback_ai_manager, AIModelType
from config.settings import BotConfig

class SearchStrategy(Enum):
    """استراتيجيات البحث الذكية"""
    ADAPTIVE = "adaptive"           # تكيفي - يتعلم من النتائج
    CONTEXTUAL = "contextual"       # سياقي - يفهم المعنى العميق
    SEMANTIC = "semantic"           # دلالي - يبحث بالمعنى
    HYBRID = "hybrid"              # مختلط - يجمع عدة طرق
    PREDICTIVE = "predictive"       # تنبؤي - يتوقع الاحتياجات
    COLLABORATIVE = "collaborative" # تعاوني - ينسق بين المحركات

class SearchContext(Enum):
    """سياق البحث"""
    BREAKING_NEWS = "breaking_news"     # أخبار عاجلة
    GAME_REVIEWS = "game_reviews"       # مراجعات الألعاب
    INDUSTRY_NEWS = "industry_news"     # أخبار الصناعة
    TECH_UPDATES = "tech_updates"       # تحديثات تقنية
    ESPORTS = "esports"                # الرياضات الإلكترونية
    GENERAL_GAMING = "general_gaming"   # ألعاب عامة

@dataclass
class SearchQuery:
    """استعلام بحث ذكي"""
    original_query: str
    processed_query: str
    context: SearchContext
    intent: str
    entities: List[str]
    keywords: List[str]
    semantic_vector: Optional[List[float]] = None
    priority_score: float = 1.0
    expected_result_type: str = "article"
    
@dataclass
class SearchResult:
    """نتيجة بحث محسنة"""
    title: str
    url: str
    content: str
    summary: str
    source: str
    engine: str
    relevance_score: float
    quality_score: float
    freshness_score: float
    authority_score: float
    final_score: float
    timestamp: datetime
    context_match: float
    semantic_similarity: float
    extracted_entities: List[str]
    metadata: Dict[str, Any]

class IntelligentSearchManager:
    """مدير البحث الذكي المتقدم - الجيل الجديد"""
    
    def __init__(self):
        self.db_path = "cache/intelligent_search.db"
        self.learning_data_path = "cache/search_learning_data.pkl"
        
        # إحصائيات التعلم
        self.learning_stats = {
            'total_searches': 0,
            'successful_searches': 0,
            'strategy_performance': defaultdict(list),
            'engine_performance': defaultdict(list),
            'context_patterns': defaultdict(int),
            'query_improvements': defaultdict(list)
        }
        
        # ذاكرة قصيرة المدى للتعلم السريع
        self.short_term_memory = deque(maxlen=100)
        
        # نماذج التعلم
        self.query_patterns = defaultdict(list)
        self.success_patterns = defaultdict(float)
        self.engine_preferences = defaultdict(dict)
        
        # إعدادات الذكاء الاصطناعي
        self.ai_config = {
            'context_analysis_threshold': 0.7,
            'semantic_similarity_threshold': 0.6,
            'learning_rate': 0.1,
            'adaptation_frequency': 10  # كل 10 عمليات بحث
        }
        
        # تهيئة النظام
        self._initialize_database()
        self._load_learning_data()
        
        logger.info("🧠 تم تهيئة مدير البحث الذكي المتقدم")
    
    def _initialize_database(self):
        """تهيئة قاعدة بيانات التعلم"""
        try:
            os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
            
            with sqlite3.connect(self.db_path) as conn:
                # جدول تاريخ البحث
                conn.execute('''
                    CREATE TABLE IF NOT EXISTS search_history (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        query_hash TEXT UNIQUE NOT NULL,
                        original_query TEXT NOT NULL,
                        processed_query TEXT NOT NULL,
                        context TEXT NOT NULL,
                        strategy TEXT NOT NULL,
                        engines_used TEXT NOT NULL,
                        results_count INTEGER NOT NULL,
                        success_score REAL NOT NULL,
                        execution_time REAL NOT NULL,
                        timestamp REAL NOT NULL,
                        metadata TEXT
                    )
                ''')
                
                # جدول أنماط النجاح
                conn.execute('''
                    CREATE TABLE IF NOT EXISTS success_patterns (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        pattern_type TEXT NOT NULL,
                        pattern_data TEXT NOT NULL,
                        success_rate REAL NOT NULL,
                        usage_count INTEGER NOT NULL,
                        last_updated REAL NOT NULL
                    )
                ''')
                
                # جدول تفضيلات المحركات
                conn.execute('''
                    CREATE TABLE IF NOT EXISTS engine_preferences (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        context TEXT NOT NULL,
                        engine TEXT NOT NULL,
                        performance_score REAL NOT NULL,
                        usage_count INTEGER NOT NULL,
                        last_performance REAL NOT NULL
                    )
                ''')
                
                # إنشاء الفهارس
                conn.execute('CREATE INDEX IF NOT EXISTS idx_query_hash ON search_history(query_hash)')
                conn.execute('CREATE INDEX IF NOT EXISTS idx_context ON search_history(context)')
                conn.execute('CREATE INDEX IF NOT EXISTS idx_timestamp ON search_history(timestamp)')
                
            logger.info("✅ تم تهيئة قاعدة بيانات التعلم")
            
        except Exception as e:
            logger.error(f"❌ فشل في تهيئة قاعدة بيانات التعلم: {e}")
    
    def _load_learning_data(self):
        """تحميل بيانات التعلم المحفوظة"""
        try:
            if os.path.exists(self.learning_data_path):
                with open(self.learning_data_path, 'rb') as f:
                    data = pickle.load(f)
                    self.learning_stats = data.get('learning_stats', self.learning_stats)
                    self.query_patterns = data.get('query_patterns', self.query_patterns)
                    self.success_patterns = data.get('success_patterns', self.success_patterns)
                    self.engine_preferences = data.get('engine_preferences', self.engine_preferences)
                
                logger.info("📚 تم تحميل بيانات التعلم المحفوظة")
            else:
                logger.info("📝 بدء بيانات تعلم جديدة")
                
        except Exception as e:
            logger.error(f"❌ فشل في تحميل بيانات التعلم: {e}")
    
    def _save_learning_data(self):
        """حفظ بيانات التعلم"""
        try:
            os.makedirs(os.path.dirname(self.learning_data_path), exist_ok=True)
            
            data = {
                'learning_stats': dict(self.learning_stats),
                'query_patterns': dict(self.query_patterns),
                'success_patterns': dict(self.success_patterns),
                'engine_preferences': dict(self.engine_preferences),
                'last_save': datetime.now().isoformat()
            }
            
            with open(self.learning_data_path, 'wb') as f:
                pickle.dump(data, f)
                
            logger.debug("💾 تم حفظ بيانات التعلم")
            
        except Exception as e:
            logger.error(f"❌ فشل في حفظ بيانات التعلم: {e}")

    async def intelligent_search(self, 
                                query: str, 
                                max_results: int = 10,
                                context: Optional[SearchContext] = None,
                                strategy: Optional[SearchStrategy] = None) -> List[SearchResult]:
        """البحث الذكي الرئيسي"""
        start_time = time.time()
        self.learning_stats['total_searches'] += 1
        
        try:
            # 1. تحليل الاستعلام وفهم السياق
            processed_query = await self._analyze_query(query, context)
            
            # 2. اختيار الاستراتيجية المثلى
            if not strategy:
                strategy = await self._select_optimal_strategy(processed_query)
            
            # 3. تنفيذ البحث باستخدام الاستراتيجية المختارة
            results = await self._execute_search_strategy(processed_query, strategy, max_results)
            
            # 4. تقييم وترتيب النتائج
            scored_results = await self._score_and_rank_results(results, processed_query)
            
            # 5. التعلم من النتائج
            await self._learn_from_results(processed_query, strategy, scored_results, time.time() - start_time)
            
            # 6. حفظ في الذاكرة قصيرة المدى
            self.short_term_memory.append({
                'query': processed_query,
                'strategy': strategy,
                'results_count': len(scored_results),
                'success_score': self._calculate_success_score(scored_results),
                'timestamp': datetime.now()
            })
            
            self.learning_stats['successful_searches'] += 1
            logger.info(f"🎯 البحث الذكي مكتمل: {len(scored_results)} نتيجة في {time.time() - start_time:.2f}ث")
            
            return scored_results
            
        except Exception as e:
            logger.error(f"❌ فشل في البحث الذكي: {e}")
            # العودة للنظام التقليدي كخطة بديلة
            return await self._fallback_search(query, max_results)

    async def _analyze_query(self, query: str, context: Optional[SearchContext] = None) -> SearchQuery:
        """تحليل الاستعلام وفهم السياق العميق"""
        try:
            # تنظيف الاستعلام
            cleaned_query = query.strip().lower()

            # استخراج الكيانات والكلمات المفتاحية
            entities = await self._extract_entities(cleaned_query)
            keywords = await self._extract_keywords(cleaned_query)

            # تحديد السياق إذا لم يكن محدداً
            if not context:
                context = await self._detect_context(cleaned_query, entities)

            # تحديد النية من الاستعلام
            intent = await self._detect_intent(cleaned_query)

            # معالجة الاستعلام للحصول على أفضل نسخة
            processed_query = await self._process_query_for_search(cleaned_query, context, intent)

            # حساب نقاط الأولوية
            priority_score = self._calculate_priority_score(cleaned_query, context, intent)

            return SearchQuery(
                original_query=query,
                processed_query=processed_query,
                context=context,
                intent=intent,
                entities=entities,
                keywords=keywords,
                priority_score=priority_score
            )

        except Exception as e:
            logger.error(f"❌ فشل في تحليل الاستعلام: {e}")
            # إرجاع تحليل أساسي
            return SearchQuery(
                original_query=query,
                processed_query=query,
                context=SearchContext.GENERAL_GAMING,
                intent="search",
                entities=[],
                keywords=query.split(),
                priority_score=1.0
            )

    async def _extract_entities(self, query: str) -> List[str]:
        """استخراج الكيانات من الاستعلام"""
        entities = []

        # قوائم الكيانات المعروفة
        game_companies = ['sony', 'microsoft', 'nintendo', 'valve', 'epic', 'activision', 'ubisoft', 'ea']
        game_platforms = ['ps5', 'xbox', 'pc', 'steam', 'switch', 'mobile']
        game_genres = ['rpg', 'fps', 'moba', 'battle royale', 'racing', 'sports']

        query_lower = query.lower()

        # البحث عن الكيانات
        for company in game_companies:
            if company in query_lower:
                entities.append(f"company:{company}")

        for platform in game_platforms:
            if platform in query_lower:
                entities.append(f"platform:{platform}")

        for genre in game_genres:
            if genre in query_lower:
                entities.append(f"genre:{genre}")

        return entities

    async def _extract_keywords(self, query: str) -> List[str]:
        """استخراج الكلمات المفتاحية المهمة"""
        # كلمات الإيقاف
        stop_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'}

        # تقسيم وتنظيف
        words = query.lower().split()
        keywords = [word for word in words if word not in stop_words and len(word) > 2]

        # إضافة كلمات مفتاحية متعلقة بالألعاب
        gaming_keywords = []
        for keyword in keywords:
            if any(gaming_term in keyword for gaming_term in ['game', 'play', 'gamer', 'gaming']):
                gaming_keywords.append(keyword)

        return keywords + gaming_keywords

    async def _detect_context(self, query: str, entities: List[str]) -> SearchContext:
        """تحديد سياق البحث"""
        query_lower = query.lower()

        # كلمات دالة على السياق
        if any(word in query_lower for word in ['breaking', 'news', 'announced', 'revealed']):
            return SearchContext.BREAKING_NEWS
        elif any(word in query_lower for word in ['review', 'rating', 'score', 'opinion']):
            return SearchContext.GAME_REVIEWS
        elif any(word in query_lower for word in ['industry', 'market', 'sales', 'revenue']):
            return SearchContext.INDUSTRY_NEWS
        elif any(word in query_lower for word in ['update', 'patch', 'version', 'tech']):
            return SearchContext.TECH_UPDATES
        elif any(word in query_lower for word in ['esports', 'tournament', 'championship', 'competitive']):
            return SearchContext.ESPORTS
        else:
            return SearchContext.GENERAL_GAMING

    async def _detect_intent(self, query: str) -> str:
        """تحديد نية المستخدم من الاستعلام"""
        query_lower = query.lower()

        if any(word in query_lower for word in ['latest', 'new', 'recent', 'today']):
            return "find_latest"
        elif any(word in query_lower for word in ['best', 'top', 'recommended']):
            return "find_best"
        elif any(word in query_lower for word in ['how', 'guide', 'tutorial']):
            return "find_guide"
        elif any(word in query_lower for word in ['release', 'launch', 'coming']):
            return "find_upcoming"
        else:
            return "general_search"

    async def _process_query_for_search(self, query: str, context: SearchContext, intent: str) -> str:
        """معالجة الاستعلام للحصول على أفضل نسخة للبحث"""
        processed = query

        # إضافة كلمات سياقية
        if context == SearchContext.BREAKING_NEWS:
            processed += " breaking news gaming"
        elif context == SearchContext.GAME_REVIEWS:
            processed += " game review"
        elif context == SearchContext.INDUSTRY_NEWS:
            processed += " gaming industry news"
        elif context == SearchContext.TECH_UPDATES:
            processed += " gaming technology update"
        elif context == SearchContext.ESPORTS:
            processed += " esports gaming"

        # إضافة كلمات نية
        if intent == "find_latest":
            processed += " latest 2025"
        elif intent == "find_upcoming":
            processed += " upcoming release"

        return processed

    def _calculate_priority_score(self, query: str, context: SearchContext, intent: str) -> float:
        """حساب نقاط أولوية الاستعلام"""
        score = 1.0

        # زيادة الأولوية للأخبار العاجلة
        if context == SearchContext.BREAKING_NEWS:
            score += 0.5

        # زيادة الأولوية للمحتوى الحديث
        if intent == "find_latest":
            score += 0.3

        # زيادة الأولوية للاستعلامات الطويلة (أكثر تحديداً)
        if len(query.split()) > 3:
            score += 0.2

        return min(score, 2.0)  # حد أقصى 2.0

    async def _select_optimal_strategy(self, query: SearchQuery) -> SearchStrategy:
        """اختيار الاستراتيجية المثلى للبحث"""
        try:
            # تحليل الأداء السابق لهذا النوع من الاستعلامات
            context_performance = self.engine_preferences.get(query.context.value, {})

            # اختيار الاستراتيجية بناءً على السياق والأداء السابق
            if query.context == SearchContext.BREAKING_NEWS:
                # للأخبار العاجلة، استخدم الاستراتيجية السريعة
                return SearchStrategy.COLLABORATIVE
            elif query.context == SearchContext.GAME_REVIEWS:
                # للمراجعات، استخدم البحث الدلالي
                return SearchStrategy.SEMANTIC
            elif len(query.entities) > 2:
                # للاستعلامات المعقدة، استخدم الاستراتيجية التكيفية
                return SearchStrategy.ADAPTIVE
            else:
                # للاستعلامات العامة، استخدم الاستراتيجية المختلطة
                return SearchStrategy.HYBRID

        except Exception as e:
            logger.error(f"❌ فشل في اختيار الاستراتيجية: {e}")
            return SearchStrategy.HYBRID

    async def _execute_search_strategy(self, query: SearchQuery, strategy: SearchStrategy, max_results: int) -> List[Dict]:
        """تنفيذ استراتيجية البحث المختارة"""
        try:
            if strategy == SearchStrategy.COLLABORATIVE:
                return await self._collaborative_search(query, max_results)
            elif strategy == SearchStrategy.SEMANTIC:
                return await self._semantic_search(query, max_results)
            elif strategy == SearchStrategy.ADAPTIVE:
                return await self._adaptive_search(query, max_results)
            elif strategy == SearchStrategy.PREDICTIVE:
                return await self._predictive_search(query, max_results)
            else:  # HYBRID
                return await self._hybrid_search(query, max_results)

        except Exception as e:
            logger.error(f"❌ فشل في تنفيذ استراتيجية {strategy.value}: {e}")
            # العودة للبحث الأساسي
            return await self._basic_search(query, max_results)

    async def _collaborative_search(self, query: SearchQuery, max_results: int) -> List[Dict]:
        """البحث التعاوني - ينسق بين عدة محركات"""
        all_results = []

        # تقسيم النتائج بين المحركات
        engines = [
            ('tavily', tavily_search, max_results // 2),
            ('serpapi', serpapi_search, max_results // 3),
            ('enhanced', enhanced_search_manager, max_results // 4)
        ]

        # تنفيذ البحث بالتوازي
        tasks = []
        for engine_name, engine, count in engines:
            if hasattr(engine, 'search') or hasattr(engine, 'comprehensive_search'):
                task = self._search_with_engine(engine_name, engine, query.processed_query, count)
                tasks.append(task)

        # انتظار النتائج
        results_list = await asyncio.gather(*tasks, return_exceptions=True)

        # جمع النتائج الناجحة
        for i, results in enumerate(results_list):
            if isinstance(results, list):
                for result in results:
                    result['search_engine'] = engines[i][0]
                    all_results.append(result)

        return all_results

    async def _semantic_search(self, query: SearchQuery, max_results: int) -> List[Dict]:
        """البحث الدلالي - يفهم المعنى العميق"""
        # استخدام النماذج الاحتياطية للفهم العميق
        try:
            # تحليل دلالي للاستعلام
            semantic_analysis = await fallback_ai_manager.analyze_search_intent(
                query.original_query,
                context=query.context.value
            )

            # توليد استعلامات بديلة بناءً على التحليل الدلالي
            alternative_queries = semantic_analysis.get('alternative_queries', [query.processed_query])

            all_results = []
            for alt_query in alternative_queries[:3]:  # أفضل 3 استعلامات
                results = await self._search_with_best_engine(alt_query, max_results // 3)
                all_results.extend(results)

            return all_results

        except Exception as e:
            logger.error(f"❌ فشل في البحث الدلالي: {e}")
            return await self._basic_search(query, max_results)

    async def _adaptive_search(self, query: SearchQuery, max_results: int) -> List[Dict]:
        """البحث التكيفي - يتعلم من النتائج السابقة"""
        # اختيار المحرك الأفضل بناءً على الأداء السابق
        best_engine = self._get_best_engine_for_context(query.context)

        # البحث باستخدام المحرك الأفضل
        results = await self._search_with_engine(best_engine['name'], best_engine['instance'],
                                               query.processed_query, max_results)

        # إذا لم تكن النتائج كافية، جرب محركات أخرى
        if len(results) < max_results // 2:
            backup_engines = self._get_backup_engines(query.context, exclude=best_engine['name'])
            for engine in backup_engines[:2]:  # أفضل محركين احتياطيين
                backup_results = await self._search_with_engine(
                    engine['name'], engine['instance'],
                    query.processed_query, max_results // 3
                )
                results.extend(backup_results)

        return results

    async def _predictive_search(self, query: SearchQuery, max_results: int) -> List[Dict]:
        """البحث التنبؤي - يتوقع ما يريده المستخدم"""
        # تحليل الأنماط السابقة للاستعلامات المشابهة
        similar_patterns = self._find_similar_query_patterns(query)

        # توليد استعلامات تنبؤية
        predictive_queries = self._generate_predictive_queries(query, similar_patterns)

        all_results = []
        for pred_query in predictive_queries:
            results = await self._search_with_best_engine(pred_query, max_results // len(predictive_queries))
            all_results.extend(results)

        return all_results

    async def _hybrid_search(self, query: SearchQuery, max_results: int) -> List[Dict]:
        """البحث المختلط - يجمع عدة طرق"""
        all_results = []

        # 40% بحث تقليدي
        traditional_results = await self._basic_search(query, int(max_results * 0.4))
        all_results.extend(traditional_results)

        # 30% بحث تعاوني
        collaborative_results = await self._collaborative_search(query, int(max_results * 0.3))
        all_results.extend(collaborative_results)

        # 30% بحث دلالي
        semantic_results = await self._semantic_search(query, int(max_results * 0.3))
        all_results.extend(semantic_results)

        return all_results

    async def _basic_search(self, query: SearchQuery, max_results: int) -> List[Dict]:
        """البحث الأساسي - خطة بديلة"""
        try:
            # استخدام النظام المحسن الموجود
            if enhanced_search_manager:
                result = await enhanced_search_manager.comprehensive_search(
                    query.processed_query, max_results
                )
                return result.get('results', [])
            else:
                # استخدام Tavily كخطة بديلة
                return await tavily_search.search(query.processed_query, max_results=max_results)

        except Exception as e:
            logger.error(f"❌ فشل في البحث الأساسي: {e}")
            return []

    async def _search_with_engine(self, engine_name: str, engine_instance: Any,
                                query: str, max_results: int) -> List[Dict]:
        """البحث باستخدام محرك محدد"""
        try:
            if engine_name == 'tavily' and hasattr(engine_instance, 'search'):
                return await engine_instance.search(query, max_results=max_results)
            elif engine_name == 'serpapi' and hasattr(engine_instance, 'search'):
                return await engine_instance.search(query, num_results=max_results)
            elif engine_name == 'enhanced' and hasattr(engine_instance, 'comprehensive_search'):
                result = await engine_instance.comprehensive_search(query, max_results)
                return result.get('results', [])
            else:
                logger.warning(f"⚠️ محرك غير مدعوم: {engine_name}")
                return []

        except Exception as e:
            logger.error(f"❌ فشل في البحث باستخدام {engine_name}: {e}")
            return []

    async def _search_with_best_engine(self, query: str, max_results: int) -> List[Dict]:
        """البحث باستخدام أفضل محرك متاح"""
        # ترتيب المحركات حسب الأولوية
        engines = [
            ('tavily', tavily_search),
            ('enhanced', enhanced_search_manager),
            ('serpapi', serpapi_search)
        ]

        for engine_name, engine_instance in engines:
            try:
                results = await self._search_with_engine(engine_name, engine_instance, query, max_results)
                if results:
                    return results
            except Exception as e:
                logger.warning(f"⚠️ فشل {engine_name}: {e}")
                continue

        return []

    async def _score_and_rank_results(self, results: List[Dict], query: SearchQuery) -> List[SearchResult]:
        """تقييم وترتيب النتائج"""
        scored_results = []

        for result in results:
            try:
                # حساب نقاط مختلفة
                relevance_score = self._calculate_relevance_score(result, query)
                quality_score = self._calculate_quality_score(result)
                freshness_score = self._calculate_freshness_score(result)
                authority_score = self._calculate_authority_score(result)

                # حساب النقاط النهائية
                final_score = (
                    relevance_score * 0.4 +
                    quality_score * 0.3 +
                    freshness_score * 0.2 +
                    authority_score * 0.1
                )

                # إنشاء نتيجة محسنة
                search_result = SearchResult(
                    title=result.get('title', ''),
                    url=result.get('url', ''),
                    content=result.get('content', ''),
                    summary=result.get('summary', ''),
                    source=result.get('source', ''),
                    engine=result.get('search_engine', 'unknown'),
                    relevance_score=relevance_score,
                    quality_score=quality_score,
                    freshness_score=freshness_score,
                    authority_score=authority_score,
                    final_score=final_score,
                    timestamp=datetime.now(),
                    context_match=self._calculate_context_match(result, query),
                    semantic_similarity=self._calculate_semantic_similarity(result, query),
                    extracted_entities=await self._extract_entities(result.get('content', '')),
                    metadata=result
                )

                scored_results.append(search_result)

            except Exception as e:
                logger.error(f"❌ فشل في تقييم النتيجة: {e}")
                continue

        # ترتيب حسب النقاط النهائية
        scored_results.sort(key=lambda x: x.final_score, reverse=True)

        return scored_results

    def _calculate_relevance_score(self, result: Dict, query: SearchQuery) -> float:
        """حساب نقاط الصلة"""
        score = 0.0

        title = result.get('title', '').lower()
        content = result.get('content', '').lower()

        # فحص وجود الكلمات المفتاحية
        for keyword in query.keywords:
            if keyword.lower() in title:
                score += 0.3
            if keyword.lower() in content:
                score += 0.1

        # فحص وجود الكيانات
        for entity in query.entities:
            entity_name = entity.split(':')[-1]
            if entity_name in title:
                score += 0.4
            if entity_name in content:
                score += 0.2

        return min(score, 1.0)

    def _calculate_quality_score(self, result: Dict) -> float:
        """حساب نقاط الجودة"""
        score = 0.5  # نقاط أساسية

        title = result.get('title', '')
        content = result.get('content', '')

        # طول المحتوى
        if len(content) > 500:
            score += 0.2
        if len(content) > 1000:
            score += 0.1

        # جودة العنوان
        if len(title) > 20 and len(title) < 100:
            score += 0.1

        # وجود ملخص
        if result.get('summary'):
            score += 0.1

        return min(score, 1.0)

    def _calculate_freshness_score(self, result: Dict) -> float:
        """حساب نقاط الحداثة"""
        # إذا كان هناك تاريخ نشر
        if 'published_date' in result:
            try:
                pub_date = result['published_date']
                if isinstance(pub_date, str):
                    from dateutil import parser
                    pub_date = parser.parse(pub_date)

                days_old = (datetime.now() - pub_date).days

                if days_old <= 1:
                    return 1.0
                elif days_old <= 7:
                    return 0.8
                elif days_old <= 30:
                    return 0.6
                else:
                    return 0.3
            except:
                pass

        # فحص كلمات دالة على الحداثة
        text = f"{result.get('title', '')} {result.get('content', '')}".lower()
        fresh_indicators = ['today', 'yesterday', 'breaking', 'latest', '2025', 'new', 'just']

        for indicator in fresh_indicators:
            if indicator in text:
                return 0.8

        return 0.5

    def _calculate_authority_score(self, result: Dict) -> float:
        """حساب نقاط المصداقية"""
        source = result.get('source', '').lower()
        url = result.get('url', '').lower()

        # مصادر موثوقة
        trusted_sources = [
            'ign.com', 'gamespot.com', 'polygon.com', 'kotaku.com',
            'eurogamer.net', 'pcgamer.com', 'gameinformer.com'
        ]

        for trusted in trusted_sources:
            if trusted in url or trusted in source:
                return 0.9

        # مصادر متوسطة الثقة
        medium_sources = ['reddit.com', 'twitter.com', 'youtube.com']
        for medium in medium_sources:
            if medium in url:
                return 0.6

        return 0.5

    def _calculate_context_match(self, result: Dict, query: SearchQuery) -> float:
        """حساب تطابق السياق"""
        content = f"{result.get('title', '')} {result.get('content', '')}".lower()

        context_keywords = {
            SearchContext.BREAKING_NEWS: ['breaking', 'news', 'announced', 'revealed'],
            SearchContext.GAME_REVIEWS: ['review', 'rating', 'score', 'opinion'],
            SearchContext.INDUSTRY_NEWS: ['industry', 'market', 'sales', 'revenue'],
            SearchContext.TECH_UPDATES: ['update', 'patch', 'version', 'tech'],
            SearchContext.ESPORTS: ['esports', 'tournament', 'championship'],
            SearchContext.GENERAL_GAMING: ['game', 'gaming', 'player']
        }

        keywords = context_keywords.get(query.context, [])
        matches = sum(1 for keyword in keywords if keyword in content)

        return min(matches / len(keywords) if keywords else 0.5, 1.0)

    def _calculate_semantic_similarity(self, result: Dict, query: SearchQuery) -> float:
        """حساب التشابه الدلالي (مبسط)"""
        # هذا تنفيذ مبسط - يمكن تحسينه باستخدام نماذج embedding
        result_text = f"{result.get('title', '')} {result.get('content', '')}".lower()
        query_text = query.original_query.lower()

        # حساب التشابه بناءً على الكلمات المشتركة
        result_words = set(result_text.split())
        query_words = set(query_text.split())

        if not query_words:
            return 0.0

        common_words = result_words.intersection(query_words)
        similarity = len(common_words) / len(query_words)

        return min(similarity, 1.0)

    async def _learn_from_results(self, query: SearchQuery, strategy: SearchStrategy,
                                results: List[SearchResult], execution_time: float):
        """التعلم من نتائج البحث"""
        try:
            # حساب نقاط النجاح
            success_score = self._calculate_success_score(results)

            # تحديث إحصائيات الاستراتيجية
            self.learning_stats['strategy_performance'][strategy.value].append(success_score)

            # تحديث تفضيلات المحركات
            for result in results:
                engine = result.engine
                self.learning_stats['engine_performance'][engine].append(result.final_score)

                # تحديث تفضيلات المحرك للسياق
                context_key = query.context.value
                if context_key not in self.engine_preferences:
                    self.engine_preferences[context_key] = {}

                if engine not in self.engine_preferences[context_key]:
                    self.engine_preferences[context_key][engine] = []

                self.engine_preferences[context_key][engine].append(result.final_score)

            # حفظ في قاعدة البيانات
            await self._save_search_history(query, strategy, results, success_score, execution_time)

            # حفظ بيانات التعلم كل 10 عمليات بحث
            if self.learning_stats['total_searches'] % 10 == 0:
                self._save_learning_data()

        except Exception as e:
            logger.error(f"❌ فشل في التعلم من النتائج: {e}")

    def _calculate_success_score(self, results: List[SearchResult]) -> float:
        """حساب نقاط نجاح البحث"""
        if not results:
            return 0.0

        # متوسط النقاط النهائية للنتائج
        avg_score = sum(result.final_score for result in results) / len(results)

        # مكافأة للعدد المناسب من النتائج
        count_bonus = min(len(results) / 10, 0.2)

        return min(avg_score + count_bonus, 1.0)

    async def _fallback_search(self, query: str, max_results: int) -> List[SearchResult]:
        """البحث الاحتياطي عند فشل النظام الذكي"""
        try:
            # استخدام النظام المحسن الموجود
            if enhanced_search_manager:
                result = await enhanced_search_manager.comprehensive_search(query, max_results)
                raw_results = result.get('results', [])
            else:
                # استخدام Tavily
                raw_results = await tavily_search.search(query, max_results=max_results)

            # تحويل إلى SearchResult
            fallback_results = []
            for result in raw_results:
                search_result = SearchResult(
                    title=result.get('title', ''),
                    url=result.get('url', ''),
                    content=result.get('content', ''),
                    summary=result.get('summary', ''),
                    source=result.get('source', ''),
                    engine='fallback',
                    relevance_score=0.5,
                    quality_score=0.5,
                    freshness_score=0.5,
                    authority_score=0.5,
                    final_score=0.5,
                    timestamp=datetime.now(),
                    context_match=0.5,
                    semantic_similarity=0.5,
                    extracted_entities=[],
                    metadata=result
                )
                fallback_results.append(search_result)

            return fallback_results

        except Exception as e:
            logger.error(f"❌ فشل في البحث الاحتياطي: {e}")
            return []

    def _get_best_engine_for_context(self, context: SearchContext) -> Dict:
        """الحصول على أفضل محرك للسياق المحدد"""
        context_key = context.value

        if context_key in self.engine_preferences:
            # حساب متوسط الأداء لكل محرك
            engine_averages = {}
            for engine, scores in self.engine_preferences[context_key].items():
                if scores:
                    engine_averages[engine] = sum(scores) / len(scores)

            if engine_averages:
                best_engine_name = max(engine_averages, key=engine_averages.get)
                return self._get_engine_instance(best_engine_name)

        # إرجاع المحرك الافتراضي
        return {'name': 'tavily', 'instance': tavily_search}

    def _get_backup_engines(self, context: SearchContext, exclude: str = None) -> List[Dict]:
        """الحصول على محركات احتياطية"""
        all_engines = [
            {'name': 'tavily', 'instance': tavily_search},
            {'name': 'serpapi', 'instance': serpapi_search},
            {'name': 'enhanced', 'instance': enhanced_search_manager}
        ]

        # استبعاد المحرك المحدد
        if exclude:
            all_engines = [e for e in all_engines if e['name'] != exclude]

        return all_engines

    def _get_engine_instance(self, engine_name: str) -> Dict:
        """الحصول على مثيل المحرك"""
        engines = {
            'tavily': tavily_search,
            'serpapi': serpapi_search,
            'enhanced': enhanced_search_manager
        }

        return {
            'name': engine_name,
            'instance': engines.get(engine_name, tavily_search)
        }

    def _find_similar_query_patterns(self, query: SearchQuery) -> List[Dict]:
        """البحث عن أنماط استعلامات مشابهة"""
        similar_patterns = []

        # البحث في الذاكرة قصيرة المدى
        for memory_item in self.short_term_memory:
            if memory_item['query'].context == query.context:
                # حساب التشابه
                similarity = self._calculate_query_similarity(query, memory_item['query'])
                if similarity > 0.6:
                    similar_patterns.append({
                        'query': memory_item['query'],
                        'strategy': memory_item['strategy'],
                        'success_score': memory_item['success_score'],
                        'similarity': similarity
                    })

        # ترتيب حسب التشابه والنجاح
        similar_patterns.sort(key=lambda x: x['similarity'] * x['success_score'], reverse=True)

        return similar_patterns[:5]  # أفضل 5 أنماط

    def _calculate_query_similarity(self, query1: SearchQuery, query2: SearchQuery) -> float:
        """حساب التشابه بين استعلامين"""
        # تشابه الكلمات المفتاحية
        keywords1 = set(query1.keywords)
        keywords2 = set(query2.keywords)

        if not keywords1 or not keywords2:
            return 0.0

        common_keywords = keywords1.intersection(keywords2)
        keyword_similarity = len(common_keywords) / max(len(keywords1), len(keywords2))

        # تشابه الكيانات
        entities1 = set(query1.entities)
        entities2 = set(query2.entities)

        entity_similarity = 0.0
        if entities1 or entities2:
            common_entities = entities1.intersection(entities2)
            entity_similarity = len(common_entities) / max(len(entities1), len(entities2)) if (entities1 or entities2) else 0

        # تشابه السياق
        context_similarity = 1.0 if query1.context == query2.context else 0.0

        # تشابه النية
        intent_similarity = 1.0 if query1.intent == query2.intent else 0.0

        # حساب التشابه الإجمالي
        total_similarity = (
            keyword_similarity * 0.4 +
            entity_similarity * 0.3 +
            context_similarity * 0.2 +
            intent_similarity * 0.1
        )

        return total_similarity

    def _generate_predictive_queries(self, query: SearchQuery, patterns: List[Dict]) -> List[str]:
        """توليد استعلامات تنبؤية"""
        predictive_queries = [query.processed_query]

        # إضافة استعلامات من الأنماط الناجحة
        for pattern in patterns[:3]:
            if pattern['success_score'] > 0.7:
                predictive_queries.append(pattern['query'].processed_query)

        # توليد استعلامات بناءً على السياق
        if query.context == SearchContext.BREAKING_NEWS:
            predictive_queries.extend([
                f"{query.original_query} breaking news",
                f"{query.original_query} latest announcement",
                f"{query.original_query} just revealed"
            ])
        elif query.context == SearchContext.GAME_REVIEWS:
            predictive_queries.extend([
                f"{query.original_query} review score",
                f"{query.original_query} rating opinion",
                f"{query.original_query} gameplay analysis"
            ])

        return list(set(predictive_queries))  # إزالة التكرار

    async def _save_search_history(self, query: SearchQuery, strategy: SearchStrategy,
                                 results: List[SearchResult], success_score: float,
                                 execution_time: float):
        """حفظ تاريخ البحث في قاعدة البيانات"""
        try:
            query_hash = hashlib.md5(query.original_query.encode()).hexdigest()
            engines_used = list(set(result.engine for result in results))

            with sqlite3.connect(self.db_path) as conn:
                conn.execute('''
                    INSERT OR REPLACE INTO search_history
                    (query_hash, original_query, processed_query, context, strategy,
                     engines_used, results_count, success_score, execution_time, timestamp, metadata)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    query_hash,
                    query.original_query,
                    query.processed_query,
                    query.context.value,
                    strategy.value,
                    json.dumps(engines_used),
                    len(results),
                    success_score,
                    execution_time,
                    time.time(),
                    json.dumps({
                        'intent': query.intent,
                        'entities': query.entities,
                        'keywords': query.keywords,
                        'priority_score': query.priority_score
                    })
                ))

        except Exception as e:
            logger.error(f"❌ فشل في حفظ تاريخ البحث: {e}")

    def get_performance_stats(self) -> Dict:
        """الحصول على إحصائيات الأداء"""
        stats = {
            'total_searches': self.learning_stats['total_searches'],
            'successful_searches': self.learning_stats['successful_searches'],
            'success_rate': 0.0,
            'strategy_performance': {},
            'engine_performance': {},
            'context_distribution': dict(self.learning_stats['context_patterns'])
        }

        if self.learning_stats['total_searches'] > 0:
            stats['success_rate'] = self.learning_stats['successful_searches'] / self.learning_stats['total_searches']

        # حساب متوسط أداء الاستراتيجيات
        for strategy, scores in self.learning_stats['strategy_performance'].items():
            if scores:
                stats['strategy_performance'][strategy] = {
                    'average_score': sum(scores) / len(scores),
                    'usage_count': len(scores),
                    'best_score': max(scores),
                    'worst_score': min(scores)
                }

        # حساب متوسط أداء المحركات
        for engine, scores in self.learning_stats['engine_performance'].items():
            if scores:
                stats['engine_performance'][engine] = {
                    'average_score': sum(scores) / len(scores),
                    'usage_count': len(scores),
                    'best_score': max(scores),
                    'worst_score': min(scores)
                }

        return stats

    async def optimize_search_strategy(self):
        """تحسين استراتيجيات البحث بناءً على البيانات المجمعة"""
        try:
            # تحليل الأداء وتحديث التفضيلات
            performance_stats = self.get_performance_stats()

            # تحديث تفضيلات المحركات بناءً على الأداء
            for context, engines in self.engine_preferences.items():
                for engine, scores in engines.items():
                    if len(scores) > 5:  # عينة كافية
                        avg_score = sum(scores) / len(scores)
                        # إزالة النقاط القديمة إذا كان الأداء ضعيف
                        if avg_score < 0.5:
                            self.engine_preferences[context][engine] = scores[-3:]  # احتفظ بآخر 3 فقط

            logger.info("🔧 تم تحسين استراتيجيات البحث")

        except Exception as e:
            logger.error(f"❌ فشل في تحسين استراتيجيات البحث: {e}")

# إنشاء مثيل عام
intelligent_search_manager = IntelligentSearchManager()
