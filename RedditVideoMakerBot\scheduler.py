#!/usr/bin/env python3
"""
نظام الجدولة والتشغيل المستمر لـ Reddit Video Maker Bot
يعمل كل 10 ساعات (صباحاً ومساءً) مع إعادة التشغيل التلقائي
"""

import schedule
import time
import logging
import sys
import traceback
import threading
from datetime import datetime, timedelta
from pathlib import Path
import psutil
import os
import signal
from typing import Optional

# إعداد نظام السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/scheduler.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# إنشاء مجلد السجلات
Path("logs").mkdir(exist_ok=True)

class VideoScheduler:
    def __init__(self):
        self.is_running = False
        self.current_process: Optional[psutil.Process] = None
        self.last_success = None
        self.error_count = 0
        self.max_errors = 5
        self.restart_delay = 300  # 5 دقائق
        
        # إعداد معالج الإشارات للإغلاق الآمن
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
    def _signal_handler(self, signum, frame):
        """معالج إشارات الإغلاق الآمن"""
        logger.info(f"تم استلام إشارة الإغلاق: {signum}")
        self.stop_scheduler()
        sys.exit(0)
        
    def create_video(self):
        """تشغيل عملية إنشاء الفيديو"""
        if self.is_running:
            logger.warning("عملية إنشاء فيديو قيد التشغيل بالفعل، تم تخطي هذه الجولة")
            return
            
        try:
            self.is_running = True
            logger.info("🎬 بدء عملية إنشاء فيديو جديد...")
            
            # استيراد وتشغيل الوحدة الرئيسية
            from main import main
            from automation.telegram_bot import send_notification
            from automation.youtube_uploader import upload_to_youtube
            
            # إنشاء الفيديو
            main()
            
            # البحث عن أحدث فيديو في مجلد results
            results_dir = Path("results")
            if results_dir.exists():
                video_files = list(results_dir.glob("*.mp4"))
                if video_files:
                    # ترتيب حسب تاريخ الإنشاء
                    latest_video = max(video_files, key=lambda x: x.stat().st_mtime)
                    
                    # رفع الفيديو على YouTube
                    video_url = upload_to_youtube(latest_video)
                    
                    # إرسال إشعار نجاح
                    message = f"✅ تم إنشاء ونشر فيديو جديد بنجاح!\n🎥 الملف: {latest_video.name}\n🔗 الرابط: {video_url}"
                    send_notification(message)
                    
                    self.last_success = datetime.now()
                    self.error_count = 0
                    logger.info(f"✅ تم إنشاء ونشر الفيديو بنجاح: {latest_video.name}")
                else:
                    raise Exception("لم يتم العثور على ملفات فيديو في مجلد results")
            else:
                raise Exception("مجلد results غير موجود")
                
        except Exception as e:
            self.error_count += 1
            error_msg = f"❌ خطأ في إنشاء الفيديو (المحاولة {self.error_count}/{self.max_errors}): {str(e)}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            
            # إرسال إشعار خطأ
            try:
                from automation.telegram_bot import send_notification
                send_notification(f"{error_msg}\n\n```\n{traceback.format_exc()}\n```")
            except:
                pass
                
            # إعادة تشغيل النظام إذا تجاوز عدد الأخطاء الحد المسموح
            if self.error_count >= self.max_errors:
                logger.critical("تم تجاوز الحد الأقصى للأخطاء، سيتم إعادة تشغيل النظام...")
                self.restart_system()
                
        finally:
            self.is_running = False
            
    def restart_system(self):
        """إعادة تشغيل النظام بالكامل"""
        try:
            from automation.telegram_bot import send_notification
            send_notification("🔄 إعادة تشغيل النظام بسبب تجاوز عدد الأخطاء...")
        except:
            pass
            
        logger.info("🔄 إعادة تشغيل النظام...")
        time.sleep(self.restart_delay)
        
        # إعادة تشغيل البرنامج
        os.execv(sys.executable, ['python'] + sys.argv)
        
    def setup_schedule(self):
        """إعداد جدولة المهام"""
        # جدولة كل 10 ساعات
        schedule.every(10).hours.do(self.create_video)
        
        # جدولة محددة: 8 صباحاً و 8 مساءً
        schedule.every().day.at("08:00").do(self.create_video)
        schedule.every().day.at("20:00").do(self.create_video)
        
        logger.info("📅 تم إعداد الجدولة: كل 10 ساعات + 8 صباحاً و 8 مساءً")
        
    def run_scheduler(self):
        """تشغيل نظام الجدولة"""
        logger.info("🚀 بدء تشغيل نظام الجدولة...")
        
        # إرسال إشعار بدء التشغيل
        try:
            from automation.telegram_bot import send_notification
            send_notification("🚀 تم بدء تشغيل نظام إنشاء الفيديوهات التلقائي!")
        except Exception as e:
            logger.warning(f"فشل في إرسال إشعار البدء: {e}")
            
        # تشغيل فيديو أول فوراً
        threading.Thread(target=self.create_video, daemon=True).start()
        
        # حلقة الجدولة الرئيسية
        while True:
            try:
                schedule.run_pending()
                time.sleep(60)  # فحص كل دقيقة
                
                # فحص حالة النظام كل ساعة
                if datetime.now().minute == 0:
                    self.health_check()
                    
            except KeyboardInterrupt:
                logger.info("تم إيقاف النظام بواسطة المستخدم")
                break
            except Exception as e:
                logger.error(f"خطأ في حلقة الجدولة: {e}")
                time.sleep(60)
                
    def health_check(self):
        """فحص حالة النظام"""
        try:
            # فحص استخدام الذاكرة
            memory_percent = psutil.virtual_memory().percent
            disk_percent = psutil.disk_usage('/').percent
            
            # تسجيل الحالة
            logger.info(f"📊 حالة النظام - الذاكرة: {memory_percent}% | القرص: {disk_percent}%")
            
            # تحذير إذا كانت الموارد مرتفعة
            if memory_percent > 90 or disk_percent > 90:
                warning_msg = f"⚠️ تحذير: استخدام مرتفع للموارد - الذاكرة: {memory_percent}% | القرص: {disk_percent}%"
                logger.warning(warning_msg)
                
                try:
                    from automation.telegram_bot import send_notification
                    send_notification(warning_msg)
                except:
                    pass
                    
        except Exception as e:
            logger.error(f"خطأ في فحص حالة النظام: {e}")
            
    def stop_scheduler(self):
        """إيقاف نظام الجدولة"""
        logger.info("🛑 إيقاف نظام الجدولة...")
        
        # إيقاف العمليات الجارية
        if self.current_process:
            try:
                self.current_process.terminate()
                self.current_process.wait(timeout=30)
            except:
                try:
                    self.current_process.kill()
                except:
                    pass
                    
        # إرسال إشعار الإيقاف
        try:
            from automation.telegram_bot import send_notification
            send_notification("🛑 تم إيقاف نظام إنشاء الفيديوهات")
        except:
            pass

def main():
    """الدالة الرئيسية"""
    scheduler = VideoScheduler()
    scheduler.setup_schedule()
    scheduler.run_scheduler()

if __name__ == "__main__":
    main()
