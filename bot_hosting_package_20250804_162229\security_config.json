{"max_login_attempts": 5, "lockout_duration": 300, "session_timeout": 3600, "rate_limits": {"messages": {"limit": 30, "window": 60}, "commands": {"limit": 10, "window": 60}, "api_calls": {"limit": 100, "window": 60}, "file_uploads": {"limit": 5, "window": 300}}, "blocked_patterns": ["<script[^>]*>.*?</script>", "javascript:", "on\\w+\\s*=", "eval\\s*\\(", "exec\\s*\\(", "union\\s+select", "drop\\s+table", "delete\\s+from", "insert\\s+into", "update\\s+.*\\s+set", "\\.\\./"], "allowed_file_types": [".jpg", ".jpeg", ".png", ".gif", ".pdf", ".zip", ".rar", ".txt", ".doc", ".docx"], "max_file_size": 10485760, "encryption_enabled": true, "audit_enabled": true, "threat_detection": {"enabled": true, "sensitivity": "medium", "auto_block": true, "quarantine_suspicious": true}, "ip_filtering": {"enabled": true, "whitelist": [], "blacklist": [], "geo_blocking": {"enabled": false, "blocked_countries": []}}, "data_protection": {"encrypt_sensitive_data": true, "data_retention_days": 30, "auto_cleanup": true, "backup_encryption": true}, "monitoring": {"log_all_requests": true, "alert_on_suspicious": true, "daily_reports": true, "real_time_monitoring": true}, "admin_security": {"require_2fa": false, "admin_session_timeout": 1800, "admin_ip_whitelist": [], "admin_action_logging": true}}