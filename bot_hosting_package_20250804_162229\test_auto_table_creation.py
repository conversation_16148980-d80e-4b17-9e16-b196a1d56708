#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار النظام التلقائي لإنشاء الجداول
Test automatic table creation system
"""

import os
import sys
import logging
from datetime import datetime

# تحميل متغيرات البيئة
from dotenv import load_dotenv
load_dotenv()

# إعداد التسجيل
logging.basicConfig(
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    level=logging.INFO,
    datefmt="%Y-%m-%d %H:%M:%S",
)
logger = logging.getLogger(__name__)

def test_table_creation():
    """اختبار إنشاء الجداول تلقائياً"""
    logger.info("🔧 بدء اختبار النظام التلقائي لإنشاء الجداول...")
    
    try:
        # استيراد الدوال المطلوبة
        from supabase_client import (
            test_supabase_connection,
            create_all_missing_tables,
            create_notifications_tables
        )
        
        # 1. اختبار الاتصال مع Supabase
        logger.info("📡 اختبار الاتصال مع Supabase...")
        if test_supabase_connection():
            logger.info("✅ الاتصال مع Supabase يعمل")
        else:
            logger.error("❌ فشل في الاتصال مع Supabase")
            return False
        
        # 2. اختبار إنشاء جميع الجداول
        logger.info("🔧 اختبار إنشاء جميع الجداول...")
        if create_all_missing_tables():
            logger.info("✅ تم إنشاء الجداول بنجاح")
        else:
            logger.warning("⚠️ فشل في إنشاء بعض الجداول، تحقق من ملفات SQL المُنشأة")
        
        # 3. اختبار إنشاء جداول الإشعارات
        logger.info("🔔 اختبار جداول الإشعارات...")
        if create_notifications_tables():
            logger.info("✅ جداول الإشعارات تعمل")
        else:
            logger.warning("⚠️ مشكلة في جداول الإشعارات")
        
        # 4. التحقق من الملفات المُنشأة
        logger.info("📄 التحقق من ملفات SQL المُنشأة...")
        sql_files = [f for f in os.listdir('.') if f.startswith('create_') and f.endswith('.sql')]
        
        if sql_files:
            logger.info(f"📋 تم العثور على {len(sql_files)} ملف SQL:")
            for file in sql_files:
                logger.info(f"   - {file}")
            logger.info("💡 يمكنك تنفيذ هذه الملفات في Supabase SQL Editor")
        else:
            logger.info("📋 لم يتم إنشاء ملفات SQL إضافية")
        
        return True
        
    except ImportError as e:
        logger.error(f"❌ خطأ في الاستيراد: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ خطأ في الاختبار: {e}")
        return False

def test_table_access():
    """اختبار الوصول للجداول"""
    logger.info("🔍 اختبار الوصول للجداول...")
    
    try:
        from supabase_client import safe_supabase_request, SUPABASE_URL
        
        # قائمة الجداول للاختبار
        tables_to_test = [
            'mods',
            'saved_notifications',
            'notification_broadcasts',
            'notification_user_logs',
            'tasks',
            'user_task_completions'
        ]
        
        accessible_tables = []
        inaccessible_tables = []
        
        for table_name in tables_to_test:
            try:
                url = f"{SUPABASE_URL}/rest/v1/{table_name}?limit=1"
                response = safe_supabase_request('GET', url)
                
                if response and response.status_code == 200:
                    logger.info(f"✅ الجدول {table_name}: متاح")
                    accessible_tables.append(table_name)
                else:
                    logger.warning(f"⚠️ الجدول {table_name}: غير متاح")
                    inaccessible_tables.append(table_name)
                    
            except Exception as e:
                logger.warning(f"❌ خطأ في الوصول للجدول {table_name}: {e}")
                inaccessible_tables.append(table_name)
        
        # النتائج
        logger.info(f"📊 النتائج:")
        logger.info(f"   ✅ جداول متاحة: {len(accessible_tables)}")
        logger.info(f"   ⚠️ جداول غير متاحة: {len(inaccessible_tables)}")
        
        if inaccessible_tables:
            logger.info(f"📋 الجداول غير المتاحة: {', '.join(inaccessible_tables)}")
            logger.info("💡 قد تحتاج لإنشاء هذه الجداول يدوياً في Supabase")
        
        return len(accessible_tables) > 0
        
    except Exception as e:
        logger.error(f"❌ خطأ في اختبار الوصول للجداول: {e}")
        return False

def generate_manual_instructions():
    """إنشاء تعليمات التنفيذ اليدوي"""
    logger.info("📋 إنشاء تعليمات التنفيذ اليدوي...")
    
    try:
        instructions = f"""
# 📋 تعليمات إنشاء الجداول يدوياً
# Manual Table Creation Instructions

تاريخ الإنشاء: {datetime.now()}

## 🔧 الخطوات المطلوبة:

### 1. الدخول إلى Supabase Dashboard
- اذهب إلى: https://app.supabase.com
- سجل الدخول إلى حسابك
- اختر مشروعك

### 2. فتح SQL Editor
- من القائمة الجانبية، اختر "SQL Editor"
- انقر على "New Query"

### 3. تنفيذ ملفات SQL
قم بتنفيذ الملفات التالية بالترتيب:

"""
        
        # البحث عن ملفات SQL
        sql_files = [f for f in os.listdir('.') if f.startswith('create_') and f.endswith('.sql')]
        
        if sql_files:
            for i, file in enumerate(sql_files, 1):
                instructions += f"{i}. {file}\n"
        else:
            instructions += "- create_all_tables_fixed.sql (إذا كان متوفراً)\n"
        
        instructions += """
### 4. التحقق من النجاح
بعد تنفيذ الملفات، شغل هذا الاستعلام للتحقق:

```sql
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
ORDER BY table_name;
```

### 5. تشغيل البوت
بعد إنشاء الجداول بنجاح:

```bash
python main.py
```

## 🆘 في حالة المشاكل:
- تأكد من صحة إعدادات SUPABASE_URL و SUPABASE_KEY
- تأكد من وجود صلاحيات إنشاء الجداول
- راجع سجلات Supabase للأخطاء التفصيلية
"""
        
        # حفظ التعليمات
        with open('MANUAL_TABLE_CREATION.md', 'w', encoding='utf-8') as f:
            f.write(instructions)
        
        logger.info("✅ تم إنشاء ملف MANUAL_TABLE_CREATION.md")
        return True
        
    except Exception as e:
        logger.error(f"❌ خطأ في إنشاء التعليمات: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔧 اختبار النظام التلقائي لإنشاء الجداول")
    print("=" * 60)
    
    success_count = 0
    total_tests = 3
    
    # اختبار 1: إنشاء الجداول
    if test_table_creation():
        success_count += 1
    
    # اختبار 2: الوصول للجداول
    if test_table_access():
        success_count += 1
    
    # اختبار 3: إنشاء التعليمات
    if generate_manual_instructions():
        success_count += 1
    
    # النتائج النهائية
    print("\n" + "=" * 60)
    print(f"📊 النتائج: {success_count}/{total_tests} اختبارات نجحت")
    
    if success_count == total_tests:
        print("🎉 جميع الاختبارات نجحت!")
        print("📋 يمكنك الآن تشغيل البوت:")
        print("   python main.py")
    elif success_count > 0:
        print("⚠️ بعض الاختبارات نجحت.")
        print("📋 راجع ملف MANUAL_TABLE_CREATION.md للتعليمات")
    else:
        print("❌ فشلت جميع الاختبارات.")
        print("🔧 تحقق من إعدادات Supabase")
    
    return success_count > 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
