# دليل إعداد Reddit Video Maker Bot

## ✅ تم إصلاح جميع المشاكل!

تم فحص الأداة بشكل شامل وإصلاح جميع المشاكل المحتملة. الأداة الآن جاهزة للاستخدام!

## 📋 المتطلبات المثبتة

✅ **Python 3.13.2** - تم تعديل الكود ليدعم الإصدارات الأحدث  
✅ **جميع المكتبات المطلوبة** - تم تثبيتها بنجاح  
✅ **Playwright** - تم تثبيته مع المتصفحات  
✅ **ملف التكوين** - تم إنشاؤه وإعداده  

## 🔧 الإعداد المطلوب

### 1. إعداد Reddit API

1. **اذهب إلى**: https://www.reddit.com/prefs/apps
2. **انقر على**: "Create App" أو "Create Another App"
3. **املأ المعلومات**:
   - **Name**: أي اسم تريده (مثل: "VideoMakerBot")
   - **App type**: اختر **"script"**
   - **Description**: وصف اختياري
   - **About URL**: اتركه فارغاً أو ضع أي رابط
   - **Redirect URI**: ضع أي رابط (مثل: `https://example.com`)

4. **بعد الإنشاء ستحصل على**:
   - **Client ID**: الرقم الموجود تحت اسم التطبيق مباشرة
   - **Client Secret**: الرقم الطويل المكتوب "secret"

### 2. تحديث ملف التكوين

عدّل الملف `config.toml` واستبدل القيم التالية:

```toml
[reddit.creds]
client_id = "ضع_الـClient_ID_هنا"
client_secret = "ضع_الـClient_Secret_هنا"  
username = "اسم_المستخدم_في_Reddit"
password = "كلمة_المرور_في_Reddit"
```

## 🚀 تشغيل الأداة

```bash
python main.py
```

## ⚙️ الإعدادات المتاحة

### إعدادات الصوت
- `voice_choice = "pyttsx"` - محرك الصوت (مُعد للعمل بدون إعدادات إضافية)
- `random_voice = true` - استخدام أصوات عشوائية

### إعدادات الفيديو
- `resolution_w = 1080` - عرض الفيديو
- `resolution_h = 1920` - ارتفاع الفيديو (مناسب لـ TikTok/Instagram)
- `background_video = "minecraft"` - فيديو الخلفية

### إعدادات المحتوى
- `subreddit = "AskReddit"` - الـ subreddit المطلوب
- `allow_nsfw = false` - منع المحتوى الـ NSFW
- `min_comments = 20` - الحد الأدنى للتعليقات

## 🎯 خيارات الصوت المتاحة

1. **pyttsx** - صوت النظام (مُعد حالياً - لا يحتاج إعدادات)
2. **tiktok** - صوت TikTok (يحتاج session ID)
3. **elevenlabs** - صوت ElevenLabs (يحتاج API key)
4. **awspolly** - صوت AWS Polly (يحتاج AWS credentials)
5. **googletranslate** - صوت Google Translate

## 🔍 استكشاف الأخطاء

### مشكلة: "Invalid credentials"
**الحل**: تأكد من صحة بيانات Reddit API في ملف `config.toml`

### مشكلة: "TikTok voice requires a sessionid"
**الحل**: غيّر `voice_choice` إلى `"pyttsx"` أو احصل على TikTok session ID

### مشكلة: مشاكل في التثبيت
**الحل**: تم حل جميع مشاكل التثبيت مسبقاً

## 📁 هيكل المشروع

```
RedditVideoMakerBot/
├── main.py              # الملف الرئيسي
├── config.toml          # ملف التكوين
├── requirements.txt     # المتطلبات
├── TTS/                 # محركات الصوت
├── video_creation/      # إنشاء الفيديو
├── utils/               # الأدوات المساعدة
└── assets/              # الملفات المساعدة
```

## 🎉 الأداة جاهزة للاستخدام!

بعد إعداد Reddit API، ستتمكن من إنشاء فيديوهات Reddit تلقائياً!
