# نظام استخراج المحتوى المتقدم
import requests
from bs4 import BeautifulSoup
import time
import re
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple
import json
from urllib.parse import urljoin, urlparse
import hashlib
from .logger import logger
from .database import db
from .web_search import WebSearch
from .advanced_news_apis import advanced_news_apis
from .advanced_web_scraper import advanced_web_scraper
from .serpapi_search import serpapi_search
from .tavily_search import tavily_search
from .advanced_search_manager import advanced_search_manager
from .enhanced_search_manager import enhanced_search_manager, SearchPriority


from config.settings import BotConfig
from config.sources_blacklist import is_source_disabled, get_timeout_for_source, get_user_agent_for_source

class ContentScraper:
    """محرك استخراج المحتوى من المصادر المختلفة"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'ar,en-US;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        })
        self.last_request_time = {}
        self.min_delay = 2  # ثانيتين بين الطلبات
        self.failed_sources = set()  # تتبع المصادر المعطلة
        self.max_retries = 2  # عدد المحاولات لكل مصدر
        self.timeout_settings = {
            'default': 30,
            'slow_sites': 60,
            'fast_sites': 15
        }

        # تهيئة WebSearch
        search_engine_id = BotConfig.GOOGLE_SEARCH_ENGINE_ID
        self.web_search = WebSearch(search_engine_id) if search_engine_id else None

    def reset_failed_sources(self):
        """إعادة تعيين قائمة المصادر المعطلة (يمكن استدعاؤها دورياً)"""
        if self.failed_sources:
            logger.info(f"🔄 إعادة تعيين {len(self.failed_sources)} مصدر معطل")
            self.failed_sources.clear()

    def get_failed_sources(self) -> set:
        """الحصول على قائمة المصادر المعطلة"""
        return self.failed_sources.copy()

    def _get_timeout_for_url(self, url: str) -> int:
        """تحديد timeout مناسب حسب نوع الموقع"""
        domain = urlparse(url).netloc.lower()

        # مواقع بطيئة معروفة
        slow_sites = ['reddit.com', 'wikipedia.org', 'youtube.com']
        if any(site in domain for site in slow_sites):
            return self.timeout_settings['slow_sites']

        # مواقع سريعة معروفة
        fast_sites = ['ign.com', 'gamespot.com', 'polygon.com']
        if any(site in domain for site in fast_sites):
            return self.timeout_settings['fast_sites']

        return self.timeout_settings['default']
    
    def _respect_rate_limit(self, domain: str):
        """احترام حدود معدل الطلبات"""
        if domain in self.last_request_time:
            elapsed = time.time() - self.last_request_time[domain]
            if elapsed < self.min_delay:
                sleep_time = self.min_delay - elapsed
                logger.debug(f"⏰ انتظار {sleep_time:.2f} ثانية لاحترام حدود المعدل للنطاق {domain}")
                time.sleep(sleep_time)
        
        self.last_request_time[domain] = time.time()
    
    def fetch_page(self, url: str, timeout: int = None) -> Optional[BeautifulSoup]:
        """جلب صفحة ويب وتحليلها"""
        # تحقق من المصادر المعطلة محلياً
        if url in self.failed_sources:
            logger.debug(f"⏭️ تخطي مصدر معطل محلياً: {url}")
            return None

        # تحقق من المصادر المعطلة في القائمة السوداء
        if is_source_disabled(url):
            logger.debug(f"⏭️ تخطي مصدر في القائمة السوداء: {url}")
            self.failed_sources.add(url)
            return None

        try:
            # تحديث User-Agent للمصدر المحدد
            original_user_agent = self.session.headers.get('User-Agent')
            custom_user_agent = get_user_agent_for_source(url)
            if custom_user_agent != original_user_agent:
                self.session.headers['User-Agent'] = custom_user_agent
                logger.debug(f"🔧 استخدام User-Agent مخصص لـ {url}")
            domain = urlparse(url).netloc
            self._respect_rate_limit(domain)

            # تحديد timeout مناسب
            if timeout is None:
                timeout = get_timeout_for_source(url)

            logger.debug(f"🌐 جلب الصفحة: {url} (timeout: {timeout}s)")

            response = self.session.get(url, timeout=timeout)
            response.raise_for_status()

            # التحقق من نوع المحتوى
            content_type = response.headers.get('content-type', '').lower()
            if 'text/html' not in content_type:
                logger.warning(f"⚠️ نوع محتوى غير متوقع: {content_type} للرابط {url}")
                return None

            soup = BeautifulSoup(response.content, 'html.parser')
            logger.debug(f"✅ تم جلب الصفحة بنجاح: {url}")

            return soup

        except requests.exceptions.HTTPError as e:
            if e.response.status_code in [403, 404, 410]:
                # إضافة المصدر للقائمة السوداء
                self.failed_sources.add(url)
                logger.warning(f"🚫 تم إضافة {url} للمصادر المعطلة (HTTP {e.response.status_code})")
            else:
                logger.error(f"❌ فشل في جلب الصفحة {url} | نوع الخطأ: HTTPError | التفاصيل: {e.response.status_code} {e.response.reason}")
            db.log_error("http_error", f"{e.response.status_code}: {e.response.reason}", url)
            return None
        except requests.exceptions.RequestException as e:
            logger.error(f"❌ فشل في جلب الصفحة {url} | نوع الخطأ: {type(e).__name__} | التفاصيل: {str(e)}")
            db.log_error("network_error", str(e), url)
            return None
        except Exception as e:
            logger.error(f"❌ خطأ غير متوقع أثناء جلب الصفحة {url} | نوع الخطأ: {type(e).__name__} | التفاصيل: {str(e)}")
            db.log_error("scraping_error", str(e), url)
            return None
        finally:
            # استعادة User-Agent الأصلي
            if 'original_user_agent' in locals() and original_user_agent:
                self.session.headers['User-Agent'] = original_user_agent
    
    def extract_articles(self, url: str, site_type: str) -> List[Dict]:
        """استخراج المقالات من موقع أو البحث عبر الويب"""
        # إذا كان النوع هو بحث جوجل، فقم بالبحث مباشرةً وتجاوز جلب الصفحة
        if site_type == "google_search":
            query = url  # في هذه الحالة، 'url' هو استعلام البحث
            return self.search_and_extract_articles(query)

        # بالنسبة للأنواع الأخرى، استمر في جلب الصفحة كالمعتاد
        soup = self.fetch_page(url)
        if not soup:
            return []

        articles = []

        try:
            if site_type == "official_site":
                articles = self._extract_official_site(soup, url)
            elif site_type == "gaming_site":
                articles = self._extract_gaming_site(soup, url)
            elif site_type == "arabic_site":
                articles = self._extract_arabic_site(soup, url)
            elif site_type == "review_site":
                articles = self._extract_review_site(soup, url)
            elif site_type == "forum_site":
                articles = self._extract_forum_site(soup, url)
            elif site_type == "news_site":
                # تأكد من وجود هذه الدالة أو إزالتها إذا لم تكن موجودة
                articles = self._extract_news_site(soup, url) if hasattr(self, '_extract_news_site') else self._extract_generic(soup, url)
            else:
                articles = self._extract_generic(soup, url)

            logger.info(f"📰 تم استخراج {len(articles)} مقال من {url}")

            return articles

        except Exception as e:
            logger.error(f"❌ فشل في استخراج المقالات من {url}", e)
            return []
    
    def _extract_official_site(self, soup: BeautifulSoup, base_url: str) -> List[Dict]:
        """استخراج من موقع ألعاب رسمي"""
        articles = []
        
        # البحث عن مقالات في صفحة الأخبار الرسمية
        article_selectors = [
            'article.article-card',
            '.article-tile',
            '.news-item',
            '.article-preview',
            'article'
        ]
        
        for selector in article_selectors:
            article_elements = soup.select(selector)
            if article_elements:
                break
        
        for element in article_elements:
            try:
                # استخراج العنوان
                title_elem = element.select_one('h1, h2, h3, .title, .headline, .article-title')
                if not title_elem:
                    continue
                
                title = title_elem.get_text(strip=True)
                
                # استخراج الرابط
                link_elem = element.select_one('a')
                if link_elem and link_elem.get('href'):
                    article_url = urljoin(base_url, link_elem.get('href'))
                else:
                    continue
                
                # استخراج الملخص
                summary_elem = element.select_one('.excerpt, .summary, .description, p')
                summary = summary_elem.get_text(strip=True) if summary_elem else ""
                
                # استخراج التاريخ
                date_elem = element.select_one('.date, .published, time')
                published_date = self._extract_date(date_elem) if date_elem else datetime.now()
                
                # استخراج الصورة
                img_elem = element.select_one('img')
                image_url = None
                if img_elem and img_elem.get('src'):
                    image_url = urljoin(base_url, img_elem.get('src'))
                
                articles.append({
                    'title': title,
                    'url': article_url,
                    'summary': summary,
                    'published_date': published_date,
                    'image_url': image_url,
                    'source': 'official_site',
                    'source_url': base_url
                })
                
            except Exception as e:
                logger.warning(f"⚠️ تخطي عنصر بسبب خطأ في الاستخراج", e)
                continue
        
        return articles
    
    def _extract_gaming_site(self, soup: BeautifulSoup, base_url: str) -> List[Dict]:
        """استخراج من مواقع الألعاب"""
        articles = []
        
        # محددات شائعة لمواقع الألعاب
        article_selectors = [
            'article',
            '.post',
            '.news-item',
            '.article-item',
            '.content-item',
            '.story'
        ]
        
        for selector in article_selectors:
            article_elements = soup.select(selector)
            if article_elements:
                break
        
        for element in article_elements:
            try:
                # استخراج العنوان
                title_elem = element.select_one('h1, h2, h3, h4, .title, .headline')
                if not title_elem:
                    continue
                
                title = title_elem.get_text(strip=True)
                
                # استخراج الرابط
                link_elem = element.select_one('a') or title_elem.select_one('a')
                if link_elem and link_elem.get('href'):
                    article_url = urljoin(base_url, link_elem.get('href'))
                else:
                    continue
                
                # استخراج المحتوى
                content_elem = element.select_one('.content, .excerpt, .summary, p')
                content = content_elem.get_text(strip=True) if content_elem else ""
                
                # استخراج التاريخ
                date_elem = element.select_one('.date, .published, .timestamp, time')
                published_date = self._extract_date(date_elem) if date_elem else datetime.now()
                
                articles.append({
                    'title': title,
                    'url': article_url,
                    'summary': content,
                    'published_date': published_date,
                    'source': 'gaming_site',
                    'source_url': base_url
                })
                
            except Exception as e:
                logger.warning(f"⚠️ تخطي عنصر من موقع الألعاب", e)
                continue
        
        return articles
    
    def _extract_arabic_site(self, soup: BeautifulSoup, base_url: str) -> List[Dict]:
        """استخراج من المواقع العربية"""
        articles = []
        
        # معالجة خاصة للمواقع العربية
        article_selectors = [
            'article',
            '.post',
            '.entry',
            '.article',
            '.news-item'
        ]
        
        for selector in article_selectors:
            article_elements = soup.select(selector)
            if article_elements:
                break
        
        for element in article_elements:
            try:
                # استخراج العنوان
                title_elem = element.select_one('h1, h2, h3, .title, .entry-title')
                if not title_elem:
                    continue
                
                title = title_elem.get_text(strip=True)
                
                # استخراج الرابط
                link_elem = element.select_one('a') or title_elem.select_one('a')
                if link_elem and link_elem.get('href'):
                    article_url = urljoin(base_url, link_elem.get('href'))
                else:
                    continue
                
                # استخراج المحتوى
                content_elem = element.select_one('.excerpt, .content, .entry-content, p')
                content = content_elem.get_text(strip=True) if content_elem else ""
                
                articles.append({
                    'title': title,
                    'url': article_url,
                    'summary': content,
                    'published_date': datetime.now(),
                    'source': 'arabic_site',
                    'source_url': base_url
                })
                
            except Exception as e:
                logger.warning(f"⚠️ تخطي عنصر من الموقع العربي", e)
                continue
        
        return articles

    def _extract_review_site(self, soup: BeautifulSoup, base_url: str) -> List[Dict]:
        """استخراج من مواقع المراجعات (مثل IGN, GameSpot)"""
        articles = []
        
        article_selectors = [
            'article.review',
            '.review-item',
            'div[class*="review-summary"]'
        ]
        
        for selector in article_selectors:
            article_elements = soup.select(selector)
            if article_elements:
                break

        for element in article_elements:
            try:
                title_elem = element.select_one('h1, h2, h3, .title, .review-title')
                if not title_elem:
                    continue
                title = title_elem.get_text(strip=True)
                
                link_elem = element.select_one('a')
                if not (link_elem and link_elem.get('href')):
                    continue
                article_url = urljoin(base_url, link_elem.get('href'))
                
                summary_elem = element.select_one('.summary, .deck, .review-deck, p')
                summary = summary_elem.get_text(strip=True) if summary_elem else ""
                
                articles.append({
                    'title': title,
                    'url': article_url,
                    'summary': summary,
                    'published_date': datetime.now(),
                    'source': 'review_site',
                    'source_url': base_url
                })
            except Exception as e:
                logger.warning(f"⚠️ تخطي عنصر من موقع المراجعات", e)
                continue
        
        return articles

    def _extract_forum_site(self, soup: BeautifulSoup, base_url: str) -> List[Dict]:
        """استخراج من مواقع المنتديات (مثل Reddit)"""
        articles = []
        
        # محددات خاصة بالمنتديات
        thread_selectors = [
            '.thread-item',
            'div[data-testid="post-container"]', # Reddit
            '.discussion-list-item'
        ]
        
        for selector in thread_selectors:
            thread_elements = soup.select(selector)
            if thread_elements:
                break

        for element in thread_elements:
            try:
                title_elem = element.select_one('h3, h2, a[data-testid="post-title"]')
                if not title_elem:
                    continue
                title = title_elem.get_text(strip=True)
                
                link_elem = element.select_one('a[data-testid="post-title"]') or element.select_one('a.thread-title-link')
                if not (link_elem and link_elem.get('href')):
                    continue
                article_url = urljoin(base_url, link_elem.get('href'))
                
                articles.append({
                    'title': title,
                    'url': article_url,
                    'summary': f"موضوع من منتدى: {title}",
                    'published_date': datetime.now(),
                    'source': 'forum_site',
                    'source_url': base_url
                })
            except Exception as e:
                logger.warning(f"⚠️ تخطي عنصر من موقع المنتدى", e)
                continue
        
        return articles
    
    def _extract_generic(self, soup: BeautifulSoup, base_url: str) -> List[Dict]:
        """استخراج عام من أي موقع"""
        articles = []
        
        # محاولة العثور على مقالات بطرق مختلفة
        possible_selectors = [
            'article',
            '.post',
            '.entry',
            '.news',
            '.content-item',
            'div[class*="article"]',
            'div[class*="post"]'
        ]
        
        for selector in possible_selectors:
            elements = soup.select(selector)
            if len(elements) > 0:
                for element in elements:
                    try:
                        # البحث عن العنوان
                        title_elem = element.select_one('h1, h2, h3, h4, h5')
                        if not title_elem:
                            continue
                        
                        title = title_elem.get_text(strip=True)

                        # البحث عن الرابط
                        link_elem = element.select_one('a')
                        if link_elem and link_elem.get('href'):
                            article_url = urljoin(base_url, link_elem.get('href'))
                        else:
                            continue
                        
                        articles.append({
                            'title': title,
                            'url': article_url,
                            'summary': '',
                            'published_date': datetime.now(),
                            'source': 'generic',
                            'source_url': base_url
                        })
                        
                    except Exception as e:
                        continue
                
                if articles:
                    break
        
        return articles
    
    
    
    def _extract_date(self, date_elem) -> datetime:
        """استخراج التاريخ من عنصر HTML"""
        if not date_elem:
            return datetime.now()
        
        try:
            # محاولة استخراج التاريخ من خاصيات مختلفة
            date_text = None
            
            if date_elem.get('datetime'):
                date_text = date_elem.get('datetime')
            elif date_elem.get('title'):
                date_text = date_elem.get('title')
            else:
                date_text = date_elem.get_text(strip=True)
            
            if date_text:
                # أنماط تاريخ مختلفة
                date_patterns = [
                    '%Y-%m-%d',
                    '%Y-%m-%dT%H:%M:%S',
                    '%Y-%m-%d %H:%M:%S',
                    '%d/%m/%Y',
                    '%m/%d/%Y',
                    '%B %d, %Y',
                    '%d %B %Y'
                ]
                
                for pattern in date_patterns:
                    try:
                        return datetime.strptime(date_text[:len(pattern)], pattern)
                    except ValueError:
                        continue
            
            return datetime.now()
            
        except Exception as e:
            logger.debug(f"⚠️ فشل في استخراج التاريخ: {e}")
            return datetime.now()
    
    def get_full_article_content(self, article_url: str) -> Optional[Dict]:
        """استخراج المحتوى الكامل للمقال"""
        soup = self.fetch_page(article_url)
        if not soup:
            return None
        
        try:
            # استخراج العنوان
            title_elem = soup.select_one('h1, .title, .headline, .entry-title')
            title = title_elem.get_text(strip=True) if title_elem else ""
            
            # استخراج المحتوى الرئيسي
            content_selectors = [
                '.content',
                '.entry-content', 
                '.article-content',
                '.post-content',
                'article',
                '.main-content'
            ]
            
            content = ""
            for selector in content_selectors:
                content_elem = soup.select_one(selector)
                if content_elem:
                    # تنظيف المحتوى من العناصر غير المرغوب فيها
                    for unwanted in content_elem.select('script, style, .ads, .advertisement'):
                        unwanted.decompose()
                    
                    content = content_elem.get_text(separator='\n', strip=True)
                    break
            
            # استخراج الصور
            images = []
            img_elements = soup.select('img')
            for img in img_elements:
                if img.get('src'):
                    img_url = urljoin(article_url, img.get('src'))
                    alt_text = img.get('alt', '')
                    images.append({
                        'url': img_url,
                        'alt': alt_text
                    })
            
            # استخراج الكلمات المفتاحية من المحتوى
            keywords = self._extract_keywords_from_content(f"{title} {content}")
            
            logger.log_content_processing(
                "محتوى كامل",
                article_url,
                "استخراج",
                "نجح"
            )
            
            return {
                'title': title,
                'content': content,
                'images': images,
                'keywords': keywords,
                'url': article_url
            }
            
        except Exception as e:
            logger.error(f"❌ فشل في استخراج المحتوى الكامل من {article_url}", e)
            return None
    
    def _extract_keywords_from_content(self, content: str) -> List[str]:
        """استخراج الكلمات المفتاحية من المحتوى"""
        # كلمات مفتاحية عامة للألعاب
        gaming_keywords = [
            'game', 'games', 'gaming', 'video game', 'video games',
            'update', 'release', 'new', 'trailer', 'gameplay',
            'ps5', 'ps4', 'xbox', 'nintendo', 'pc', 'steam',
            'epic games', 'ubisoft', 'ea', 'activision', 'blizzard',
            'rpg', 'fps', 'moba', 'action', 'adventure', 'strategy',
            'لعبة', 'ألعاب', 'فيديو', 'تحديث', 'إصدار', 'جديد',
            'بلايستيشن', 'اكسبوكس', 'نينتندو', 'بي سي', 'ستيم'
        ]

        content_lower = content.lower()
        found_keywords = []

        for keyword in gaming_keywords:
            if keyword.lower() in content_lower:
                found_keywords.append(keyword)

        # إضافة كلمات مفتاحية إضافية من المحتوى
        words = re.findall(r'\b\w+\b', content_lower)
        word_freq = {}

        for word in words:
            if len(word) > 3 and word not in ['this', 'that', 'with', 'have', 'will']:
                word_freq[word] = word_freq.get(word, 0) + 1

        # أخذ أكثر الكلمات تكراراً
        frequent_words = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)[:5]
        found_keywords.extend([word for word, freq in frequent_words if freq > 2])

        return list(set(found_keywords))[:10]  # الحد الأقصى 10 كلمات مفتاحية

    def search_and_extract_articles(self, query: str, num_results: int = 8) -> List[Dict]:
        """
        البحث عن مقالات باستخدام WebSearch واستخراج محتواها مع تحسينات متقدمة.
        """
        if not self.web_search or not self.web_search.enabled:
            logger.error("❌ محرك البحث عبر الويب غير مهيأ أو معطل.")
            return []

        logger.info(f"🔍 بدء البحث المحسن عن مقالات حول: '{query}'")

        # البحث الأساسي
        search_results = self.web_search.search(query, num_results=num_results)

        if not search_results:
            logger.warning(f"⚠️ لم يتم العثور على نتائج بحث لـ '{query}'")
            # محاولة بحث بديل بكلمات مفتاحية مختلفة
            alternative_query = self._generate_alternative_query(query)
            if alternative_query:
                logger.info(f"🔄 محاولة بحث بديل: '{alternative_query}'")
                search_results = self.web_search.search(alternative_query, num_results=num_results)

        if not search_results:
            return []

        articles = []
        processed_domains = set()  # لتجنب تكرار المصادر

        for result in search_results:
            try:
                # تجنب تكرار المصادر من نفس الموقع
                domain = urlparse(result['link']).netloc
                if domain in processed_domains:
                    continue
                processed_domains.add(domain)

                # فلترة المصادر غير المرغوب فيها
                if self._is_valid_gaming_source(result['link'], result.get('title', '')):
                    article_content = self.get_full_article_content(result['link'])
                    if article_content and len(article_content.get('content', '')) > 200:

                        # استخراج تاريخ النشر من المحتوى إن أمكن
                        published_date = self._extract_publish_date_from_content(article_content.get('content', ''))

                        # فحص حداثة المحتوى
                        if self._is_recent_content(published_date, article_content.get('content', '')):
                            articles.append({
                                'title': article_content.get('title', result.get('title')),
                                'url': result['link'],
                                'summary': article_content.get('content', result.get('snippet'))[:500],  # تحديد طول الملخص
                                'content': article_content.get('content', ''),
                                'published_date': published_date,
                                'image_url': article_content['images'][0]['url'] if article_content.get('images') else None,
                                'source': 'google_search',
                                'source_url': result['link'],
                                'keywords': article_content.get('keywords', []),
                                'content_quality': self._assess_content_quality(article_content.get('content', ''))
                            })
                        else:
                            logger.debug(f"⏭️ تخطي محتوى قديم: {result.get('title', '')}")
                    else:
                        logger.debug(f"⏭️ تخطي محتوى قصير أو فارغ: {result.get('title', '')}")
                else:
                    logger.debug(f"⏭️ تخطي مصدر غير مناسب: {result['link']}")

            except Exception as e:
                logger.warning(f"⚠️ خطأ في معالجة نتيجة البحث: {result.get('link', 'unknown')}", e)
                continue

        # ترتيب المقالات حسب الجودة والحداثة
        articles.sort(key=lambda x: (x.get('content_quality', 0), x.get('published_date', datetime.min)), reverse=True)

        logger.info(f"✅ تم استخراج {len(articles)} مقال عالي الجودة من نتائج البحث.")
        return articles[:6]  # إرجاع أفضل 6 مقالات

    def _generate_alternative_query(self, original_query: str) -> str:
        """توليد استعلام بحث بديل"""
        alternatives = {
            "gaming news today": "latest video game news",
            "video game updates": "game patches releases",
            "new game releases": "upcoming games launch",
            "gaming industry news": "video game industry updates",
            "indie games news": "independent games announcements"
        }

        for key, alt in alternatives.items():
            if key in original_query.lower():
                return alt

        # إضافة كلمات مفتاحية بديلة
        if "gaming" in original_query:
            return original_query.replace("gaming", "video game")
        elif "game" in original_query:
            return original_query.replace("game", "gaming")

        return None

    def _is_valid_gaming_source(self, url: str, title: str) -> bool:
        """فحص ما إذا كان المصدر مناسب للألعاب"""
        domain = urlparse(url).netloc.lower()

        # مصادر موثوقة للألعاب
        trusted_gaming_domains = [
            'ign.com', 'gamespot.com', 'polygon.com', 'kotaku.com',
            'eurogamer.net', 'pcgamer.com', 'gameinformer.com',
            'destructoid.com', 'rockpapershotgun.com', 'gamesradar.com',
            'gamedeveloper.com', 'gamesindustry.biz', 'theverge.com',
            'arstechnica.com', 'engadget.com', 'techcrunch.com'
        ]

        # فحص النطاق
        if any(trusted_domain in domain for trusted_domain in trusted_gaming_domains):
            return True

        # فحص العنوان للكلمات المفتاحية المتعلقة بالألعاب
        gaming_keywords = [
            'game', 'gaming', 'video game', 'console', 'pc gaming',
            'mobile game', 'indie game', 'aaa game', 'esports',
            'playstation', 'xbox', 'nintendo', 'steam'
        ]

        title_lower = title.lower()
        if any(keyword in title_lower for keyword in gaming_keywords):
            return True

        return False

    def _extract_publish_date_from_content(self, content: str) -> datetime:
        """استخراج تاريخ النشر من المحتوى"""
        # البحث عن أنماط التاريخ الشائعة
        date_patterns = [
            r'(\d{1,2}[/-]\d{1,2}[/-]\d{4})',  # MM/DD/YYYY or DD/MM/YYYY
            r'(\d{4}[/-]\d{1,2}[/-]\d{1,2})',  # YYYY/MM/DD
            r'(January|February|March|April|May|June|July|August|September|October|November|December)\s+\d{1,2},?\s+\d{4}',
            r'(\d{1,2}\s+(January|February|March|April|May|June|July|August|September|October|November|December)\s+\d{4})'
        ]

        for pattern in date_patterns:
            match = re.search(pattern, content, re.IGNORECASE)
            if match:
                try:
                    date_str = match.group(0)
                    # محاولة تحليل التاريخ
                    from dateutil import parser
                    return parser.parse(date_str)
                except:
                    continue

        # إذا لم يتم العثور على تاريخ، استخدم التاريخ الحالي
        return datetime.now()

    def _is_recent_content(self, published_date: datetime, content: str) -> bool:
        """فحص ما إذا كان المحتوى حديث"""
        # فحص التاريخ
        days_old = (datetime.now() - published_date).days
        if days_old <= 7:  # أقل من أسبوع
            return True
        elif days_old <= 30:  # أقل من شهر
            # فحص إضافي للكلمات الدالة على الحداثة
            recent_keywords = [
                'today', 'yesterday', 'this week', 'recently', 'just announced',
                'breaking', 'latest', 'new', 'updated', 'fresh', 'current',
                'اليوم', 'أمس', 'هذا الأسبوع', 'مؤخراً', 'جديد', 'حديث', 'عاجل'
            ]
            content_lower = content.lower()
            return any(keyword in content_lower for keyword in recent_keywords)

        return False

    def _assess_content_quality(self, content: str) -> int:
        """تقييم جودة المحتوى من 1 إلى 10"""
        if not content:
            return 0

        score = 5  # نقطة البداية

        # طول المحتوى
        if len(content) > 1000:
            score += 2
        elif len(content) > 500:
            score += 1

        # وجود كلمات مفتاحية متعلقة بالألعاب
        gaming_keywords = [
            'game', 'gaming', 'player', 'console', 'pc', 'mobile',
            'gameplay', 'graphics', 'story', 'multiplayer', 'single-player'
        ]
        keyword_count = sum(1 for keyword in gaming_keywords if keyword in content.lower())
        score += min(keyword_count, 3)  # حد أقصى 3 نقاط

        # وجود أسماء ألعاب أو شركات
        game_companies = [
            'nintendo', 'sony', 'microsoft', 'valve', 'epic games',
            'activision', 'ubisoft', 'ea games', 'bethesda'
        ]
        company_count = sum(1 for company in game_companies if company in content.lower())
        score += min(company_count, 2)  # حد أقصى 2 نقطة

        return min(score, 10)  # حد أقصى 10 نقاط

    def extract_from_rss_feeds(self) -> List[Dict]:
        """استخراج المحتوى من RSS feeds للمواقع الرئيسية"""
        rss_feeds = [
            'https://feeds.ign.com/ign/news',  # رابط IGN RSS الجديد والصحيح
            'https://www.gamespot.com/feeds/news/',
            'https://www.polygon.com/rss/index.xml',
            'https://kotaku.com/rss',
            'https://www.eurogamer.net/feed',
            'https://www.pcgamer.com/rss/',
            'https://www.gameinformer.com/news.xml',
            'https://www.destructoid.com/feed/',
            'https://www.rockpapershotgun.com/feed'
        ]

        articles = []

        for feed_url in rss_feeds[:5]:  # أفضل 5 مصادر RSS
            try:
                logger.info(f"📡 جلب RSS feed من: {feed_url}")

                response = self.session.get(feed_url, timeout=15)
                response.raise_for_status()

                # تحليل RSS
                soup = BeautifulSoup(response.content, 'xml')
                items = soup.find_all('item')

                for item in items[:5]:  # أفضل 5 مقالات من كل feed
                    try:
                        title = item.find('title').get_text(strip=True) if item.find('title') else ''
                        link = item.find('link').get_text(strip=True) if item.find('link') else ''
                        description = item.find('description').get_text(strip=True) if item.find('description') else ''
                        pub_date = item.find('pubDate').get_text(strip=True) if item.find('pubDate') else ''

                        if title and link:
                            # تحويل تاريخ النشر
                            published_date = datetime.now()
                            if pub_date:
                                try:
                                    from dateutil import parser
                                    published_date = parser.parse(pub_date)
                                except:
                                    pass

                            # فحص حداثة المقال
                            days_old = (datetime.now() - published_date).days
                            if days_old <= 3:  # مقالات آخر 3 أيام فقط
                                articles.append({
                                    'title': title,
                                    'url': link,
                                    'summary': description,
                                    'content': description,
                                    'published_date': published_date,
                                    'source': 'rss_feed',
                                    'source_url': feed_url,
                                    'keywords': self._extract_keywords_from_content(f"{title} {description}"),
                                    'content_quality': self._assess_content_quality(description)
                                })

                    except Exception as e:
                        logger.debug(f"خطأ في معالجة عنصر RSS: {e}")
                        continue

                time.sleep(2)  # تأخير بين RSS feeds

            except Exception as e:
                logger.warning(f"⚠️ فشل في جلب RSS feed من {feed_url}: {e}")
                continue

        logger.info(f"📡 تم جمع {len(articles)} مقال من RSS feeds")
        return articles

    async def advanced_search_and_extract(self, query: str, max_results: int = 50) -> List[Dict]:
        """بحث واستخراج متقدم باستخدام APIs متعددة ونظام الاستخراج المحسن مع البحث العميق"""
        try:
            logger.info(f"🚀 بدء البحث المتقدم والعميق عن: {query}")

            all_articles = []



            # 1. البحث باستخدام APIs الأخبار المتقدمة
            logger.info("📰 البحث في APIs الأخبار المتقدمة...")
            news_api_articles = await advanced_news_apis.search_gaming_news_comprehensive(
                keywords=[query],
                max_articles=max_results // 3,
                days_back=7
            )
            all_articles.extend(news_api_articles)

            # 2. البحث باستخدام محركات البحث المتقدمة
            logger.info("🔍 البحث باستخدام محركات البحث المتقدمة...")
            web_scraper_articles = await advanced_web_scraper.comprehensive_gaming_search(
                query=query,
                max_results=max_results // 3,
                include_direct_scraping=True
            )
            all_articles.extend(web_scraper_articles)

            # 3. تصفية وتنظيف النتائج
            filtered_articles = await self._filter_advanced_results(all_articles)

            # 4. ترتيب حسب الجودة والصلة
            sorted_articles = self._sort_advanced_results(filtered_articles, query)

            logger.info(f"✅ البحث المتقدم: تم العثور على {len(sorted_articles)} مقال عالي الجودة")

            return sorted_articles[:max_results]

        except Exception as e:
            logger.error(f"❌ فشل في البحث المتقدم والعميق: {e}")
            return []

    async def _filter_advanced_results(self, articles: List[Dict]) -> List[Dict]:
        """تصفية متقدمة للنتائج"""
        try:
            filtered_articles = []
            seen_urls = set()
            seen_content_hashes = set()

            for article in articles:
                # فحص التكرار بالـ URL
                url = article.get('url', '')
                if url in seen_urls:
                    continue

                # فحص التكرار بالمحتوى
                content = article.get('content', '') or article.get('summary', '')
                if content:
                    content_hash = hashlib.md5(content.encode('utf-8')).hexdigest()
                    if content_hash in seen_content_hashes:
                        continue
                    seen_content_hashes.add(content_hash)

                # فحص الجودة الأساسية
                title = article.get('title', '')
                if len(title) < 15 or len(content) < 50:
                    continue

                # فحص الصلة بالألعاب
                if not self._is_gaming_related_advanced(title, content):
                    continue

                # فحص التكرار في قاعدة البيانات
                is_duplicate, _ = db.is_duplicate_content(content, title, [])
                if is_duplicate:
                    continue

                # إضافة معلومات إضافية
                article['advanced_extraction'] = True
                article['extraction_timestamp'] = datetime.now().isoformat()
                article['quality_score'] = self._calculate_advanced_quality_score(article)

                filtered_articles.append(article)
                seen_urls.add(url)

            return filtered_articles

        except Exception as e:
            logger.error(f"❌ خطأ في التصفية المتقدمة: {e}")
            return articles

    def _is_gaming_related_advanced(self, title: str, content: str) -> bool:
        """فحص متقدم للصلة بالألعاب"""
        text_to_check = f"{title} {content}".lower()

        # كلمات مفتاحية أساسية
        primary_keywords = [
            'game', 'gaming', 'video game', 'gamer', 'gameplay',
            'console', 'pc gaming', 'mobile game', 'esports'
        ]

        # كلمات مفتاحية ثانوية
        secondary_keywords = [
            'nintendo', 'playstation', 'xbox', 'steam', 'epic games',
            'indie game', 'aaa game', 'multiplayer', 'single player',
            'rpg', 'fps', 'mmorpg', 'battle royale'
        ]

        # أسماء ألعاب شهيرة
        popular_games = [
            'minecraft', 'fortnite', 'call of duty', 'fifa', 'gta',
            'cyberpunk', 'witcher', 'assassins creed', 'valorant',
            'league of legends', 'overwatch', 'apex legends'
        ]

        # يجب أن يحتوي على كلمة أساسية واحدة على الأقل
        has_primary = any(keyword in text_to_check for keyword in primary_keywords)

        # أو كلمة ثانوية + اسم لعبة
        has_secondary = any(keyword in text_to_check for keyword in secondary_keywords)
        has_game_name = any(game in text_to_check for game in popular_games)

        return has_primary or (has_secondary and has_game_name)

    def _calculate_advanced_quality_score(self, article: Dict) -> float:
        """حساب نقاط الجودة المتقدمة"""
        score = 0.0

        # نقاط العنوان
        title = article.get('title', '')
        if len(title) > 20:
            score += 2.0
        if len(title) > 50:
            score += 1.0

        # نقاط المحتوى
        content = article.get('content', '')
        if len(content) > 200:
            score += 2.0
        if len(content) > 500:
            score += 2.0
        if len(content) > 1000:
            score += 1.0

        # نقاط المصدر
        source = article.get('source', '').lower()
        trusted_sources = ['ign.com', 'gamespot.com', 'polygon.com', 'kotaku.com']
        if any(trusted in source for trusted in trusted_sources):
            score += 3.0

        # نقاط طريقة الاستخراج
        extraction_method = article.get('extraction_method', '')
        if extraction_method in ['newsapi', 'newsdata']:
            score += 1.0
        elif extraction_method == 'web_scraping':
            score += 0.5

        # نقاط الحداثة
        pub_date = article.get('published_date')
        if pub_date:
            if isinstance(pub_date, str):
                from dateutil import parser
                pub_date = parser.parse(pub_date)

            days_old = (datetime.now() - pub_date).days
            if days_old <= 1:
                score += 2.0
            elif days_old <= 3:
                score += 1.0

        return min(score, 10.0)

    def _sort_advanced_results(self, articles: List[Dict], query: str) -> List[Dict]:
        """ترتيب متقدم للنتائج"""
        try:
            def calculate_relevance_score(article):
                title = article.get('title', '').lower()
                content = article.get('content', '').lower()

                query_words = query.lower().split()
                relevance_score = 0

                # نقاط العنوان (أهمية عالية)
                for word in query_words:
                    if word in title:
                        relevance_score += 5

                # نقاط المحتوى
                for word in query_words:
                    if word in content:
                        relevance_score += 1

                return relevance_score

            # حساب نقاط الصلة
            for article in articles:
                article['relevance_score'] = calculate_relevance_score(article)

            # ترتيب حسب الجودة والصلة
            def sort_key(article):
                quality_score = article.get('quality_score', 0)
                relevance_score = article.get('relevance_score', 0)

                # وزن أكبر للجودة والصلة
                return (quality_score * 0.6) + (relevance_score * 0.4)

            return sorted(articles, key=sort_key, reverse=True)

        except Exception as e:
            logger.error(f"❌ خطأ في الترتيب المتقدم: {e}")
            return articles

    def _sort_advanced_results(self, articles: List[Dict], query: str) -> List[Dict]:
        """ترتيب النتائج حسب الجودة والصلة"""
        try:
            def calculate_enhanced_score(article):
                title = article.get('title', '').lower()
                content = article.get('content', '').lower()

                query_words = query.lower().split()
                relevance_score = 0

                # نقاط العنوان (أهمية عالية)
                for word in query_words:
                    if word in title:
                        relevance_score += 5

                # نقاط المحتوى
                for word in query_words:
                    if word in content:
                        relevance_score += 1

                # نقاط الجودة الأساسية
                quality_score = article.get('quality_score', 0)

                # النقاط الإجمالية
                total_score = (quality_score * 0.6) + (relevance_score * 0.4)

                return total_score

            # حساب النقاط المحسنة
            for article in articles:
                article['enhanced_score'] = calculate_enhanced_score(article)

            # ترتيب تنازلي حسب النقاط المحسنة
            return sorted(articles, key=lambda x: x.get('enhanced_score', 0), reverse=True)

        except Exception as e:
            logger.error(f"❌ خطأ في الترتيب المحسن: {e}")
            return articles

    async def get_advanced_extraction_stats(self) -> Dict:
        """الحصول على إحصائيات الاستخراج المتقدم"""
        try:
            news_api_stats = advanced_news_apis.get_usage_stats()
            web_scraper_stats = advanced_web_scraper.get_extraction_stats()

            return {
                'news_apis': news_api_stats,
                'web_scraper': web_scraper_stats,
                'combined_stats': {
                    'total_articles_found': (
                        news_api_stats.get('total_articles_found', 0) +
                        web_scraper_stats.get('total_articles_extracted', 0)
                    ),
                    'overall_success_rate': (
                        news_api_stats.get('success_rate', 0) +
                        web_scraper_stats.get('success_rate', 0)
                    ) / 2
                }
            }

        except Exception as e:
            logger.error(f"❌ خطأ في جمع الإحصائيات المحسنة: {e}")
            return {}

    async def advanced_search_and_extract_with_serpapi(self, keyword: str, max_results: int = 20) -> List[Dict]:
        """البحث والاستخراج المتقدم باستخدام SerpAPI - الطريقة الأفضل"""
        try:
            logger.info(f"🚀 بدء البحث المتقدم باستخدام SerpAPI عن: {keyword}")

            if not serpapi_search.enabled:
                logger.warning("⚠️ SerpAPI غير مفعل، استخدام الطريقة التقليدية")
                return await self.advanced_search_and_extract(keyword, max_results)

            # البحث باستخدام SerpAPI
            search_results = await serpapi_search.search(
                query=f"{keyword} gaming news",
                num_results=max_results,
                tbm='nws',  # البحث في الأخبار
                tbs='qdr:w'  # الأسبوع الماضي
            )

            if not search_results:
                logger.info(f"📭 لم يتم العثور على نتائج SerpAPI لـ: {keyword}")
                return []

            # استخراج المحتوى من الروابط
            extracted_articles = []

            for result in search_results[:max_results]:
                try:
                    url = result.get('link', '')
                    if not url:
                        continue

                    # استخراج المحتوى من الرابط
                    soup = self.fetch_page(url)
                    if not soup:
                        continue

                    # استخراج المحتوى المفصل
                    article_content = self._extract_article_content(soup, url)

                    if article_content and len(article_content.get('content', '')) > 200:
                        # دمج بيانات SerpAPI مع المحتوى المستخرج
                        enhanced_article = {
                            **article_content,
                            'title': result.get('title', article_content.get('title', '')),
                            'summary': result.get('snippet', article_content.get('summary', '')),
                            'source': result.get('source', 'SerpAPI'),
                            'published_date': result.get('date', article_content.get('published_date')),
                            'search_keyword': keyword,
                            'search_engine': 'SerpAPI',
                            'content_quality': result.get('relevance_score', 7),
                            'extraction_method': 'serpapi_enhanced'
                        }

                        extracted_articles.append(enhanced_article)
                        logger.debug(f"✅ استخراج ناجح من: {url[:50]}...")

                    # تأخير بين الطلبات
                    await asyncio.sleep(1)

                except Exception as e:
                    logger.debug(f"⚠️ فشل في استخراج المحتوى من: {url[:50]}... | {e}")
                    continue

            logger.info(f"🚀 SerpAPI: تم استخراج {len(extracted_articles)} مقال عالي الجودة")
            return extracted_articles

        except Exception as e:
            logger.error(f"❌ فشل في البحث المتقدم باستخدام SerpAPI: {e}")
            return []

    async def advanced_search_and_extract_with_tavily(self, keyword: str, max_results: int = 15) -> List[Dict]:
        """البحث والاستخراج المتقدم باستخدام Tavily مع Gemini 2.5 Pro كبديل"""
        try:
            logger.info(f"🔍 بدء البحث العميق باستخدام Tavily عن: {keyword}")

            if not tavily_search.enabled:
                logger.warning("⚠️ Tavily غير مفعل، استخدام الطريقة التقليدية...")
                return await self.advanced_search_and_extract(keyword, max_results)

            # فحص حدود الاستخدام قبل البحث
            usage_stats = tavily_search.get_usage_stats()
            if usage_stats['daily_remaining'] <= 2:
                logger.warning("⚠️ اقتراب من الحد اليومي لـ Tavily - استخدام الطريقة التقليدية...")
                return await self.advanced_search_and_extract(keyword, max_results)

            # البحث العميق باستخدام Tavily
            search_results = await tavily_search.search(
                query=keyword,
                search_depth="advanced",
                max_results=max_results,
                include_answer=True,
                include_raw_content=True
            )

            if not search_results:
                logger.info(f"📭 لم يتم العثور على نتائج Tavily لـ: {keyword}")
                logger.info("🔄 استخدام الطريقة التقليدية كبديل...")
                return await self.advanced_search_and_extract(keyword, max_results)

            # معالجة النتائج وتحسينها
            processed_articles = []

            for result in search_results:
                try:
                    # تحويل نتيجة Tavily إلى تنسيق المقال
                    if result.get('is_ai_generated'):
                        # معالجة خاصة للملخص الذكي
                        ai_article = {
                            'title': result.get('title', ''),
                            'content': result.get('content', ''),
                            'summary': result.get('content', '')[:300] + "...",
                            'url': result.get('url', ''),
                            'source': 'Tavily AI',
                            'published_date': datetime.now(),
                            'search_keyword': keyword,
                            'search_engine': 'Tavily-AI',
                            'content_quality': 9,  # جودة عالية للمحتوى المولد بالذكاء الاصطناعي
                            'extraction_method': 'tavily_ai_summary',
                            'is_ai_generated': True,
                            'gaming_relevance': result.get('gaming_relevance', 5)
                        }
                        processed_articles.append(ai_article)

                    else:
                        # معالجة النتائج العادية
                        url = result.get('url', '')
                        if not url or url.startswith('tavily://'):
                            continue

                        # استخدام المحتوى المستخرج من Tavily مباشرة (أفضل من إعادة الاستخراج)
                        enhanced_article = {
                            'title': result.get('title', ''),
                            'content': result.get('content', ''),
                            'summary': result.get('content', '')[:200] + "..." if len(result.get('content', '')) > 200 else result.get('content', ''),
                            'url': url,
                            'source': result.get('source', 'Tavily'),
                            'published_date': result.get('published_date') or datetime.now(),
                            'search_keyword': keyword,
                            'search_engine': 'Tavily-Deep',
                            'content_quality': min(result.get('relevance_score', 5), 10),
                            'extraction_method': 'tavily_deep_search',
                            'gaming_relevance': result.get('gaming_relevance', 0),
                            'raw_content': result.get('raw_content', '')
                        }

                        # فلترة المحتوى عالي الجودة فقط
                        if (len(enhanced_article['content']) > 100 and
                            enhanced_article['content_quality'] >= 5 and
                            enhanced_article['gaming_relevance'] >= 2):
                            processed_articles.append(enhanced_article)

                except Exception as e:
                    logger.debug(f"⚠️ فشل في معالجة نتيجة Tavily: {e}")
                    continue

            # ترتيب حسب الجودة والصلة
            processed_articles.sort(
                key=lambda x: (x.get('content_quality', 0), x.get('gaming_relevance', 0)),
                reverse=True
            )

            logger.info(f"🔍 Tavily: تم معالجة {len(processed_articles)} مقال عالي الجودة")

            # عرض إحصائيات الاستخدام المحدثة
            updated_stats = tavily_search.get_usage_stats()
            logger.info(f"📊 استخدام Tavily: {updated_stats['current_daily_usage']}/{updated_stats['daily_limit']} اليوم، {updated_stats['current_monthly_usage']}/{updated_stats['monthly_limit']} الشهر")

            return processed_articles

        except Exception as e:
            logger.error(f"❌ خطأ في البحث العميق باستخدام Tavily: {e}")
            # العودة للطريقة التقليدية كبديل
            logger.info("🔄 العودة للطريقة التقليدية كبديل...")
            return await self.advanced_search_and_extract(keyword, max_results)



    async def advanced_search_with_fallbacks(self, keyword: str, max_results: int = 15) -> List[Dict]:
        """البحث المتقدم مع نظام البدائل التلقائي - الطريقة الجديدة الأفضل"""
        try:
            logger.info(f"🚀 بدء البحث المتقدم مع البدائل عن: {keyword}")

            # استخدام مدير البحث المتقدم الجديد
            results = await advanced_search_manager.advanced_search(
                query=keyword,
                max_results=max_results,
                search_type="gaming_news"
            )

            if results:
                logger.info(f"✅ البحث المتقدم: تم العثور على {len(results)} نتيجة عالية الجودة")

                # معالجة إضافية للنتائج
                processed_results = []
                for result in results:
                    try:
                        # إضافة معلومات إضافية
                        enhanced_result = {
                            **result,
                            'content_quality': self._calculate_content_quality(result),
                            'gaming_relevance': self._calculate_gaming_relevance(result),
                            'extraction_method': 'advanced_search_with_fallbacks',
                            'search_timestamp': datetime.now().isoformat()
                        }

                        # فلترة النتائج عالية الجودة فقط
                        if (enhanced_result['content_quality'] >= 6 and
                            enhanced_result['gaming_relevance'] >= 3):
                            processed_results.append(enhanced_result)

                    except Exception as e:
                        logger.debug(f"خطأ في معالجة النتيجة: {e}")
                        continue

                logger.info(f"🔍 تم فلترة {len(processed_results)} نتيجة عالية الجودة من {len(results)}")
                return processed_results

            else:
                logger.warning("⚠️ لم يتم العثور على نتائج من البحث المتقدم")
                return []

        except Exception as e:
            logger.error(f"❌ فشل في البحث المتقدم مع البدائل: {e}")
            return []

    async def enhanced_search_with_ai_fallbacks(self, keyword: str, max_results: int = 15, priority: str = "balanced") -> List[Dict]:
        """البحث المحسن مع النماذج الاحتياطية للذكاء الاصطناعي - الطريقة الأحدث والأفضل"""
        try:
            logger.info(f"🚀 بدء البحث المحسن مع النماذج الاحتياطية عن: {keyword}")

            # تحويل الأولوية إلى enum
            priority_map = {
                "fast": SearchPriority.FAST,
                "balanced": SearchPriority.BALANCED,
                "high_quality": SearchPriority.HIGH_QUALITY,
                "emergency": SearchPriority.EMERGENCY
            }

            search_priority = priority_map.get(priority, SearchPriority.BALANCED)

            # استخدام المدير المحسن الجديد
            search_result = await enhanced_search_manager.comprehensive_search(
                query=keyword,
                max_results=max_results,
                priority=search_priority,
                include_ai_analysis=True
            )

            if search_result and search_result.get('success'):
                results = search_result.get('results', [])

                logger.info(f"✅ البحث المحسن: تم العثور على {len(results)} نتيجة")
                logger.info(f"📊 الاستراتيجية المستخدمة: {search_result.get('best_strategy', 'غير محدد')}")
                logger.info(f"🎯 نقاط الجودة: {search_result.get('quality_score', 0):.2f}")

                # معالجة إضافية للنتائج
                processed_results = []
                for result in results:
                    try:
                        # إضافة معلومات إضافية
                        enhanced_result = {
                            **result,
                            'content_quality': self._calculate_content_quality(result),
                            'gaming_relevance': self._calculate_gaming_relevance(result),
                            'extraction_method': 'enhanced_search_with_ai_fallbacks',
                            'search_strategy': search_result.get('best_strategy', 'unknown'),
                            'search_timestamp': datetime.now().isoformat(),
                            'ai_enhanced': search_result.get('ai_analysis') is not None
                        }

                        # إضافة تحليل AI إذا كان متوفراً
                        if search_result.get('ai_analysis') and not result.get('is_ai_generated'):
                            enhanced_result['ai_analysis'] = search_result['ai_analysis']

                        # فلترة النتائج عالية الجودة فقط
                        if (enhanced_result['content_quality'] >= 5 and
                            enhanced_result['gaming_relevance'] >= 2):
                            processed_results.append(enhanced_result)

                    except Exception as e:
                        logger.debug(f"خطأ في معالجة النتيجة: {e}")
                        # إضافة النتيجة الأصلية في حالة الخطأ
                        processed_results.append(result)
                        continue

                logger.info(f"🎯 تم فلترة {len(processed_results)} نتيجة عالية الجودة من {len(results)}")
                return processed_results

            else:
                logger.warning(f"⚠️ فشل البحث المحسن لـ: {keyword}")
                error_msg = search_result.get('error', 'خطأ غير محدد') if search_result else 'لا توجد نتائج'
                logger.warning(f"السبب: {error_msg}")
                return []

        except Exception as e:
            logger.error(f"❌ خطأ في البحث المحسن مع النماذج الاحتياطية: {e}")
            # العودة للطريقة التقليدية في حالة الخطأ
            logger.info("🔄 العودة للطريقة التقليدية...")
            return await self.advanced_search_with_fallbacks(keyword, max_results)

    async def quick_ai_search(self, keyword: str, max_results: int = 5) -> List[Dict]:
        """بحث سريع باستخدام النماذج الاحتياطية للحالات العاجلة"""
        return await self.enhanced_search_with_ai_fallbacks(
            keyword=keyword,
            max_results=max_results,
            priority="fast"
        )

    async def deep_ai_search(self, keyword: str, max_results: int = 20) -> List[Dict]:
        """بحث عميق عالي الجودة مع تحليل AI شامل"""
        return await self.enhanced_search_with_ai_fallbacks(
            keyword=keyword,
            max_results=max_results,
            priority="high_quality"
        )

    async def emergency_ai_search(self, keyword: str, max_results: int = 3) -> List[Dict]:
        """بحث طوارئ - أي نتيجة متاحة بأسرع وقت"""
        return await self.enhanced_search_with_ai_fallbacks(
            keyword=keyword,
            max_results=max_results,
            priority="emergency"
        )

    def _calculate_content_quality(self, result: Dict) -> int:
        """حساب جودة المحتوى (1-10)"""
        try:
            score = 5  # نقطة البداية

            title = result.get('title', '')
            content = result.get('content', '')

            # فحص طول المحتوى
            if len(content) > 200:
                score += 2
            elif len(content) > 100:
                score += 1

            # فحص طول العنوان
            if 10 <= len(title) <= 100:
                score += 1

            # فحص وجود URL صحيح
            url = result.get('url', '')
            if url and url.startswith('http'):
                score += 1

            # فحص مصدر موثوق
            source = result.get('source', '').lower()
            trusted_sources = ['ign', 'gamespot', 'polygon', 'kotaku', 'pcgamer']
            if any(trusted in source for trusted in trusted_sources):
                score += 1

            return min(score, 10)

        except Exception:
            return 5

    def _calculate_gaming_relevance(self, result: Dict) -> int:
        """حساب صلة المحتوى بالألعاب (1-10)"""
        try:
            score = 0

            text_to_check = f"{result.get('title', '')} {result.get('content', '')}".lower()

            # كلمات مفتاحية أساسية
            basic_keywords = ['game', 'gaming', 'video game', 'player', 'console']
            score += sum(2 for keyword in basic_keywords if keyword in text_to_check)

            # كلمات مفتاحية متقدمة
            advanced_keywords = ['pc gaming', 'mobile gaming', 'esports', 'indie game', 'aaa game']
            score += sum(1 for keyword in advanced_keywords if keyword in text_to_check)

            # أسماء منصات
            platforms = ['playstation', 'xbox', 'nintendo', 'steam', 'epic games']
            score += sum(1 for platform in platforms if platform in text_to_check)

            return min(score, 10)

        except Exception:
            return 3

    def _extract_article_content(self, soup: BeautifulSoup, url: str) -> Optional[Dict]:
        """استخراج محتوى مقال مفصل من صفحة ويب"""
        try:
            # استخراج العنوان
            title_selectors = ['h1', '.article-title', '.entry-title', '.headline', '.title']
            title = ""
            for selector in title_selectors:
                title_elem = soup.select_one(selector)
                if title_elem:
                    title = title_elem.get_text(strip=True)
                    break

            # استخراج المحتوى
            content_selectors = [
                'article', '.article-content', '.entry-content',
                '.post-content', '.content', '.article-body'
            ]
            content = ""
            for selector in content_selectors:
                content_elem = soup.select_one(selector)
                if content_elem:
                    # إزالة العناصر غير المرغوبة
                    for unwanted in content_elem.select('script, style, .ads, .advertisement'):
                        unwanted.decompose()
                    content = content_elem.get_text(strip=True)
                    break

            # استخراج الملخص
            summary_selectors = ['.excerpt', '.summary', '.description', 'meta[name="description"]']
            summary = ""
            for selector in summary_selectors:
                if selector.startswith('meta'):
                    summary_elem = soup.select_one(selector)
                    if summary_elem:
                        summary = summary_elem.get('content', '')
                else:
                    summary_elem = soup.select_one(selector)
                    if summary_elem:
                        summary = summary_elem.get_text(strip=True)
                if summary:
                    break

            # إذا لم يتم العثور على ملخص، استخدم أول 200 حرف من المحتوى
            if not summary and content:
                summary = content[:200] + "..." if len(content) > 200 else content

            # استخراج التاريخ
            date_selectors = ['time', '.date', '.published', '.publish-date']
            published_date = datetime.now()
            for selector in date_selectors:
                date_elem = soup.select_one(selector)
                if date_elem:
                    date_text = date_elem.get('datetime') or date_elem.get_text(strip=True)
                    extracted_date = self._extract_date_from_text(date_text)
                    if extracted_date:
                        published_date = extracted_date
                        break

            if title and content and len(content) > 100:
                return {
                    'title': title,
                    'content': content,
                    'summary': summary,
                    'url': url,
                    'published_date': published_date,
                    'source': urlparse(url).netloc,
                    'extraction_method': 'detailed_scraping'
                }

            return None

        except Exception as e:
            logger.debug(f"خطأ في استخراج محتوى المقال: {e}")
            return None

    def _extract_date_from_text(self, date_text: str) -> Optional[datetime]:
        """استخراج التاريخ من نص"""
        try:
            from dateutil import parser
            return parser.parse(date_text)
        except Exception:
            return None
