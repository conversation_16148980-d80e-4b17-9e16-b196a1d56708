#!/bin/bash

# GitHub Uploader Tool Runner Script
# أداة رفع المشاريع على GitHub

# ألوان للنص
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}================================================${NC}"
echo -e "${BLUE}🚀 أداة رفع المشاريع على GitHub${NC}"
echo -e "${BLUE}================================================${NC}"
echo

# التحقق من وجود Python
if ! command -v python3 &> /dev/null && ! command -v python &> /dev/null; then
    echo -e "${RED}❌ Python غير مثبت على النظام${NC}"
    echo -e "${YELLOW}يرجى تثبيت Python من: https://python.org${NC}"
    exit 1
fi

# تحديد أمر Python
if command -v python3 &> /dev/null; then
    PYTHON_CMD="python3"
else
    PYTHON_CMD="python"
fi

echo -e "${GREEN}✅ تم العثور على Python: $($PYTHON_CMD --version)${NC}"

# التحقق من وجود Git
if ! command -v git &> /dev/null; then
    echo -e "${RED}❌ Git غير مثبت على النظام${NC}"
    echo -e "${YELLOW}يرجى تثبيت Git من: https://git-scm.com${NC}"
    exit 1
fi

echo -e "${GREEN}✅ تم العثور على Git: $(git --version)${NC}"

# التحقق من وجود ملف المتطلبات
if [ ! -f "requirements.txt" ]; then
    echo -e "${RED}❌ ملف requirements.txt غير موجود${NC}"
    exit 1
fi

# التحقق من وجود pip
if ! command -v pip3 &> /dev/null && ! command -v pip &> /dev/null; then
    echo -e "${RED}❌ pip غير مثبت${NC}"
    echo -e "${YELLOW}يرجى تثبيت pip${NC}"
    exit 1
fi

# تحديد أمر pip
if command -v pip3 &> /dev/null; then
    PIP_CMD="pip3"
else
    PIP_CMD="pip"
fi

# تثبيت المتطلبات
echo -e "${YELLOW}📦 تثبيت المتطلبات...${NC}"
if $PIP_CMD install -r requirements.txt > /dev/null 2>&1; then
    echo -e "${GREEN}✅ تم تثبيت المتطلبات بنجاح${NC}"
else
    echo -e "${YELLOW}⚠️  جاري المحاولة مرة أخرى مع عرض التفاصيل...${NC}"
    $PIP_CMD install -r requirements.txt
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ فشل في تثبيت المتطلبات${NC}"
        exit 1
    fi
fi

echo

# التحقق من وجود الملف الرئيسي
if [ ! -f "github_uploader.py" ]; then
    echo -e "${RED}❌ ملف github_uploader.py غير موجود${NC}"
    exit 1
fi

# تشغيل الأداة
echo -e "${BLUE}🚀 تشغيل أداة GitHub Uploader...${NC}"
echo
$PYTHON_CMD github_uploader.py

echo
echo -e "${GREEN}👋 شكراً لاستخدام الأداة!${NC}"
