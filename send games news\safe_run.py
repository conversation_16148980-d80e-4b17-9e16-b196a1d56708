#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل آمن للنظام مع معالجة شاملة للأخطاء
"""

import asyncio
import sys
import os
import traceback
from datetime import datetime

# إضافة المجلد الحالي للمسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class SafeRunner:
    """مشغل آمن للنظام"""
    
    def __init__(self):
        self.start_time = datetime.now()
        self.errors_encountered = []
        self.warnings_encountered = []
    
    async def run_safely(self):
        """تشغيل النظام بأمان"""
        print("🛡️ بدء التشغيل الآمن للنظام...")
        print("="*60)
        
        try:
            # فحص المتطلبات الأساسية
            await self.check_prerequisites()
            
            # تشغيل النظام
            await self.run_system()
            
        except KeyboardInterrupt:
            print("\n⌨️ تم إيقاف النظام بواسطة المستخدم")
            
        except Exception as e:
            print(f"\n❌ خطأ عام في النظام: {e}")
            print("📋 تفاصيل الخطأ:")
            traceback.print_exc()
            
        finally:
            await self.generate_safety_report()
    
    async def check_prerequisites(self):
        """فحص المتطلبات الأساسية"""
        print("🔍 فحص المتطلبات الأساسية...")
        
        # فحص الملفات المطلوبة
        required_files = [
            'main.py',
            'modules/content_generator.py',
            'modules/content_scraper.py',
            'config/settings.py'
        ]
        
        missing_files = []
        for file_path in required_files:
            if not os.path.exists(file_path):
                missing_files.append(file_path)
        
        if missing_files:
            raise FileNotFoundError(f"ملفات مطلوبة غير موجودة: {missing_files}")
        
        print("✅ جميع الملفات المطلوبة موجودة")
        
        # فحص الاستيرادات
        try:
            import random
            from main import GamingNewsBot
            print("✅ جميع الاستيرادات تعمل بنجاح")
        except ImportError as e:
            raise ImportError(f"فشل في استيراد الوحدات: {e}")
    
    async def run_system(self):
        """تشغيل النظام الرئيسي"""
        print("\n🚀 بدء تشغيل النظام الرئيسي...")
        
        try:
            # استيراد النظام الرئيسي
            from main import GamingNewsBot
            
            # إنشاء مثيل من البوت
            print("🤖 إنشاء مثيل من البوت...")
            bot = GamingNewsBot()
            print("✅ تم إنشاء البوت بنجاح")
            
            # اختبار الوظائف الأساسية
            print("\n🧪 اختبار الوظائف الأساسية...")
            
            # اختبار البحث التاريخي
            try:
                print("📅 اختبار البحث التاريخي...")
                historical_content = await bot._search_historical_content()
                print(f"✅ البحث التاريخي: {len(historical_content)} محتوى")
            except Exception as e:
                self.warnings_encountered.append(f"تحذير في البحث التاريخي: {e}")
                print(f"⚠️ تحذير في البحث التاريخي: {e}")
            
            # اختبار المحتوى الرائج
            try:
                print("🔥 اختبار المحتوى الرائج...")
                trending_content = await bot._create_trending_content()
                print(f"✅ المحتوى الرائج: {len(trending_content)} محتوى")
            except Exception as e:
                self.warnings_encountered.append(f"تحذير في المحتوى الرائج: {e}")
                print(f"⚠️ تحذير في المحتوى الرائج: {e}")
            
            # اختبار المصادر العميقة
            try:
                print("🌐 اختبار المصادر العميقة...")
                deeper_content = await bot._search_deeper_sources()
                print(f"✅ المصادر العميقة: {len(deeper_content)} محتوى")
            except Exception as e:
                self.warnings_encountered.append(f"تحذير في المصادر العميقة: {e}")
                print(f"⚠️ تحذير في المصادر العميقة: {e}")
            
            # إحصائيات إجمالية
            total_content = len(historical_content) + len(trending_content) + len(deeper_content)
            
            print(f"\n📊 إحصائيات الاختبار:")
            print(f"  📅 محتوى تاريخي: {len(historical_content)}")
            print(f"  🔥 محتوى رائج: {len(trending_content)}")
            print(f"  🌐 مصادر عميقة: {len(deeper_content)}")
            print(f"  📈 إجمالي المحتوى: {total_content}")
            
            if total_content > 0:
                print("\n🎉 النظام يعمل بنجاح!")
                print("✅ تم حل مشكلة عدم وجود محتوى جديد")
                
                # عرض عينة من المحتوى
                if trending_content:
                    print(f"\n📝 عينة من المحتوى الرائج:")
                    for i, content in enumerate(trending_content[:2], 1):
                        title = content.get('title', 'بدون عنوان')
                        print(f"  {i}. {title[:60]}...")
                
                # سؤال المستخدم عن التشغيل الكامل
                print(f"\n❓ هل تريد تشغيل النظام الكامل؟ (y/n): ", end="")
                choice = input().lower().strip()
                
                if choice in ['y', 'yes', 'نعم', 'ن']:
                    print("\n🚀 بدء التشغيل الكامل...")
                    await self.run_full_system(bot)
                else:
                    print("✅ تم إنهاء الاختبار بنجاح")
            else:
                print("\n⚠️ لم يتم إنشاء محتوى - النظام يحتاج مراجعة")
                self.warnings_encountered.append("لم يتم إنشاء أي محتوى")
            
        except Exception as e:
            self.errors_encountered.append(f"خطأ في تشغيل النظام: {e}")
            print(f"❌ خطأ في تشغيل النظام: {e}")
            raise
    
    async def run_full_system(self, bot):
        """تشغيل النظام الكامل"""
        try:
            print("🔄 تشغيل دورة عمل واحدة...")
            
            # تشغيل دورة عمل واحدة
            await bot.run_single_cycle()
            
            print("✅ تم إكمال دورة العمل بنجاح")
            
        except Exception as e:
            self.errors_encountered.append(f"خطأ في التشغيل الكامل: {e}")
            print(f"❌ خطأ في التشغيل الكامل: {e}")
            print("💡 يمكنك مراجعة السجلات للمزيد من التفاصيل")
    
    async def generate_safety_report(self):
        """إنشاء تقرير الأمان"""
        duration = (datetime.now() - self.start_time).total_seconds()
        
        print("\n" + "="*60)
        print("🛡️ تقرير التشغيل الآمن")
        print("="*60)
        
        print(f"⏱️ مدة التشغيل: {duration:.1f} ثانية")
        print(f"📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # إحصائيات الأخطاء والتحذيرات
        print(f"\n📊 الإحصائيات:")
        print(f"  ❌ أخطاء: {len(self.errors_encountered)}")
        print(f"  ⚠️ تحذيرات: {len(self.warnings_encountered)}")
        
        # عرض الأخطاء
        if self.errors_encountered:
            print(f"\n❌ الأخطاء المواجهة:")
            for i, error in enumerate(self.errors_encountered, 1):
                print(f"  {i}. {error}")
        
        # عرض التحذيرات
        if self.warnings_encountered:
            print(f"\n⚠️ التحذيرات:")
            for i, warning in enumerate(self.warnings_encountered, 1):
                print(f"  {i}. {warning}")
        
        # تقييم عام
        if len(self.errors_encountered) == 0:
            if len(self.warnings_encountered) == 0:
                grade = "A+ ممتاز"
                message = "🎉 النظام يعمل بكفاءة تامة!"
                status = "جاهز للإنتاج"
            elif len(self.warnings_encountered) <= 2:
                grade = "A جيد جداً"
                message = "👍 النظام يعمل بشكل ممتاز مع تحذيرات بسيطة"
                status = "جاهز للاستخدام"
            else:
                grade = "B جيد"
                message = "⚠️ النظام يعمل مع عدة تحذيرات"
                status = "يحتاج مراقبة"
        else:
            if len(self.errors_encountered) <= 2:
                grade = "C مقبول"
                message = "🔧 النظام يعمل مع بعض الأخطاء"
                status = "يحتاج إصلاحات"
            else:
                grade = "D يحتاج عمل"
                message = "🚨 النظام يواجه أخطاء متعددة"
                status = "يحتاج مراجعة شاملة"
        
        print(f"\n🏆 التقييم العام: {grade}")
        print(f"💬 الحالة: {message}")
        print(f"🎯 الحالة: {status}")
        
        # توصيات
        print(f"\n💡 التوصيات:")
        if len(self.errors_encountered) == 0:
            print("  ✅ النظام مستقر وجاهز للاستخدام")
            print("  ✅ يمكن تشغيل: python main.py")
            print("  📊 راقب السجلات للتأكد من الاستمرارية")
        else:
            print("  🔧 راجع الأخطاء المذكورة أعلاه")
            print("  🔧 أصلح المشاكل قبل التشغيل الكامل")
            print("  🔧 اختبر النظام مرة أخرى بعد الإصلاح")
        
        if len(self.warnings_encountered) > 0:
            print("  ⚠️ راقب التحذيرات وحسن الأداء تدريجياً")
        
        print("  📈 حدث النظام دورياً للحصول على أفضل أداء")

async def main():
    """الدالة الرئيسية"""
    runner = SafeRunner()
    await runner.run_safely()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except Exception as e:
        print(f"\n💥 خطأ كارثي: {e}")
        print("📞 اتصل بالدعم التقني للمساعدة")
