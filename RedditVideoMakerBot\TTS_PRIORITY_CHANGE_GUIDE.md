# 🎤 دليل تغيير أولوية محركات TTS

## 📋 ملخص التغييرات

تم تعديل النظام بحيث **يستخدم البدائل أولاً قبل ElevenLabs** بدلاً من استخدام ElevenLabs كأولوية قصوى.

## 🔄 الترتيب الجديد

### **الترتيب السابق:**
1. ~~ElevenLabs~~ (كان الأول دائماً)
2. ~~GoogleTranslate~~
3. ~~AWSPolly~~
4. ~~pyttsx~~

### **الترتيب الجديد:**
1. **GoogleTranslate** (الأول الآن)
2. **AWSPolly** (الثاني)
3. **pyttsx** (الثالث)
4. **ElevenLabs** (الأخير)

## ⚙️ الملفات المُعدلة

### 1. **`config.toml`**
```toml
[settings.tts]
voice_choice = "auto"  # تم تغييره من "ElevenLabs"

[settings.tts.priority_order]
primary = ["GoogleTranslate", "AWSPolly", "pyttsx", "ElevenLabs"]
```

### 2. **`TTS/smart_tts_manager.py`**
- إزالة الإجبار على جعل ElevenLabs الأول
- استخدام ترتيب الأولوية من الإعدادات مباشرة

### 3. **`TTS/engine_wrapper.py`**
- تحديث التعليقات والإشعارات
- تغيير المحرك الافتراضي من ElevenLabs إلى GoogleTranslate

## 🚀 كيفية العمل الآن

### **سير العمل الجديد:**
1. **يحاول GoogleTranslate أولاً** (مجاني وسريع)
2. إذا فشل، يحاول **AWSPolly**
3. إذا فشل، يحاول **pyttsx** (محلي)
4. **أخيراً** يحاول **ElevenLabs** (إذا فشلت جميع البدائل)

### **المزايا:**
- ✅ **توفير في التكلفة**: استخدام المحركات المجانية أولاً
- ✅ **سرعة أكبر**: GoogleTranslate سريع جداً
- ✅ **موثوقية عالية**: عدة بدائل متاحة
- ✅ **ElevenLabs كاحتياطي**: متوفر عند الحاجة للجودة العالية

## 🧪 اختبار النظام

### **تشغيل الاختبار:**
```bash
python test_simple_tts.py
```

### **النتيجة المتوقعة:**
```
🎯 ترتيب الأولوية: ['GoogleTranslate', 'AWSPolly', 'pyttsx', 'ElevenLabs']
✅ المحرك الأول صحيح: GoogleTranslate
✅ ElevenLabs في المرتبة: 4
```

## 🔧 إعدادات إضافية

### **للعودة لـ ElevenLabs كأولوية:**
```toml
[settings.tts]
voice_choice = "ElevenLabs"  # بدلاً من "auto"
```

### **لتخصيص ترتيب مختلف:**
```toml
[settings.tts.priority_order]
primary = ["pyttsx", "GoogleTranslate", "ElevenLabs", "AWSPolly"]
```

### **لاستخدام محرك واحد فقط:**
```toml
[settings.tts]
voice_choice = "GoogleTranslate"  # سيستخدم GoogleTranslate فقط
```

## 📊 مقارنة المحركات

| المحرك | التكلفة | السرعة | الجودة | الموثوقية |
|---------|---------|--------|---------|-----------|
| **GoogleTranslate** | 🆓 مجاني | ⚡ سريع جداً | 🔊 جيدة | ⭐⭐⭐⭐ |
| **AWSPolly** | 💰 مدفوع | ⚡ سريع | 🔊 ممتازة | ⭐⭐⭐⭐⭐ |
| **pyttsx** | 🆓 مجاني | ⚡ سريع | 🔊 متوسطة | ⭐⭐⭐ |
| **ElevenLabs** | 💰 مدفوع | 🐌 بطيء | 🔊 ممتازة جداً | ⭐⭐⭐⭐ |

## 🎯 التوصيات

### **للاستخدام العادي:**
- ✅ استخدم الترتيب الجديد (GoogleTranslate أولاً)
- ✅ وفر تكاليف ElevenLabs للمحتوى المهم

### **للجودة العالية:**
- 🎵 غيّر `voice_choice` إلى `"ElevenLabs"` مؤقتاً
- 🎵 أو ضع ElevenLabs في المرتبة الأولى

### **للتوفير الكامل:**
- 💰 استخدم `"GoogleTranslate"` أو `"pyttsx"` فقط
- 💰 أزل ElevenLabs من القائمة تماماً

## 🔍 استكشاف الأخطاء

### **مشكلة: لا يعمل أي محرك**
```bash
# فحص الإعدادات
python test_simple_tts.py

# فحص المحركات المتاحة
python -c "from TTS.GTTS import GTTS; print('GoogleTranslate متوفر')"
```

### **مشكلة: جودة الصوت منخفضة**
- جرب تغيير الترتيب لوضع AWSPolly أولاً
- أو استخدم ElevenLabs للمحتوى المهم

## ✅ خلاصة

🎉 **تم بنجاح تغيير ترتيب أولوية محركات TTS!**

- ✅ GoogleTranslate الآن هو الأول
- ✅ ElevenLabs أصبح الأخير (احتياطي)
- ✅ توفير في التكاليف
- ✅ سرعة أكبر في الإنتاج
- ✅ موثوقية عالية مع عدة بدائل
