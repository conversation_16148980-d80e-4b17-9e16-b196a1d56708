#!/usr/bin/env python3
"""
عرض توضيحي لأداة GitHub Uploader
Demo script for GitHub Uploader Tool
"""

import os
import tempfile
import shutil
from pathlib import Path
from github_uploader import GitHubUploader

def create_demo_projects():
    """إنشاء مشاريع تجريبية للعرض التوضيحي"""
    print("🎯 إنشاء مشاريع تجريبية...")
    
    # إنشاء مجلد المشاريع التجريبية
    demo_dir = Path("./demo_projects")
    demo_dir.mkdir(exist_ok=True)
    
    # مشروع Python Bot
    python_bot_dir = demo_dir / "telegram_news_bot"
    python_bot_dir.mkdir(exist_ok=True)
    
    # ملف البوت الرئيسي
    bot_code = '''#!/usr/bin/env python3
"""
بوت Telegram للأخبار
Telegram News Bot
"""

import asyncio
import logging
from datetime import datetime

# إعداد السجلات
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TelegramNewsBot:
    """بوت Telegram لجلب ونشر الأخبار"""
    
    def __init__(self, token: str):
        self.token = token
        self.is_running = False
        
    async def start(self):
        """بدء تشغيل البوت"""
        logger.info("🚀 بدء تشغيل بوت الأخبار...")
        self.is_running = True
        
        while self.is_running:
            await self.fetch_news()
            await asyncio.sleep(3600)  # كل ساعة
    
    async def fetch_news(self):
        """جلب الأخبار من المصادر"""
        logger.info("📰 جلب الأخبار الجديدة...")
        # هنا يتم جلب الأخبار من APIs
        pass
    
    async def send_news(self, news_item):
        """إرسال خبر للمشتركين"""
        logger.info(f"📤 إرسال خبر: {news_item['title']}")
        # هنا يتم إرسال الخبر
        pass
    
    def stop(self):
        """إيقاف البوت"""
        logger.info("⏹️ إيقاف البوت...")
        self.is_running = False

async def main():
    """الدالة الرئيسية"""
    token = "YOUR_BOT_TOKEN_HERE"
    bot = TelegramNewsBot(token)
    
    try:
        await bot.start()
    except KeyboardInterrupt:
        bot.stop()
        logger.info("👋 تم إيقاف البوت بواسطة المستخدم")

if __name__ == "__main__":
    asyncio.run(main())
'''
    
    with open(python_bot_dir / "main.py", "w", encoding="utf-8") as f:
        f.write(bot_code)
    
    # ملف المتطلبات
    requirements = """# متطلبات بوت Telegram للأخبار
python-telegram-bot==20.7
aiohttp==3.9.1
feedparser==6.0.10
beautifulsoup4==4.12.2
requests==2.31.0
python-dotenv==1.0.0
asyncio==3.4.3
"""
    
    with open(python_bot_dir / "requirements.txt", "w", encoding="utf-8") as f:
        f.write(requirements)
    
    # ملف الإعدادات
    config = """# إعدادات البوت
BOT_TOKEN=your_telegram_bot_token_here
NEWS_API_KEY=your_news_api_key_here
UPDATE_INTERVAL=3600
MAX_NEWS_PER_UPDATE=5
"""
    
    with open(python_bot_dir / ".env.example", "w", encoding="utf-8") as f:
        f.write(config)
    
    print(f"✅ تم إنشاء مشروع Python Bot في: {python_bot_dir}")
    
    # مشروع JavaScript Discord Bot
    js_bot_dir = demo_dir / "discord_music_bot"
    js_bot_dir.mkdir(exist_ok=True)
    
    # ملف البوت الرئيسي
    js_bot_code = '''// Discord Music Bot
// بوت Discord للموسيقى

const { Client, GatewayIntentBits, SlashCommandBuilder } = require('discord.js');
const { joinVoiceChannel, createAudioPlayer, createAudioResource } = require('@discordjs/voice');
const ytdl = require('ytdl-core');

class DiscordMusicBot {
    constructor(token) {
        this.token = token;
        this.client = new Client({
            intents: [
                GatewayIntentBits.Guilds,
                GatewayIntentBits.GuildMessages,
                GatewayIntentBits.GuildVoiceStates,
                GatewayIntentBits.MessageContent
            ]
        });
        
        this.queue = new Map();
        this.setupEvents();
        this.setupCommands();
    }
    
    setupEvents() {
        this.client.once('ready', () => {
            console.log(`🎵 ${this.client.user.tag} جاهز للعمل!`);
        });
        
        this.client.on('interactionCreate', async interaction => {
            if (!interaction.isChatInputCommand()) return;
            
            const { commandName } = interaction;
            
            switch (commandName) {
                case 'play':
                    await this.playMusic(interaction);
                    break;
                case 'stop':
                    await this.stopMusic(interaction);
                    break;
                case 'skip':
                    await this.skipMusic(interaction);
                    break;
            }
        });
    }
    
    setupCommands() {
        const commands = [
            new SlashCommandBuilder()
                .setName('play')
                .setDescription('تشغيل موسيقى من YouTube')
                .addStringOption(option =>
                    option.setName('url')
                        .setDescription('رابط YouTube')
                        .setRequired(true)
                ),
            new SlashCommandBuilder()
                .setName('stop')
                .setDescription('إيقاف الموسيقى'),
            new SlashCommandBuilder()
                .setName('skip')
                .setDescription('تخطي الأغنية الحالية')
        ];
        
        // تسجيل الأوامر...
    }
    
    async playMusic(interaction) {
        const url = interaction.options.getString('url');
        const voiceChannel = interaction.member.voice.channel;
        
        if (!voiceChannel) {
            return interaction.reply('❌ يجب أن تكون في قناة صوتية!');
        }
        
        try {
            const connection = joinVoiceChannel({
                channelId: voiceChannel.id,
                guildId: interaction.guild.id,
                adapterCreator: interaction.guild.voiceAdapterCreator,
            });
            
            const stream = ytdl(url, { filter: 'audioonly' });
            const resource = createAudioResource(stream);
            const player = createAudioPlayer();
            
            player.play(resource);
            connection.subscribe(player);
            
            await interaction.reply(`🎵 بدء تشغيل: ${url}`);
        } catch (error) {
            console.error('خطأ في تشغيل الموسيقى:', error);
            await interaction.reply('❌ حدث خطأ في تشغيل الموسيقى');
        }
    }
    
    async stopMusic(interaction) {
        // إيقاف الموسيقى
        await interaction.reply('⏹️ تم إيقاف الموسيقى');
    }
    
    async skipMusic(interaction) {
        // تخطي الأغنية
        await interaction.reply('⏭️ تم تخطي الأغنية');
    }
    
    start() {
        this.client.login(this.token);
    }
}

// تشغيل البوت
const bot = new DiscordMusicBot(process.env.DISCORD_TOKEN);
bot.start();

module.exports = DiscordMusicBot;
'''
    
    with open(js_bot_dir / "index.js", "w", encoding="utf-8") as f:
        f.write(js_bot_code)
    
    # ملف package.json
    package_json = '''{
  "name": "discord-music-bot",
  "version": "1.0.0",
  "description": "بوت Discord للموسيقى مع دعم YouTube",
  "main": "index.js",
  "scripts": {
    "start": "node index.js",
    "dev": "nodemon index.js",
    "test": "jest"
  },
  "keywords": ["discord", "bot", "music", "youtube", "arabic"],
  "author": "Your Name",
  "license": "MIT",
  "dependencies": {
    "discord.js": "^14.14.1",
    "@discordjs/voice": "^0.16.1",
    "ytdl-core": "^4.11.5",
    "ffmpeg-static": "^5.2.0",
    "dotenv": "^16.3.1"
  },
  "devDependencies": {
    "nodemon": "^3.0.2",
    "jest": "^29.7.0"
  },
  "engines": {
    "node": ">=16.0.0"
  }
}'''
    
    with open(js_bot_dir / "package.json", "w", encoding="utf-8") as f:
        f.write(package_json)
    
    print(f"✅ تم إنشاء مشروع JavaScript Bot في: {js_bot_dir}")
    
    # مشروع Web Scraper
    scraper_dir = demo_dir / "news_scraper"
    scraper_dir.mkdir(exist_ok=True)
    
    scraper_code = '''#!/usr/bin/env python3
"""
أداة استخراج الأخبار من المواقع
News Web Scraper Tool
"""

import requests
from bs4 import BeautifulSoup
import json
import csv
from datetime import datetime
import time

class NewsScraper:
    """أداة استخراج الأخبار من مواقع مختلفة"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
    def scrape_website(self, url: str, selectors: dict) -> list:
        """
        استخراج الأخبار من موقع محدد
        
        Args:
            url: رابط الموقع
            selectors: محددات CSS للعناصر
            
        Returns:
            قائمة بالأخبار المستخرجة
        """
        try:
            response = self.session.get(url)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            articles = []
            
            for article in soup.select(selectors['article']):
                title = article.select_one(selectors['title'])
                link = article.select_one(selectors['link'])
                summary = article.select_one(selectors['summary'])
                date = article.select_one(selectors['date'])
                
                articles.append({
                    'title': title.get_text().strip() if title else '',
                    'link': link.get('href') if link else '',
                    'summary': summary.get_text().strip() if summary else '',
                    'date': date.get_text().strip() if date else '',
                    'source': url,
                    'scraped_at': datetime.now().isoformat()
                })
                
            return articles
            
        except Exception as e:
            print(f"خطأ في استخراج البيانات من {url}: {e}")
            return []
    
    def save_to_json(self, articles: list, filename: str):
        """حفظ البيانات في ملف JSON"""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(articles, f, ensure_ascii=False, indent=2)
        print(f"✅ تم حفظ {len(articles)} خبر في {filename}")
    
    def save_to_csv(self, articles: list, filename: str):
        """حفظ البيانات في ملف CSV"""
        if not articles:
            return
            
        with open(filename, 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=articles[0].keys())
            writer.writeheader()
            writer.writerows(articles)
        print(f"✅ تم حفظ {len(articles)} خبر في {filename}")

def main():
    """الدالة الرئيسية"""
    scraper = NewsScraper()
    
    # إعدادات المواقع
    websites = {
        'example_news': {
            'url': 'https://example-news.com',
            'selectors': {
                'article': '.article',
                'title': '.title',
                'link': 'a',
                'summary': '.summary',
                'date': '.date'
            }
        }
    }
    
    all_articles = []
    
    for site_name, config in websites.items():
        print(f"🔍 استخراج الأخبار من {site_name}...")
        articles = scraper.scrape_website(config['url'], config['selectors'])
        all_articles.extend(articles)
        time.sleep(2)  # تأخير بين الطلبات
    
    # حفظ النتائج
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    scraper.save_to_json(all_articles, f"news_{timestamp}.json")
    scraper.save_to_csv(all_articles, f"news_{timestamp}.csv")
    
    print(f"🎉 تم استخراج {len(all_articles)} خبر بنجاح!")

if __name__ == "__main__":
    main()
'''
    
    with open(scraper_dir / "scraper.py", "w", encoding="utf-8") as f:
        f.write(scraper_code)
    
    # متطلبات Web Scraper
    scraper_requirements = """# متطلبات أداة استخراج الأخبار
requests==2.31.0
beautifulsoup4==4.12.2
lxml==4.9.3
pandas==2.1.4
selenium==4.15.2
scrapy==2.11.0
"""
    
    with open(scraper_dir / "requirements.txt", "w", encoding="utf-8") as f:
        f.write(scraper_requirements)
    
    print(f"✅ تم إنشاء مشروع Web Scraper في: {scraper_dir}")
    
    print("\n🎉 تم إنشاء جميع المشاريع التجريبية بنجاح!")
    print(f"📁 المجلد: {demo_dir.absolute()}")
    
    return demo_dir

def demo_upload():
    """عرض توضيحي لرفع المشاريع"""
    print("=" * 60)
    print("🚀 عرض توضيحي لأداة GitHub Uploader")
    print("=" * 60)
    
    # إنشاء المشاريع التجريبية
    demo_dir = create_demo_projects()
    
    print("\n" + "=" * 40)
    print("📋 المشاريع المتاحة للرفع:")
    print("=" * 40)
    
    projects = list(demo_dir.iterdir())
    for i, project in enumerate(projects, 1):
        if project.is_dir():
            print(f"{i}. {project.name}")
    
    print("\n" + "=" * 40)
    print("⚠️  ملاحظة مهمة:")
    print("=" * 40)
    print("هذا عرض توضيحي فقط!")
    print("لرفع المشاريع فعلياً، تحتاج إلى:")
    print("1. GitHub Personal Access Token")
    print("2. تشغيل الأداة الرئيسية: python github_uploader.py")
    print("3. اختيار المشروع من مجلد demo_projects/")
    
    print("\n" + "=" * 40)
    print("🔧 خطوات الرفع الفعلي:")
    print("=" * 40)
    print("1. احصل على GitHub Token من: https://github.com/settings/tokens")
    print("2. شغل الأداة: python github_uploader.py")
    print("3. أدخل الـ Token")
    print("4. اختر 'رفع مشروع جديد'")
    print("5. أدخل مسار المشروع (مثل: ./demo_projects/telegram_news_bot)")
    print("6. أدخل اسم المستودع")
    print("7. اتبع التعليمات")
    
    print("\n" + "=" * 40)
    print("📚 أمثلة أسماء المستودعات:")
    print("=" * 40)
    print("- telegram-news-bot")
    print("- discord-music-bot")
    print("- news-web-scraper")
    
    return demo_dir

if __name__ == "__main__":
    try:
        demo_upload()
    except KeyboardInterrupt:
        print("\n👋 تم إيقاف العرض التوضيحي")
    except Exception as e:
        print(f"❌ خطأ في العرض التوضيحي: {e}")
