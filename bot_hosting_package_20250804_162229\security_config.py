"""
نظام الحماية المتقدم للبوت
يوفر حماية شاملة ضد الهجمات والاستخدام غير المصرح به
"""

import logging
import hashlib
import hmac
import time
import re
import json
import os
from functools import wraps
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import ipaddress

# إعداد التسجيل الأمني
security_logger = logging.getLogger('security')
security_handler = logging.FileHandler('security.log', encoding='utf-8')
security_formatter = logging.Formatter(
    '%(asctime)s - SECURITY - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
security_handler.setFormatter(security_formatter)
security_logger.addHandler(security_handler)
security_logger.setLevel(logging.INFO)

class SecurityConfig:
    """إعدادات الحماية الأساسية"""
    
    # إعدادات التشفير
    ENCRYPTION_KEY = os.environ.get('ENCRYPTION_KEY', 'default_key_change_in_production')
    HASH_ALGORITHM = 'sha256'
    
    # إعدادات معدل الطلبات
    RATE_LIMIT_REQUESTS = 30  # عدد الطلبات المسموحة
    RATE_LIMIT_WINDOW = 60    # النافذة الزمنية بالثواني
    
    # إعدادات الحماية من البروت فورس
    MAX_LOGIN_ATTEMPTS = 5
    LOCKOUT_DURATION = 300  # 5 دقائق
    
    # قائمة المستخدمين المحظورين
    BLOCKED_USERS = set()
    
    # قائمة عناوين IP المحظورة
    BLOCKED_IPS = set()
    
    # إعدادات التحقق من صحة البيانات
    MAX_MESSAGE_LENGTH = 4096
    MAX_FILENAME_LENGTH = 255
    ALLOWED_FILE_EXTENSIONS = {'.jpg', '.jpeg', '.png', '.gif', '.pdf', '.zip', '.rar'}
    
    # إعدادات الحماية من XSS و SQL Injection
    DANGEROUS_PATTERNS = [
        r'<script[^>]*>.*?</script>',
        r'javascript:',
        r'on\w+\s*=',
        r'eval\s*\(',
        r'exec\s*\(',
        r'union\s+select',
        r'drop\s+table',
        r'delete\s+from',
        r'insert\s+into',
        r'update\s+.*\s+set'
    ]

class RateLimiter:
    """نظام تحديد معدل الطلبات"""
    
    def __init__(self):
        self.requests = {}
        self.blocked_until = {}
    
    def is_allowed(self, user_id: str) -> bool:
        """فحص ما إذا كان المستخدم مسموح له بإرسال طلب"""
        current_time = time.time()
        
        # فحص إذا كان المستخدم محظور مؤقتاً
        if user_id in self.blocked_until:
            if current_time < self.blocked_until[user_id]:
                return False
            else:
                del self.blocked_until[user_id]
        
        # تنظيف الطلبات القديمة
        if user_id in self.requests:
            self.requests[user_id] = [
                req_time for req_time in self.requests[user_id]
                if current_time - req_time < SecurityConfig.RATE_LIMIT_WINDOW
            ]
        else:
            self.requests[user_id] = []
        
        # فحص عدد الطلبات
        if len(self.requests[user_id]) >= SecurityConfig.RATE_LIMIT_REQUESTS:
            # حظر مؤقت
            self.blocked_until[user_id] = current_time + SecurityConfig.LOCKOUT_DURATION
            security_logger.warning(f"Rate limit exceeded for user {user_id}")
            return False
        
        # إضافة الطلب الحالي
        self.requests[user_id].append(current_time)
        return True

class InputValidator:
    """نظام التحقق من صحة المدخلات"""
    
    @staticmethod
    def sanitize_text(text: str) -> str:
        """تنظيف النص من المحتوى الضار"""
        if not isinstance(text, str):
            return ""
        
        # إزالة الأنماط الخطيرة
        for pattern in SecurityConfig.DANGEROUS_PATTERNS:
            text = re.sub(pattern, '', text, flags=re.IGNORECASE)
        
        # تحديد طول النص
        if len(text) > SecurityConfig.MAX_MESSAGE_LENGTH:
            text = text[:SecurityConfig.MAX_MESSAGE_LENGTH]
        
        return text.strip()
    
    @staticmethod
    def validate_url(url: str) -> bool:
        """التحقق من صحة الرابط"""
        if not isinstance(url, str):
            return False
        
        # نمط بسيط للتحقق من الروابط
        url_pattern = re.compile(
            r'^https?://'  # http:// أو https://
            r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'  # domain
            r'localhost|'  # localhost
            r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # IP
            r'(?::\d+)?'  # port
            r'(?:/?|[/?]\S+)$', re.IGNORECASE)
        
        return bool(url_pattern.match(url))
    
    @staticmethod
    def validate_filename(filename: str) -> bool:
        """التحقق من صحة اسم الملف"""
        if not isinstance(filename, str):
            return False
        
        if len(filename) > SecurityConfig.MAX_FILENAME_LENGTH:
            return False
        
        # فحص الامتداد
        _, ext = os.path.splitext(filename.lower())
        if ext not in SecurityConfig.ALLOWED_FILE_EXTENSIONS:
            return False
        
        # فحص الأحرف الخطيرة
        dangerous_chars = ['..', '/', '\\', ':', '*', '?', '"', '<', '>', '|']
        for char in dangerous_chars:
            if char in filename:
                return False
        
        return True

class SecurityMonitor:
    """نظام مراقبة الأمان"""
    
    def __init__(self):
        self.suspicious_activities = {}
        self.failed_attempts = {}
    
    def log_suspicious_activity(self, user_id: str, activity: str, details: str = ""):
        """تسجيل نشاط مشبوه"""
        timestamp = datetime.now().isoformat()
        
        if user_id not in self.suspicious_activities:
            self.suspicious_activities[user_id] = []
        
        self.suspicious_activities[user_id].append({
            'timestamp': timestamp,
            'activity': activity,
            'details': details
        })
        
        security_logger.warning(f"Suspicious activity from user {user_id}: {activity} - {details}")
        
        # إذا تجاوز عدد الأنشطة المشبوهة الحد المسموح
        if len(self.suspicious_activities[user_id]) > 10:
            self.block_user(user_id, "Multiple suspicious activities")
    
    def block_user(self, user_id: str, reason: str):
        """حظر مستخدم"""
        SecurityConfig.BLOCKED_USERS.add(user_id)
        security_logger.error(f"User {user_id} blocked: {reason}")
    
    def is_user_blocked(self, user_id: str) -> bool:
        """فحص ما إذا كان المستخدم محظور"""
        return user_id in SecurityConfig.BLOCKED_USERS

# إنشاء كائنات النظام
rate_limiter = RateLimiter()
security_monitor = SecurityMonitor()

def security_check(func):
    """ديكوريتر للفحص الأمني"""
    @wraps(func)
    async def wrapper(update, context, *args, **kwargs):
        user_id = str(update.effective_user.id) if update.effective_user else "unknown"
        
        # فحص إذا كان المستخدم محظور
        if security_monitor.is_user_blocked(user_id):
            security_logger.warning(f"Blocked user {user_id} attempted access")
            return
        
        # فحص معدل الطلبات
        if not rate_limiter.is_allowed(user_id):
            security_logger.warning(f"Rate limit exceeded for user {user_id}")
            return
        
        try:
            return await func(update, context, *args, **kwargs)
        except Exception as e:
            security_logger.error(f"Error in {func.__name__} for user {user_id}: {e}")
            raise
    
    return wrapper

def admin_only(func):
    """ديكوريتر للتحقق من صلاحيات المشرف"""
    @wraps(func)
    async def wrapper(update, context, *args, **kwargs):
        user_id = str(update.effective_user.id) if update.effective_user else "unknown"
        admin_id = os.environ.get("ADMIN_CHAT_ID", "")
        
        if user_id != admin_id:
            security_logger.warning(f"Unauthorized admin access attempt by user {user_id}")
            security_monitor.log_suspicious_activity(user_id, "unauthorized_admin_access")
            return
        
        return await func(update, context, *args, **kwargs)
    
    return wrapper

def encrypt_data(data: str) -> str:
    """تشفير البيانات"""
    key = SecurityConfig.ENCRYPTION_KEY.encode()
    message = data.encode()
    signature = hmac.new(key, message, hashlib.sha256).hexdigest()
    return f"{signature}:{data}"

def decrypt_data(encrypted_data: str) -> Optional[str]:
    """فك تشفير البيانات"""
    try:
        signature, data = encrypted_data.split(':', 1)
        key = SecurityConfig.ENCRYPTION_KEY.encode()
        message = data.encode()
        expected_signature = hmac.new(key, message, hashlib.sha256).hexdigest()
        
        if hmac.compare_digest(signature, expected_signature):
            return data
        else:
            security_logger.warning("Data integrity check failed")
            return None
    except Exception as e:
        security_logger.error(f"Decryption error: {e}")
        return None

def hash_password(password: str) -> str:
    """تشفير كلمة المرور"""
    salt = os.urandom(32)
    pwdhash = hashlib.pbkdf2_hmac('sha256', password.encode('utf-8'), salt, 100000)
    return salt.hex() + pwdhash.hex()

def verify_password(stored_password: str, provided_password: str) -> bool:
    """التحقق من كلمة المرور"""
    try:
        salt = bytes.fromhex(stored_password[:64])
        stored_hash = stored_password[64:]
        pwdhash = hashlib.pbkdf2_hmac('sha256', provided_password.encode('utf-8'), salt, 100000)
        return pwdhash.hex() == stored_hash
    except Exception:
        return False

# تهيئة النظام
def initialize_security():
    """تهيئة نظام الحماية"""
    security_logger.info("Security system initialized")
    
    # إنشاء مجلد السجلات إذا لم يكن موجوداً
    os.makedirs('logs', exist_ok=True)
    
    # تحميل قوائم الحظر من الملفات
    try:
        if os.path.exists('blocked_users.json'):
            with open('blocked_users.json', 'r') as f:
                blocked_users = json.load(f)
                SecurityConfig.BLOCKED_USERS.update(blocked_users)
    except Exception as e:
        security_logger.error(f"Error loading blocked users: {e}")

# تشغيل التهيئة
initialize_security()
