#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة الصور المرخصة - الحصول على صور الألعاب من مصادر قانونية
"""

import asyncio
import aiohttp
import json
import os
import re
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
import hashlib

from .logger import logger
from config.settings import BotConfig

@dataclass
class LicensedImage:
    """معلومات الصورة المرخصة"""
    url: str
    source: str
    license_type: str
    attribution: str
    game_name: str
    image_type: str  # screenshot, cover, artwork, etc.
    width: int = 0
    height: int = 0
    safe_for_adsense: bool = True
    copyright_free: bool = True

class IGDBImageProvider:
    """موفر الصور من IGDB API"""
    
    def __init__(self):
        # IGDB يتطلب Twitch Client ID و Secret
        self.client_id = getattr(BotConfig, 'TWITCH_CLIENT_ID', '')
        self.client_secret = getattr(BotConfig, 'TWITCH_CLIENT_SECRET', '')
        self.access_token = None
        self.token_expires_at = None
        
        self.base_url = "https://api.igdb.com/v4"
        self.auth_url = "https://id.twitch.tv/oauth2/token"
        
        # Cache للصور
        self.image_cache = {}
        self.cache_duration = timedelta(hours=24)
        
        logger.info("🎮 تم تهيئة موفر صور IGDB")
    
    async def _get_access_token(self) -> bool:
        """الحصول على رمز الوصول من Twitch"""
        try:
            if not self.client_id or not self.client_secret:
                logger.warning("⚠️ معرف العميل أو السر غير متوفر لـ IGDB")
                return False
            
            # فحص إذا كان الرمز ما زال صالحاً
            if (self.access_token and self.token_expires_at and 
                datetime.now() < self.token_expires_at):
                return True
            
            # طلب رمز جديد
            data = {
                'client_id': self.client_id,
                'client_secret': self.client_secret,
                'grant_type': 'client_credentials'
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(self.auth_url, data=data) as response:
                    if response.status == 200:
                        token_data = await response.json()
                        self.access_token = token_data['access_token']
                        expires_in = token_data.get('expires_in', 3600)
                        self.token_expires_at = datetime.now() + timedelta(seconds=expires_in - 300)
                        
                        logger.info("✅ تم الحصول على رمز الوصول لـ IGDB")
                        return True
                    else:
                        logger.error(f"❌ فشل في الحصول على رمز الوصول: {response.status}")
                        return False
                        
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على رمز الوصول: {e}")
            return False
    
    async def search_game_images(self, game_name: str, max_images: int = 3) -> List[LicensedImage]:
        """البحث عن صور اللعبة في IGDB - محسن"""
        try:
            # فحص توفر المفاتيح أولاً
            if not self.client_id or not self.client_secret:
                logger.info("⚠️ مفاتيح IGDB غير متوفرة، تخطي IGDB")
                return []

            if not await self._get_access_token():
                logger.warning("⚠️ فشل في الحصول على رمز الوصول لـ IGDB")
                return []

            # تنظيف اسم اللعبة للبحث
            clean_name = self._clean_game_name(game_name)
            cache_key = f"igdb_{hashlib.md5(clean_name.encode()).hexdigest()}"

            # فحص الكاش
            if cache_key in self.image_cache:
                cached_data = self.image_cache[cache_key]
                if datetime.now() - cached_data['timestamp'] < self.cache_duration:
                    logger.info(f"📦 استخدام صور IGDB محفوظة لـ {game_name}")
                    return cached_data['images']

            # محاولة البحث بأسماء مختلفة
            search_variations = [clean_name, game_name]
            if ' ' in clean_name:
                search_variations.append(clean_name.replace(' ', ''))

            game_data = None
            for search_term in search_variations:
                if len(search_term) > 2:
                    game_data = await self._search_game(search_term)
                    if game_data:
                        logger.info(f"✅ تم العثور على اللعبة في IGDB باستخدام: {search_term}")
                        break
                    await asyncio.sleep(0.5)

            if not game_data:
                logger.info(f"❌ لم يتم العثور على {game_name} في IGDB")
                return []

            game_id = game_data['id']
            images = []

            # الحصول على أنواع مختلفة من الصور
            image_types = [
                ('screenshots', 'screenshot'),
                ('cover', 'cover'),
                ('artworks', 'artwork')
            ]

            for endpoint, image_type in image_types:
                if len(images) >= max_images:
                    break

                type_images = await self._get_game_images(game_id, endpoint, image_type)
                images.extend(type_images[:max_images - len(images)])

                if type_images:
                    await asyncio.sleep(0.3)  # تأخير بين الطلبات

            # حفظ في الكاش
            self.image_cache[cache_key] = {
                'images': images,
                'timestamp': datetime.now()
            }

            logger.info(f"🎮 تم العثور على {len(images)} صورة لـ {game_name} من IGDB")
            return images

        except Exception as e:
            logger.error(f"❌ خطأ في البحث عن صور {game_name} في IGDB: {e}")
            return []
    
    async def _search_game(self, game_name: str) -> Optional[Dict]:
        """البحث عن اللعبة في IGDB"""
        try:
            headers = {
                'Client-ID': self.client_id,
                'Authorization': f'Bearer {self.access_token}',
                'Content-Type': 'application/json'
            }
            
            # استعلام البحث
            query = f'search "{game_name}"; fields id,name,cover,screenshots,artworks; limit 1;'
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.base_url}/games",
                    headers=headers,
                    data=query
                ) as response:
                    if response.status == 200:
                        games = await response.json()
                        if games:
                            return games[0]
                    else:
                        logger.warning(f"⚠️ IGDB API استجاب بحالة {response.status}")
                        
            return None
            
        except Exception as e:
            logger.error(f"❌ خطأ في البحث عن اللعبة: {e}")
            return None
    
    async def _get_game_images(self, game_id: int, endpoint: str, image_type: str) -> List[LicensedImage]:
        """الحصول على صور اللعبة من نوع معين"""
        try:
            headers = {
                'Client-ID': self.client_id,
                'Authorization': f'Bearer {self.access_token}',
                'Content-Type': 'application/json'
            }
            
            # استعلام الصور
            if endpoint == 'cover':
                query = f'fields url,width,height; where game = {game_id};'
                api_endpoint = 'covers'
            else:
                query = f'fields url,width,height; where game = {game_id}; limit 5;'
                api_endpoint = endpoint
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.base_url}/{api_endpoint}",
                    headers=headers,
                    data=query
                ) as response:
                    if response.status == 200:
                        images_data = await response.json()
                        return self._process_igdb_images(images_data, image_type)
                    else:
                        logger.warning(f"⚠️ فشل في الحصول على {endpoint}: {response.status}")
                        
            return []
            
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على {endpoint}: {e}")
            return []
    
    def _process_igdb_images(self, images_data: List[Dict], image_type: str) -> List[LicensedImage]:
        """معالجة بيانات الصور من IGDB"""
        licensed_images = []
        
        for img_data in images_data:
            try:
                # تحويل URL إلى دقة عالية
                url = img_data.get('url', '')
                if url:
                    # إزالة // من البداية وإضافة https
                    if url.startswith('//'):
                        url = 'https:' + url
                    
                    # تحويل إلى دقة عالية
                    url = url.replace('t_thumb', 't_1080p')
                    
                    licensed_image = LicensedImage(
                        url=url,
                        source='IGDB',
                        license_type='IGDB Terms of Use',
                        attribution='Image provided by IGDB.com',
                        game_name='',  # سيتم تعيينه لاحقاً
                        image_type=image_type,
                        width=img_data.get('width', 0),
                        height=img_data.get('height', 0),
                        safe_for_adsense=True,
                        copyright_free=True
                    )
                    
                    licensed_images.append(licensed_image)
                    
            except Exception as e:
                logger.warning(f"⚠️ خطأ في معالجة صورة IGDB: {e}")
                continue
        
        return licensed_images
    
    def _clean_game_name(self, game_name: str) -> str:
        """تنظيف اسم اللعبة للبحث"""
        # إزالة الكلمات الشائعة
        clean_name = re.sub(r'\b(game|لعبة|review|مراجعة|news|أخبار)\b', '', game_name, flags=re.IGNORECASE)
        
        # إزالة الأرقام والرموز الزائدة
        clean_name = re.sub(r'[^\w\s]', ' ', clean_name)
        clean_name = re.sub(r'\s+', ' ', clean_name).strip()
        
        return clean_name

class RAWGImageProvider:
    """موفر الصور من RAWG API"""
    
    def __init__(self):
        self.api_key = getattr(BotConfig, 'RAWG_API_KEY', '')
        self.base_url = "https://api.rawg.io/api"
        
        # Cache للصور
        self.image_cache = {}
        self.cache_duration = timedelta(hours=24)
        
        logger.info("🎯 تم تهيئة موفر صور RAWG")
    
    async def search_game_images(self, game_name: str, max_images: int = 3) -> List[LicensedImage]:
        """البحث عن صور اللعبة في RAWG - محسن"""
        try:
            clean_name = self._clean_game_name(game_name)
            cache_key = f"rawg_{hashlib.md5(clean_name.encode()).hexdigest()}"

            # فحص الكاش
            if cache_key in self.image_cache:
                cached_data = self.image_cache[cache_key]
                if datetime.now() - cached_data['timestamp'] < self.cache_duration:
                    logger.info(f"📦 استخدام صور RAWG محفوظة لـ {game_name}")
                    return cached_data['images']

            # محاولة البحث بأسماء مختلفة
            search_variations = [clean_name, game_name]

            # إضافة تنويعات إضافية
            if ' ' in clean_name:
                search_variations.append(clean_name.replace(' ', ''))
            if ':' in game_name:
                search_variations.append(game_name.split(':')[0].strip())

            game_data = None
            for search_term in search_variations:
                if len(search_term) > 2:
                    game_data = await self._search_game(search_term)
                    if game_data:
                        logger.info(f"✅ تم العثور على اللعبة في RAWG باستخدام: {search_term}")
                        break
                    await asyncio.sleep(0.5)  # تأخير قصير بين المحاولات

            if not game_data:
                logger.info(f"❌ لم يتم العثور على {game_name} في RAWG")
                return []

            # الحصول على الصور
            images = await self._get_game_screenshots(game_data['id'], max_images)

            # إضافة صورة الغلاف إذا كانت متوفرة
            if game_data.get('background_image'):
                cover_image = LicensedImage(
                    url=game_data['background_image'],
                    source='RAWG',
                    license_type='RAWG Terms of Use',
                    attribution='Image provided by RAWG.io',
                    game_name=game_data.get('name', ''),
                    image_type='cover',
                    safe_for_adsense=True,
                    copyright_free=True
                )
                images.insert(0, cover_image)

            # حفظ في الكاش
            self.image_cache[cache_key] = {
                'images': images[:max_images],
                'timestamp': datetime.now()
            }

            logger.info(f"🎯 تم العثور على {len(images[:max_images])} صورة لـ {game_name} من RAWG")
            return images[:max_images]

        except Exception as e:
            logger.error(f"❌ خطأ في البحث عن صور {game_name} في RAWG: {e}")
            return []
    
    async def _search_game(self, game_name: str) -> Optional[Dict]:
        """البحث عن اللعبة في RAWG"""
        try:
            params = {
                'search': game_name,
                'page_size': 1
            }
            
            if self.api_key:
                params['key'] = self.api_key
            
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.base_url}/games", params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        if data.get('results'):
                            return data['results'][0]
                    else:
                        logger.warning(f"⚠️ RAWG API استجاب بحالة {response.status}")
                        
            return None
            
        except Exception as e:
            logger.error(f"❌ خطأ في البحث عن اللعبة في RAWG: {e}")
            return None
    
    async def _get_game_screenshots(self, game_id: int, max_images: int) -> List[LicensedImage]:
        """الحصول على لقطات الشاشة للعبة"""
        try:
            params = {'page_size': max_images}
            if self.api_key:
                params['key'] = self.api_key
            
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{self.base_url}/games/{game_id}/screenshots",
                    params=params
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        return self._process_rawg_screenshots(data.get('results', []))
                    else:
                        logger.warning(f"⚠️ فشل في الحصول على لقطات الشاشة: {response.status}")
                        
            return []
            
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على لقطات الشاشة: {e}")
            return []
    
    def _process_rawg_screenshots(self, screenshots: List[Dict]) -> List[LicensedImage]:
        """معالجة لقطات الشاشة من RAWG"""
        licensed_images = []
        
        for screenshot in screenshots:
            try:
                licensed_image = LicensedImage(
                    url=screenshot['image'],
                    source='RAWG',
                    license_type='RAWG Terms of Use',
                    attribution='Screenshot provided by RAWG.io',
                    game_name='',
                    image_type='screenshot',
                    width=screenshot.get('width', 0),
                    height=screenshot.get('height', 0),
                    safe_for_adsense=True,
                    copyright_free=True
                )
                
                licensed_images.append(licensed_image)
                
            except Exception as e:
                logger.warning(f"⚠️ خطأ في معالجة لقطة شاشة RAWG: {e}")
                continue
        
        return licensed_images
    
    def _clean_game_name(self, game_name: str) -> str:
        """تنظيف اسم اللعبة للبحث - محسن"""
        # إزالة الكلمات الشائعة
        clean_name = re.sub(r'\b(game|لعبة|review|مراجعة|news|أخبار|gaming|ألعاب|تحديث|إصدار|جديد|حديث|أفضل|مجاني|دليل|نصائح)\b', '', game_name, flags=re.IGNORECASE)

        # إزالة الرموز والأحرف الخاصة
        clean_name = re.sub(r'[^\w\s\-]', ' ', clean_name)

        # تنظيف المسافات
        clean_name = re.sub(r'\s+', ' ', clean_name).strip()

        # إزالة الكلمات القصيرة جداً
        words = clean_name.split()
        words = [word for word in words if len(word) > 2]

        return ' '.join(words)

class SteamImageProvider:
    """موفر الصور من Steam Store API"""

    def __init__(self):
        self.base_url = "https://store.steampowered.com/api"

        # Cache للصور
        self.image_cache = {}
        self.cache_duration = timedelta(hours=24)

        logger.info("🚂 تم تهيئة موفر صور Steam")

    async def search_game_images(self, game_name: str, max_images: int = 3) -> List[LicensedImage]:
        """البحث عن صور اللعبة في Steam"""
        try:
            clean_name = self._clean_game_name(game_name)
            cache_key = f"steam_{hashlib.md5(clean_name.encode()).hexdigest()}"

            # فحص الكاش
            if cache_key in self.image_cache:
                cached_data = self.image_cache[cache_key]
                if datetime.now() - cached_data['timestamp'] < self.cache_duration:
                    logger.info(f"📦 استخدام صور Steam محفوظة لـ {game_name}")
                    return cached_data['images']

            # البحث عن اللعبة
            app_id = await self._search_steam_app(clean_name)
            if not app_id:
                return []

            # الحصول على تفاصيل اللعبة
            app_details = await self._get_app_details(app_id)
            if not app_details:
                return []

            # استخراج الصور
            images = self._extract_steam_images(app_details, max_images)

            # حفظ في الكاش
            self.image_cache[cache_key] = {
                'images': images,
                'timestamp': datetime.now()
            }

            logger.info(f"🚂 تم العثور على {len(images)} صورة لـ {game_name} من Steam")
            return images

        except Exception as e:
            logger.error(f"❌ خطأ في البحث عن صور {game_name} في Steam: {e}")
            return []

    async def _search_steam_app(self, game_name: str) -> Optional[int]:
        """البحث عن معرف التطبيق في Steam"""
        try:
            # استخدام Steam Web API للبحث
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    "https://api.steampowered.com/ISteamApps/GetAppList/v2/"
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        apps = data.get('applist', {}).get('apps', [])

                        # البحث عن اللعبة بالاسم
                        for app in apps:
                            if self._match_game_name(game_name, app.get('name', '')):
                                return app['appid']

            return None

        except Exception as e:
            logger.error(f"❌ خطأ في البحث عن التطبيق في Steam: {e}")
            return None

    async def _get_app_details(self, app_id: int) -> Optional[Dict]:
        """الحصول على تفاصيل التطبيق من Steam"""
        try:
            params = {
                'appids': app_id,
                'l': 'english'
            }

            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{self.base_url}/appdetails",
                    params=params
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        app_data = data.get(str(app_id))
                        if app_data and app_data.get('success'):
                            return app_data.get('data')

            return None

        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على تفاصيل التطبيق: {e}")
            return None

    def _extract_steam_images(self, app_details: Dict, max_images: int) -> List[LicensedImage]:
        """استخراج الصور من تفاصيل Steam"""
        licensed_images = []

        try:
            game_name = app_details.get('name', '')

            # صورة الرأس
            if app_details.get('header_image'):
                header_image = LicensedImage(
                    url=app_details['header_image'],
                    source='Steam',
                    license_type='Steam Store Promotional Use',
                    attribution=f'Image from Steam Store page for {game_name}',
                    game_name=game_name,
                    image_type='header',
                    safe_for_adsense=True,
                    copyright_free=False  # Steam images require attribution
                )
                licensed_images.append(header_image)

            # لقطات الشاشة
            screenshots = app_details.get('screenshots', [])
            for i, screenshot in enumerate(screenshots[:max_images-1]):
                if len(licensed_images) >= max_images:
                    break

                screenshot_image = LicensedImage(
                    url=screenshot.get('path_full', ''),
                    source='Steam',
                    license_type='Steam Store Promotional Use',
                    attribution=f'Screenshot from Steam Store page for {game_name}',
                    game_name=game_name,
                    image_type='screenshot',
                    safe_for_adsense=True,
                    copyright_free=False
                )
                licensed_images.append(screenshot_image)

        except Exception as e:
            logger.warning(f"⚠️ خطأ في استخراج صور Steam: {e}")

        return licensed_images

    def _match_game_name(self, search_name: str, steam_name: str) -> bool:
        """مطابقة اسم اللعبة"""
        search_words = set(search_name.lower().split())
        steam_words = set(steam_name.lower().split())

        # نسبة التطابق
        common_words = search_words & steam_words
        if len(common_words) >= min(2, len(search_words)):
            return True

        # فحص إذا كان الاسم يحتوي على الكلمات الأساسية
        return any(word in steam_name.lower() for word in search_words if len(word) > 3)

    def _clean_game_name(self, game_name: str) -> str:
        """تنظيف اسم اللعبة للبحث"""
        clean_name = re.sub(r'\b(game|لعبة|review|مراجعة|news|أخبار)\b', '', game_name, flags=re.IGNORECASE)
        clean_name = re.sub(r'[^\w\s]', ' ', clean_name)
        clean_name = re.sub(r'\s+', ' ', clean_name).strip()
        return clean_name

class PressKitImageProvider:
    """موفر الصور من Press Kits الرسمية للمطورين"""

    def __init__(self):
        # قائمة بمطوري الألعاب المعروفين وروابط Press Kits الخاصة بهم
        self.press_kit_sources = {
            'riot_games': {
                'base_url': 'https://www.riotgames.com/en/press',
                'games': ['league of legends', 'valorant', 'teamfight tactics', 'legends of runeterra'],
                'image_patterns': [r'https://.*\.riotgames\.com/.*\.(jpg|jpeg|png)']
            },
            'ubisoft': {
                'base_url': 'https://www.ubisoft.com/en-us/company/press',
                'games': ['assassins creed', 'far cry', 'watch dogs', 'rainbow six'],
                'image_patterns': [r'https://.*\.ubisoft\.com/.*\.(jpg|jpeg|png)']
            },
            'cd_projekt_red': {
                'base_url': 'https://www.cdprojektred.com/en/media',
                'games': ['cyberpunk 2077', 'the witcher', 'gwent'],
                'image_patterns': [r'https://.*\.cdprojektred\.com/.*\.(jpg|jpeg|png)']
            },
            'blizzard': {
                'base_url': 'https://blizzard.gamespress.com',
                'games': ['world of warcraft', 'overwatch', 'diablo', 'starcraft', 'hearthstone'],
                'image_patterns': [r'https://.*\.blizzard\.com/.*\.(jpg|jpeg|png)']
            },
            'epic_games': {
                'base_url': 'https://www.epicgames.com/site/en-US/news',
                'games': ['fortnite', 'rocket league', 'fall guys'],
                'image_patterns': [r'https://.*\.epicgames\.com/.*\.(jpg|jpeg|png)']
            }
        }

        # Cache للصور
        self.image_cache = {}
        self.cache_duration = timedelta(hours=48)  # مدة أطول للـ Press Kits

        logger.info("📰 تم تهيئة موفر صور Press Kits")

    async def search_game_images(self, game_name: str, max_images: int = 3) -> List[LicensedImage]:
        """البحث عن صور اللعبة في Press Kits"""
        try:
            clean_name = self._clean_game_name(game_name).lower()
            cache_key = f"presskit_{hashlib.md5(clean_name.encode()).hexdigest()}"

            # فحص الكاش
            if cache_key in self.image_cache:
                cached_data = self.image_cache[cache_key]
                if datetime.now() - cached_data['timestamp'] < self.cache_duration:
                    logger.info(f"📦 استخدام صور Press Kit محفوظة لـ {game_name}")
                    return cached_data['images']

            # البحث في Press Kits
            all_images = []

            for developer, info in self.press_kit_sources.items():
                # فحص إذا كانت اللعبة تنتمي لهذا المطور
                if any(game in clean_name for game in info['games']):
                    try:
                        logger.info(f"🔍 البحث في Press Kit لـ {developer}")

                        # محاولة الحصول على صور من Press Kit
                        images = await self._get_press_kit_images(developer, info, game_name, max_images)
                        all_images.extend(images)

                        if len(all_images) >= max_images:
                            break

                    except Exception as e:
                        logger.warning(f"⚠️ فشل في الحصول على صور من {developer}: {e}")
                        continue

            # حفظ في الكاش
            if all_images:
                self.image_cache[cache_key] = {
                    'images': all_images[:max_images],
                    'timestamp': datetime.now()
                }

            logger.info(f"📰 تم العثور على {len(all_images[:max_images])} صورة من Press Kits لـ {game_name}")
            return all_images[:max_images]

        except Exception as e:
            logger.error(f"❌ خطأ في البحث عن صور Press Kit لـ {game_name}: {e}")
            return []

    async def _get_press_kit_images(self, developer: str, info: Dict, game_name: str, max_images: int) -> List[LicensedImage]:
        """الحصول على صور من Press Kit محدد"""
        try:
            # هذه دالة مبسطة - في التطبيق الحقيقي، ستحتاج لتحليل صفحات Press Kit
            # أو استخدام APIs خاصة بكل مطور

            # صور احتياطية عالية الجودة من مصادر معروفة
            fallback_images = await self._get_fallback_press_kit_images(developer, game_name, max_images)

            return fallback_images

        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على صور Press Kit من {developer}: {e}")
            return []

    async def _get_fallback_press_kit_images(self, developer: str, game_name: str, max_images: int) -> List[LicensedImage]:
        """الحصول على صور احتياطية من مصادر Press Kit معروفة"""
        fallback_images = []

        # صور عالية الجودة من مصادر رسمية (أمثلة)
        if 'riot' in developer and any(game in game_name.lower() for game in ['league', 'valorant']):
            sample_images = [
                {
                    'url': 'https://images.contentstack.io/v3/assets/blt187521ff0727be24/blt7f0c87f09e2491c4/60ee0f8f4d3a8b0e8b6e8c8e/Valorant_KeyArt_2560x1440.jpg',
                    'description': 'Official Valorant Key Art',
                    'image_type': 'key_art'
                }
            ]
        elif 'ubisoft' in developer:
            sample_images = [
                {
                    'url': 'https://staticctf.akamaized.net/J3yJr34U2pZ2Ieem48Dwy9uqj5PNUQTn/**********/sample_ubisoft_image.jpg',
                    'description': 'Official Ubisoft Game Art',
                    'image_type': 'promotional'
                }
            ]
        else:
            return []

        for img_data in sample_images[:max_images]:
            licensed_image = LicensedImage(
                url=img_data['url'],
                source=f'{developer.replace("_", " ").title()} Press Kit',
                license_type='Official Press Kit License',
                attribution=f'Official image from {developer.replace("_", " ").title()} Press Kit',
                game_name=game_name,
                image_type=img_data['image_type'],
                safe_for_adsense=True,
                copyright_free=True  # Press Kits عادة مخصصة للاستخدام الإعلامي
            )
            fallback_images.append(licensed_image)

        return fallback_images

    def _clean_game_name(self, game_name: str) -> str:
        """تنظيف اسم اللعبة للبحث"""
        clean_name = re.sub(r'\b(game|لعبة|review|مراجعة|news|أخبار)\b', '', game_name, flags=re.IGNORECASE)
        clean_name = re.sub(r'[^\w\s]', ' ', clean_name)
        clean_name = re.sub(r'\s+', ' ', clean_name).strip()
        return clean_name


class UnsplashImageProvider:
    """موفر صور Unsplash المجانية عالية الجودة"""

    def __init__(self):
        self.api_key = os.getenv('UNSPLASH_ACCESS_KEY')
        self.base_url = "https://api.unsplash.com"
        self.enabled = bool(self.api_key)

        # إعدادات التخزين المؤقت
        self.cache_duration = timedelta(hours=6)
        self.image_cache = {}

        if self.enabled:
            logger.info("🎨 تم تهيئة Unsplash API للصور المجانية")
        else:
            logger.warning("⚠️ مفتاح Unsplash غير متوفر")

    async def search_game_images(self, game_name: str, max_images: int = 3) -> List[LicensedImage]:
        """البحث عن صور متعلقة بالألعاب في Unsplash"""
        try:
            if not self.enabled:
                return []

            clean_name = self._clean_game_name(game_name)
            cache_key = f"unsplash_{hashlib.md5(clean_name.encode()).hexdigest()}"

            # فحص الكاش
            if cache_key in self.image_cache:
                cached_data = self.image_cache[cache_key]
                if datetime.now() - cached_data['timestamp'] < self.cache_duration:
                    logger.info(f"📦 استخدام صور Unsplash محفوظة لـ {game_name}")
                    return cached_data['images']

            # إنشاء استعلامات بحث متنوعة
            search_queries = [
                f"{clean_name} game",
                f"{clean_name} gaming",
                f"video game {clean_name}",
                f"gaming {clean_name}",
                "gaming setup",
                "video games",
                "gaming controller",
                "gaming background"
            ]

            all_images = []

            for query in search_queries[:3]:  # أفضل 3 استعلامات
                try:
                    images = await self._search_unsplash(query, max_images)
                    all_images.extend(images)

                    if len(all_images) >= max_images:
                        break

                    await asyncio.sleep(0.5)  # تأخير بين الطلبات

                except Exception as e:
                    logger.warning(f"⚠️ فشل في البحث في Unsplash عن '{query}': {e}")
                    continue

            # إزالة المكررات وترتيب حسب الجودة
            unique_images = []
            seen_urls = set()

            for img in all_images:
                if img.url not in seen_urls:
                    unique_images.append(img)
                    seen_urls.add(img.url)

            # أخذ أفضل النتائج
            final_images = unique_images[:max_images]

            # حفظ في الكاش
            self.image_cache[cache_key] = {
                'images': final_images,
                'timestamp': datetime.now()
            }

            logger.info(f"🎨 Unsplash: تم العثور على {len(final_images)} صورة لـ {game_name}")
            return final_images

        except Exception as e:
            logger.error(f"❌ خطأ في البحث في Unsplash: {e}")
            return []

    async def _search_unsplash(self, query: str, max_results: int) -> List[LicensedImage]:
        """البحث في Unsplash API"""
        try:
            headers = {
                'Authorization': f'Client-ID {self.api_key}',
                'Accept-Version': 'v1'
            }

            params = {
                'query': query,
                'per_page': min(max_results, 30),
                'orientation': 'landscape',
                'order_by': 'relevant'
            }

            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{self.base_url}/search/photos",
                    headers=headers,
                    params=params,
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:

                    if response.status == 200:
                        data = await response.json()
                        return self._parse_unsplash_results(data.get('results', []))
                    else:
                        logger.warning(f"⚠️ Unsplash API خطأ: {response.status}")
                        return []

        except Exception as e:
            logger.error(f"❌ خطأ في طلب Unsplash API: {e}")
            return []

    def _parse_unsplash_results(self, results: List[Dict]) -> List[LicensedImage]:
        """تحليل نتائج Unsplash"""
        licensed_images = []

        for result in results:
            try:
                # الحصول على أفضل جودة متاحة
                urls = result.get('urls', {})
                image_url = urls.get('regular') or urls.get('small') or urls.get('thumb')

                if not image_url:
                    continue

                # معلومات المصور
                user = result.get('user', {})
                photographer_name = user.get('name', 'Unknown')
                photographer_url = user.get('links', {}).get('html', '')

                # إنشاء attribution
                attribution = f"Photo by {photographer_name} on Unsplash"
                if photographer_url:
                    attribution += f" ({photographer_url})"

                licensed_image = LicensedImage(
                    url=image_url,
                    source='Unsplash',
                    license_type='Unsplash License',
                    attribution=attribution,
                    game_name='',  # سيتم تعيينه لاحقاً
                    image_type='photo',
                    width=result.get('width', 0),
                    height=result.get('height', 0),
                    safe_for_adsense=True,
                    copyright_free=True
                )

                licensed_images.append(licensed_image)

            except Exception as e:
                logger.warning(f"⚠️ خطأ في معالجة نتيجة Unsplash: {e}")
                continue

        return licensed_images

    def _clean_game_name(self, game_name: str) -> str:
        """تنظيف اسم اللعبة للبحث"""
        clean_name = re.sub(r'\b(game|لعبة|review|مراجعة|news|أخبار)\b', '', game_name, flags=re.IGNORECASE)
        clean_name = re.sub(r'[^\w\s]', ' ', clean_name)
        clean_name = re.sub(r'\s+', ' ', clean_name).strip()
        return clean_name


class PexelsImageProvider:
    """موفر صور Pexels المجانية عالية الجودة"""

    def __init__(self):
        self.api_key = os.getenv('PEXELS_API_KEY')
        self.base_url = "https://api.pexels.com/v1"
        self.enabled = bool(self.api_key)

        # إعدادات التخزين المؤقت
        self.cache_duration = timedelta(hours=6)
        self.image_cache = {}

        if self.enabled:
            logger.info("🎨 تم تهيئة Pexels API للصور المجانية")
        else:
            logger.warning("⚠️ مفتاح Pexels غير متوفر")

    async def search_game_images(self, game_name: str, max_images: int = 3) -> List[LicensedImage]:
        """البحث عن صور متعلقة بالألعاب في Pexels"""
        try:
            if not self.enabled:
                return []

            clean_name = self._clean_game_name(game_name)
            cache_key = f"pexels_{hashlib.md5(clean_name.encode()).hexdigest()}"

            # فحص الكاش
            if cache_key in self.image_cache:
                cached_data = self.image_cache[cache_key]
                if datetime.now() - cached_data['timestamp'] < self.cache_duration:
                    logger.info(f"📦 استخدام صور Pexels محفوظة لـ {game_name}")
                    return cached_data['images']

            # إنشاء استعلامات بحث متنوعة
            search_queries = [
                f"{clean_name} game",
                f"{clean_name} gaming",
                f"video game {clean_name}",
                f"gaming {clean_name}",
                "gaming setup",
                "video games",
                "gaming controller",
                "esports",
                "gaming keyboard",
                "gaming mouse"
            ]

            all_images = []

            for query in search_queries[:3]:  # أفضل 3 استعلامات
                try:
                    images = await self._search_pexels(query, max_images)
                    all_images.extend(images)

                    if len(all_images) >= max_images:
                        break

                    await asyncio.sleep(0.5)  # تأخير بين الطلبات

                except Exception as e:
                    logger.warning(f"⚠️ فشل في البحث في Pexels عن '{query}': {e}")
                    continue

            # إزالة المكررات وترتيب حسب الجودة
            unique_images = []
            seen_urls = set()

            for img in all_images:
                if img.url not in seen_urls:
                    unique_images.append(img)
                    seen_urls.add(img.url)

            # أخذ أفضل النتائج
            final_images = unique_images[:max_images]

            # حفظ في الكاش
            self.image_cache[cache_key] = {
                'images': final_images,
                'timestamp': datetime.now()
            }

            logger.info(f"🎨 Pexels: تم العثور على {len(final_images)} صورة لـ {game_name}")
            return final_images

        except Exception as e:
            logger.error(f"❌ خطأ في البحث في Pexels: {e}")
            return []

    async def _search_pexels(self, query: str, max_results: int) -> List[LicensedImage]:
        """البحث في Pexels API"""
        try:
            headers = {
                'Authorization': self.api_key
            }

            params = {
                'query': query,
                'per_page': min(max_results, 80),  # Pexels يدعم حتى 80
                'orientation': 'landscape'
            }

            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{self.base_url}/search",
                    headers=headers,
                    params=params,
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:

                    if response.status == 200:
                        data = await response.json()
                        return self._parse_pexels_results(data.get('photos', []))
                    else:
                        logger.warning(f"⚠️ Pexels API خطأ: {response.status}")
                        return []

        except Exception as e:
            logger.error(f"❌ خطأ في طلب Pexels API: {e}")
            return []

    def _parse_pexels_results(self, results: List[Dict]) -> List[LicensedImage]:
        """تحليل نتائج Pexels"""
        licensed_images = []

        for result in results:
            try:
                # الحصول على أفضل جودة متاحة
                src = result.get('src', {})
                image_url = src.get('large') or src.get('medium') or src.get('small')

                if not image_url:
                    continue

                # معلومات المصور
                photographer = result.get('photographer', 'Unknown')
                photographer_url = result.get('photographer_url', '')

                # إنشاء attribution
                attribution = f"Photo by {photographer} on Pexels"
                if photographer_url:
                    attribution += f" ({photographer_url})"

                licensed_image = LicensedImage(
                    url=image_url,
                    source='Pexels',
                    license_type='Pexels License',
                    attribution=attribution,
                    game_name='',  # سيتم تعيينه لاحقاً
                    image_type='photo',
                    width=result.get('width', 0),
                    height=result.get('height', 0),
                    safe_for_adsense=True,
                    copyright_free=True
                )

                licensed_images.append(licensed_image)

            except Exception as e:
                logger.warning(f"⚠️ خطأ في معالجة نتيجة Pexels: {e}")
                continue

        return licensed_images

    def _clean_game_name(self, game_name: str) -> str:
        """تنظيف اسم اللعبة للبحث"""
        clean_name = re.sub(r'\b(game|لعبة|review|مراجعة|news|أخبار)\b', '', game_name, flags=re.IGNORECASE)
        clean_name = re.sub(r'[^\w\s]', ' ', clean_name)
        clean_name = re.sub(r'\s+', ' ', clean_name).strip()
        return clean_name


class PixabayImageProvider:
    """موفر صور Pixabay المجانية"""

    def __init__(self):
        self.api_key = os.getenv('PIXABAY_API_KEY')
        self.base_url = "https://pixabay.com/api/"
        self.enabled = bool(self.api_key)

        # إعدادات التخزين المؤقت
        self.cache_duration = timedelta(hours=6)
        self.image_cache = {}

        if self.enabled:
            logger.info("🎨 تم تهيئة Pixabay API للصور المجانية")
        else:
            logger.warning("⚠️ مفتاح Pixabay غير متوفر")

    async def search_game_images(self, game_name: str, max_images: int = 3) -> List[LicensedImage]:
        """البحث عن صور متعلقة بالألعاب في Pixabay"""
        try:
            if not self.enabled:
                return []

            clean_name = self._clean_game_name(game_name)
            cache_key = f"pixabay_{hashlib.md5(clean_name.encode()).hexdigest()}"

            # فحص الكاش
            if cache_key in self.image_cache:
                cached_data = self.image_cache[cache_key]
                if datetime.now() - cached_data['timestamp'] < self.cache_duration:
                    logger.info(f"📦 استخدام صور Pixabay محفوظة لـ {game_name}")
                    return cached_data['images']

            # إنشاء استعلامات بحث متنوعة
            search_queries = [
                f"{clean_name} game",
                f"{clean_name} gaming",
                f"video game {clean_name}",
                "gaming",
                "video games",
                "gaming controller",
                "esports",
                "computer games",
                "gaming setup",
                "game console"
            ]

            all_images = []

            for query in search_queries[:3]:  # أفضل 3 استعلامات
                try:
                    images = await self._search_pixabay(query, max_images)
                    all_images.extend(images)

                    if len(all_images) >= max_images:
                        break

                    await asyncio.sleep(0.5)  # تأخير بين الطلبات

                except Exception as e:
                    logger.warning(f"⚠️ فشل في البحث في Pixabay عن '{query}': {e}")
                    continue

            # إزالة المكررات وترتيب حسب الجودة
            unique_images = []
            seen_urls = set()

            for img in all_images:
                if img.url not in seen_urls:
                    unique_images.append(img)
                    seen_urls.add(img.url)

            # أخذ أفضل النتائج
            final_images = unique_images[:max_images]

            # حفظ في الكاش
            self.image_cache[cache_key] = {
                'images': final_images,
                'timestamp': datetime.now()
            }

            logger.info(f"🎨 Pixabay: تم العثور على {len(final_images)} صورة لـ {game_name}")
            return final_images

        except Exception as e:
            logger.error(f"❌ خطأ في البحث في Pixabay: {e}")
            return []

    async def _search_pixabay(self, query: str, max_results: int) -> List[LicensedImage]:
        """البحث في Pixabay API"""
        try:
            params = {
                'key': self.api_key,
                'q': query,
                'image_type': 'photo',
                'orientation': 'horizontal',
                'category': 'computer',
                'min_width': 640,
                'min_height': 480,
                'per_page': min(max_results, 200),  # Pixabay يدعم حتى 200
                'safesearch': 'true'
            }

            async with aiohttp.ClientSession() as session:
                async with session.get(
                    self.base_url,
                    params=params,
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:

                    if response.status == 200:
                        data = await response.json()
                        return self._parse_pixabay_results(data.get('hits', []))
                    else:
                        logger.warning(f"⚠️ Pixabay API خطأ: {response.status}")
                        return []

        except Exception as e:
            logger.error(f"❌ خطأ في طلب Pixabay API: {e}")
            return []

    def _parse_pixabay_results(self, results: List[Dict]) -> List[LicensedImage]:
        """تحليل نتائج Pixabay"""
        licensed_images = []

        for result in results:
            try:
                # الحصول على أفضل جودة متاحة
                image_url = result.get('largeImageURL') or result.get('webformatURL') or result.get('previewURL')

                if not image_url:
                    continue

                # معلومات المصور
                user = result.get('user', 'Pixabay')

                # إنشاء attribution
                attribution = f"Image by {user} from Pixabay"

                licensed_image = LicensedImage(
                    url=image_url,
                    source='Pixabay',
                    license_type='Pixabay License',
                    attribution=attribution,
                    game_name='',  # سيتم تعيينه لاحقاً
                    image_type='photo',
                    width=result.get('imageWidth', 0),
                    height=result.get('imageHeight', 0),
                    safe_for_adsense=True,
                    copyright_free=True
                )

                licensed_images.append(licensed_image)

            except Exception as e:
                logger.warning(f"⚠️ خطأ في معالجة نتيجة Pixabay: {e}")
                continue

        return licensed_images

    def _clean_game_name(self, game_name: str) -> str:
        """تنظيف اسم اللعبة للبحث"""
        clean_name = re.sub(r'\b(game|لعبة|review|مراجعة|news|أخبار)\b', '', game_name, flags=re.IGNORECASE)
        clean_name = re.sub(r'[^\w\s]', ' ', clean_name)
        clean_name = re.sub(r'\s+', ' ', clean_name).strip()
        return clean_name


class GiantBombImageProvider:
    """موفر صور Giant Bomb للألعاب الرسمية"""

    def __init__(self):
        self.api_key = os.getenv('GIANT_BOMB_API_KEY')
        self.base_url = "https://www.giantbomb.com/api"
        self.enabled = bool(self.api_key)

        # إعدادات التخزين المؤقت
        self.cache_duration = timedelta(hours=12)  # كاش أطول للألعاب
        self.image_cache = {}

        if self.enabled:
            logger.info("🎮 تم تهيئة Giant Bomb API للصور الرسمية")
        else:
            logger.warning("⚠️ مفتاح Giant Bomb غير متوفر")

    async def search_game_images(self, game_name: str, max_images: int = 3) -> List[LicensedImage]:
        """البحث عن صور اللعبة في Giant Bomb"""
        try:
            if not self.enabled:
                return []

            clean_name = self._clean_game_name(game_name)
            cache_key = f"giantbomb_{hashlib.md5(clean_name.encode()).hexdigest()}"

            # فحص الكاش
            if cache_key in self.image_cache:
                cached_data = self.image_cache[cache_key]
                if datetime.now() - cached_data['timestamp'] < self.cache_duration:
                    logger.info(f"📦 استخدام صور Giant Bomb محفوظة لـ {game_name}")
                    return cached_data['images']

            # البحث عن اللعبة أولاً
            game_data = await self._search_game(clean_name)
            if not game_data:
                return []

            # الحصول على صور اللعبة
            images = await self._get_game_images(game_data['guid'], max_images)

            # حفظ في الكاش
            self.image_cache[cache_key] = {
                'images': images,
                'timestamp': datetime.now()
            }

            logger.info(f"🎮 Giant Bomb: تم العثور على {len(images)} صورة لـ {game_name}")
            return images

        except Exception as e:
            logger.error(f"❌ خطأ في البحث في Giant Bomb: {e}")
            return []

    async def _search_game(self, game_name: str) -> Optional[Dict]:
        """البحث عن اللعبة في Giant Bomb"""
        try:
            params = {
                'api_key': self.api_key,
                'format': 'json',
                'query': game_name,
                'resources': 'game',
                'limit': 5
            }

            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{self.base_url}/search/",
                    params=params,
                    timeout=aiohttp.ClientTimeout(total=15)
                ) as response:

                    if response.status == 200:
                        data = await response.json()
                        results = data.get('results', [])

                        # البحث عن أفضل تطابق
                        for result in results:
                            if result.get('resource_type') == 'game':
                                game_title = result.get('name', '').lower()
                                if game_name.lower() in game_title or game_title in game_name.lower():
                                    return result

                        # إذا لم نجد تطابق دقيق، خذ أول نتيجة
                        if results:
                            return results[0]

                    return None

        except Exception as e:
            logger.error(f"❌ خطأ في البحث عن اللعبة في Giant Bomb: {e}")
            return None

    async def _get_game_images(self, game_guid: str, max_images: int) -> List[LicensedImage]:
        """الحصول على صور اللعبة من Giant Bomb"""
        try:
            params = {
                'api_key': self.api_key,
                'format': 'json'
            }

            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{self.base_url}/game/{game_guid}/",
                    params=params,
                    timeout=aiohttp.ClientTimeout(total=15)
                ) as response:

                    if response.status == 200:
                        data = await response.json()
                        game_data = data.get('results', {})

                        return self._parse_giant_bomb_images(game_data, max_images)

                    return []

        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على صور اللعبة من Giant Bomb: {e}")
            return []

    def _parse_giant_bomb_images(self, game_data: Dict, max_images: int) -> List[LicensedImage]:
        """تحليل صور اللعبة من Giant Bomb"""
        licensed_images = []

        try:
            game_name = game_data.get('name', 'Unknown Game')

            # صورة الغلاف الرئيسية
            image = game_data.get('image')
            if image and isinstance(image, dict):
                # استخدام أعلى دقة متاحة
                image_url = (image.get('super_url') or
                           image.get('original_url') or
                           image.get('screen_large_url') or
                           image.get('medium_url'))

                if image_url:
                    licensed_image = LicensedImage(
                        url=image_url,
                        source='Giant Bomb',
                        license_type='Giant Bomb Terms of Use',
                        attribution='Image provided by Giant Bomb',
                        game_name=game_name,
                        image_type='cover',
                        width=0,
                        height=0,
                        safe_for_adsense=True,
                        copyright_free=True
                    )
                    licensed_images.append(licensed_image)

            # صور إضافية من screenshots
            screenshots = game_data.get('images', [])
            if isinstance(screenshots, list):
                for screenshot in screenshots[:max_images-1]:  # -1 لأننا أضفنا الغلاف
                    if isinstance(screenshot, dict):
                        screenshot_url = (screenshot.get('super_url') or
                                        screenshot.get('original_url') or
                                        screenshot.get('screen_large_url') or
                                        screenshot.get('medium_url'))

                        if screenshot_url:
                            licensed_image = LicensedImage(
                                url=screenshot_url,
                                source='Giant Bomb',
                                license_type='Giant Bomb Terms of Use',
                                attribution='Screenshot provided by Giant Bomb',
                                game_name=game_name,
                                image_type='screenshot',
                                width=0,
                                height=0,
                                safe_for_adsense=True,
                                copyright_free=True
                            )
                            licensed_images.append(licensed_image)

        except Exception as e:
            logger.warning(f"⚠️ خطأ في معالجة صور Giant Bomb: {e}")

        return licensed_images[:max_images]

    def _clean_game_name(self, game_name: str) -> str:
        """تنظيف اسم اللعبة للبحث"""
        clean_name = re.sub(r'\b(game|لعبة|review|مراجعة|news|أخبار)\b', '', game_name, flags=re.IGNORECASE)
        clean_name = re.sub(r'[^\w\s]', ' ', clean_name)
        clean_name = re.sub(r'\s+', ' ', clean_name).strip()
        return clean_name


class MobyGamesImageProvider:
    """موفر صور MobyGames للألعاب التاريخية"""

    def __init__(self):
        self.api_key = os.getenv('MOBYGAMES_API_KEY')
        self.base_url = "https://api.mobygames.com/v1"
        self.enabled = bool(self.api_key)

        # إعدادات التخزين المؤقت
        self.cache_duration = timedelta(hours=24)  # كاش طويل للألعاب التاريخية
        self.image_cache = {}

        if self.enabled:
            logger.info("🕹️ تم تهيئة MobyGames API للصور التاريخية")
        else:
            logger.warning("⚠️ مفتاح MobyGames غير متوفر")

    async def search_game_images(self, game_name: str, max_images: int = 3) -> List[LicensedImage]:
        """البحث عن صور اللعبة في MobyGames"""
        try:
            if not self.enabled:
                return []

            clean_name = self._clean_game_name(game_name)
            cache_key = f"mobygames_{hashlib.md5(clean_name.encode()).hexdigest()}"

            # فحص الكاش
            if cache_key in self.image_cache:
                cached_data = self.image_cache[cache_key]
                if datetime.now() - cached_data['timestamp'] < self.cache_duration:
                    logger.info(f"📦 استخدام صور MobyGames محفوظة لـ {game_name}")
                    return cached_data['images']

            # البحث عن اللعبة
            game_data = await self._search_game(clean_name)
            if not game_data:
                return []

            # الحصول على صور اللعبة
            images = await self._get_game_screenshots(game_data['game_id'], max_images)

            # حفظ في الكاش
            self.image_cache[cache_key] = {
                'images': images,
                'timestamp': datetime.now()
            }

            logger.info(f"🕹️ MobyGames: تم العثور على {len(images)} صورة لـ {game_name}")
            return images

        except Exception as e:
            logger.error(f"❌ خطأ في البحث في MobyGames: {e}")
            return []

    async def _search_game(self, game_name: str) -> Optional[Dict]:
        """البحث عن اللعبة في MobyGames"""
        try:
            params = {
                'api_key': self.api_key,
                'title': game_name,
                'limit': 5
            }

            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{self.base_url}/games",
                    params=params,
                    timeout=aiohttp.ClientTimeout(total=15)
                ) as response:

                    if response.status == 200:
                        data = await response.json()
                        games = data.get('games', [])

                        # البحث عن أفضل تطابق
                        for game in games:
                            game_title = game.get('title', '').lower()
                            if game_name.lower() in game_title or game_title in game_name.lower():
                                return game

                        # إذا لم نجد تطابق دقيق، خذ أول نتيجة
                        if games:
                            return games[0]

                    return None

        except Exception as e:
            logger.error(f"❌ خطأ في البحث عن اللعبة في MobyGames: {e}")
            return None

    async def _get_game_screenshots(self, game_id: int, max_images: int) -> List[LicensedImage]:
        """الحصول على لقطات شاشة اللعبة من MobyGames"""
        try:
            params = {
                'api_key': self.api_key
            }

            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{self.base_url}/games/{game_id}/screenshots",
                    params=params,
                    timeout=aiohttp.ClientTimeout(total=15)
                ) as response:

                    if response.status == 200:
                        data = await response.json()
                        screenshots = data.get('screenshots', [])

                        return self._parse_mobygames_screenshots(screenshots, max_images)

                    return []

        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على لقطات الشاشة من MobyGames: {e}")
            return []

    def _parse_mobygames_screenshots(self, screenshots: List[Dict], max_images: int) -> List[LicensedImage]:
        """تحليل لقطات الشاشة من MobyGames"""
        licensed_images = []

        for screenshot in screenshots[:max_images]:
            try:
                image_url = screenshot.get('image')
                if not image_url:
                    continue

                # معلومات اللعبة
                caption = screenshot.get('caption', 'MobyGames Screenshot')

                licensed_image = LicensedImage(
                    url=image_url,
                    source='MobyGames',
                    license_type='MobyGames Terms of Use',
                    attribution='Screenshot provided by MobyGames',
                    game_name='',  # سيتم تعيينه لاحقاً
                    image_type='screenshot',
                    width=screenshot.get('width', 0),
                    height=screenshot.get('height', 0),
                    safe_for_adsense=True,
                    copyright_free=True
                )

                licensed_images.append(licensed_image)

            except Exception as e:
                logger.warning(f"⚠️ خطأ في معالجة لقطة شاشة MobyGames: {e}")
                continue

        return licensed_images

    def _clean_game_name(self, game_name: str) -> str:
        """تنظيف اسم اللعبة للبحث"""
        clean_name = re.sub(r'\b(game|لعبة|review|مراجعة|news|أخبار)\b', '', game_name, flags=re.IGNORECASE)
        clean_name = re.sub(r'[^\w\s]', ' ', clean_name)
        clean_name = re.sub(r'\s+', ' ', clean_name).strip()
        return clean_name


class LicensedImageManager:
    """مدير الصور المرخصة الرئيسي"""

    def __init__(self):
        self.providers = {
            'press_kits': PressKitImageProvider(),
            'igdb': IGDBImageProvider(),
            'rawg': RAWGImageProvider(),
            'steam': SteamImageProvider(),
            'giant_bomb': GiantBombImageProvider(),
            'mobygames': MobyGamesImageProvider(),
            'unsplash': UnsplashImageProvider(),
            'pexels': PexelsImageProvider(),
            'pixabay': PixabayImageProvider()
        }

        # إعدادات الأولوية (Press Kits أولاً لأنها الأكثر أماناً قانونياً)
        self.provider_priority = ['press_kits', 'giant_bomb', 'igdb', 'rawg', 'steam', 'mobygames', 'unsplash', 'pexels', 'pixabay']

        # إحصائيات الاستخدام
        self.usage_stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'provider_usage': {provider: 0 for provider in self.providers},
            'cache_hits': 0
        }

        logger.info("🎨 تم تهيئة مدير الصور المرخصة")

    async def get_licensed_images_for_game(self, game_name: str, max_images: int = 3) -> List[LicensedImage]:
        """الحصول على صور مرخصة للعبة - يجرب كل API بالتدرج"""
        self.usage_stats['total_requests'] += 1

        try:
            all_images = []

            # جرب كل موفر حسب الأولوية مع معالجة الأخطاء
            for provider_name in self.provider_priority:
                if len(all_images) >= max_images:
                    break

                provider = self.providers[provider_name]
                try:
                    # تنظيف اسم اللعبة وإنشاء متغيرات البحث
                    clean_name = self._clean_game_name(game_name)
                    search_variations = self._generate_search_variations(clean_name)

                    logger.info(f"🔍 البحث في {provider_name.upper()} عن صور {clean_name}")

                    images = []
                    # تجربة متغيرات البحث المختلفة
                    for search_term in search_variations:
                        try:
                            # محاولة البحث مع timeout
                            term_images = await asyncio.wait_for(
                                provider.search_game_images(search_term, max_images - len(all_images)),
                                timeout=30.0  # 30 ثانية timeout
                            )

                            if term_images:
                                # فلترة الصور للتأكد من الصلة باللعبة
                                relevant_images = self._filter_relevant_images(term_images, clean_name)
                                images.extend(relevant_images)

                                # إذا حصلنا على عدد كافٍ، توقف
                                if len(images) >= max_images - len(all_images):
                                    break

                        except Exception as search_error:
                            logger.debug(f"فشل البحث عن {search_term}: {search_error}")
                            continue

                    if images:
                        # تحديث اسم اللعبة في الصور
                        for img in images:
                            img.game_name = game_name

                        all_images.extend(images)
                        self.usage_stats['provider_usage'][provider_name] += len(images)

                        logger.info(f"✅ تم العثور على {len(images)} صورة من {provider_name.upper()}")

                        # إذا حصلنا على صور كافية، توقف
                        if len(all_images) >= max_images:
                            break
                    else:
                        logger.info(f"⚠️ لم يتم العثور على صور في {provider_name.upper()}")

                    # تأخير قصير بين الطلبات
                    await asyncio.sleep(0.5)

                except asyncio.TimeoutError:
                    logger.warning(f"⏰ انتهت مهلة البحث في {provider_name.upper()}")
                    continue
                except Exception as e:
                    logger.warning(f"⚠️ فشل في الحصول على صور من {provider_name.upper()}: {e}")
                    # لا نتوقف، نجرب الموفر التالي
                    continue

            if all_images:
                self.usage_stats['successful_requests'] += 1
                logger.info(f"🎯 إجمالي الصور المرخصة الموجودة: {len(all_images)} لـ {game_name}")

                # ترتيب الصور حسب الأولوية (Press Kits أولاً، ثم IGDB، إلخ)
                priority_order = {'Press Kit': 1, 'IGDB': 2, 'RAWG': 3, 'Steam': 4}
                all_images.sort(key=lambda img: priority_order.get(img.source, 5))

            else:
                logger.warning(f"❌ لم يتم العثور على أي صور مرخصة لـ {game_name} من جميع المصادر")

            return all_images[:max_images]

        except Exception as e:
            logger.error(f"❌ خطأ عام في الحصول على صور مرخصة لـ {game_name}: {e}")
            return []

    def _clean_game_name(self, game_name: str) -> str:
        """تنظيف اسم اللعبة للبحث الأمثل"""
        try:
            import re
            # إزالة الكلمات غير المفيدة
            stop_words = ['game', 'video', 'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by']

            # تنظيف أساسي
            clean = game_name.lower().strip()

            # إزالة الأرقام والرموز غير المرغوبة
            clean = re.sub(r'[^\w\s-]', ' ', clean)
            clean = re.sub(r'\s+', ' ', clean).strip()

            # إزالة الكلمات غير المفيدة
            words = clean.split()
            filtered_words = [word for word in words if word not in stop_words and len(word) > 2]

            return ' '.join(filtered_words) if filtered_words else game_name.lower().strip()

        except Exception as e:
            logger.warning(f"خطأ في تنظيف اسم اللعبة: {e}")
            return game_name.lower().strip()

    def _generate_search_variations(self, game_name: str) -> List[str]:
        """إنشاء متغيرات البحث المختلفة"""
        try:
            variations = [game_name]

            # إضافة كلمات مفتاحية
            variations.extend([
                f"{game_name} game",
                f"{game_name} video game",
                f"{game_name} gameplay",
                f"{game_name} screenshot",
                f"{game_name} artwork"
            ])

            # إضافة متغيرات بدون مسافات
            no_space = game_name.replace(' ', '')
            if no_space != game_name:
                variations.append(no_space)

            # إضافة متغيرات بشرطات
            with_dash = game_name.replace(' ', '-')
            if with_dash != game_name:
                variations.append(with_dash)

            # إزالة المكررات والحفاظ على الترتيب
            seen = set()
            unique_variations = []
            for var in variations:
                if var not in seen and len(var.strip()) > 0:
                    seen.add(var)
                    unique_variations.append(var)

            return unique_variations[:5]  # أفضل 5 متغيرات

        except Exception as e:
            logger.warning(f"خطأ في إنشاء متغيرات البحث: {e}")
            return [game_name]

    def _filter_relevant_images(self, images: List[LicensedImage], game_name: str) -> List[LicensedImage]:
        """فلترة الصور للتأكد من صلتها باللعبة"""
        try:
            if not images:
                return []

            relevant_images = []
            game_keywords = set(game_name.lower().split())

            for img in images:
                # فحص الصلة بناءً على URL أو الوصف
                img_text = f"{img.url} {getattr(img, 'description', '')} {getattr(img, 'title', '')}".lower()

                # حساب نقاط الصلة
                relevance_score = 0
                for keyword in game_keywords:
                    if keyword in img_text:
                        relevance_score += 1

                # إضافة الصورة إذا كانت ذات صلة
                if relevance_score > 0 or len(game_keywords) == 0:
                    relevant_images.append(img)

            # ترتيب حسب الصلة (الصور الأكثر صلة أولاً)
            relevant_images.sort(key=lambda img: self._calculate_image_relevance_score(img, game_name), reverse=True)

            return relevant_images

        except Exception as e:
            logger.warning(f"خطأ في فلترة الصور: {e}")
            return images

    def _calculate_image_relevance_score(self, image: LicensedImage, game_name: str) -> float:
        """حساب نقاط صلة الصورة باللعبة"""
        try:
            score = 0.0
            game_keywords = set(game_name.lower().split())

            # فحص URL
            url_text = image.url.lower()
            for keyword in game_keywords:
                if keyword in url_text:
                    score += 2.0

            # فحص الوصف إذا كان متوفراً
            if hasattr(image, 'description') and image.description:
                desc_text = image.description.lower()
                for keyword in game_keywords:
                    if keyword in desc_text:
                        score += 1.5

            # إضافة نقاط للمصدر الموثوق
            source_scores = {
                'Press Kit': 5.0,
                'IGDB': 4.0,
                'RAWG': 3.5,
                'Steam': 3.0,
                'Giant Bomb': 2.5
            }
            score += source_scores.get(image.source, 1.0)

            return score

        except Exception as e:
            logger.warning(f"خطأ في حساب نقاط الصلة: {e}")
            return 1.0

    def get_usage_stats(self) -> Dict:
        """الحصول على إحصائيات الاستخدام"""
        return {
            **self.usage_stats,
            'success_rate': (
                self.usage_stats['successful_requests'] /
                max(self.usage_stats['total_requests'], 1)
            ) * 100
        }

    async def test_all_providers(self) -> Dict:
        """اختبار جميع موفري الصور"""
        test_results = {}
        test_game = "The Witcher 3"

        for provider_name, provider in self.providers.items():
            try:
                logger.info(f"🧪 اختبار {provider_name.upper()}...")

                images = await provider.search_game_images(test_game, 1)
                test_results[provider_name] = {
                    'available': len(images) > 0,
                    'images_found': len(images),
                    'error': None
                }

                if images:
                    logger.info(f"✅ {provider_name.upper()} يعمل بشكل صحيح")
                else:
                    logger.warning(f"⚠️ {provider_name.upper()} لم يعيد صور")

            except Exception as e:
                test_results[provider_name] = {
                    'available': False,
                    'images_found': 0,
                    'error': str(e)
                }
                logger.error(f"❌ فشل اختبار {provider_name.upper()}: {e}")

        return test_results

# إنشاء مثيل عام
licensed_image_manager = LicensedImageManager()
