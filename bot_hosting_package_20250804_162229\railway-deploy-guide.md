# 🚀 دليل رفع البوت على Railway

## الخطوة 1: إنشاء حساب
1. اذهب إلى [railway.app](https://railway.app)
2. اضغط "Start a New Project"
3. سجل دخول بـ GitHub أو Discord أو Email

## الخطوة 2: إنشاء مشروع جديد
1. اضغط "Deploy from GitHub repo" ثم "Deploy without a repo"
2. اختر "Empty Project"
3. اضغط "Add a Service" ثم "Empty Service"

## الخطوة 3: رفع الملفات
1. اضغط على الخدمة التي أنشأتها
2. اذهب إلى تبويب "Settings"
3. في قسم "Source" اضغط "Connect Repo"
4. اختر "Upload Files" أو "Drag & Drop"
5. ارفع جميع ملفات البوت:
   - main.py
   - requirements.txt
   - runtime.txt
   - Procfile
   - .env (ستضيف المتغيرات لاحقاً)
   - جميع ملفات JSON

## الخطوة 4: إعداد متغيرات البيئة
1. اذهب إلى تبويب "Variables"
2. أضف المتغيرات التالية:
   ```
   BOT_TOKEN=your_bot_token_here
   ADMIN_CHAT_ID=your_admin_id_here
   SUPABASE_URL=your_supabase_url
   SUPABASE_KEY=your_supabase_key
   ENVIRONMENT=production
   DEBUG=false
   LOG_LEVEL=INFO
   ```

## الخطوة 5: تشغيل البوت
1. اذهب إلى تبويب "Deployments"
2. سيبدأ التشغيل تلقائياً
3. راقب اللوجز للتأكد من عدم وجود أخطاء

## الخطوة 6: الحصول على الرابط
1. اذهب إلى تبويب "Settings"
2. في قسم "Networking" ستجد الرابط العام
3. استخدم هذا الرابط في إعدادات البوت

## نصائح مهمة:
- ✅ Railway يدعم Python 3.11
- ✅ يعيد التشغيل تلقائياً عند الأخطاء
- ✅ 500 ساعة مجانية = ~16 ساعة يومياً
- ⚠️ تأكد من رفع جميع ملفات JSON
- ⚠️ لا ترفع ملف .env، استخدم Variables
