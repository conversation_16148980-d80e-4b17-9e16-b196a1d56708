[{"id": "ERR_1752806575_0", "timestamp": "2025-07-18T02:42:55.248420", "type": "TimeoutError", "message": "Locator.screenshot: Timeout 30000ms exceeded.\nCall log:\n  - waiting for locator(\"[data-test-id=\\\"post-content\\\"]\")\n", "traceback": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Desktop\\RedditVideoMakerBot\\automation\\error_handler.py\", line 320, in wrapper\n    return func(*args, **kwargs)\n  File \"C:\\Users\\<USER>\\Desktop\\RedditVideoMakerBot\\main.py\", line 105, in main\n    get_screenshots_of_reddit_posts(reddit_object, number_of_comments)\n    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\Desktop\\RedditVideoMakerBot\\video_creation\\screenshot_downloader.py\", line 203, in get_screenshots_of_reddit_posts\n    raise e\n  File \"C:\\Users\\<USER>\\Desktop\\RedditVideoMakerBot\\video_creation\\screenshot_downloader.py\", line 185, in get_screenshots_of_reddit_posts\n    page.locator('[data-test-id=\"post-content\"]').screenshot(path=postcontentpath)\n    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\playwright\\sync_api\\_generated.py\", line 17322, in screenshot\n    self._sync(\n    ~~~~~~~~~~^\n        self._impl_obj.screenshot(\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^\n    ...<11 lines>...\n        )\n        ^\n    )\n    ^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\playwright\\_impl\\_sync_base.py\", line 115, in _sync\n    return task.result()\n           ~~~~~~~~~~~^^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\playwright\\_impl\\_locator.py\", line 544, in screenshot\n    return await self._with_element(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^\n    ...<3 lines>...\n    )\n    ^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\playwright\\_impl\\_locator.py\", line 112, in _with_element\n    handle = await self.element_handle(timeout=timeout)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\playwright\\_impl\\_locator.py\", line 315, in element_handle\n    handle = await self._frame.wait_for_selector(\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n        self._selector, strict=True, state=\"attached\", **params\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\playwright\\_impl\\_frame.py\", line 341, in wait_for_selector\n    await self._channel.send(\n        \"waitForSelector\", self._timeout, locals_to_params(locals())\n    )\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\playwright\\_impl\\_connection.py\", line 69, in send\n    return await self._connection.wrap_api_call(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    ...<3 lines>...\n    )\n    ^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\playwright\\_impl\\_connection.py\", line 558, in wrap_api_call\n    raise rewrite_error(error, f\"{parsed_st['apiName']}: {error}\") from None\nplaywright._impl._errors.TimeoutError: Locator.screenshot: Timeout 30000ms exceeded.\nCall log:\n  - waiting for locator(\"[data-test-id=\\\"post-content\\\"]\")\n\n", "context": {"function": "main_video_creation"}, "severity": "critical", "resolved": false}, {"id": "ERR_1752812692_1", "timestamp": "2025-07-18T04:24:52.763015", "type": "RuntimeError", "message": "no running event loop", "traceback": "Traceback (most recent call last):\n  File \"c:\\Users\\<USER>\\Desktop\\RedditVideoMakerBot\\main.py\", line 108, in main\n    length, number_of_comments = save_text_to_mp3(reddit_object)\n                                 ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\Desktop\\RedditVideoMakerBot\\video_creation\\voices.py\", line 49, in save_text_to_mp3\n    return text_to_mp3.run()\n           ~~~~~~~~~~~~~~~^^\n  File \"c:\\Users\\<USER>\\Desktop\\RedditVideoMakerBot\\TTS\\engine_wrapper.py\", line 83, in run\n    self.call_tts(\"title\", process_text(self.reddit_object[\"thread_title\"]))\n    ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\Desktop\\RedditVideoMakerBot\\TTS\\engine_wrapper.py\", line 167, in call_tts\n    self.tts_module.run(\n    ~~~~~~~~~~~~~~~~~~~^\n        text,\n        ^^^^^\n        filepath=filepath,\n        ^^^^^^^^^^^^^^^^^^\n        random_voice=settings.config[\"settings\"][\"tts\"][\"random_voice\"],\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File \"c:\\Users\\<USER>\\Desktop\\RedditVideoMakerBot\\TTS\\elevenlabs.py\", line 21, in run\n    self._run_free_version(text, filepath)\n    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\Desktop\\RedditVideoMakerBot\\TTS\\elevenlabs.py\", line 52, in _run_free_version\n    raise Exception(f\"ElevenLabs Free API Error: {response.status_code} - {response.text}\")\nException: ElevenLabs Free API Error: 401 - {\"detail\":{\"status\":\"needs_authorization\",\"message\":\"Neither authorization header nor xi-api-key received, please provide one.\"}}\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"c:\\Users\\<USER>\\Desktop\\RedditVideoMakerBot\\automation\\error_handler.py\", line 320, in wrapper\n    return func(*args, **kwargs)\n  File \"c:\\Users\\<USER>\\Desktop\\RedditVideoMakerBot\\main.py\", line 113, in main\n    smart_monitor.log_error(\"tts_engine_failure\", str(e), {\"reddit_id\": reddit_object.get(\"thread_id\")})\n    ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\Desktop\\RedditVideoMakerBot\\automation\\smart_monitor.py\", line 61, in log_error\n    self._analyze_and_respond(error_type, error_message, context)\n    ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\Desktop\\RedditVideoMakerBot\\automation\\smart_monitor.py\", line 76, in _analyze_and_respond\n    asyncio.create_task(self._attempt_recovery(error_type, error_message, context))\n    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Program Files\\Python313\\Lib\\asyncio\\tasks.py\", line 407, in create_task\n    loop = events.get_running_loop()\nRuntimeError: no running event loop\n", "context": {"function": "main_video_creation"}, "severity": "critical", "resolved": false}, {"id": "ERR_1752813392_2", "timestamp": "2025-07-18T04:36:32.620737", "type": "Exception", "message": "ElevenLabs Free API Error: 401 - {\"detail\":{\"status\":\"needs_authorization\",\"message\":\"Neither authorization header nor xi-api-key received, please provide one.\"}}", "traceback": "Traceback (most recent call last):\n  File \"c:\\Users\\<USER>\\Desktop\\RedditVideoMakerBot\\automation\\error_handler.py\", line 320, in wrapper\n    return func(*args, **kwargs)\n  File \"c:\\Users\\<USER>\\Desktop\\RedditVideoMakerBot\\main.py\", line 108, in main\n    length, number_of_comments = save_text_to_mp3(reddit_object)\n                                 ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\Desktop\\RedditVideoMakerBot\\video_creation\\voices.py\", line 49, in save_text_to_mp3\n    return text_to_mp3.run()\n           ~~~~~~~~~~~~~~~^^\n  File \"c:\\Users\\<USER>\\Desktop\\RedditVideoMakerBot\\TTS\\engine_wrapper.py\", line 83, in run\n    self.call_tts(\"title\", process_text(self.reddit_object[\"thread_title\"]))\n    ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\Desktop\\RedditVideoMakerBot\\TTS\\engine_wrapper.py\", line 167, in call_tts\n    self.tts_module.run(\n    ~~~~~~~~~~~~~~~~~~~^\n        text,\n        ^^^^^\n        filepath=filepath,\n        ^^^^^^^^^^^^^^^^^^\n        random_voice=settings.config[\"settings\"][\"tts\"][\"random_voice\"],\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File \"c:\\Users\\<USER>\\Desktop\\RedditVideoMakerBot\\TTS\\elevenlabs.py\", line 21, in run\n    self._run_free_version(text, filepath)\n    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\Desktop\\RedditVideoMakerBot\\TTS\\elevenlabs.py\", line 52, in _run_free_version\n    raise Exception(f\"ElevenLabs Free API Error: {response.status_code} - {response.text}\")\nException: ElevenLabs Free API Error: 401 - {\"detail\":{\"status\":\"needs_authorization\",\"message\":\"Neither authorization header nor xi-api-key received, please provide one.\"}}\n", "context": {"function": "main_video_creation"}, "severity": "critical", "resolved": false}, {"id": "ERR_1752813755_3", "timestamp": "2025-07-18T04:42:35.178355", "type": "TimeoutError", "message": "Locator.screenshot: Timeout 30000ms exceeded.\nCall log:\n  - waiting for locator(\"#t1_n3seewj\")\n", "traceback": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Desktop\\RedditVideoMakerBot\\automation\\error_handler.py\", line 320, in wrapper\n    return func(*args, **kwargs)\n  File \"C:\\Users\\<USER>\\Desktop\\RedditVideoMakerBot\\main.py\", line 120, in main\n    get_screenshots_of_reddit_posts(reddit_object, number_of_comments)\n    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\Desktop\\RedditVideoMakerBot\\video_creation\\screenshot_downloader.py\", line 298, in get_screenshots_of_reddit_posts\n    page.locator(f\"#t1_{comment['comment_id']}\").screenshot(\n    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^\n        path=f\"assets/temp/{reddit_id}/png/comment_{idx}.png\"\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\playwright\\sync_api\\_generated.py\", line 17322, in screenshot\n    self._sync(\n    ~~~~~~~~~~^\n        self._impl_obj.screenshot(\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^\n    ...<11 lines>...\n        )\n        ^\n    )\n    ^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\playwright\\_impl\\_sync_base.py\", line 115, in _sync\n    return task.result()\n           ~~~~~~~~~~~^^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\playwright\\_impl\\_locator.py\", line 544, in screenshot\n    return await self._with_element(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^\n    ...<3 lines>...\n    )\n    ^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\playwright\\_impl\\_locator.py\", line 112, in _with_element\n    handle = await self.element_handle(timeout=timeout)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\playwright\\_impl\\_locator.py\", line 315, in element_handle\n    handle = await self._frame.wait_for_selector(\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n        self._selector, strict=True, state=\"attached\", **params\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\playwright\\_impl\\_frame.py\", line 341, in wait_for_selector\n    await self._channel.send(\n        \"waitForSelector\", self._timeout, locals_to_params(locals())\n    )\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\playwright\\_impl\\_connection.py\", line 69, in send\n    return await self._connection.wrap_api_call(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    ...<3 lines>...\n    )\n    ^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\playwright\\_impl\\_connection.py\", line 558, in wrap_api_call\n    raise rewrite_error(error, f\"{parsed_st['apiName']}: {error}\") from None\nplaywright._impl._errors.TimeoutError: Locator.screenshot: Timeout 30000ms exceeded.\nCall log:\n  - waiting for locator(\"#t1_n3seewj\")\n\n", "context": {"function": "main_video_creation"}, "severity": "critical", "resolved": false}, {"id": "ERR_1752816188_4", "timestamp": "2025-07-18T05:23:08.977520", "type": "DownloadError", "message": "\u001b[0;31mERROR:\u001b[0m \r[download] Got error: HTTPSConnectionPool(host='rr3---sn-4g5ednz7.googlevideo.com', port=443): Read timed out. (read timeout=20.0)", "traceback": "Traceback (most recent call last):\n  File \"c:\\Users\\<USER>\\Desktop\\RedditVideoMakerBot\\automation\\error_handler.py\", line 320, in wrapper\n    return func(*args, **kwargs)\n  File \"c:\\Users\\<USER>\\Desktop\\RedditVideoMakerBot\\main.py\", line 135, in main\n    download_background_audio(bg_config[\"audio\"])\n    ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\Desktop\\RedditVideoMakerBot\\video_creation\\background.py\", line 118, in download_background_audio\n    ydl.download([uri])\n    ~~~~~~~~~~~~^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yt_dlp\\YoutubeDL.py\", line 3644, in download\n    self.__download_wrapper(self.extract_info)(\n    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^\n        url, force_generic_extractor=self.params.get('force_generic_extractor', False))\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yt_dlp\\YoutubeDL.py\", line 3617, in wrapper\n    res = func(*args, **kwargs)\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yt_dlp\\YoutubeDL.py\", line 1651, in extract_info\n    return self.__extract_info(url, self.get_info_extractor(key), download, extra_info, process)\n           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yt_dlp\\YoutubeDL.py\", line 1662, in wrapper\n    return func(self, *args, **kwargs)\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yt_dlp\\YoutubeDL.py\", line 1818, in __extract_info\n    return self.process_ie_result(ie_result, download, extra_info)\n           ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yt_dlp\\YoutubeDL.py\", line 1877, in process_ie_result\n    ie_result = self.process_video_result(ie_result, download=download)\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yt_dlp\\YoutubeDL.py\", line 3050, in process_video_result\n    self.process_info(new_info)\n    ~~~~~~~~~~~~~~~~~^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yt_dlp\\YoutubeDL.py\", line 185, in wrapper\n    return func(self, *args, **kwargs)\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yt_dlp\\YoutubeDL.py\", line 3518, in process_info\n    success, real_download = self.dl(temp_filename, info_dict)\n                             ~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yt_dlp\\YoutubeDL.py\", line 3238, in dl\n    return fd.download(name, new_info, subtitle)\n           ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yt_dlp\\downloader\\common.py\", line 468, in download\n    ret = self.real_download(filename, info_dict)\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yt_dlp\\downloader\\http.py\", line 365, in real_download\n    for retry in RetryManager(self.params.get('retries'), self.report_retry):\n                 ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yt_dlp\\utils\\_utils.py\", line 5252, in __iter__\n    self.error_callback(self.error, self.attempt, self.retries)\n    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yt_dlp\\downloader\\common.py\", line 414, in report_retry\n    RetryManager.report_retry(\n    ~~~~~~~~~~~~~~~~~~~~~~~~~^\n        err, count, retries, info=self.__to_screen,\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    ...<2 lines>...\n        sleep_func=self.params.get('retry_sleep_functions', {}).get(is_frag or 'http'),\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n        suffix=f'fragment{\"s\" if frag_index is None else f\" {frag_index}\"}' if is_frag else None)\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yt_dlp\\utils\\_utils.py\", line 5259, in report_retry\n    return error(f'{e}. Giving up after {count - 1} retries') if count > 1 else error(str(e))\n                                                                                ~~~~~^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yt_dlp\\downloader\\common.py\", line 417, in <lambda>\n    error=IDENTITY if not fatal else lambda e: self.report_error(f'\\r[download] Got error: {e}'),\n                                               ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yt_dlp\\YoutubeDL.py\", line 1120, in report_error\n    self.trouble(f'{self._format_err(\"ERROR:\", self.Styles.ERROR)} {message}', *args, **kwargs)\n    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yt_dlp\\YoutubeDL.py\", line 1059, in trouble\n    raise DownloadError(message, exc_info)\nyt_dlp.utils.DownloadError: \u001b[0;31mERROR:\u001b[0m \r[download] Got error: HTTPSConnectionPool(host='rr3---sn-4g5ednz7.googlevideo.com', port=443): Read timed out. (read timeout=20.0)\n", "context": {"function": "main_video_creation"}, "severity": "critical", "resolved": false}, {"id": "ERR_1752861211_5", "timestamp": "2025-07-18T17:53:31.005234", "type": "AttributeError", "message": "'NoneType' object has no attribute 'max_chars'", "traceback": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Desktop\\RedditVideoMakerBot\\automation\\error_handler.py\", line 320, in wrapper\n    return func(*args, **kwargs)\n  File \"C:\\Users\\<USER>\\Desktop\\RedditVideoMakerBot\\main.py\", line 108, in main\n    length, number_of_comments = save_text_to_mp3(reddit_object)\n                                 ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\Desktop\\RedditVideoMakerBot\\video_creation\\voices.py\", line 49, in save_text_to_mp3\n    return text_to_mp3.run()\n           ~~~~~~~~~~~~~~~^^\n  File \"C:\\Users\\<USER>\\Desktop\\RedditVideoMakerBot\\TTS\\engine_wrapper.py\", line 104, in run\n    len(comment[\"comment_body\"]) > self.tts_module.max_chars\n                                   ^^^^^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'NoneType' object has no attribute 'max_chars'\n", "context": {"function": "main_video_creation"}, "severity": "critical", "resolved": false}, {"id": "ERR_1752862938_6", "timestamp": "2025-07-18T18:22:18.268366", "type": "NameError", "message": "name 'logger' is not defined", "traceback": "Traceback (most recent call last):\n  File \"c:\\Users\\<USER>\\Desktop\\RedditVideoMakerBot\\automation\\error_handler.py\", line 320, in wrapper\n    return func(*args, **kwargs)\n  File \"c:\\Users\\<USER>\\Desktop\\RedditVideoMakerBot\\main.py\", line 108, in main\n    length, number_of_comments = save_text_to_mp3(reddit_object)\n                                 ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\Desktop\\RedditVideoMakerBot\\video_creation\\voices.py\", line 49, in save_text_to_mp3\n    return text_to_mp3.run()\n           ~~~~~~~~~~~~~~~^^\n  File \"c:\\Users\\<USER>\\Desktop\\RedditVideoMakerBot\\TTS\\engine_wrapper.py\", line 109, in run\n    logger.info(f\"🎤 معالجة {len(comments_to_process)} تعليق من أصل {len(self.reddit_object['comments'])}\")\n    ^^^^^^\nNameError: name 'logger' is not defined\n", "context": {"function": "main_video_creation"}, "severity": "critical", "resolved": false}, {"id": "ERR_1753617985_7", "timestamp": "2025-07-27T12:06:25.592447", "type": "Exception", "message": "فشل في إنشاء الفيديو الرئيسي: ffmpeg version 2025-03-31-git-35c091f4b7-essentials_build-www.gyan.dev Copyright (c) 2000-2025 the FFmpeg developers\r\n  built with gcc 14.2.0 (Rev1, Built by MSYS2 project)\r\n  configuration: --enable-gpl --enable-version3 --enable-static --disable-w32threads --disable-autodetect --enable-fontconfig --enable-iconv --enable-gnutls --enable-libxml2 --enable-gmp --enable-bzlib --enable-lzma --enable-zlib --enable-libsrt --enable-libssh --enable-libzmq --enable-avisynth --enable-sdl2 --enable-libwebp --enable-libx264 --enable-libx265 --enable-libxvid --enable-libaom --enable-libopenjpeg --enable-libvpx --enable-mediafoundation --enable-libass --enable-libfreetype --enable-libfribidi --enable-libharfbuzz --enable-libvidstab --enable-libvmaf --enable-libzimg --enable-amf --enable-cuda-llvm --enable-cuvid --enable-dxva2 --enable-d3d11va --enable-d3d12va --enable-ffnvcodec --enable-libvpl --enable-nvdec --enable-nvenc --enable-vaapi --enable-libgme --enable-libopenmpt --enable-libopencore-amrwb --enable-libmp3lame --enable-libtheora --enable-libvo-amrwbenc --enable-libgsm --enable-libopencore-amrnb --enable-libopus --enable-libspeex --enable-libvorbis --enable-librubberband\r\n  libavutil      60.  0.100 / 60.  0.100\r\n  libavcodec     62.  0.100 / 62.  0.100\r\n  libavformat    62.  0.100 / 62.  0.100\r\n  libavdevice    62.  0.100 / 62.  0.100\r\n  libavfilter    11.  0.100 / 11.  0.100\r\n  libswscale      9.  0.100 /  9.  0.100\r\n  libswresample   6.  0.100 /  6.  0.100\r\n  libpostproc    59.  0.100 / 59.  0.100\r\nInput #0, mov,mp4,m4a,3gp,3g2,mj2, from 'assets/temp/1ma7i7c/background_noaudio.mp4':\r\n  Metadata:\r\n    major_brand     : isom\r\n    minor_version   : 512\r\n    compatible_brands: isomiso2avc1mp41\r\n    encoder         : Lavf62.0.100\r\n  Duration: 00:00:10.03, start: 0.000000, bitrate: 18960 kb/s\r\n  Stream #0:0[0x1](und): Video: h264 (High) (avc1 / 0x31637661), yuv420p(tv, bt709, progressive), 608x1080 [SAR 1:1 DAR 76:135], 18954 kb/s, 59.94 fps, 59.94 tbr, 60k tbn (default)\r\n    Metadata:\r\n      handler_name    : VideoHandler\r\n      vendor_id       : [0][0][0][0]\r\n      encoder         : Lavc62.0.100 libx264\r\nInput #1, png_pipe, from 'assets/temp/1ma7i7c/png/title.png':\r\n  Duration: N/A, bitrate: N/A\r\n  Stream #1:0: Video: png, rgba(pc, gbr/unknown/unknown), 1080x1920, 25 fps, 25 tbr, 25 tbn\r\nInput #2, png_pipe, from 'assets/temp/1ma7i7c/png/comment_0.png':\r\n  Duration: N/A, bitrate: N/A\r\n  Stream #2:0: Video: png, rgb24(pc, gbr/unknown/unknown), 1080x400, 25 fps, 25 tbr, 25 tbn\r\nInput #3, png_pipe, from 'assets/temp/1ma7i7c/png/comment_1.png':\r\n  Duration: N/A, bitrate: N/A\r\n  Stream #3:0: Video: png, rgb24(pc, gbr/unknown/unknown), 1080x400, 25 fps, 25 tbr, 25 tbn\r\nInput #4, png_pipe, from 'assets/temp/1ma7i7c/png/comment_2.png':\r\n  Duration: N/A, bitrate: N/A\r\n  Stream #4:0: Video: png, rgb24(pc, gbr/unknown/unknown), 1080x400, 25 fps, 25 tbr, 25 tbn\r\nInput #5, png_pipe, from 'assets/temp/1ma7i7c/png/comment_3.png':\r\n  Duration: N/A, bitrate: N/A\r\n  Stream #5:0: Video: png, rgb24(pc, gbr/unknown/unknown), 1080x400, 25 fps, 25 tbr, 25 tbn\r\nInput #6, png_pipe, from 'assets/temp/1ma7i7c/png/comment_4.png':\r\n  Duration: N/A, bitrate: N/A\r\n  Stream #6:0: Video: png, rgb24(pc, gbr/unknown/unknown), 1080x400, 25 fps, 25 tbr, 25 tbn\r\nInput #7, png_pipe, from 'assets/temp/1ma7i7c/png/comment_5.png':\r\n  Duration: N/A, bitrate: N/A\r\n  Stream #7:0: Video: png, rgb24(pc, gbr/unknown/unknown), 1080x400, 25 fps, 25 tbr, 25 tbn\r\nInput #8, png_pipe, from 'assets/temp/1ma7i7c/png/comment_6.png':\r\n  Duration: N/A, bitrate: N/A\r\n  Stream #8:0: Video: png, rgb24(pc, gbr/unknown/unknown), 1080x400, 25 fps, 25 tbr, 25 tbn\r\nInput #9, png_pipe, from 'assets/temp/1ma7i7c/png/comment_7.png':\r\n  Duration: N/A, bitrate: N/A\r\n  Stream #9:0: Video: png, rgb24(pc, gbr/unknown/unknown), 1080x400, 25 fps, 25 tbr, 25 tbn\r\nInput #10, png_pipe, from 'assets/temp/1ma7i7c/png/comment_8.png':\r\n  Duration: N/A, bitrate: N/A\r\n  Stream #10:0: Video: png, rgb24(pc, gbr/unknown/unknown), 1080x400, 25 fps, 25 tbr, 25 tbn\r\nInput #11, mp3, from 'assets/temp/1ma7i7c/audio.mp3':\r\n  Metadata:\r\n    encoder         : Lavf62.0.100\r\n  Duration: 00:01:07.29, start: 0.025057, bitrate: 192 kb/s\r\n  Stream #11:0: Audio: mp3 (mp3float), 44100 Hz, mono, fltp, 192 kb/s\r\nStream mapping:\r\n  Stream #0:0 (h264) -> overlay\r\n  Stream #1:0 (png) -> scale:default\r\n  Stream #2:0 (png) -> scale:default\r\n  Stream #3:0 (png) -> scale:default\r\n  Stream #4:0 (png) -> scale:default\r\n  Stream #5:0 (png) -> scale:default\r\n  Stream #6:0 (png) -> scale:default\r\n  Stream #7:0 (png) -> scale:default\r\n  Stream #8:0 (png) -> scale:default\r\n  Stream #9:0 (png) -> scale:default\r\n  Stream #10:0 (png) -> scale:default\r\n  scale:default -> Stream #0:0 (libx264)\r\n  Stream #11:0 -> #0:1 (mp3 (mp3float) -> aac (native))\r\nPress [q] to stop, [?] for help\r\n[libx264 @ 000001cd761150c0] using SAR=1216/1215\r\n[libx264 @ 000001cd761150c0] using cpu capabilities: MMX2 SSE2Fast SSSE3 SSE4.2 AVX FMA3 BMI2 AVX2\r\n[libx264 @ 000001cd761150c0] profile High, level 4.1, 4:2:0, 8-bit\r\n[libx264 @ 000001cd761150c0] 264 - core 165 r3214 fe9e4a7 - H.264/MPEG-4 AVC codec - Copyleft 2003-2025 - http://www.videolan.org/x264.html - options: cabac=1 ref=3 deblock=1:0:0 analyse=0x3:0x113 me=hex subme=7 psy=1 psy_rd=1.00:0.00 mixed_ref=1 me_range=16 chroma_me=1 trellis=1 8x8dct=1 cqm=0 deadzone=21,11 fast_pskip=1 chroma_qp_offset=-2 threads=8 lookahead_threads=1 sliced_threads=0 nr=0 decimate=1 interlaced=0 bluray_compat=0 constrained_intra=0 bframes=3 b_pyramid=2 b_adapt=1 b_bias=0 direct=1 weightb=1 open_gop=0 weightp=2 keyint=250 keyint_min=25 scenecut=40 intra_refresh=0 rc_lookahead=40 rc=abr mbtree=1 bitrate=20000 ratetol=1.0 qcomp=0.60 qpmin=0 qpmax=69 qpstep=4 ip_ratio=1.40 aq=1:1.00\r\nOutput #0, mp4, to 'results/AskReddit/What if Congress representatives and state representatives were paid their states median income.mp4':\r\n  Metadata:\r\n    major_brand     : isom\r\n    minor_version   : 512\r\n    compatible_brands: isomiso2avc1mp41\r\n    encoder         : Lavf62.0.100\r\n  Stream #0:0: Video: h264 (avc1 / 0x31637661), yuv420p(tv, bt709, progressive), 720x1280 [SAR 1216:1215 DAR 76:135], q=2-31, 20000 kb/s, 59.94 fps, 60k tbn\r\n    Metadata:\r\n      encoder         : Lavc62.0.100 libx264\r\n    Side data:\r\n      cpb: bitrate max/min/avg: 0/0/20000000 buffer size: 0 vbv_delay: N/A\r\n  Stream #0:1: Audio: aac (LC) (mp4a / 0x6134706D), 44100 Hz, mono, fltp, 192 kb/s\r\n    Metadata:\r\n      encoder         : Lavc62.0.100 aac\r\nframe=    0 fps=0.0 q=0.0 size=       0KiB time=N/A bitrate=N/A speed=N/A    \rframe=    5 fps=4.8 q=21.0 size=     256KiB time=00:00:00.05 bitrate=41908.8kbits/s speed=0.0482x    \rframe=   22 fps= 14 q=22.0 size=    1024KiB time=00:00:00.31 bitrate=26465.1kbits/s speed=0.204x    \rframe=   37 fps= 17 q=22.0 size=    1536KiB time=00:00:00.58 bitrate=21549.8kbits/s speed=0.274x    \rframe=   56 fps= 21 q=21.0 size=    2048KiB time=00:00:00.90 bitrate=18623.2kbits/s speed=0.334x    \rframe=   76 fps= 24 q=19.0 size=    2560KiB time=00:00:01.23 bitrate=16987.3kbits/s speed=0.384x    \rframe=  100 fps= 27 q=18.0 size=    3328KiB time=00:00:01.63 bitrate=16675.2kbits/s speed=0.438x    \rframe=  120 fps= 28 q=18.0 size=    4096KiB time=00:00:01.93 bitrate=17338.6kbits/s speed=0.453x    \r[out#0/mp4 @ 000001cd760c9980] video:8108KiB audio:74KiB subtitle:0KiB other streams:0KiB global headers:0KiB muxing overhead: 0.082684%\r\nframe=  226 fps= 32 q=-1.0 Lsize=    8189KiB time=00:00:02.95 bitrate=22719.0kbits/s speed=0.42x    \r\n[libx264 @ 000001cd761150c0] frame I:1     Avg QP:13.99  size:111708\r\n[libx264 @ 000001cd761150c0] frame P:61    Avg QP:12.35  size: 69967\r\n[libx264 @ 000001cd761150c0] frame B:164   Avg QP:15.45  size: 23919\r\n[libx264 @ 000001cd761150c0] consecutive B-frames:  1.3%  3.5%  6.6% 88.5%\r\n[libx264 @ 000001cd761150c0] mb I  I16..4: 14.2% 51.0% 34.8%\r\n[libx264 @ 000001cd761150c0] mb P  I16..4:  9.0% 22.3%  8.2%  P16..4: 19.4% 18.1% 11.6%  0.0%  0.0%    skip:11.4%\r\n[libx264 @ 000001cd761150c0] mb B  I16..4:  1.7%  2.6%  1.1%  B16..8: 30.9% 16.0%  4.9%  direct: 8.9%  skip:33.9%  L0:46.8% L1:42.8% BI:10.4%\r\n[libx264 @ 000001cd761150c0] final ratefactor: 8.89\r\n[libx264 @ 000001cd761150c0] 8x8 transform intra:54.4% inter:51.0%\r\n[libx264 @ 000001cd761150c0] coded y,uvDC,uvAC intra: 54.8% 87.9% 81.5% inter: 24.9% 43.1% 26.6%\r\n[libx264 @ 000001cd761150c0] i16 v,h,dc,p: 44% 40%  6% 10%\r\n[libx264 @ 000001cd761150c0] i8 v,h,dc,ddl,ddr,vr,hd,vl,hu: 26% 29% 19%  4%  4%  4%  6%  5%  4%\r\n[libx264 @ 000001cd761150c0] i4 v,h,dc,ddl,ddr,vr,hd,vl,hu: 33% 29% 13%  3%  5%  4%  5%  4%  3%\r\n[libx264 @ 000001cd761150c0] i8c dc,h,v,p: 36% 32% 24%  8%\r\n[libx264 @ 000001cd761150c0] Weighted P-Frames: Y:0.0% UV:0.0%\r\n[libx264 @ 000001cd761150c0] ref P L0: 65.7%  8.7% 17.7%  7.9%\r\n[libx264 @ 000001cd761150c0] ref B L0: 87.6% 10.1%  2.3%\r\n[libx264 @ 000001cd761150c0] ref B L1: 96.1%  3.9%\r\n[libx264 @ 000001cd761150c0] kb/s:17160.13\r\n[aac @ 000001cd76115800] Qavg: 43049.391\r\nExiting normally, received signal 15.\r\n", "traceback": "Traceback (most recent call last):\n  File \"c:\\Users\\<USER>\\Desktop\\cender\\RedditVideoMakerBot\\video_creation\\final_video.py\", line 448, in make_final_video\n    ).overwrite_output().global_args(\"-progress\", progress.output_file.name).run(\n                                                                             ~~~^\n        quiet=True,\n        ^^^^^^^^^^^\n    ...<2 lines>...\n        capture_stderr=False,\n        ^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\ffmpeg\\_run.py\", line 325, in run\n    raise Error('ffmpeg', out, err)\nffmpeg._run.Error: ffmpeg error (see stderr output for detail)\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"c:\\Users\\<USER>\\Desktop\\cender\\RedditVideoMakerBot\\automation\\error_handler.py\", line 320, in wrapper\n    return func(*args, **kwargs)\n  File \"c:\\Users\\<USER>\\Desktop\\cender\\RedditVideoMakerBot\\main.py\", line 162, in main\n    video_path = make_final_video(number_of_comments, length, reddit_object, bg_config)\n  File \"c:\\Users\\<USER>\\Desktop\\cender\\RedditVideoMakerBot\\video_creation\\final_video.py\", line 458, in make_final_video\n    raise Exception(f\"فشل في إنشاء الفيديو الرئيسي: {error_msg}\")\nException: فشل في إنشاء الفيديو الرئيسي: ffmpeg version 2025-03-31-git-35c091f4b7-essentials_build-www.gyan.dev Copyright (c) 2000-2025 the FFmpeg developers\r\n  built with gcc 14.2.0 (Rev1, Built by MSYS2 project)\r\n  configuration: --enable-gpl --enable-version3 --enable-static --disable-w32threads --disable-autodetect --enable-fontconfig --enable-iconv --enable-gnutls --enable-libxml2 --enable-gmp --enable-bzlib --enable-lzma --enable-zlib --enable-libsrt --enable-libssh --enable-libzmq --enable-avisynth --enable-sdl2 --enable-libwebp --enable-libx264 --enable-libx265 --enable-libxvid --enable-libaom --enable-libopenjpeg --enable-libvpx --enable-mediafoundation --enable-libass --enable-libfreetype --enable-libfribidi --enable-libharfbuzz --enable-libvidstab --enable-libvmaf --enable-libzimg --enable-amf --enable-cuda-llvm --enable-cuvid --enable-dxva2 --enable-d3d11va --enable-d3d12va --enable-ffnvcodec --enable-libvpl --enable-nvdec --enable-nvenc --enable-vaapi --enable-libgme --enable-libopenmpt --enable-libopencore-amrwb --enable-libmp3lame --enable-libtheora --enable-libvo-amrwbenc --enable-libgsm --enable-libopencore-amrnb --enable-libopus --enable-libspeex --enable-libvorbis --enable-librubberband\r\n  libavutil      60.  0.100 / 60.  0.100\r\n  libavcodec     62.  0.100 / 62.  0.100\r\n  libavformat    62.  0.100 / 62.  0.100\r\n  libavdevice    62.  0.100 / 62.  0.100\r\n  libavfilter    11.  0.100 / 11.  0.100\r\n  libswscale      9.  0.100 /  9.  0.100\r\n  libswresample   6.  0.100 /  6.  0.100\r\n  libpostproc    59.  0.100 / 59.  0.100\r\nInput #0, mov,mp4,m4a,3gp,3g2,mj2, from 'assets/temp/1ma7i7c/background_noaudio.mp4':\r\n  Metadata:\r\n    major_brand     : isom\r\n    minor_version   : 512\r\n    compatible_brands: isomiso2avc1mp41\r\n    encoder         : Lavf62.0.100\r\n  Duration: 00:00:10.03, start: 0.000000, bitrate: 18960 kb/s\r\n  Stream #0:0[0x1](und): Video: h264 (High) (avc1 / 0x31637661), yuv420p(tv, bt709, progressive), 608x1080 [SAR 1:1 DAR 76:135], 18954 kb/s, 59.94 fps, 59.94 tbr, 60k tbn (default)\r\n    Metadata:\r\n      handler_name    : VideoHandler\r\n      vendor_id       : [0][0][0][0]\r\n      encoder         : Lavc62.0.100 libx264\r\nInput #1, png_pipe, from 'assets/temp/1ma7i7c/png/title.png':\r\n  Duration: N/A, bitrate: N/A\r\n  Stream #1:0: Video: png, rgba(pc, gbr/unknown/unknown), 1080x1920, 25 fps, 25 tbr, 25 tbn\r\nInput #2, png_pipe, from 'assets/temp/1ma7i7c/png/comment_0.png':\r\n  Duration: N/A, bitrate: N/A\r\n  Stream #2:0: Video: png, rgb24(pc, gbr/unknown/unknown), 1080x400, 25 fps, 25 tbr, 25 tbn\r\nInput #3, png_pipe, from 'assets/temp/1ma7i7c/png/comment_1.png':\r\n  Duration: N/A, bitrate: N/A\r\n  Stream #3:0: Video: png, rgb24(pc, gbr/unknown/unknown), 1080x400, 25 fps, 25 tbr, 25 tbn\r\nInput #4, png_pipe, from 'assets/temp/1ma7i7c/png/comment_2.png':\r\n  Duration: N/A, bitrate: N/A\r\n  Stream #4:0: Video: png, rgb24(pc, gbr/unknown/unknown), 1080x400, 25 fps, 25 tbr, 25 tbn\r\nInput #5, png_pipe, from 'assets/temp/1ma7i7c/png/comment_3.png':\r\n  Duration: N/A, bitrate: N/A\r\n  Stream #5:0: Video: png, rgb24(pc, gbr/unknown/unknown), 1080x400, 25 fps, 25 tbr, 25 tbn\r\nInput #6, png_pipe, from 'assets/temp/1ma7i7c/png/comment_4.png':\r\n  Duration: N/A, bitrate: N/A\r\n  Stream #6:0: Video: png, rgb24(pc, gbr/unknown/unknown), 1080x400, 25 fps, 25 tbr, 25 tbn\r\nInput #7, png_pipe, from 'assets/temp/1ma7i7c/png/comment_5.png':\r\n  Duration: N/A, bitrate: N/A\r\n  Stream #7:0: Video: png, rgb24(pc, gbr/unknown/unknown), 1080x400, 25 fps, 25 tbr, 25 tbn\r\nInput #8, png_pipe, from 'assets/temp/1ma7i7c/png/comment_6.png':\r\n  Duration: N/A, bitrate: N/A\r\n  Stream #8:0: Video: png, rgb24(pc, gbr/unknown/unknown), 1080x400, 25 fps, 25 tbr, 25 tbn\r\nInput #9, png_pipe, from 'assets/temp/1ma7i7c/png/comment_7.png':\r\n  Duration: N/A, bitrate: N/A\r\n  Stream #9:0: Video: png, rgb24(pc, gbr/unknown/unknown), 1080x400, 25 fps, 25 tbr, 25 tbn\r\nInput #10, png_pipe, from 'assets/temp/1ma7i7c/png/comment_8.png':\r\n  Duration: N/A, bitrate: N/A\r\n  Stream #10:0: Video: png, rgb24(pc, gbr/unknown/unknown), 1080x400, 25 fps, 25 tbr, 25 tbn\r\nInput #11, mp3, from 'assets/temp/1ma7i7c/audio.mp3':\r\n  Metadata:\r\n    encoder         : Lavf62.0.100\r\n  Duration: 00:01:07.29, start: 0.025057, bitrate: 192 kb/s\r\n  Stream #11:0: Audio: mp3 (mp3float), 44100 Hz, mono, fltp, 192 kb/s\r\nStream mapping:\r\n  Stream #0:0 (h264) -> overlay\r\n  Stream #1:0 (png) -> scale:default\r\n  Stream #2:0 (png) -> scale:default\r\n  Stream #3:0 (png) -> scale:default\r\n  Stream #4:0 (png) -> scale:default\r\n  Stream #5:0 (png) -> scale:default\r\n  Stream #6:0 (png) -> scale:default\r\n  Stream #7:0 (png) -> scale:default\r\n  Stream #8:0 (png) -> scale:default\r\n  Stream #9:0 (png) -> scale:default\r\n  Stream #10:0 (png) -> scale:default\r\n  scale:default -> Stream #0:0 (libx264)\r\n  Stream #11:0 -> #0:1 (mp3 (mp3float) -> aac (native))\r\nPress [q] to stop, [?] for help\r\n[libx264 @ 000001cd761150c0] using SAR=1216/1215\r\n[libx264 @ 000001cd761150c0] using cpu capabilities: MMX2 SSE2Fast SSSE3 SSE4.2 AVX FMA3 BMI2 AVX2\r\n[libx264 @ 000001cd761150c0] profile High, level 4.1, 4:2:0, 8-bit\r\n[libx264 @ 000001cd761150c0] 264 - core 165 r3214 fe9e4a7 - H.264/MPEG-4 AVC codec - Copyleft 2003-2025 - http://www.videolan.org/x264.html - options: cabac=1 ref=3 deblock=1:0:0 analyse=0x3:0x113 me=hex subme=7 psy=1 psy_rd=1.00:0.00 mixed_ref=1 me_range=16 chroma_me=1 trellis=1 8x8dct=1 cqm=0 deadzone=21,11 fast_pskip=1 chroma_qp_offset=-2 threads=8 lookahead_threads=1 sliced_threads=0 nr=0 decimate=1 interlaced=0 bluray_compat=0 constrained_intra=0 bframes=3 b_pyramid=2 b_adapt=1 b_bias=0 direct=1 weightb=1 open_gop=0 weightp=2 keyint=250 keyint_min=25 scenecut=40 intra_refresh=0 rc_lookahead=40 rc=abr mbtree=1 bitrate=20000 ratetol=1.0 qcomp=0.60 qpmin=0 qpmax=69 qpstep=4 ip_ratio=1.40 aq=1:1.00\r\nOutput #0, mp4, to 'results/AskReddit/What if Congress representatives and state representatives were paid their states median income.mp4':\r\n  Metadata:\r\n    major_brand     : isom\r\n    minor_version   : 512\r\n    compatible_brands: isomiso2avc1mp41\r\n    encoder         : Lavf62.0.100\r\n  Stream #0:0: Video: h264 (avc1 / 0x31637661), yuv420p(tv, bt709, progressive), 720x1280 [SAR 1216:1215 DAR 76:135], q=2-31, 20000 kb/s, 59.94 fps, 60k tbn\r\n    Metadata:\r\n      encoder         : Lavc62.0.100 libx264\r\n    Side data:\r\n      cpb: bitrate max/min/avg: 0/0/20000000 buffer size: 0 vbv_delay: N/A\r\n  Stream #0:1: Audio: aac (LC) (mp4a / 0x6134706D), 44100 Hz, mono, fltp, 192 kb/s\r\n    Metadata:\r\n      encoder         : Lavc62.0.100 aac\r\nframe=    0 fps=0.0 q=0.0 size=       0KiB time=N/A bitrate=N/A speed=N/A    \rframe=    5 fps=4.8 q=21.0 size=     256KiB time=00:00:00.05 bitrate=41908.8kbits/s speed=0.0482x    \rframe=   22 fps= 14 q=22.0 size=    1024KiB time=00:00:00.31 bitrate=26465.1kbits/s speed=0.204x    \rframe=   37 fps= 17 q=22.0 size=    1536KiB time=00:00:00.58 bitrate=21549.8kbits/s speed=0.274x    \rframe=   56 fps= 21 q=21.0 size=    2048KiB time=00:00:00.90 bitrate=18623.2kbits/s speed=0.334x    \rframe=   76 fps= 24 q=19.0 size=    2560KiB time=00:00:01.23 bitrate=16987.3kbits/s speed=0.384x    \rframe=  100 fps= 27 q=18.0 size=    3328KiB time=00:00:01.63 bitrate=16675.2kbits/s speed=0.438x    \rframe=  120 fps= 28 q=18.0 size=    4096KiB time=00:00:01.93 bitrate=17338.6kbits/s speed=0.453x    \r[out#0/mp4 @ 000001cd760c9980] video:8108KiB audio:74KiB subtitle:0KiB other streams:0KiB global headers:0KiB muxing overhead: 0.082684%\r\nframe=  226 fps= 32 q=-1.0 Lsize=    8189KiB time=00:00:02.95 bitrate=22719.0kbits/s speed=0.42x    \r\n[libx264 @ 000001cd761150c0] frame I:1     Avg QP:13.99  size:111708\r\n[libx264 @ 000001cd761150c0] frame P:61    Avg QP:12.35  size: 69967\r\n[libx264 @ 000001cd761150c0] frame B:164   Avg QP:15.45  size: 23919\r\n[libx264 @ 000001cd761150c0] consecutive B-frames:  1.3%  3.5%  6.6% 88.5%\r\n[libx264 @ 000001cd761150c0] mb I  I16..4: 14.2% 51.0% 34.8%\r\n[libx264 @ 000001cd761150c0] mb P  I16..4:  9.0% 22.3%  8.2%  P16..4: 19.4% 18.1% 11.6%  0.0%  0.0%    skip:11.4%\r\n[libx264 @ 000001cd761150c0] mb B  I16..4:  1.7%  2.6%  1.1%  B16..8: 30.9% 16.0%  4.9%  direct: 8.9%  skip:33.9%  L0:46.8% L1:42.8% BI:10.4%\r\n[libx264 @ 000001cd761150c0] final ratefactor: 8.89\r\n[libx264 @ 000001cd761150c0] 8x8 transform intra:54.4% inter:51.0%\r\n[libx264 @ 000001cd761150c0] coded y,uvDC,uvAC intra: 54.8% 87.9% 81.5% inter: 24.9% 43.1% 26.6%\r\n[libx264 @ 000001cd761150c0] i16 v,h,dc,p: 44% 40%  6% 10%\r\n[libx264 @ 000001cd761150c0] i8 v,h,dc,ddl,ddr,vr,hd,vl,hu: 26% 29% 19%  4%  4%  4%  6%  5%  4%\r\n[libx264 @ 000001cd761150c0] i4 v,h,dc,ddl,ddr,vr,hd,vl,hu: 33% 29% 13%  3%  5%  4%  5%  4%  3%\r\n[libx264 @ 000001cd761150c0] i8c dc,h,v,p: 36% 32% 24%  8%\r\n[libx264 @ 000001cd761150c0] Weighted P-Frames: Y:0.0% UV:0.0%\r\n[libx264 @ 000001cd761150c0] ref P L0: 65.7%  8.7% 17.7%  7.9%\r\n[libx264 @ 000001cd761150c0] ref B L0: 87.6% 10.1%  2.3%\r\n[libx264 @ 000001cd761150c0] ref B L1: 96.1%  3.9%\r\n[libx264 @ 000001cd761150c0] kb/s:17160.13\r\n[aac @ 000001cd76115800] Qavg: 43049.391\r\nExiting normally, received signal 15.\r\n\n", "context": {"function": "main_video_creation"}, "severity": "critical", "resolved": false}]