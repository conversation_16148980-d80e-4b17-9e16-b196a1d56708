# مدير خدمات تحويل النص إلى صوت المتعددة - Enhanced Speech-to-Text Manager
import asyncio
import aiohttp
import json
import time
import hashlib
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum
import logging

from config.settings import BotConfig
from modules.logger import logger
from modules.database import db
from modules.speech_cache_manager import speech_cache_manager

class SpeechService(Enum):
    """أنواع خدمات تحويل النص إلى صوت"""
    ASSEMBLYAI = "assemblyai"
    SPEECHMATICS = "speechmatics"
    IBM_WATSON = "ibm_watson"
    AZURE_SPEECH = "azure_speech"
    GOOGLE_CLOUD = "google_cloud"
    WITAI = "witai"
    WHISPER = "whisper"  # البديل الاحتياطي

@dataclass
class ServiceConfig:
    """تكوين خدمة تحويل النص إلى صوت"""
    name: str
    api_key: str
    api_url: str
    free_limit_monthly: int  # بالدقائق
    priority: int  # 1 = أعلى أولوية
    enabled: bool = True
    supports_arabic: bool = True
    supports_english: bool = True
    max_file_size_mb: int = 25
    timeout_seconds: int = 300

@dataclass
class TranscriptionResult:
    """نتيجة تحويل النص إلى صوت"""
    success: bool
    text: str
    confidence: float
    language: str
    service_used: str
    processing_time: float
    error_message: Optional[str] = None
    word_count: int = 0
    duration_seconds: float = 0.0

class SpeechToTextManager:
    """مدير خدمات تحويل النص إلى صوت المتعددة"""
    
    def __init__(self):
        self.services = self._initialize_services()
        self.usage_stats = self._load_usage_stats()
        self.cache = {}  # تخزين مؤقت للنتائج
        self.fallback_whisper = None  # سيتم تهيئته عند الحاجة
        
    def _initialize_services(self) -> Dict[SpeechService, ServiceConfig]:
        """تهيئة جميع خدمات تحويل النص إلى صوت"""
        services = {}
        
        # 1. AssemblyAI - الأولوية الأولى (416 ساعة مجانية)
        if BotConfig.ASSEMBLYAI_API_KEY:
            services[SpeechService.ASSEMBLYAI] = ServiceConfig(
                name="AssemblyAI",
                api_key=BotConfig.ASSEMBLYAI_API_KEY,
                api_url=BotConfig.ASSEMBLYAI_API_URL,
                free_limit_monthly=BotConfig.ASSEMBLYAI_FREE_HOURS * 60,  # تحويل لدقائق
                priority=1,
                max_file_size_mb=50,
                timeout_seconds=600
            )
            
        # 2. Speechmatics - الأولوية الثانية (480 دقيقة شهرياً)
        if BotConfig.SPEECHMATICS_API_KEY:
            services[SpeechService.SPEECHMATICS] = ServiceConfig(
                name="Speechmatics",
                api_key=BotConfig.SPEECHMATICS_API_KEY,
                api_url=BotConfig.SPEECHMATICS_API_URL,
                free_limit_monthly=BotConfig.SPEECHMATICS_FREE_MINUTES_MONTHLY,
                priority=2,
                max_file_size_mb=100,
                timeout_seconds=900
            )
            
        # 3. IBM Watson - الأولوية الثالثة (500 دقيقة شهرياً)
        if BotConfig.IBM_WATSON_API_KEY and BotConfig.IBM_WATSON_URL:
            services[SpeechService.IBM_WATSON] = ServiceConfig(
                name="IBM Watson",
                api_key=BotConfig.IBM_WATSON_API_KEY,
                api_url=BotConfig.IBM_WATSON_URL,
                free_limit_monthly=BotConfig.IBM_WATSON_FREE_MINUTES_MONTHLY,
                priority=3,
                max_file_size_mb=100,
                timeout_seconds=600
            )
            
        # 4. Microsoft Azure - الأولوية الرابعة (300 دقيقة شهرياً)
        if BotConfig.AZURE_SPEECH_KEY and BotConfig.AZURE_SPEECH_REGION:
            services[SpeechService.AZURE_SPEECH] = ServiceConfig(
                name="Microsoft Azure",
                api_key=BotConfig.AZURE_SPEECH_KEY,
                api_url=BotConfig.AZURE_SPEECH_ENDPOINT,
                free_limit_monthly=BotConfig.AZURE_FREE_MINUTES_MONTHLY,
                priority=4,
                max_file_size_mb=25,
                timeout_seconds=300
            )
            
        # 5. Google Cloud - الأولوية الخامسة (60 دقيقة شهرياً)
        if BotConfig.GOOGLE_CLOUD_SPEECH_KEY:
            services[SpeechService.GOOGLE_CLOUD] = ServiceConfig(
                name="Google Cloud Speech",
                api_key=BotConfig.GOOGLE_CLOUD_SPEECH_KEY,
                api_url="https://speech.googleapis.com/v1/speech:recognize",
                free_limit_monthly=BotConfig.GOOGLE_CLOUD_FREE_MINUTES_MONTHLY,
                priority=5,
                max_file_size_mb=10,
                timeout_seconds=300
            )
            
        # 6. Wit.ai - الأولوية السادسة (مجاني بلا حدود)
        if BotConfig.WITAI_ACCESS_TOKEN:
            services[SpeechService.WITAI] = ServiceConfig(
                name="Wit.ai",
                api_key=BotConfig.WITAI_ACCESS_TOKEN,
                api_url=BotConfig.WITAI_API_URL,
                free_limit_monthly=999999,  # بلا حدود
                priority=6,
                max_file_size_mb=20,
                timeout_seconds=120
            )
            
        # 7. Whisper - البديل الاحتياطي الأخير
        services[SpeechService.WHISPER] = ServiceConfig(
            name="Whisper (Fallback)",
            api_key=BotConfig.WHISPER_API_KEY,
            api_url=BotConfig.WHISPER_API_URL,
            free_limit_monthly=999999,  # بلا حدود (محلي)
            priority=99,  # أقل أولوية
            max_file_size_mb=25,
            timeout_seconds=300
        )
        
        logger.info(f"🎤 تم تهيئة {len(services)} خدمة لتحويل النص إلى صوت")
        return services
        
    def _load_usage_stats(self) -> Dict[str, Dict]:
        """تحميل إحصائيات الاستخدام"""
        try:
            stats_file = "cache/speech_to_text_usage.json"
            if os.path.exists(stats_file):
                with open(stats_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            logger.warning(f"⚠️ خطأ في تحميل إحصائيات الاستخدام: {e}")
        
        return {}
        
    def _save_usage_stats(self):
        """حفظ إحصائيات الاستخدام"""
        try:
            os.makedirs("cache", exist_ok=True)
            stats_file = "cache/speech_to_text_usage.json"
            with open(stats_file, 'w', encoding='utf-8') as f:
                json.dump(self.usage_stats, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.warning(f"⚠️ خطأ في حفظ إحصائيات الاستخدام: {e}")
            
    def _get_monthly_usage(self, service: SpeechService) -> int:
        """الحصول على الاستخدام الشهري لخدمة معينة (بالدقائق)"""
        current_month = datetime.now().strftime("%Y-%m")
        service_name = service.value
        
        if service_name not in self.usage_stats:
            self.usage_stats[service_name] = {}
            
        return self.usage_stats[service_name].get(current_month, 0)
        
    def _update_usage(self, service: SpeechService, duration_minutes: float):
        """تحديث إحصائيات الاستخدام"""
        current_month = datetime.now().strftime("%Y-%m")
        service_name = service.value
        
        if service_name not in self.usage_stats:
            self.usage_stats[service_name] = {}
            
        current_usage = self.usage_stats[service_name].get(current_month, 0)
        self.usage_stats[service_name][current_month] = current_usage + duration_minutes
        
        self._save_usage_stats()
        
    def _get_available_services(self, duration_minutes: float) -> List[Tuple[SpeechService, ServiceConfig]]:
        """الحصول على الخدمات المتاحة مرتبة حسب الأولوية"""
        available = []
        
        for service, config in self.services.items():
            if not config.enabled:
                continue
                
            # فحص الحد الشهري
            monthly_usage = self._get_monthly_usage(service)
            if monthly_usage + duration_minutes > config.free_limit_monthly:
                logger.debug(f"⚠️ {config.name}: تجاوز الحد الشهري ({monthly_usage}/{config.free_limit_monthly} دقيقة)")
                continue
                
            available.append((service, config))
            
        # ترتيب حسب الأولوية
        available.sort(key=lambda x: x[1].priority)
        
        return available
        
    def _generate_cache_key(self, audio_data: bytes, language: str = "auto") -> str:
        """إنشاء مفتاح تخزين مؤقت للصوت"""
        audio_hash = hashlib.md5(audio_data).hexdigest()
        return f"{audio_hash}_{language}"
        
    async def transcribe_audio(self, audio_data: bytes, video_id: str = "", 
                             video_title: str = "", language: str = "auto",
                             duration_seconds: float = 0.0) -> TranscriptionResult:
        """تحويل الصوت إلى نص باستخدام أفضل خدمة متاحة"""
        
        start_time = time.time()
        duration_minutes = max(1.0, duration_seconds / 60.0)  # حد أدنى دقيقة واحدة
        
        logger.info(f"🎤 بدء تحويل الصوت إلى نص - المدة: {duration_minutes:.1f} دقيقة")

        # فحص التخزين المؤقت الذكي أولاً
        cached_result = speech_cache_manager.get_transcription(audio_data, language)
        if cached_result:
            logger.info(f"📋 تم العثور على نتيجة محفوظة في التخزين المؤقت الذكي")
            # تحويل النتيجة لتنسيق TranscriptionResult
            return TranscriptionResult(
                success=cached_result['success'],
                text=cached_result['text'],
                confidence=cached_result['confidence'],
                language=cached_result['language'],
                service_used=cached_result['service_used'],
                processing_time=cached_result['processing_time'],
                word_count=cached_result['word_count'],
                duration_seconds=cached_result['duration_seconds']
            )

        # فحص التخزين المؤقت القديم (للتوافق)
        cache_key = self._generate_cache_key(audio_data, language)
        if cache_key in self.cache:
            cached_result = self.cache[cache_key]
            logger.info(f"📋 استخدام النتيجة المحفوظة مؤقتاً (النظام القديم)")
            return cached_result
            
        # الحصول على الخدمات المتاحة
        available_services = self._get_available_services(duration_minutes)
        
        if not available_services:
            logger.warning("⚠️ لا توجد خدمات متاحة، استخدام Whisper كبديل")
            return await self._transcribe_with_whisper(audio_data, video_id, video_title, language)
            
        # محاولة كل خدمة حسب الأولوية
        for service, config in available_services:
            try:
                logger.info(f"🔄 محاولة {config.name}...")
                
                result = await self._transcribe_with_service(
                    service, config, audio_data, language, duration_seconds
                )
                
                if result.success:
                    # تحديث الاستخدام
                    self._update_usage(service, duration_minutes)

                    # حفظ في التخزين المؤقت الذكي
                    speech_cache_manager.store_transcription(
                        audio_data, result.text, result.confidence,
                        result.language, result.service_used, duration_seconds
                    )

                    # حفظ في التخزين المؤقت القديم (للتوافق)
                    self.cache[cache_key] = result

                    processing_time = time.time() - start_time
                    result.processing_time = processing_time

                    logger.info(f"✅ نجح {config.name} - {len(result.text)} حرف في {processing_time:.1f}ث")
                    return result
                else:
                    logger.warning(f"❌ فشل {config.name}: {result.error_message}")
                    
            except Exception as e:
                logger.error(f"❌ خطأ في {config.name}: {e}")
                continue
                
        # إذا فشلت جميع الخدمات، استخدم Whisper
        logger.warning("⚠️ فشلت جميع الخدمات، استخدام Whisper كبديل أخير")
        return await self._transcribe_with_whisper(audio_data, video_id, video_title, language)

    async def _transcribe_with_service(self, service: SpeechService, config: ServiceConfig,
                                     audio_data: bytes, language: str, duration_seconds: float) -> TranscriptionResult:
        """تحويل الصوت باستخدام خدمة محددة"""

        if service == SpeechService.ASSEMBLYAI:
            return await self._transcribe_assemblyai(config, audio_data, language)
        elif service == SpeechService.SPEECHMATICS:
            return await self._transcribe_speechmatics(config, audio_data, language)
        elif service == SpeechService.IBM_WATSON:
            return await self._transcribe_ibm_watson(config, audio_data, language)
        elif service == SpeechService.AZURE_SPEECH:
            return await self._transcribe_azure_speech(config, audio_data, language)
        elif service == SpeechService.GOOGLE_CLOUD:
            return await self._transcribe_google_cloud(config, audio_data, language)
        elif service == SpeechService.WITAI:
            return await self._transcribe_witai(config, audio_data, language)
        else:
            return await self._transcribe_with_whisper(audio_data, "", "", language)

    async def _transcribe_assemblyai(self, config: ServiceConfig, audio_data: bytes, language: str) -> TranscriptionResult:
        """تحويل الصوت باستخدام AssemblyAI"""
        try:
            async with aiohttp.ClientSession() as session:
                # 1. رفع الملف الصوتي
                headers = {"authorization": config.api_key}

                async with session.post(
                    BotConfig.ASSEMBLYAI_UPLOAD_URL,
                    headers=headers,
                    data=audio_data,
                    timeout=aiohttp.ClientTimeout(total=config.timeout_seconds)
                ) as upload_response:

                    if upload_response.status != 200:
                        error_text = await upload_response.text()
                        return TranscriptionResult(
                            success=False, text="", confidence=0.0, language=language,
                            service_used="AssemblyAI", processing_time=0.0,
                            error_message=f"فشل رفع الملف: {error_text}"
                        )

                    upload_result = await upload_response.json()
                    audio_url = upload_result["upload_url"]

                # 2. إرسال طلب التحويل
                transcript_request = {
                    "audio_url": audio_url,
                    "language_detection": True if language == "auto" else False,
                    "language_code": None if language == "auto" else ("ar" if language == "arabic" else "en"),
                    "punctuate": True,
                    "format_text": True
                }

                async with session.post(
                    config.api_url,
                    headers=headers,
                    json=transcript_request,
                    timeout=aiohttp.ClientTimeout(total=config.timeout_seconds)
                ) as transcript_response:

                    if transcript_response.status != 200:
                        error_text = await transcript_response.text()
                        return TranscriptionResult(
                            success=False, text="", confidence=0.0, language=language,
                            service_used="AssemblyAI", processing_time=0.0,
                            error_message=f"فشل طلب التحويل: {error_text}"
                        )

                    transcript_result = await transcript_response.json()
                    transcript_id = transcript_result["id"]

                # 3. انتظار اكتمال التحويل
                max_wait_time = 600  # 10 دقائق
                wait_start = time.time()

                while time.time() - wait_start < max_wait_time:
                    async with session.get(
                        f"{config.api_url}/{transcript_id}",
                        headers=headers,
                        timeout=aiohttp.ClientTimeout(total=30)
                    ) as status_response:

                        if status_response.status == 200:
                            status_result = await status_response.json()

                            if status_result["status"] == "completed":
                                text = status_result.get("text", "")
                                confidence = status_result.get("confidence", 0.0)
                                detected_language = status_result.get("language_code", language)

                                return TranscriptionResult(
                                    success=True, text=text, confidence=confidence,
                                    language=detected_language, service_used="AssemblyAI",
                                    processing_time=0.0, word_count=len(text.split())
                                )
                            elif status_result["status"] == "error":
                                error_msg = status_result.get("error", "خطأ غير محدد")
                                return TranscriptionResult(
                                    success=False, text="", confidence=0.0, language=language,
                                    service_used="AssemblyAI", processing_time=0.0,
                                    error_message=f"خطأ في التحويل: {error_msg}"
                                )

                    await asyncio.sleep(5)  # انتظار 5 ثوان قبل المحاولة التالية

                return TranscriptionResult(
                    success=False, text="", confidence=0.0, language=language,
                    service_used="AssemblyAI", processing_time=0.0,
                    error_message="انتهت مهلة انتظار التحويل"
                )

        except Exception as e:
            return TranscriptionResult(
                success=False, text="", confidence=0.0, language=language,
                service_used="AssemblyAI", processing_time=0.0,
                error_message=f"خطأ في AssemblyAI: {str(e)}"
            )

    async def _transcribe_speechmatics(self, config: ServiceConfig, audio_data: bytes, language: str) -> TranscriptionResult:
        """تحويل الصوت باستخدام Speechmatics"""
        try:
            async with aiohttp.ClientSession() as session:
                # إعداد البيانات
                lang_code = "ar" if language == "arabic" else "en"
                if language == "auto":
                    lang_code = "en"  # افتراضي

                job_config = {
                    "type": "transcription",
                    "transcription_config": {
                        "language": lang_code,
                        "operating_point": "enhanced",
                        "enable_partials": False,
                        "max_delay": 10
                    }
                }

                # إنشاء FormData
                data = aiohttp.FormData()
                data.add_field('config', json.dumps(job_config), content_type='application/json')
                data.add_field('data_file', audio_data, filename='audio.mp3', content_type='audio/mpeg')

                headers = {"Authorization": f"Bearer {config.api_key}"}

                async with session.post(
                    config.api_url,
                    headers=headers,
                    data=data,
                    timeout=aiohttp.ClientTimeout(total=config.timeout_seconds)
                ) as response:

                    if response.status != 201:
                        error_text = await response.text()
                        return TranscriptionResult(
                            success=False, text="", confidence=0.0, language=language,
                            service_used="Speechmatics", processing_time=0.0,
                            error_message=f"فشل إرسال الطلب: {error_text}"
                        )

                    result = await response.json()
                    job_id = result["id"]

                # انتظار اكتمال المعالجة
                max_wait_time = 600  # 10 دقائق
                wait_start = time.time()

                while time.time() - wait_start < max_wait_time:
                    async with session.get(
                        f"{config.api_url}/{job_id}",
                        headers=headers,
                        timeout=aiohttp.ClientTimeout(total=30)
                    ) as status_response:

                        if status_response.status == 200:
                            status_result = await status_response.json()

                            if status_result["job"]["status"] == "done":
                                # الحصول على النتيجة
                                async with session.get(
                                    f"{config.api_url}/{job_id}/transcript",
                                    headers=headers,
                                    timeout=aiohttp.ClientTimeout(total=30)
                                ) as transcript_response:

                                    if transcript_response.status == 200:
                                        transcript_data = await transcript_response.json()

                                        # استخراج النص
                                        text_parts = []
                                        confidence_scores = []

                                        for result in transcript_data.get("results", []):
                                            for alternative in result.get("alternatives", []):
                                                text_parts.append(alternative.get("content", ""))
                                                confidence_scores.append(alternative.get("confidence", 0.0))

                                        full_text = " ".join(text_parts)
                                        avg_confidence = sum(confidence_scores) / len(confidence_scores) if confidence_scores else 0.0

                                        return TranscriptionResult(
                                            success=True, text=full_text, confidence=avg_confidence,
                                            language=lang_code, service_used="Speechmatics",
                                            processing_time=0.0, word_count=len(full_text.split())
                                        )
                            elif status_result["job"]["status"] == "rejected":
                                error_msg = status_result["job"].get("errors", ["خطأ غير محدد"])[0]
                                return TranscriptionResult(
                                    success=False, text="", confidence=0.0, language=language,
                                    service_used="Speechmatics", processing_time=0.0,
                                    error_message=f"تم رفض المهمة: {error_msg}"
                                )

                    await asyncio.sleep(5)

                return TranscriptionResult(
                    success=False, text="", confidence=0.0, language=language,
                    service_used="Speechmatics", processing_time=0.0,
                    error_message="انتهت مهلة انتظار التحويل"
                )

        except Exception as e:
            return TranscriptionResult(
                success=False, text="", confidence=0.0, language=language,
                service_used="Speechmatics", processing_time=0.0,
                error_message=f"خطأ في Speechmatics: {str(e)}"
            )

    async def _transcribe_ibm_watson(self, config: ServiceConfig, audio_data: bytes, language: str) -> TranscriptionResult:
        """تحويل الصوت باستخدام IBM Watson"""
        try:
            import base64

            # تحديد اللغة
            lang_model = "ar-MS_BroadbandModel" if language == "arabic" else "en-US_BroadbandModel"
            if language == "auto":
                lang_model = "en-US_BroadbandModel"  # افتراضي

            # ترميز الصوت
            audio_base64 = base64.b64encode(audio_data).decode('utf-8')

            request_data = {
                "audio": audio_base64,
                "content_type": "audio/mp3",
                "model": lang_model,
                "timestamps": True,
                "word_confidence": True,
                "smart_formatting": True
            }

            async with aiohttp.ClientSession() as session:
                # إعداد المصادقة
                auth = aiohttp.BasicAuth("apikey", config.api_key)
                headers = {"Content-Type": "application/json"}

                async with session.post(
                    f"{config.api_url}/v1/recognize",
                    headers=headers,
                    auth=auth,
                    json=request_data,
                    timeout=aiohttp.ClientTimeout(total=config.timeout_seconds)
                ) as response:

                    if response.status != 200:
                        error_text = await response.text()
                        return TranscriptionResult(
                            success=False, text="", confidence=0.0, language=language,
                            service_used="IBM Watson", processing_time=0.0,
                            error_message=f"فشل الطلب: {error_text}"
                        )

                    result = await response.json()

                    # استخراج النص والثقة
                    text_parts = []
                    confidence_scores = []

                    for result_item in result.get("results", []):
                        for alternative in result_item.get("alternatives", []):
                            text_parts.append(alternative.get("transcript", ""))
                            confidence_scores.append(alternative.get("confidence", 0.0))

                    full_text = " ".join(text_parts).strip()
                    avg_confidence = sum(confidence_scores) / len(confidence_scores) if confidence_scores else 0.0

                    return TranscriptionResult(
                        success=True, text=full_text, confidence=avg_confidence,
                        language=language, service_used="IBM Watson",
                        processing_time=0.0, word_count=len(full_text.split())
                    )

        except Exception as e:
            return TranscriptionResult(
                success=False, text="", confidence=0.0, language=language,
                service_used="IBM Watson", processing_time=0.0,
                error_message=f"خطأ في IBM Watson: {str(e)}"
            )

    async def _transcribe_azure_speech(self, config: ServiceConfig, audio_data: bytes, language: str) -> TranscriptionResult:
        """تحويل الصوت باستخدام Microsoft Azure Speech"""
        try:
            # تحديد اللغة
            lang_code = "ar-SA" if language == "arabic" else "en-US"
            if language == "auto":
                lang_code = "en-US"  # افتراضي

            headers = {
                "Ocp-Apim-Subscription-Key": config.api_key,
                "Content-Type": "audio/wav; codecs=audio/pcm; samplerate=16000",
                "Accept": "application/json"
            }

            # إضافة معاملات اللغة والتنسيق
            params = {
                "language": lang_code,
                "format": "detailed",
                "profanity": "masked"
            }

            async with aiohttp.ClientSession() as session:
                async with session.post(
                    config.api_url,
                    headers=headers,
                    params=params,
                    data=audio_data,
                    timeout=aiohttp.ClientTimeout(total=config.timeout_seconds)
                ) as response:

                    if response.status != 200:
                        error_text = await response.text()
                        return TranscriptionResult(
                            success=False, text="", confidence=0.0, language=language,
                            service_used="Azure Speech", processing_time=0.0,
                            error_message=f"فشل الطلب: {error_text}"
                        )

                    result = await response.json()

                    # استخراج النص
                    if result.get("RecognitionStatus") == "Success":
                        text = result.get("DisplayText", "")
                        confidence = result.get("Confidence", 0.0)

                        return TranscriptionResult(
                            success=True, text=text, confidence=confidence,
                            language=lang_code, service_used="Azure Speech",
                            processing_time=0.0, word_count=len(text.split())
                        )
                    else:
                        error_msg = result.get("RecognitionStatus", "خطأ غير محدد")
                        return TranscriptionResult(
                            success=False, text="", confidence=0.0, language=language,
                            service_used="Azure Speech", processing_time=0.0,
                            error_message=f"فشل التعرف: {error_msg}"
                        )

        except Exception as e:
            return TranscriptionResult(
                success=False, text="", confidence=0.0, language=language,
                service_used="Azure Speech", processing_time=0.0,
                error_message=f"خطأ في Azure Speech: {str(e)}"
            )

    async def _transcribe_google_cloud(self, config: ServiceConfig, audio_data: bytes, language: str) -> TranscriptionResult:
        """تحويل الصوت باستخدام Google Cloud Speech"""
        try:
            import base64

            # تحديد اللغة
            lang_code = "ar-SA" if language == "arabic" else "en-US"
            if language == "auto":
                lang_code = "en-US"  # افتراضي

            # ترميز الصوت
            audio_base64 = base64.b64encode(audio_data).decode('utf-8')

            request_data = {
                "config": {
                    "encoding": "MP3",
                    "sampleRateHertz": 16000,
                    "languageCode": lang_code,
                    "enableAutomaticPunctuation": True,
                    "enableWordTimeOffsets": True,
                    "enableWordConfidence": True
                },
                "audio": {
                    "content": audio_base64
                }
            }

            headers = {
                "Authorization": f"Bearer {config.api_key}",
                "Content-Type": "application/json"
            }

            async with aiohttp.ClientSession() as session:
                async with session.post(
                    config.api_url,
                    headers=headers,
                    json=request_data,
                    timeout=aiohttp.ClientTimeout(total=config.timeout_seconds)
                ) as response:

                    if response.status != 200:
                        error_text = await response.text()
                        return TranscriptionResult(
                            success=False, text="", confidence=0.0, language=language,
                            service_used="Google Cloud", processing_time=0.0,
                            error_message=f"فشل الطلب: {error_text}"
                        )

                    result = await response.json()

                    # استخراج النص
                    text_parts = []
                    confidence_scores = []

                    for result_item in result.get("results", []):
                        for alternative in result_item.get("alternatives", []):
                            text_parts.append(alternative.get("transcript", ""))
                            confidence_scores.append(alternative.get("confidence", 0.0))

                    full_text = " ".join(text_parts).strip()
                    avg_confidence = sum(confidence_scores) / len(confidence_scores) if confidence_scores else 0.0

                    return TranscriptionResult(
                        success=True, text=full_text, confidence=avg_confidence,
                        language=lang_code, service_used="Google Cloud",
                        processing_time=0.0, word_count=len(full_text.split())
                    )

        except Exception as e:
            return TranscriptionResult(
                success=False, text="", confidence=0.0, language=language,
                service_used="Google Cloud", processing_time=0.0,
                error_message=f"خطأ في Google Cloud: {str(e)}"
            )

    async def _transcribe_witai(self, config: ServiceConfig, audio_data: bytes, language: str) -> TranscriptionResult:
        """تحويل الصوت باستخدام Wit.ai"""
        try:
            headers = {
                "Authorization": f"Bearer {config.api_key}",
                "Content-Type": "audio/mpeg3"
            }

            # إضافة معاملات اللغة إذا كانت محددة
            params = {}
            if language == "arabic":
                params["v"] = "20220622"  # إصدار يدعم العربية بشكل أفضل

            async with aiohttp.ClientSession() as session:
                async with session.post(
                    config.api_url,
                    headers=headers,
                    params=params,
                    data=audio_data,
                    timeout=aiohttp.ClientTimeout(total=config.timeout_seconds)
                ) as response:

                    if response.status != 200:
                        error_text = await response.text()
                        return TranscriptionResult(
                            success=False, text="", confidence=0.0, language=language,
                            service_used="Wit.ai", processing_time=0.0,
                            error_message=f"فشل الطلب: {error_text}"
                        )

                    result = await response.json()

                    # استخراج النص
                    text = result.get("text", "")
                    confidence = result.get("confidence", 0.0)

                    if text:
                        return TranscriptionResult(
                            success=True, text=text, confidence=confidence,
                            language=language, service_used="Wit.ai",
                            processing_time=0.0, word_count=len(text.split())
                        )
                    else:
                        return TranscriptionResult(
                            success=False, text="", confidence=0.0, language=language,
                            service_used="Wit.ai", processing_time=0.0,
                            error_message="لم يتم التعرف على أي نص"
                        )

        except Exception as e:
            return TranscriptionResult(
                success=False, text="", confidence=0.0, language=language,
                service_used="Wit.ai", processing_time=0.0,
                error_message=f"خطأ في Wit.ai: {str(e)}"
            )

    async def _transcribe_with_whisper(self, audio_data: bytes, video_id: str,
                                     video_title: str, language: str) -> TranscriptionResult:
        """تحويل الصوت باستخدام Whisper كبديل احتياطي"""
        try:
            # استيراد محلل YouTube المتقدم للوصول لـ Whisper
            if not self.fallback_whisper:
                from modules.advanced_youtube_analyzer import AdvancedYouTubeAnalyzer
                self.fallback_whisper = AdvancedYouTubeAnalyzer()

            # استخدام نظام Whisper المحسن الموجود
            result = await self.fallback_whisper.extract_text_with_whisper(
                audio_data, video_id, video_title, language
            )

            if result and result.get('success'):
                text = result.get('text', '')
                confidence = result.get('confidence', 0.8)  # ثقة افتراضية لـ Whisper

                return TranscriptionResult(
                    success=True, text=text, confidence=confidence,
                    language=language, service_used="Whisper (Fallback)",
                    processing_time=0.0, word_count=len(text.split())
                )
            else:
                error_msg = result.get('error', 'فشل Whisper') if result else 'فشل في تهيئة Whisper'
                return TranscriptionResult(
                    success=False, text="", confidence=0.0, language=language,
                    service_used="Whisper (Fallback)", processing_time=0.0,
                    error_message=error_msg
                )

        except Exception as e:
            return TranscriptionResult(
                success=False, text="", confidence=0.0, language=language,
                service_used="Whisper (Fallback)", processing_time=0.0,
                error_message=f"خطأ في Whisper: {str(e)}"
            )

    def get_service_status(self) -> Dict[str, Dict]:
        """الحصول على حالة جميع الخدمات"""
        status = {}
        current_month = datetime.now().strftime("%Y-%m")

        for service, config in self.services.items():
            monthly_usage = self._get_monthly_usage(service)
            remaining_minutes = max(0, config.free_limit_monthly - monthly_usage)

            status[service.value] = {
                "name": config.name,
                "enabled": config.enabled,
                "priority": config.priority,
                "monthly_limit": config.free_limit_monthly,
                "monthly_usage": monthly_usage,
                "remaining_minutes": remaining_minutes,
                "usage_percentage": (monthly_usage / config.free_limit_monthly) * 100 if config.free_limit_monthly > 0 else 0,
                "api_key_configured": bool(config.api_key),
                "supports_arabic": config.supports_arabic,
                "supports_english": config.supports_english
            }

        return status

    def get_usage_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات الاستخدام التفصيلية"""
        current_month = datetime.now().strftime("%Y-%m")

        stats = {
            "current_month": current_month,
            "total_services": len(self.services),
            "enabled_services": sum(1 for config in self.services.values() if config.enabled),
            "services_with_api_keys": sum(1 for config in self.services.values() if config.api_key),
            "cache_size": len(self.cache),
            "monthly_usage_by_service": {},
            "total_monthly_usage": 0
        }

        for service, config in self.services.items():
            monthly_usage = self._get_monthly_usage(service)
            stats["monthly_usage_by_service"][service.value] = {
                "name": config.name,
                "usage_minutes": monthly_usage,
                "limit_minutes": config.free_limit_monthly,
                "remaining_minutes": max(0, config.free_limit_monthly - monthly_usage)
            }
            stats["total_monthly_usage"] += monthly_usage

        return stats

    def clear_cache(self):
        """مسح التخزين المؤقت"""
        self.cache.clear()
        logger.info("🗑️ تم مسح تخزين النتائج المؤقت")

    def reset_monthly_usage(self, service: SpeechService = None):
        """إعادة تعيين الاستخدام الشهري (للاختبار أو بداية الشهر)"""
        current_month = datetime.now().strftime("%Y-%m")

        if service:
            service_name = service.value
            if service_name in self.usage_stats:
                self.usage_stats[service_name][current_month] = 0
                logger.info(f"🔄 تم إعادة تعيين استخدام {self.services[service].name}")
        else:
            for service_name in self.usage_stats:
                if current_month in self.usage_stats[service_name]:
                    self.usage_stats[service_name][current_month] = 0
            logger.info("🔄 تم إعادة تعيين استخدام جميع الخدمات")

        self._save_usage_stats()


# إنشاء مثيل عام للاستخدام
speech_to_text_manager = SpeechToTextManager()
