# 🐳 دليل استخدام Docker - GitHub Uploader Tool

هذا الدليل يشرح كيفية استخدام أداة GitHub Uploader مع Docker.

## 📋 المتطلبات

- Docker Engine 20.10+
- Docker Compose 2.0+
- Git (للمشاريع المحلية)

## 🚀 البدء السريع

### 1. إعداد متغيرات البيئة
```bash
# نسخ ملف الإعدادات
cp .env.example .env

# تعديل الإعدادات (أضف GitHub Token)
nano .env
```

### 2. بناء الصورة
```bash
# بناء الصورة
docker build -t github-uploader .

# أو باستخدام docker-compose
docker-compose build
```

### 3. تشغيل الأداة
```bash
# تشغيل تفاعلي
docker run -it --rm \
  -v $(pwd)/projects:/app/projects:ro \
  -v $(pwd)/output:/app/output \
  --env-file .env \
  github-uploader

# أو باستخدام docker-compose
docker-compose run --rm github-uploader
```

## 📁 هيكل المجلدات

```
project-root/
├── projects/          # مجلد المشاريع المحلية
│   ├── my-bot/
│   ├── web-scraper/
│   └── api-server/
├── output/           # مجلد النتائج والسجلات
├── .env             # متغيرات البيئة
└── docker-compose.yml
```

## 🔧 الإعداد المفصل

### إعداد متغيرات البيئة
```bash
# في ملف .env
GITHUB_TOKEN=ghp_xxxxxxxxxxxxxxxxxxxx
GITHUB_USERNAME=yourusername
LANGUAGE=ar
AUTO_CREATE_GITIGNORE=true
AUTO_CREATE_README=true
```

### ربط المجلدات
```yaml
# في docker-compose.yml
volumes:
  - ./projects:/app/projects:ro    # مشاريعك المحلية (للقراءة فقط)
  - ./output:/app/output           # مجلد النتائج
  - ~/.gitconfig:/home/<USER>/.gitconfig:ro  # إعدادات Git
```

## 🎯 أمثلة الاستخدام

### رفع مشروع واحد
```bash
# وضع مشروعك في مجلد projects/
mkdir -p projects/my-awesome-bot
cp -r /path/to/your/bot/* projects/my-awesome-bot/

# تشغيل الأداة
docker-compose run --rm github-uploader
```

### رفع متعدد (Batch Upload)
```bash
# وضع عدة مشاريع
projects/
├── telegram-bot/
├── discord-bot/
└── web-scraper/

# تعديل .env
BATCH_UPLOAD=true

# تشغيل الأداة
docker-compose run --rm github-uploader
```

### استخدام مع CI/CD
```yaml
# في .github/workflows/upload.yml
name: Upload to GitHub
on:
  push:
    branches: [main]

jobs:
  upload:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Upload with GitHub Uploader
        run: |
          docker run --rm \
            -v ${{ github.workspace }}:/app/projects/current:ro \
            -e GITHUB_TOKEN=${{ secrets.GITHUB_TOKEN }} \
            -e AUTO_CREATE_README=true \
            github-uploader
```

## 🛠️ أوامر Docker المفيدة

### بناء وتشغيل
```bash
# بناء الصورة
docker build -t github-uploader .

# تشغيل تفاعلي
docker run -it --rm github-uploader

# تشغيل مع ربط المجلدات
docker run -it --rm \
  -v $(pwd)/projects:/app/projects:ro \
  -v $(pwd)/output:/app/output \
  github-uploader

# تشغيل مع متغيرات البيئة
docker run -it --rm \
  --env-file .env \
  github-uploader
```

### إدارة الحاويات
```bash
# عرض الحاويات النشطة
docker ps

# إيقاف جميع الحاويات
docker-compose down

# حذف الصور
docker rmi github-uploader

# تنظيف النظام
docker system prune -a
```

### السجلات والتشخيص
```bash
# عرض سجلات الحاوية
docker logs github-uploader-tool

# دخول الحاوية للتشخيص
docker exec -it github-uploader-tool bash

# فحص الصورة
docker inspect github-uploader
```

## 🔒 الأمان

### حماية GitHub Token
```bash
# استخدم Docker secrets (في production)
echo "your_token" | docker secret create github_token -

# أو متغيرات البيئة الآمنة
docker run --rm \
  -e GITHUB_TOKEN="$(cat ~/.github_token)" \
  github-uploader
```

### تشغيل كمستخدم غير root
```dockerfile
# في Dockerfile (مُطبق بالفعل)
USER uploader
```

### قراءة فقط للمشاريع
```yaml
# في docker-compose.yml
volumes:
  - ./projects:/app/projects:ro  # :ro = read-only
```

## 🚀 الإعداد للإنتاج

### استخدام Docker Swarm
```bash
# تهيئة Swarm
docker swarm init

# نشر الخدمة
docker stack deploy -c docker-compose.yml github-uploader
```

### استخدام Kubernetes
```yaml
# kubernetes-deployment.yml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: github-uploader
spec:
  replicas: 1
  selector:
    matchLabels:
      app: github-uploader
  template:
    metadata:
      labels:
        app: github-uploader
    spec:
      containers:
      - name: github-uploader
        image: github-uploader:latest
        env:
        - name: GITHUB_TOKEN
          valueFrom:
            secretKeyRef:
              name: github-secret
              key: token
        volumeMounts:
        - name: projects
          mountPath: /app/projects
          readOnly: true
      volumes:
      - name: projects
        persistentVolumeClaim:
          claimName: projects-pvc
```

## 🔧 استكشاف الأخطاء

### مشاكل شائعة

#### خطأ في الصلاحيات
```bash
# حل: تأكد من ملكية المجلدات
sudo chown -R $USER:$USER projects/ output/
```

#### خطأ في GitHub Token
```bash
# حل: تحقق من صحة الـ token
docker run --rm \
  -e GITHUB_TOKEN="your_token" \
  github-uploader \
  python -c "import requests; print(requests.get('https://api.github.com/user', headers={'Authorization': 'token your_token'}).status_code)"
```

#### مشاكل الشبكة
```bash
# حل: تحقق من الاتصال
docker run --rm github-uploader ping -c 3 api.github.com
```

### سجلات مفصلة
```bash
# تفعيل الوضع المفصل
docker run --rm \
  -e DEBUG_MODE=true \
  -e VERBOSE_OUTPUT=true \
  github-uploader
```

## 📊 المراقبة

### مراقبة الأداء
```bash
# استخدام الذاكرة والمعالج
docker stats github-uploader-tool

# مراقبة مستمرة
watch docker stats
```

### السجلات المنظمة
```bash
# إرسال السجلات لـ ELK Stack
docker run --rm \
  --log-driver=fluentd \
  --log-opt fluentd-address=localhost:24224 \
  github-uploader
```

## 🔄 التحديث

### تحديث الصورة
```bash
# سحب آخر إصدار
git pull origin main

# إعادة بناء الصورة
docker-compose build --no-cache

# إعادة تشغيل الخدمات
docker-compose up -d
```

### النسخ الاحتياطي
```bash
# نسخ احتياطي للبيانات
docker run --rm \
  -v github-uploader_uploader-data:/data \
  -v $(pwd)/backup:/backup \
  alpine tar czf /backup/uploader-backup-$(date +%Y%m%d).tar.gz -C /data .
```

## 📞 الدعم

إذا واجهت مشاكل مع Docker:

1. **تحقق من السجلات**: `docker logs container_name`
2. **فحص الإعدادات**: `docker inspect container_name`
3. **اختبار الاتصال**: `docker exec -it container_name ping api.github.com`
4. **إعادة بناء الصورة**: `docker-compose build --no-cache`

---

**استمتع باستخدام GitHub Uploader مع Docker! 🐳🚀**
