#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة الفهرسة الذكي - Intelligent Indexing Manager
يحل مشاكل الفهرسة في Google Search Console تلقائياً
"""

import asyncio
import aiohttp
import json
import os
import re
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from urllib.parse import urljoin, urlparse
import xml.etree.ElementTree as ET
from pathlib import Path

from modules.logger import logger
from modules.database import db


class IntelligentIndexingManager:
    """مدير الفهرسة الذكي - يحل مشاكل الفهرسة تلقائياً"""
    
    def __init__(self, site_url: str, webmaster_tools_credentials: str = None):
        self.site_url = site_url.rstrip('/')
        self.webmaster_credentials = webmaster_tools_credentials
        self.indexing_issues = {}
        self.fixed_issues = {}
        self.monitoring_active = False
        
        # أنواع مشاكل الفهرسة المدعومة
        self.supported_issues = {
            'canonical_tag_issue': 'صفحة بديلة تتضمن علامة أساسية مناسبة',
            'robots_blocked': 'تم الحظر باستخدام robots.txt',
            'not_found_404': 'لم يتم العثور عليها (404)',
            'redirect_error': 'صفحة تتضمن إعادة توجيه',
            'access_denied_4xx': 'تم حظر الوصول إلى الصفحة بسبب مشكلة أخرى من نوع 4xx'
        }
        
        # إحصائيات الفهرسة
        self.indexing_stats = {
            'total_pages': 0,
            'indexed_pages': 0,
            'issues_found': 0,
            'issues_fixed': 0,
            'last_check': None,
            'success_rate': 0.0
        }
        
    async def start_intelligent_monitoring(self):
        """بدء مراقبة الفهرسة الذكية"""
        logger.info("🔍 بدء نظام مراقبة الفهرسة الذكي...")
        
        self.monitoring_active = True
        
        while self.monitoring_active:
            try:
                # فحص شامل للموقع
                await self.comprehensive_site_analysis()
                
                # كشف مشاكل الفهرسة
                issues = await self.detect_indexing_issues()
                
                if issues:
                    logger.info(f"🚨 تم اكتشاف {len(issues)} مشكلة فهرسة")
                    
                    # حل المشاكل تلقائياً
                    for issue in issues:
                        await self.auto_fix_indexing_issue(issue)
                
                # تحديث الإحصائيات
                await self.update_indexing_stats()
                
                # انتظار قبل الفحص التالي (كل ساعة)
                await asyncio.sleep(3600)
                
            except Exception as e:
                logger.error(f"❌ خطأ في مراقبة الفهرسة: {e}")
                await asyncio.sleep(300)  # انتظار 5 دقائق عند الخطأ
    
    async def comprehensive_site_analysis(self):
        """تحليل شامل للموقع لاكتشاف مشاكل الفهرسة"""
        logger.info("🔍 بدء التحليل الشامل للموقع...")
        
        try:
            # 1. فحص ملف robots.txt
            await self.analyze_robots_txt()
            
            # 2. فحص خريطة الموقع
            await self.analyze_sitemap()
            
            # 3. فحص الصفحات الرئيسية
            await self.analyze_main_pages()
            
            # 4. فحص العلامات الأساسية (canonical tags)
            await self.analyze_canonical_tags()
            
            # 5. فحص إعادات التوجيه
            await self.analyze_redirects()
            
            logger.info("✅ تم إكمال التحليل الشامل للموقع")
            
        except Exception as e:
            logger.error(f"❌ خطأ في التحليل الشامل: {e}")
    
    async def analyze_robots_txt(self):
        """تحليل ملف robots.txt"""
        try:
            robots_url = f"{self.site_url}/robots.txt"
            
            async with aiohttp.ClientSession() as session:
                async with session.get(robots_url) as response:
                    if response.status == 200:
                        robots_content = await response.text()
                        
                        # فحص القواعد المشكوك فيها
                        problematic_rules = self._analyze_robots_rules(robots_content)
                        
                        if problematic_rules:
                            logger.warning(f"⚠️ قواعد مشكوك فيها في robots.txt: {problematic_rules}")
                            
                            # إضافة إلى قائمة المشاكل
                            self.indexing_issues['robots_blocked'] = {
                                'type': 'robots_blocked',
                                'description': 'قواعد حظر مشكوك فيها في robots.txt',
                                'affected_urls': problematic_rules,
                                'severity': 'high',
                                'detected_at': datetime.now().isoformat()
                            }
                    else:
                        logger.info("ℹ️ ملف robots.txt غير موجود - سيتم إنشاؤه")
                        await self._create_optimized_robots_txt()
                        
        except Exception as e:
            logger.error(f"❌ خطأ في تحليل robots.txt: {e}")
    
    def _analyze_robots_rules(self, robots_content: str) -> List[str]:
        """تحليل قواعد robots.txt للبحث عن مشاكل"""
        problematic_rules = []
        
        lines = robots_content.split('\n')
        current_user_agent = None
        
        for line in lines:
            line = line.strip()
            
            if line.startswith('User-agent:'):
                current_user_agent = line.split(':', 1)[1].strip()
            
            elif line.startswith('Disallow:'):
                disallow_path = line.split(':', 1)[1].strip()
                
                # فحص القواعد المشكوك فيها
                if self._is_problematic_disallow(disallow_path):
                    problematic_rules.append(f"{current_user_agent}: {disallow_path}")
        
        return problematic_rules
    
    def _is_problematic_disallow(self, path: str) -> bool:
        """فحص ما إذا كانت قاعدة Disallow مشكوك فيها"""
        # قواعد مشكوك فيها شائعة
        problematic_patterns = [
            '/',  # حظر كامل للموقع
            '/wp-admin',  # حظر لوحة تحكم WordPress (قد يكون مفيد)
            '/admin',  # حظر عام للإدارة
            '*.pdf',  # حظر ملفات PDF
            '*.doc',  # حظر ملفات Word
            '/search',  # حظر صفحات البحث
            '/category',  # حظر صفحات الفئات
            '/tag'  # حظر صفحات العلامات
        ]
        
        # فحص إذا كان المسار يطابق أي نمط مشكوك فيه
        for pattern in problematic_patterns:
            if path == pattern or (pattern.startswith('*') and path.endswith(pattern[1:])):
                return True
        
        return False
    
    async def _create_optimized_robots_txt(self):
        """إنشاء ملف robots.txt محسن"""
        try:
            optimized_robots = """# Robots.txt محسن للفهرسة
User-agent: *
Allow: /

# السماح لجميع محركات البحث
User-agent: Googlebot
Allow: /

User-agent: Bingbot
Allow: /

# حظر المجلدات غير المهمة فقط
Disallow: /wp-admin/
Disallow: /wp-includes/
Disallow: /wp-content/plugins/
Disallow: /wp-content/themes/
Disallow: /cgi-bin/
Disallow: /tmp/
Disallow: /cache/

# السماح بالملفات المهمة
Allow: /wp-content/uploads/
Allow: /wp-content/themes/*/css/
Allow: /wp-content/themes/*/js/
Allow: /wp-content/themes/*/images/

# خريطة الموقع
Sitemap: {}/sitemap.xml
Sitemap: {}/sitemap_index.xml

# معدل الزحف (اختياري)
Crawl-delay: 1
""".format(self.site_url, self.site_url)
            
            # حفظ الملف المحسن
            robots_path = "robots.txt"
            with open(robots_path, 'w', encoding='utf-8') as f:
                f.write(optimized_robots)
            
            logger.info("✅ تم إنشاء ملف robots.txt محسن")
            
        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء robots.txt: {e}")
    
    async def analyze_sitemap(self):
        """تحليل خريطة الموقع"""
        try:
            sitemap_urls = [
                f"{self.site_url}/sitemap.xml",
                f"{self.site_url}/sitemap_index.xml",
                f"{self.site_url}/wp-sitemap.xml"
            ]
            
            sitemap_found = False
            
            async with aiohttp.ClientSession() as session:
                for sitemap_url in sitemap_urls:
                    try:
                        async with session.get(sitemap_url) as response:
                            if response.status == 200:
                                sitemap_content = await response.text()
                                
                                # تحليل خريطة الموقع
                                urls_count = self._analyze_sitemap_content(sitemap_content)
                                logger.info(f"✅ تم العثور على خريطة الموقع: {sitemap_url} ({urls_count} رابط)")
                                
                                sitemap_found = True
                                break
                                
                    except Exception as e:
                        continue
            
            if not sitemap_found:
                logger.warning("⚠️ لم يتم العثور على خريطة الموقع - سيتم إنشاؤها")
                await self._create_optimized_sitemap()
                
        except Exception as e:
            logger.error(f"❌ خطأ في تحليل خريطة الموقع: {e}")
    
    def _analyze_sitemap_content(self, content: str) -> int:
        """تحليل محتوى خريطة الموقع"""
        try:
            root = ET.fromstring(content)
            
            # عد الروابط في خريطة الموقع
            urls_count = 0
            
            # فحص خريطة الموقع العادية
            for url in root.findall('.//{http://www.sitemaps.org/schemas/sitemap/0.9}url'):
                urls_count += 1
            
            # فحص فهرس خرائط الموقع
            for sitemap in root.findall('.//{http://www.sitemaps.org/schemas/sitemap/0.9}sitemap'):
                urls_count += 1
            
            return urls_count
            
        except Exception as e:
            logger.error(f"❌ خطأ في تحليل محتوى خريطة الموقع: {e}")
            return 0
    
    async def _create_optimized_sitemap(self):
        """إنشاء خريطة موقع محسنة"""
        try:
            # الحصول على جميع المقالات من قاعدة البيانات
            articles = db.get_all_articles()
            
            # إنشاء خريطة الموقع
            sitemap_content = self._generate_sitemap_xml(articles)
            
            # حفظ خريطة الموقع
            sitemap_path = "sitemap.xml"
            with open(sitemap_path, 'w', encoding='utf-8') as f:
                f.write(sitemap_content)
            
            logger.info("✅ تم إنشاء خريطة موقع محسنة")
            
        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء خريطة الموقع: {e}")
    
    def _generate_sitemap_xml(self, articles: List[Dict]) -> str:
        """توليد محتوى XML لخريطة الموقع"""
        sitemap_header = '''<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
        xmlns:news="http://www.google.com/schemas/sitemap-news/0.9"
        xmlns:image="http://www.google.com/schemas/sitemap-image/1.1">
'''
        
        sitemap_footer = '</urlset>'
        
        urls = []
        
        # إضافة الصفحة الرئيسية
        urls.append(f'''  <url>
    <loc>{self.site_url}/</loc>
    <lastmod>{datetime.now().strftime('%Y-%m-%d')}</lastmod>
    <changefreq>daily</changefreq>
    <priority>1.0</priority>
  </url>''')
        
        # إضافة المقالات
        for article in articles:
            if article.get('url'):
                article_url = urljoin(self.site_url, article['url'])
                
                # تاريخ آخر تعديل
                lastmod = article.get('published_date', datetime.now().strftime('%Y-%m-%d'))
                
                urls.append(f'''  <url>
    <loc>{article_url}</loc>
    <lastmod>{lastmod}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.8</priority>
  </url>''')
        
        return sitemap_header + '\n'.join(urls) + '\n' + sitemap_footer

    async def analyze_canonical_tags(self):
        """تحليل العلامات الأساسية (canonical tags)"""
        try:
            logger.info("🔍 فحص العلامات الأساسية...")

            # الحصول على قائمة الصفحات للفحص
            pages_to_check = await self._get_pages_for_analysis()

            canonical_issues = []

            async with aiohttp.ClientSession() as session:
                for page_url in pages_to_check:
                    try:
                        async with session.get(page_url) as response:
                            if response.status == 200:
                                html_content = await response.text()

                                # فحص العلامات الأساسية
                                canonical_issue = self._check_canonical_tags(page_url, html_content)
                                if canonical_issue:
                                    canonical_issues.append(canonical_issue)

                    except Exception as e:
                        logger.warning(f"⚠️ خطأ في فحص الصفحة {page_url}: {e}")
                        continue

            if canonical_issues:
                self.indexing_issues['canonical_tag_issue'] = {
                    'type': 'canonical_tag_issue',
                    'description': 'مشاكل في العلامات الأساسية',
                    'affected_pages': canonical_issues,
                    'severity': 'medium',
                    'detected_at': datetime.now().isoformat()
                }

                logger.warning(f"⚠️ تم اكتشاف {len(canonical_issues)} مشكلة في العلامات الأساسية")

        except Exception as e:
            logger.error(f"❌ خطأ في تحليل العلامات الأساسية: {e}")

    def _check_canonical_tags(self, page_url: str, html_content: str) -> Optional[Dict]:
        """فحص العلامات الأساسية في صفحة واحدة"""
        try:
            # البحث عن علامة canonical
            canonical_pattern = r'<link[^>]*rel=["\']canonical["\'][^>]*href=["\']([^"\']+)["\'][^>]*>'
            canonical_matches = re.findall(canonical_pattern, html_content, re.IGNORECASE)

            issues = []

            if not canonical_matches:
                # لا توجد علامة canonical
                issues.append("لا توجد علامة canonical")

            elif len(canonical_matches) > 1:
                # عدة علامات canonical
                issues.append(f"عدة علامات canonical: {canonical_matches}")

            else:
                canonical_url = canonical_matches[0]

                # فحص صحة الرابط الأساسي
                if not canonical_url.startswith(('http://', 'https://')):
                    issues.append(f"رابط canonical غير صحيح: {canonical_url}")

                # فحص إذا كان الرابط الأساسي يشير لنفس الصفحة
                if canonical_url != page_url:
                    # قد يكون هذا مقصود (صفحة بديلة)
                    issues.append(f"canonical يشير لصفحة أخرى: {canonical_url}")

            if issues:
                return {
                    'url': page_url,
                    'issues': issues,
                    'canonical_found': canonical_matches
                }

            return None

        except Exception as e:
            logger.error(f"❌ خطأ في فحص canonical للصفحة {page_url}: {e}")
            return None

    async def analyze_redirects(self):
        """تحليل إعادات التوجيه"""
        try:
            logger.info("🔍 فحص إعادات التوجيه...")

            pages_to_check = await self._get_pages_for_analysis()
            redirect_issues = []

            async with aiohttp.ClientSession(allow_redirects=False) as session:
                for page_url in pages_to_check:
                    try:
                        async with session.get(page_url) as response:
                            if response.status in [301, 302, 303, 307, 308]:
                                # إعادة توجيه موجودة
                                redirect_location = response.headers.get('Location', '')

                                redirect_issue = await self._analyze_redirect_chain(page_url, redirect_location, session)
                                if redirect_issue:
                                    redirect_issues.append(redirect_issue)

                    except Exception as e:
                        logger.warning(f"⚠️ خطأ في فحص إعادة التوجيه {page_url}: {e}")
                        continue

            if redirect_issues:
                self.indexing_issues['redirect_error'] = {
                    'type': 'redirect_error',
                    'description': 'مشاكل في إعادات التوجيه',
                    'affected_pages': redirect_issues,
                    'severity': 'medium',
                    'detected_at': datetime.now().isoformat()
                }

                logger.warning(f"⚠️ تم اكتشاف {len(redirect_issues)} مشكلة في إعادات التوجيه")

        except Exception as e:
            logger.error(f"❌ خطأ في تحليل إعادات التوجيه: {e}")

    async def _analyze_redirect_chain(self, original_url: str, redirect_url: str, session) -> Optional[Dict]:
        """تحليل سلسلة إعادات التوجيه"""
        try:
            redirect_chain = [original_url]
            current_url = redirect_url
            max_redirects = 10

            for i in range(max_redirects):
                if not current_url:
                    break

                redirect_chain.append(current_url)

                try:
                    async with session.get(current_url) as response:
                        if response.status in [301, 302, 303, 307, 308]:
                            current_url = response.headers.get('Location', '')
                        else:
                            # وصلنا للوجهة النهائية
                            break

                except Exception:
                    break

            # تحليل المشاكل في السلسلة
            issues = []

            if len(redirect_chain) > 4:
                issues.append(f"سلسلة إعادة توجيه طويلة: {len(redirect_chain)} خطوات")

            if len(redirect_chain) > max_redirects:
                issues.append("حلقة لا نهائية في إعادة التوجيه")

            # فحص إعادة التوجيه من HTTPS إلى HTTP
            for i in range(len(redirect_chain) - 1):
                if redirect_chain[i].startswith('https://') and redirect_chain[i+1].startswith('http://'):
                    issues.append("إعادة توجيه من HTTPS إلى HTTP (غير آمن)")

            if issues:
                return {
                    'original_url': original_url,
                    'redirect_chain': redirect_chain,
                    'issues': issues
                }

            return None

        except Exception as e:
            logger.error(f"❌ خطأ في تحليل سلسلة إعادة التوجيه: {e}")
            return None

    async def analyze_main_pages(self):
        """تحليل الصفحات الرئيسية للموقع"""
        try:
            logger.info("🔍 فحص الصفحات الرئيسية...")

            main_pages = [
                f"{self.site_url}/",
                f"{self.site_url}/about",
                f"{self.site_url}/contact",
                f"{self.site_url}/privacy",
                f"{self.site_url}/terms"
            ]

            # إضافة صفحات المقالات الحديثة
            recent_articles = db.get_recent_articles(limit=20)
            for article in recent_articles:
                if article.get('url'):
                    article_url = urljoin(self.site_url, article['url'])
                    main_pages.append(article_url)

            page_issues = []

            async with aiohttp.ClientSession() as session:
                for page_url in main_pages:
                    try:
                        async with session.get(page_url) as response:
                            if response.status == 404:
                                page_issues.append({
                                    'url': page_url,
                                    'status': 404,
                                    'issue': 'صفحة غير موجودة (404)'
                                })

                            elif response.status >= 400:
                                page_issues.append({
                                    'url': page_url,
                                    'status': response.status,
                                    'issue': f'خطأ في الوصول ({response.status})'
                                })

                    except Exception as e:
                        page_issues.append({
                            'url': page_url,
                            'status': 'error',
                            'issue': f'خطأ في الاتصال: {str(e)}'
                        })

            if page_issues:
                # تصنيف المشاكل
                not_found_issues = [p for p in page_issues if p['status'] == 404]
                access_denied_issues = [p for p in page_issues if isinstance(p['status'], int) and 400 <= p['status'] < 500]

                if not_found_issues:
                    self.indexing_issues['not_found_404'] = {
                        'type': 'not_found_404',
                        'description': 'صفحات غير موجودة (404)',
                        'affected_pages': not_found_issues,
                        'severity': 'high',
                        'detected_at': datetime.now().isoformat()
                    }

                if access_denied_issues:
                    self.indexing_issues['access_denied_4xx'] = {
                        'type': 'access_denied_4xx',
                        'description': 'مشاكل في الوصول للصفحات',
                        'affected_pages': access_denied_issues,
                        'severity': 'medium',
                        'detected_at': datetime.now().isoformat()
                    }

                logger.warning(f"⚠️ تم اكتشاف {len(page_issues)} مشكلة في الصفحات الرئيسية")

        except Exception as e:
            logger.error(f"❌ خطأ في تحليل الصفحات الرئيسية: {e}")

    async def _get_pages_for_analysis(self) -> List[str]:
        """الحصول على قائمة الصفحات للتحليل"""
        try:
            pages = []

            # الصفحة الرئيسية
            pages.append(f"{self.site_url}/")

            # المقالات الحديثة
            recent_articles = db.get_recent_articles(limit=50)
            for article in recent_articles:
                if article.get('url'):
                    article_url = urljoin(self.site_url, article['url'])
                    pages.append(article_url)

            return pages

        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على قائمة الصفحات: {e}")
            return []

    async def detect_indexing_issues(self) -> List[Dict]:
        """كشف مشاكل الفهرسة"""
        try:
            issues = []

            # تحويل مشاكل الفهرسة المكتشفة إلى قائمة
            for issue_type, issue_data in self.indexing_issues.items():
                if issue_data and issue_type not in self.fixed_issues:
                    issues.append(issue_data)

            return issues

        except Exception as e:
            logger.error(f"❌ خطأ في كشف مشاكل الفهرسة: {e}")
            return []

    async def auto_fix_indexing_issue(self, issue: Dict):
        """إصلاح مشكلة فهرسة تلقائياً"""
        try:
            issue_type = issue.get('type')
            logger.info(f"🔧 بدء إصلاح مشكلة: {self.supported_issues.get(issue_type, issue_type)}")

            if issue_type == 'canonical_tag_issue':
                await self._fix_canonical_issues(issue)

            elif issue_type == 'robots_blocked':
                await self._fix_robots_issues(issue)

            elif issue_type == 'not_found_404':
                await self._fix_404_issues(issue)

            elif issue_type == 'redirect_error':
                await self._fix_redirect_issues(issue)

            elif issue_type == 'access_denied_4xx':
                await self._fix_access_issues(issue)

            # تسجيل الإصلاح
            self.fixed_issues[issue_type] = {
                'fixed_at': datetime.now().isoformat(),
                'issue_data': issue
            }

            logger.info(f"✅ تم إصلاح مشكلة: {self.supported_issues.get(issue_type, issue_type)}")

        except Exception as e:
            logger.error(f"❌ خطأ في إصلاح مشكلة الفهرسة: {e}")

    async def _fix_canonical_issues(self, issue: Dict):
        """إصلاح مشاكل العلامات الأساسية"""
        try:
            affected_pages = issue.get('affected_pages', [])

            for page_issue in affected_pages:
                page_url = page_issue.get('url')
                issues = page_issue.get('issues', [])

                logger.info(f"🔧 إصلاح العلامات الأساسية للصفحة: {page_url}")

                # إنشاء علامة canonical صحيحة
                canonical_tag = f'<link rel="canonical" href="{page_url}" />'

                # إضافة العلامة إلى قاعدة البيانات للاستخدام في القوالب
                await self._update_page_canonical(page_url, canonical_tag)

                logger.info(f"✅ تم إصلاح العلامة الأساسية: {page_url}")

        except Exception as e:
            logger.error(f"❌ خطأ في إصلاح العلامات الأساسية: {e}")

    async def _fix_robots_issues(self, issue: Dict):
        """إصلاح مشاكل robots.txt"""
        try:
            logger.info("🔧 إصلاح مشاكل robots.txt...")

            # إنشاء ملف robots.txt محسن جديد
            await self._create_optimized_robots_txt()

            # إشعار محركات البحث بالتحديث
            await self._notify_search_engines_robots_update()

            logger.info("✅ تم إصلاح مشاكل robots.txt")

        except Exception as e:
            logger.error(f"❌ خطأ في إصلاح robots.txt: {e}")

    async def _fix_404_issues(self, issue: Dict):
        """إصلاح مشاكل الصفحات غير الموجودة"""
        try:
            affected_pages = issue.get('affected_pages', [])

            for page_issue in affected_pages:
                page_url = page_issue.get('url')

                logger.info(f"🔧 إصلاح صفحة 404: {page_url}")

                # محاولة إنشاء الصفحة أو إعادة توجيهها
                await self._handle_404_page(page_url)

            logger.info("✅ تم إصلاح مشاكل الصفحات غير الموجودة")

        except Exception as e:
            logger.error(f"❌ خطأ في إصلاح مشاكل 404: {e}")

    async def _fix_redirect_issues(self, issue: Dict):
        """إصلاح مشاكل إعادة التوجيه"""
        try:
            affected_pages = issue.get('affected_pages', [])

            for page_issue in affected_pages:
                original_url = page_issue.get('original_url')
                redirect_chain = page_issue.get('redirect_chain', [])
                issues = page_issue.get('issues', [])

                logger.info(f"🔧 إصلاح إعادة التوجيه: {original_url}")

                # تحسين سلسلة إعادة التوجيه
                await self._optimize_redirect_chain(original_url, redirect_chain)

            logger.info("✅ تم إصلاح مشاكل إعادة التوجيه")

        except Exception as e:
            logger.error(f"❌ خطأ في إصلاح مشاكل إعادة التوجيه: {e}")

    async def _fix_access_issues(self, issue: Dict):
        """إصلاح مشاكل الوصول للصفحات"""
        try:
            affected_pages = issue.get('affected_pages', [])

            for page_issue in affected_pages:
                page_url = page_issue.get('url')
                status = page_issue.get('status')

                logger.info(f"🔧 إصلاح مشكلة الوصول: {page_url} (كود: {status})")

                # محاولة إصلاح مشكلة الوصول
                await self._handle_access_issue(page_url, status)

            logger.info("✅ تم إصلاح مشاكل الوصول")

        except Exception as e:
            logger.error(f"❌ خطأ في إصلاح مشاكل الوصول: {e}")

    async def _update_page_canonical(self, page_url: str, canonical_tag: str):
        """تحديث العلامة الأساسية للصفحة"""
        try:
            # حفظ العلامة الأساسية في قاعدة البيانات
            # سيتم استخدامها في قوالب الصفحات

            # استخراج معرف المقال من الرابط
            article_id = self._extract_article_id_from_url(page_url)

            if article_id:
                # تحديث المقال في قاعدة البيانات
                db.update_article_meta(article_id, {
                    'canonical_tag': canonical_tag,
                    'canonical_url': page_url,
                    'seo_updated': datetime.now().isoformat()
                })

                logger.info(f"✅ تم تحديث العلامة الأساسية للمقال: {article_id}")

        except Exception as e:
            logger.error(f"❌ خطأ في تحديث العلامة الأساسية: {e}")

    def _extract_article_id_from_url(self, url: str) -> Optional[str]:
        """استخراج معرف المقال من الرابط"""
        try:
            # استخراج المعرف من نمط الرابط
            # مثال: /article/123 أو /post/article-title

            parsed_url = urlparse(url)
            path_parts = parsed_url.path.strip('/').split('/')

            # البحث عن معرف رقمي
            for part in path_parts:
                if part.isdigit():
                    return part

            # البحث عن عنوان المقال
            if len(path_parts) >= 2:
                return path_parts[-1]  # آخر جزء من المسار

            return None

        except Exception as e:
            logger.error(f"❌ خطأ في استخراج معرف المقال: {e}")
            return None

    async def _notify_search_engines_robots_update(self):
        """إشعار محركات البحث بتحديث robots.txt"""
        try:
            # إشعار Google
            google_ping_url = f"https://www.google.com/ping?sitemap={self.site_url}/robots.txt"

            # إشعار Bing
            bing_ping_url = f"https://www.bing.com/ping?sitemap={self.site_url}/robots.txt"

            async with aiohttp.ClientSession() as session:
                # إشعار Google
                try:
                    async with session.get(google_ping_url) as response:
                        if response.status == 200:
                            logger.info("✅ تم إشعار Google بتحديث robots.txt")
                except Exception as e:
                    logger.warning(f"⚠️ فشل إشعار Google: {e}")

                # إشعار Bing
                try:
                    async with session.get(bing_ping_url) as response:
                        if response.status == 200:
                            logger.info("✅ تم إشعار Bing بتحديث robots.txt")
                except Exception as e:
                    logger.warning(f"⚠️ فشل إشعار Bing: {e}")

        except Exception as e:
            logger.error(f"❌ خطأ في إشعار محركات البحث: {e}")

    async def _handle_404_page(self, page_url: str):
        """التعامل مع صفحة 404"""
        try:
            # استخراج معرف المقال
            article_id = self._extract_article_id_from_url(page_url)

            if article_id:
                # البحث عن المقال في قاعدة البيانات
                article = db.get_article_by_id(article_id)

                if article:
                    # المقال موجود في قاعدة البيانات لكن الصفحة غير متاحة
                    # إعادة نشر المقال
                    await self._republish_article(article)
                    logger.info(f"✅ تم إعادة نشر المقال: {article_id}")

                else:
                    # المقال غير موجود - إنشاء إعادة توجيه للصفحة الرئيسية
                    await self._create_redirect_to_homepage(page_url)
                    logger.info(f"✅ تم إنشاء إعادة توجيه للصفحة الرئيسية: {page_url}")

        except Exception as e:
            logger.error(f"❌ خطأ في التعامل مع صفحة 404: {e}")

    async def _handle_access_issue(self, page_url: str, status_code: int):
        """التعامل مع مشاكل الوصول للصفحات"""
        try:
            if status_code == 403:
                # مشكلة في الصلاحيات
                logger.info(f"🔧 إصلاح مشكلة الصلاحيات: {page_url}")
                await self._fix_permission_issue(page_url)

            elif status_code == 500:
                # خطأ في الخادم
                logger.info(f"🔧 إصلاح خطأ الخادم: {page_url}")
                await self._fix_server_error(page_url)

            else:
                # أخطاء أخرى
                logger.info(f"🔧 إصلاح خطأ عام: {page_url} (كود: {status_code})")
                await self._fix_general_access_issue(page_url, status_code)

        except Exception as e:
            logger.error(f"❌ خطأ في إصلاح مشكلة الوصول: {e}")

    async def _optimize_redirect_chain(self, original_url: str, redirect_chain: List[str]):
        """تحسين سلسلة إعادة التوجيه"""
        try:
            if len(redirect_chain) > 2:
                # سلسلة طويلة - إنشاء إعادة توجيه مباشرة
                final_url = redirect_chain[-1]

                # إنشاء إعادة توجيه مباشرة من الرابط الأصلي للوجهة النهائية
                await self._create_direct_redirect(original_url, final_url)

                logger.info(f"✅ تم تحسين سلسلة إعادة التوجيه: {original_url} -> {final_url}")

        except Exception as e:
            logger.error(f"❌ خطأ في تحسين سلسلة إعادة التوجيه: {e}")

    async def _republish_article(self, article: Dict):
        """إعادة نشر مقال"""
        try:
            # إعادة نشر المقال باستخدام نظام النشر
            from modules.publisher import PublisherManager

            publisher = PublisherManager()

            # نشر على بلوجر
            blogger_url = publisher.blogger.publish_article(article)

            if blogger_url:
                # تحديث رابط المقال في قاعدة البيانات
                db.update_article(article['id'], {'url': blogger_url})
                logger.info(f"✅ تم إعادة نشر المقال بنجاح: {blogger_url}")

        except Exception as e:
            logger.error(f"❌ خطأ في إعادة نشر المقال: {e}")

    async def _create_redirect_to_homepage(self, page_url: str):
        """إنشاء إعادة توجيه للصفحة الرئيسية"""
        try:
            # إنشاء قاعدة إعادة توجيه
            redirect_rule = {
                'from': page_url,
                'to': self.site_url,
                'type': '301',  # إعادة توجيه دائمة
                'created_at': datetime.now().isoformat()
            }

            # حفظ قاعدة إعادة التوجيه
            await self._save_redirect_rule(redirect_rule)

            logger.info(f"✅ تم إنشاء إعادة توجيه: {page_url} -> {self.site_url}")

        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء إعادة التوجيه: {e}")

    async def _create_direct_redirect(self, from_url: str, to_url: str):
        """إنشاء إعادة توجيه مباشرة"""
        try:
            redirect_rule = {
                'from': from_url,
                'to': to_url,
                'type': '301',
                'created_at': datetime.now().isoformat()
            }

            await self._save_redirect_rule(redirect_rule)

            logger.info(f"✅ تم إنشاء إعادة توجيه مباشرة: {from_url} -> {to_url}")

        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء إعادة التوجيه المباشرة: {e}")

    async def _save_redirect_rule(self, redirect_rule: Dict):
        """حفظ قاعدة إعادة التوجيه"""
        try:
            # حفظ في ملف .htaccess (للخوادم Apache)
            htaccess_path = ".htaccess"

            redirect_line = f"Redirect {redirect_rule['type']} {redirect_rule['from']} {redirect_rule['to']}\n"

            # قراءة الملف الحالي
            existing_content = ""
            if os.path.exists(htaccess_path):
                with open(htaccess_path, 'r', encoding='utf-8') as f:
                    existing_content = f.read()

            # إضافة القاعدة الجديدة إذا لم تكن موجودة
            if redirect_line.strip() not in existing_content:
                with open(htaccess_path, 'a', encoding='utf-8') as f:
                    f.write(redirect_line)

                logger.info(f"✅ تم حفظ قاعدة إعادة التوجيه في .htaccess")

            # حفظ في قاعدة البيانات أيضاً
            db.save_redirect_rule(redirect_rule)

        except Exception as e:
            logger.error(f"❌ خطأ في حفظ قاعدة إعادة التوجيه: {e}")

    async def _fix_permission_issue(self, page_url: str):
        """إصلاح مشكلة الصلاحيات"""
        try:
            # محاولة إصلاح مشكلة الصلاحيات
            # هذا يعتمد على نوع الخادم والإعدادات

            logger.info(f"🔧 محاولة إصلاح صلاحيات: {page_url}")

            # إنشاء ملف .htaccess لإصلاح الصلاحيات
            htaccess_content = """
# إصلاح مشاكل الصلاحيات
<Files "*">
    Order allow,deny
    Allow from all
</Files>

# السماح بالوصول للملفات المهمة
<FilesMatch "\.(html|htm|php|css|js|png|jpg|jpeg|gif|ico|svg)$">
    Order allow,deny
    Allow from all
</FilesMatch>
"""

            # حفظ إعدادات الصلاحيات
            with open(".htaccess", "a", encoding='utf-8') as f:
                f.write(htaccess_content)

            logger.info("✅ تم إصلاح مشكلة الصلاحيات")

        except Exception as e:
            logger.error(f"❌ خطأ في إصلاح مشكلة الصلاحيات: {e}")

    async def _fix_server_error(self, page_url: str):
        """إصلاح خطأ الخادم"""
        try:
            logger.info(f"🔧 محاولة إصلاح خطأ الخادم: {page_url}")

            # محاولة إعادة إنشاء الصفحة
            article_id = self._extract_article_id_from_url(page_url)

            if article_id:
                article = db.get_article_by_id(article_id)
                if article:
                    await self._republish_article(article)

            logger.info("✅ تم إصلاح خطأ الخادم")

        except Exception as e:
            logger.error(f"❌ خطأ في إصلاح خطأ الخادم: {e}")

    async def _fix_general_access_issue(self, page_url: str, status_code: int):
        """إصلاح مشكلة وصول عامة"""
        try:
            logger.info(f"🔧 إصلاح مشكلة وصول عامة: {page_url} (كود: {status_code})")

            # إنشاء إعادة توجيه للصفحة الرئيسية كحل مؤقت
            await self._create_redirect_to_homepage(page_url)

            logger.info("✅ تم إصلاح المشكلة بإعادة التوجيه")

        except Exception as e:
            logger.error(f"❌ خطأ في إصلاح المشكلة العامة: {e}")

    async def update_indexing_stats(self):
        """تحديث إحصائيات الفهرسة"""
        try:
            # حساب الإحصائيات
            total_issues = len(self.indexing_issues)
            fixed_issues = len(self.fixed_issues)

            # حساب معدل النجاح
            if total_issues > 0:
                success_rate = (fixed_issues / total_issues) * 100
            else:
                success_rate = 100.0

            # تحديث الإحصائيات
            self.indexing_stats.update({
                'issues_found': total_issues,
                'issues_fixed': fixed_issues,
                'success_rate': success_rate,
                'last_check': datetime.now().isoformat()
            })

            # حفظ الإحصائيات في قاعدة البيانات
            db.save_indexing_stats(self.indexing_stats)

            logger.info(f"📊 إحصائيات الفهرسة: {fixed_issues}/{total_issues} مشكلة تم حلها ({success_rate:.1f}%)")

        except Exception as e:
            logger.error(f"❌ خطأ في تحديث إحصائيات الفهرسة: {e}")

    def get_indexing_report(self) -> Dict:
        """الحصول على تقرير شامل للفهرسة"""
        try:
            report = {
                'site_url': self.site_url,
                'monitoring_status': 'نشط' if self.monitoring_active else 'متوقف',
                'stats': self.indexing_stats,
                'current_issues': self.indexing_issues,
                'fixed_issues': self.fixed_issues,
                'supported_issue_types': self.supported_issues,
                'generated_at': datetime.now().isoformat()
            }

            return report

        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء تقرير الفهرسة: {e}")
            return {}

    async def generate_seo_recommendations(self) -> List[Dict]:
        """توليد توصيات SEO لتحسين الفهرسة"""
        try:
            recommendations = []

            # توصيات عامة
            recommendations.extend([
                {
                    'type': 'general',
                    'title': 'تحسين خريطة الموقع',
                    'description': 'تأكد من وجود خريطة موقع محدثة وإرسالها لمحركات البحث',
                    'priority': 'high',
                    'action': 'إنشاء/تحديث sitemap.xml'
                },
                {
                    'type': 'general',
                    'title': 'تحسين ملف robots.txt',
                    'description': 'مراجعة وتحسين قواعد robots.txt لضمان فهرسة أفضل',
                    'priority': 'medium',
                    'action': 'مراجعة robots.txt'
                },
                {
                    'type': 'general',
                    'title': 'إضافة العلامات الأساسية',
                    'description': 'إضافة علامات canonical لجميع الصفحات لتجنب المحتوى المكرر',
                    'priority': 'high',
                    'action': 'إضافة canonical tags'
                }
            ])

            # توصيات مبنية على المشاكل المكتشفة
            for issue_type, issue_data in self.indexing_issues.items():
                if issue_type == 'canonical_tag_issue':
                    recommendations.append({
                        'type': 'issue_based',
                        'title': 'إصلاح العلامات الأساسية',
                        'description': f'تم اكتشاف {len(issue_data.get("affected_pages", []))} صفحة بمشاكل في العلامات الأساسية',
                        'priority': 'high',
                        'action': 'إصلاح canonical tags'
                    })

                elif issue_type == 'not_found_404':
                    recommendations.append({
                        'type': 'issue_based',
                        'title': 'إصلاح الصفحات المفقودة',
                        'description': f'تم اكتشاف {len(issue_data.get("affected_pages", []))} صفحة غير موجودة',
                        'priority': 'high',
                        'action': 'إصلاح أو إعادة توجيه الصفحات المفقودة'
                    })

                elif issue_type == 'redirect_error':
                    recommendations.append({
                        'type': 'issue_based',
                        'title': 'تحسين إعادات التوجيه',
                        'description': f'تم اكتشاف {len(issue_data.get("affected_pages", []))} مشكلة في إعادات التوجيه',
                        'priority': 'medium',
                        'action': 'تحسين سلاسل إعادة التوجيه'
                    })

            # ترتيب التوصيات حسب الأولوية
            priority_order = {'high': 3, 'medium': 2, 'low': 1}
            recommendations.sort(key=lambda x: priority_order.get(x['priority'], 0), reverse=True)

            return recommendations

        except Exception as e:
            logger.error(f"❌ خطأ في توليد توصيات SEO: {e}")
            return []

    async def stop_monitoring(self):
        """إيقاف مراقبة الفهرسة"""
        self.monitoring_active = False
        logger.info("⏹️ تم إيقاف مراقبة الفهرسة")

    def get_quick_status(self) -> Dict:
        """الحصول على حالة سريعة للفهرسة"""
        try:
            total_issues = len(self.indexing_issues)
            fixed_issues = len(self.fixed_issues)

            status = {
                'monitoring': self.monitoring_active,
                'total_issues': total_issues,
                'fixed_issues': fixed_issues,
                'pending_issues': total_issues - fixed_issues,
                'success_rate': self.indexing_stats.get('success_rate', 0),
                'last_check': self.indexing_stats.get('last_check'),
                'health_status': 'جيد' if total_issues == 0 else 'يحتاج انتباه' if total_issues <= 5 else 'مشاكل كثيرة'
            }

            return status

        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على الحالة السريعة: {e}")
            return {}


# إنشاء مثيل عام للاستخدام
intelligent_indexing_manager = None

def get_indexing_manager(site_url: str = None) -> IntelligentIndexingManager:
    """الحصول على مدير الفهرسة الذكي"""
    global intelligent_indexing_manager

    if intelligent_indexing_manager is None and site_url:
        intelligent_indexing_manager = IntelligentIndexingManager(site_url)

    return intelligent_indexing_manager
