#!/usr/bin/env python3
"""
تشغيل النظام الكامل مع جميع المميزات
"""

import asyncio
import logging
import sys
import subprocess
from pathlib import Path

# إعداد السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/complete_system.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

class CompleteSystemManager:
    """مدير النظام الكامل"""
    
    def __init__(self):
        self.telegram_manager_process = None
        self.smart_monitor_process = None
        self.running = False
    
    async def start_all_systems(self):
        """تشغيل جميع أنظمة البوت"""
        print("""
    🚀 Reddit Video Maker Bot - النظام الكامل
    ==========================================
    
    🤖 مدير التيليجرام للتحكم عن بُعد
    🔍 نظام المراقبة الذكي
    🔧 إصلاح تلقائي للمشاكل
    📱 إشعارات مفصلة عبر التيليجرام
    
    """)
        
        try:
            # بدء مدير التيليجرام
            await self._start_telegram_manager()
            
            # بدء نظام المراقبة الذكي
            await self._start_smart_monitor()
            
            logger.info("🎉 تم تشغيل النظام الكامل بنجاح!")
            
            # انتظار إشارة الإيقاف
            await self._wait_for_shutdown()
            
        except KeyboardInterrupt:
            logger.info("⏹️ تم إيقاف النظام بواسطة المستخدم")
        except Exception as e:
            logger.error(f"❌ خطأ في النظام: {str(e)}")
        finally:
            await self._cleanup()
    
    async def _start_telegram_manager(self):
        """تشغيل مدير التيليجرام"""
        try:
            logger.info("📱 بدء تشغيل مدير التيليجرام...")
            
            from automation.telegram_api_manager import TelegramAPIManager
            from utils import settings
            
            bot_token = settings.config["telegram"]["bot_token"]
            self.telegram_manager = TelegramAPIManager(bot_token)
            
            # تشغيل في مهمة منفصلة
            self.telegram_task = asyncio.create_task(
                self.telegram_manager.start_bot()
            )
            
            logger.info("✅ مدير التيليجرام يعمل")
            
        except Exception as e:
            logger.error(f"❌ فشل في تشغيل مدير التيليجرام: {str(e)}")
            raise
    
    async def _start_smart_monitor(self):
        """تشغيل نظام المراقبة الذكي"""
        try:
            logger.info("🔍 بدء تشغيل نظام المراقبة...")
            
            from automation.smart_monitor import smart_monitor
            
            # تشغيل في مهمة منفصلة
            self.monitor_task = asyncio.create_task(
                smart_monitor.start_monitoring()
            )
            
            logger.info("✅ نظام المراقبة يعمل")
            
        except Exception as e:
            logger.error(f"❌ فشل في تشغيل نظام المراقبة: {str(e)}")
            # لا نوقف النظام إذا فشلت المراقبة
    
    async def _wait_for_shutdown(self):
        """انتظار إشارة الإيقاف"""
        self.running = True
        
        print("""
    ✅ النظام يعمل بكامل طاقته!
    
    📱 استخدم بوت التيليجرام للتحكم:
    • /start - القائمة الرئيسية
    • /run_bot - تشغيل البوت
    • /stop_bot - إيقاف البوت
    • /status - حالة النظام
    
    اضغط Ctrl+C لإيقاف النظام الكامل
    """)
        
        try:
            # انتظار المهام
            await asyncio.gather(
                self.telegram_task,
                self.monitor_task if hasattr(self, 'monitor_task') else asyncio.sleep(0)
            )
        except asyncio.CancelledError:
            pass
    
    async def _cleanup(self):
        """تنظيف الموارد"""
        logger.info("🧹 تنظيف الموارد...")
        
        self.running = False
        
        # إلغاء المهام
        if hasattr(self, 'telegram_task'):
            self.telegram_task.cancel()
        
        if hasattr(self, 'monitor_task'):
            self.monitor_task.cancel()
        
        # إيقاف العمليات الفرعية
        if self.telegram_manager_process:
            self.telegram_manager_process.terminate()
        
        if self.smart_monitor_process:
            self.smart_monitor_process.terminate()
        
        logger.info("✅ تم تنظيف جميع الموارد")

async def main():
    """الدالة الرئيسية"""
    # التحقق من إصدار Python
    if sys.version_info.major != 3 or sys.version_info.minor < 10:
        print("❌ يتطلب Python 3.10 أو أحدث")
        sys.exit(1)
    
    # إنشاء المجلدات المطلوبة
    Path("logs").mkdir(exist_ok=True)
    
    # تشغيل النظام الكامل
    system_manager = CompleteSystemManager()
    await system_manager.start_all_systems()

def run_simple_telegram_manager():
    """تشغيل مدير التيليجرام فقط (للاختبار)"""
    print("""
    📱 تشغيل مدير التيليجرام فقط
    ================================
    
    للنظام الكامل استخدم: python start_complete_system.py
    """)
    
    subprocess.run([sys.executable, "start_telegram_manager.py"])

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Reddit Video Maker Bot - النظام الكامل")
    parser.add_argument("--telegram-only", action="store_true", 
                       help="تشغيل مدير التيليجرام فقط")
    parser.add_argument("--test-notifications", action="store_true",
                       help="اختبار نظام الإشعارات")
    
    args = parser.parse_args()
    
    if args.telegram_only:
        run_simple_telegram_manager()
    elif args.test_notifications:
        subprocess.run([sys.executable, "test_telegram_notifications.py"])
    else:
        asyncio.run(main())
