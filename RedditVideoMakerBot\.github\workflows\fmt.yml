# GitHub Action that uses <PERSON> to reformat the Python code in an incoming pull request.
# If all Python code in the pull request is compliant with Black then this Action does nothing.
# Othewrwise, Black is run and its changes are committed back to the incoming pull request.
# https://github.com/cclauss/autoblack

name: fmt
on:
  push:
    branches: ["develop"]
jobs:
  format:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Set up Python 3.10
        uses: actions/setup-python@v5
        with:
          python-version: 3.10.14
      - name: Install Black & isort
        run: pip install black isort
      - name: Run black check
        run: black --check . --line-length 101
      - name: Run isort check
        run: isort . --check-only --diff --profile black
      - name: If needed, commit changes to the pull request
        if: failure()
        run: |
          black . --line-length 101
          isort . --profile black
          git config --global user.name github-actions
          git config --global user.email 41898282+github-actions[bot]@users.noreply.github.com
          git remote set-url origin https://x-access-token:${{ secrets.GITHUB_TOKEN }}@github.com/$GITHUB_REPOSITORY
          git checkout $GITHUB_HEAD_REF
          git commit -am "fixup: Format Python code with Black"
          git push origin HEAD:develop

          
