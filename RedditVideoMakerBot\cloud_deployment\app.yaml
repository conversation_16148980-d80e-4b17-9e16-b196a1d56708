# App Engine configuration for Google Cloud Free Tier
# يستخدم F1 instance (مجاني 28 ساعة يومياً)

runtime: python39
service: default

# إعدادات Always Free Tier
instance_class: F1
automatic_scaling:
  min_instances: 0  # توفير الموارد عند عدم الاستخدام
  max_instances: 1  # مثيل واحد فقط لتوفير الموارد
  target_cpu_utilization: 0.6
  target_throughput_utilization: 0.6

# متغيرات البيئة
env_variables:
  # معرف المشروع (سيتم تحديثه تلقائياً)
  GOOGLE_CLOUD_PROJECT: reddit-video-maker-free
  
  # إعدادات Free Tier
  DEPLOYMENT_TYPE: free_tier
  MAX_MEMORY_MB: 256
  CLEANUP_ENABLED: true
  COMPRESS_ASSETS: true
  
  # إعدادات Reddit API (محدثة)
  REDDIT_CLIENT_ID: "Pp1KaZ4HWdkRwTMTK-C99Q"
  REDDIT_CLIENT_SECRET: "bTmy3_Mmytn-SdHS_4ltZQAdROpGJg"
  REDDIT_USERNAME: "Suitable_Reach782"
  REDDIT_PASSWORD: "ضع_كلمة_مرور_Reddit_هنا"
  
  # إعدادات Telegram Bot
  TELEGRAM_BOT_TOKEN: "**********************************************"
  
  # إعدادات Gemini AI
  GEMINI_API_KEY: "AIzaSyAnnYx15P89izzx_rJy9en4kaLVJ_Nuk40"
  
  # إعدادات التحسين
  VIDEO_QUALITY: medium
  MAX_VIDEO_DURATION: 45
  AUTO_CLEANUP_HOURS: 1
  STORAGE_LIMIT_MB: 4000  # 4 GB من أصل 5 GB

# معالجات الطلبات
handlers:
# ملفات ثابتة (CSS, JS, Images)
- url: /static
  static_dir: static
  expiration: 1h
  
# ملفات الخطوط
- url: /fonts
  static_dir: fonts
  expiration: 1d

# API endpoints
- url: /api/.*
  script: auto
  secure: always

# صفحات الويب
- url: /.*
  script: auto
  secure: always

# إعدادات الأمان
includes:
- include: security.yaml

# إعدادات الشبكة
network:
  forwarded_trust_ips:
    - 0.0.0.0/0

# إعدادات الموارد (محسنة للـ Free Tier)
resources:
  cpu: 1
  memory_gb: 0.6  # أقل من 1 GB لتوفير الموارد
  disk_size_gb: 10

# إعدادات الصحة
health_check:
  enable_health_check: true
  check_interval_sec: 30
  timeout_sec: 4
  unhealthy_threshold: 2
  healthy_threshold: 2

# إعدادات الأداء
performance:
  cpu_utilization: 0.6
  target_throughput_utilization: 0.6
  max_concurrent_requests: 10

# إعدادات التسجيل
logging:
  level: INFO
  
# إعدادات التنظيف التلقائي
cron:
- description: "تنظيف الملفات المؤقتة"
  url: /cleanup
  schedule: every 6 hours
  
- description: "مراقبة استخدام الموارد"
  url: /health
  schedule: every 1 hours
