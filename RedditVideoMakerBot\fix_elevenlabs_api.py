#!/usr/bin/env python3
"""
إصلاح سريع لمشاكل ElevenLabs API
يصلح جميع المشاكل المكتشفة في الاختبار
"""

import os
import sys
import logging
from pathlib import Path

# إعداد السجلات
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def fix_elevenlabs_imports():
    """إصلاح استيرادات ElevenLabs"""
    print("🔧 إصلاح استيرادات ElevenLabs...")
    
    elevenlabs_file = "TTS/elevenlabs.py"
    
    try:
        with open(elevenlabs_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # التأكد من الاستيرادات الصحيحة
        if "from elevenlabs import save" in content:
            content = content.replace("from elevenlabs import save", "# from elevenlabs import save")
            print("✅ تم تعطيل استيراد save القديم")
        
        # التأكد من استخدام API الصحيح
        if "client.generate(" in content:
            content = content.replace("client.generate(", "client.text_to_speech.convert(")
            content = content.replace("voice=voice", "voice_id=voice")
            content = content.replace("model=\"eleven_multilingual_v1\"", "model_id=\"eleven_multilingual_v1\"")
            print("✅ تم تحديث استدعاءات API")
        
        with open(elevenlabs_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم إصلاح ملف ElevenLabs")
        return True
        
    except Exception as e:
        print(f"❌ فشل في إصلاح ElevenLabs: {e}")
        return False

def fix_settings_config():
    """إصلاح مشكلة إعدادات التكوين"""
    print("\n⚙️ إصلاح إعدادات التكوين...")
    
    settings_file = "utils/settings.py"
    
    try:
        with open(settings_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # إصلاح تعريف config
        if "config = dict  # autocomplete" in content:
            content = content.replace("config = dict  # autocomplete", "config = {}  # autocomplete")
            print("✅ تم إصلاح تعريف config")
        
        with open(settings_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم إصلاح ملف الإعدادات")
        return True
        
    except Exception as e:
        print(f"❌ فشل في إصلاح الإعدادات: {e}")
        return False

def test_elevenlabs_simple():
    """اختبار بسيط لـ ElevenLabs"""
    print("\n🧪 اختبار بسيط لـ ElevenLabs...")
    
    try:
        from elevenlabs.client import ElevenLabs
        
        # اختبار المفاتيح
        api_keys = [
            "***************************************************",
            "***************************************************"
        ]
        
        working_key = None
        for api_key in api_keys:
            try:
                client = ElevenLabs(api_key=api_key)
                user = client.user.get()
                print(f"✅ مفتاح يعمل: {api_key[:10]}...")
                working_key = api_key
                break
            except Exception as e:
                print(f"❌ مفتاح لا يعمل: {api_key[:10]}... - {str(e)}")
                continue
        
        if working_key:
            print(f"✅ تم العثور على مفتاح يعمل")
            
            # اختبار إنشاء صوت بسيط
            try:
                test_text = "Hello, this is a test."
                voice_id = "ErXwobaYiN019PkySvjV"  # الصوت المجاني
                
                audio = client.text_to_speech.convert(
                    text=test_text,
                    voice_id=voice_id,
                    model_id="eleven_multilingual_v1"
                )
                
                # حفظ الملف
                test_file = "test_audio_simple.mp3"
                with open(test_file, 'wb') as f:
                    for chunk in audio:
                        f.write(chunk)
                
                if os.path.exists(test_file):
                    file_size = os.path.getsize(test_file)
                    print(f"✅ تم إنشاء ملف صوتي: {file_size} bytes")
                    os.remove(test_file)  # تنظيف
                    return True
                else:
                    print("❌ لم يتم إنشاء الملف")
                    return False
                    
            except Exception as e:
                print(f"❌ فشل في إنشاء الصوت: {str(e)}")
                return False
        else:
            print("❌ لا توجد مفاتيح تعمل")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار ElevenLabs: {e}")
        return False

def create_simple_elevenlabs_wrapper():
    """إنشاء wrapper بسيط لـ ElevenLabs"""
    print("\n🔧 إنشاء wrapper بسيط لـ ElevenLabs...")
    
    wrapper_content = '''#!/usr/bin/env python3
"""
Wrapper بسيط لـ ElevenLabs مع المفاتيح الجديدة
"""

import logging
from elevenlabs.client import ElevenLabs

logger = logging.getLogger(__name__)

class SimpleElevenLabs:
    def __init__(self):
        self.api_keys = [
            "***************************************************",
            "***************************************************"
        ]
        self.free_voice_id = "ErXwobaYiN019PkySvjV"
        self.working_client = None
        self._find_working_client()
    
    def _find_working_client(self):
        """العثور على عميل يعمل"""
        for api_key in self.api_keys:
            try:
                client = ElevenLabs(api_key=api_key)
                user = client.user.get()  # اختبار الاتصال
                self.working_client = client
                logger.info(f"✅ تم العثور على مفتاح يعمل: {api_key[:10]}...")
                return
            except Exception as e:
                logger.warning(f"⚠️ مفتاح لا يعمل: {api_key[:10]}... - {str(e)}")
                continue
        
        logger.error("❌ لا توجد مفاتيح تعمل")
    
    def generate_speech(self, text: str, output_file: str) -> bool:
        """إنشاء الكلام"""
        if not self.working_client:
            logger.error("❌ لا يوجد عميل يعمل")
            return False
        
        try:
            audio = self.working_client.text_to_speech.convert(
                text=text,
                voice_id=self.free_voice_id,
                model_id="eleven_multilingual_v1"
            )
            
            with open(output_file, 'wb') as f:
                for chunk in audio:
                    f.write(chunk)
            
            logger.info(f"✅ تم إنشاء الصوت: {output_file}")
            return True
            
        except Exception as e:
            logger.error(f"❌ فشل في إنشاء الصوت: {str(e)}")
            return False
'''
    
    try:
        with open("TTS/simple_elevenlabs.py", 'w', encoding='utf-8') as f:
            f.write(wrapper_content)
        
        print("✅ تم إنشاء wrapper بسيط")
        return True
        
    except Exception as e:
        print(f"❌ فشل في إنشاء wrapper: {e}")
        return False

def run_comprehensive_fix():
    """تشغيل إصلاح شامل"""
    print("🔧 بدء الإصلاح الشامل لـ ElevenLabs")
    print("=" * 50)
    
    fixes = [
        ("إصلاح استيرادات ElevenLabs", fix_elevenlabs_imports),
        ("إصلاح إعدادات التكوين", fix_settings_config),
        ("اختبار ElevenLabs بسيط", test_elevenlabs_simple),
        ("إنشاء wrapper بسيط", create_simple_elevenlabs_wrapper),
    ]
    
    success_count = 0
    
    for fix_name, fix_func in fixes:
        try:
            if fix_func():
                success_count += 1
        except Exception as e:
            print(f"❌ خطأ في {fix_name}: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 تم إكمال {success_count}/{len(fixes)} إصلاحات بنجاح")
    
    if success_count == len(fixes):
        print("🎉 تم إصلاح جميع المشاكل!")
        print("\nللاختبار:")
        print("python test_elevenlabs_priority.py")
        print("\nلتشغيل البوت:")
        print("python main.py")
    else:
        print("⚠️ بعض المشاكل لم يتم إصلاحها")

if __name__ == "__main__":
    try:
        run_comprehensive_fix()
        
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الإصلاح بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ حرج في الإصلاح: {e}")
        import traceback
        print(f"تفاصيل الخطأ:\n{traceback.format_exc()}")
