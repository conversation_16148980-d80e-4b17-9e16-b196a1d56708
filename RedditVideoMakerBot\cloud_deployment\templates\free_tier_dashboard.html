<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم Free Tier - مولد الفيديوهات</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .dashboard-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            margin: 20px;
            padding: 30px;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            transition: transform 0.3s;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-card.warning {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        
        .stat-card.success {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        
        .progress-custom {
            height: 10px;
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.3);
        }
        
        .progress-bar-custom {
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.9);
        }
        
        .btn-custom {
            border-radius: 25px;
            padding: 10px 30px;
            font-weight: bold;
            transition: all 0.3s;
        }
        
        .btn-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        
        .free-tier-badge {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: bold;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-left: 8px;
        }
        
        .status-healthy {
            background-color: #28a745;
            animation: pulse 2s infinite;
        }
        
        .status-warning {
            background-color: #ffc107;
        }
        
        .status-critical {
            background-color: #dc3545;
            animation: blink 1s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="dashboard-container">
            <!-- Header -->
            <div class="row mb-4">
                <div class="col-12 text-center">
                    <h1 class="display-4 mb-3">
                        <i class="bi bi-cloud"></i>
                        مولد الفيديوهات - Free Tier
                    </h1>
                    <span class="free-tier-badge">
                        <i class="bi bi-gift"></i>
                        Always Free - لا استهلاك للـ 300$
                    </span>
                </div>
            </div>
            
            <!-- Status Overview -->
            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="stat-card {% if stats.storage_usage_mb > 800 %}warning{% else %}success{% endif %}">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h5><i class="bi bi-hdd"></i> التخزين</h5>
                                <h3>{{ "%.1f"|format(stats.storage_usage_mb or 0) }} MB</h3>
                                <small>من أصل 1024 MB مجاني</small>
                            </div>
                            <div class="text-end">
                                <div class="progress-custom">
                                    <div class="progress-bar-custom" style="width: {{ ((stats.storage_usage_mb or 0) / 1024 * 100)|round }}%"></div>
                                </div>
                                <small>{{ ((stats.storage_usage_mb or 0) / 1024 * 100)|round }}%</small>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="stat-card success">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h5><i class="bi bi-lightning"></i> Cloud Functions</h5>
                                <h3>{{ stats.function_invocations_today or 0 }}</h3>
                                <small>من أصل 2M شهرياً</small>
                            </div>
                            <div class="text-end">
                                <div class="progress-custom">
                                    <div class="progress-bar-custom" style="width: {{ ((stats.function_invocations_today or 0) / 2000000 * 100)|round }}%"></div>
                                </div>
                                <small>{{ ((stats.function_invocations_today or 0) / 2000000 * 100)|round }}%</small>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="stat-card success">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h5><i class="bi bi-server"></i> App Engine</h5>
                                <h3>{{ stats.app_engine_hours_today or 0 }}h</h3>
                                <small>من أصل 28h يومياً</small>
                            </div>
                            <div class="text-end">
                                <div class="progress-custom">
                                    <div class="progress-bar-custom" style="width: {{ ((stats.app_engine_hours_today or 0) / 28 * 100)|round }}%"></div>
                                </div>
                                <small>{{ ((stats.app_engine_hours_today or 0) / 28 * 100)|round }}%</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- System Status -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">
                                <i class="bi bi-activity"></i>
                                حالة النظام
                                <span class="status-indicator status-{{ stats.status or 'healthy' }}"></span>
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>آخر تحديث:</strong> {{ stats.timestamp or 'غير متوفر' }}</p>
                                    <p><strong>الحالة:</strong> 
                                        {% if stats.status == 'healthy' %}
                                            <span class="badge bg-success">صحي</span>
                                        {% elif stats.status == 'warning' %}
                                            <span class="badge bg-warning">تحذير</span>
                                        {% else %}
                                            <span class="badge bg-danger">خطأ</span>
                                        {% endif %}
                                    </p>
                                </div>
                                <div class="col-md-6">
                                    {% if stats.warnings %}
                                        <div class="alert alert-warning">
                                            <strong>تحذيرات:</strong>
                                            <ul class="mb-0">
                                                {% for warning in stats.warnings %}
                                                    <li>{{ warning }}</li>
                                                {% endfor %}
                                            </ul>
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Actions -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">
                                <i class="bi bi-gear"></i>
                                أدوات التحكم
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <button class="btn btn-primary btn-custom w-100 mb-3" onclick="createVideo('morning')">
                                        <i class="bi bi-camera-video"></i>
                                        إنشاء فيديو صباحي
                                    </button>
                                    <button class="btn btn-info btn-custom w-100 mb-3" onclick="createVideo('evening')">
                                        <i class="bi bi-moon"></i>
                                        إنشاء فيديو مسائي
                                    </button>
                                </div>
                                <div class="col-md-6">
                                    <button class="btn btn-warning btn-custom w-100 mb-3" onclick="cleanupStorage()">
                                        <i class="bi bi-trash"></i>
                                        تنظيف التخزين
                                    </button>
                                    <button class="btn btn-success btn-custom w-100 mb-3" onclick="refreshStats()">
                                        <i class="bi bi-arrow-clockwise"></i>
                                        تحديث الإحصائيات
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Free Tier Info -->
            <div class="row">
                <div class="col-12">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">
                                <i class="bi bi-info-circle"></i>
                                معلومات Google Cloud Free Tier
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <h6><i class="bi bi-hdd"></i> Cloud Storage</h6>
                                    <ul class="list-unstyled">
                                        <li>✅ 5 GB تخزين مجاني</li>
                                        <li>✅ 100 GB نقل صادر</li>
                                        <li>✅ 5,000 عملية شهرياً</li>
                                    </ul>
                                </div>
                                <div class="col-md-4">
                                    <h6><i class="bi bi-lightning"></i> Cloud Functions</h6>
                                    <ul class="list-unstyled">
                                        <li>✅ 2M استدعاء شهرياً</li>
                                        <li>✅ 400K GB-ثانية</li>
                                        <li>✅ 200K CPU-ثانية</li>
                                    </ul>
                                </div>
                                <div class="col-md-4">
                                    <h6><i class="bi bi-server"></i> App Engine</h6>
                                    <ul class="list-unstyled">
                                        <li>✅ 28 ساعة F1 يومياً</li>
                                        <li>✅ 1 GB تخزين</li>
                                        <li>✅ 10 GB نقل صادر</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Alert Container -->
    <div id="alert-container" style="position: fixed; top: 20px; right: 20px; z-index: 1000;"></div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // تحديث تلقائي كل 5 دقائق
        setInterval(refreshStats, 300000);
        
        function createVideo(type) {
            showAlert('جاري إنشاء الفيديو...', 'info');
            
            fetch('/create-video', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({type: type})
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('تم إنشاء الفيديو بنجاح!', 'success');
                    refreshStats();
                } else {
                    showAlert('خطأ: ' + data.error, 'danger');
                }
            })
            .catch(error => {
                showAlert('خطأ في الاتصال: ' + error, 'danger');
            });
        }
        
        function cleanupStorage() {
            showAlert('جاري تنظيف التخزين...', 'info');
            
            fetch('/cleanup', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert(`تم تنظيف ${data.cleaned_files} ملف (${data.cleaned_size_mb.toFixed(1)} MB)`, 'success');
                    refreshStats();
                } else {
                    showAlert('خطأ في التنظيف: ' + data.error, 'danger');
                }
            })
            .catch(error => {
                showAlert('خطأ في الاتصال: ' + error, 'danger');
            });
        }
        
        function refreshStats() {
            location.reload();
        }
        
        function showAlert(message, type) {
            const alertContainer = document.getElementById('alert-container');
            const alertId = 'alert-' + Date.now();
            
            const alertHtml = `
                <div id="${alertId}" class="alert alert-${type} alert-dismissible fade show" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            
            alertContainer.innerHTML = alertHtml;
            
            // إزالة التنبيه بعد 5 ثوان
            setTimeout(() => {
                const alert = document.getElementById(alertId);
                if (alert) {
                    alert.remove();
                }
            }, 5000);
        }
    </script>
</body>
</html>
