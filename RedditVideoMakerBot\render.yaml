# ملف تكوين Render لبوت Reddit Video Maker
services:
  - type: web
    name: reddit-video-maker-bot
    env: python
    plan: free
    buildCommand: |
      pip install --upgrade pip
      pip install -r requirements.txt
      mkdir -p logs
      mkdir -p results
      mkdir -p assets/backgrounds/video
      mkdir -p assets/backgrounds/audio
      mkdir -p assets/temp
    startCommand: python start_render.py
    envVars:
      - key: RENDER
        value: true
      - key: PYTHONUNBUFFERED
        value: 1
      - key: PYTHONIOENCODING
        value: utf-8
      # متغيرات Reddit API (يجب تحديثها)
      - key: REDDIT_CLIENT_ID
        value: "YOUR_REDDIT_CLIENT_ID"
      - key: REDDIT_CLIENT_SECRET
        value: "YOUR_REDDIT_CLIENT_SECRET"
      - key: REDDIT_USERNAME
        value: "YOUR_REDDIT_USERNAME"
      - key: REDDIT_PASSWORD
        value: "YOUR_REDDIT_PASSWORD"
      # متغيرات Telegram Bot (يجب تحديثها)
      - key: TELEGRAM_BOT_TOKEN
        value: "YOUR_TELEGRAM_BOT_TOKEN"
      - key: TELEGRAM_CHAT_ID
        value: "YOUR_TELEGRAM_CHAT_ID"
      # متغيرات Gemini AI (يجب تحديثها)
      - key: GEMINI_API_KEY
        value: "YOUR_GEMINI_API_KEY"
      # إعدادات التحسين
      - key: VIDEO_QUALITY
        value: medium
      - key: MAX_VIDEO_DURATION
        value: 45
      - key: AUTO_CLEANUP_HOURS
        value: 1
    autoDeploy: true
    disk:
      name: reddit-bot-storage
      mountPath: /opt/render/project/src/results
      sizeGB: 1
