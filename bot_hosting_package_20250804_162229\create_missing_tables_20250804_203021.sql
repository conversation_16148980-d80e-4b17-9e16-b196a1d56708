-- SQL لإنشاء الجداول المفقودة
-- تاريخ الإنشاء: 2025-08-04 20:30:21.595143


                CREATE TABLE IF NOT EXISTS public.tasks (
                    id SERIAL PRIMARY KEY,
                    title TEXT NOT NULL,
                    description TEXT NOT NULL,
                    task_type TEXT NOT NULL CHECK (task_type IN ('join_channel', 'visit_link', 'share_content', 'custom')),
                    target_url TEXT,
                    channel_username TEXT,
                    reward_points INTEGER DEFAULT 0,
                    is_active BOOLEAN DEFAULT true,
                    created_by TEXT NOT NULL,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    expires_at TIMESTAMP WITH TIME ZONE,
                    max_completions INTEGER,
                    current_completions INTEGER DEFAULT 0
                );
                CREATE INDEX IF NOT EXISTS idx_tasks_active ON public.tasks(is_active);
                CREATE INDEX IF NOT EXISTS idx_tasks_type ON public.tasks(task_type);
            