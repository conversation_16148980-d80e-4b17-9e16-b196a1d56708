#!/usr/bin/env python3
"""
Wrapper بسيط لـ ElevenLabs مع المفاتيح الجديدة
"""

import logging
from elevenlabs.client import ElevenLabs

logger = logging.getLogger(__name__)

class SimpleElevenLabs:
    def __init__(self):
        self.api_keys = [
            "***************************************************",
            "***************************************************"
        ]
        self.free_voice_id = "ErXwobaYiN019PkySvjV"
        self.working_client = None
        self._find_working_client()
    
    def _find_working_client(self):
        """العثور على عميل يعمل"""
        for api_key in self.api_keys:
            try:
                client = ElevenLabs(api_key=api_key)
                user = client.user.get()  # اختبار الاتصال
                self.working_client = client
                logger.info(f"✅ تم العثور على مفتاح يعمل: {api_key[:10]}...")
                return
            except Exception as e:
                logger.warning(f"⚠️ مفتاح لا يعمل: {api_key[:10]}... - {str(e)}")
                continue
        
        logger.error("❌ لا توجد مفاتيح تعمل")
    
    def generate_speech(self, text: str, output_file: str) -> bool:
        """إنشاء الكلام"""
        if not self.working_client:
            logger.error("❌ لا يوجد عميل يعمل")
            return False
        
        try:
            audio = self.working_client.text_to_speech.convert(
                text=text,
                voice_id=self.free_voice_id,
                model_id="eleven_multilingual_v1"
            )
            
            with open(output_file, 'wb') as f:
                for chunk in audio:
                    f.write(chunk)
            
            logger.info(f"✅ تم إنشاء الصوت: {output_file}")
            return True
            
        except Exception as e:
            logger.error(f"❌ فشل في إنشاء الصوت: {str(e)}")
            return False
