#!/usr/bin/env python3
"""
أمثلة على استخدام أداة GitHub Uploader
"""

from github_uploader import GitHubUploader
import os

def example_upload_python_project():
    """مثال على رفع مشروع Python"""
    print("=" * 50)
    print("مثال: رفع مشروع Python")
    print("=" * 50)
    
    # إعداد الأداة
    token = "your_github_token_here"  # ضع الـ token الخاص بك هنا
    uploader = GitHubUploader(token)
    
    # رفع مشروع Python
    success = uploader.upload_project(
        project_path="./my_python_bot",  # مسار مشروع البوت
        repo_name="awesome-python-bot",
        description="بوت Python رائع يقوم بمهام متنوعة",
        private=False,
        commit_message="إضافة بوت Python الأولي"
    )
    
    if success:
        print("✅ تم رفع بوت Python بنجاح!")
    else:
        print("❌ فشل في رفع البوت")

def example_upload_discord_bot():
    """مثال على رفع بوت Discord"""
    print("=" * 50)
    print("مثال: رفع بوت Discord")
    print("=" * 50)
    
    token = "your_github_token_here"
    uploader = GitHubUploader(token)
    
    success = uploader.upload_project(
        project_path="./discord_bot",
        repo_name="my-discord-bot",
        description="بوت Discord للخادم مع ميزات متقدمة",
        private=True,  # مستودع خاص لحماية الـ tokens
        commit_message="إضافة بوت Discord مع الأوامر الأساسية"
    )
    
    if success:
        print("✅ تم رفع بوت Discord بنجاح!")

def example_upload_telegram_bot():
    """مثال على رفع بوت Telegram"""
    print("=" * 50)
    print("مثال: رفع بوت Telegram")
    print("=" * 50)
    
    token = "your_github_token_here"
    uploader = GitHubUploader(token)
    
    success = uploader.upload_project(
        project_path="./telegram_bot",
        repo_name="telegram-assistant-bot",
        description="بوت Telegram مساعد ذكي باللغة العربية",
        private=False,
        commit_message="إضافة بوت Telegram المساعد"
    )
    
    if success:
        print("✅ تم رفع بوت Telegram بنجاح!")

def example_upload_web_scraper():
    """مثال على رفع أداة Web Scraping"""
    print("=" * 50)
    print("مثال: رفع أداة Web Scraping")
    print("=" * 50)
    
    token = "your_github_token_here"
    uploader = GitHubUploader(token)
    
    success = uploader.upload_project(
        project_path="./web_scraper",
        repo_name="advanced-web-scraper",
        description="أداة متقدمة لاستخراج البيانات من المواقع",
        private=False,
        commit_message="إضافة أداة Web Scraping مع دعم JavaScript"
    )
    
    if success:
        print("✅ تم رفع أداة Web Scraping بنجاح!")

def example_upload_api_project():
    """مثال على رفع مشروع API"""
    print("=" * 50)
    print("مثال: رفع مشروع API")
    print("=" * 50)
    
    token = "your_github_token_here"
    uploader = GitHubUploader(token)
    
    success = uploader.upload_project(
        project_path="./flask_api",
        repo_name="rest-api-server",
        description="خادم API RESTful باستخدام Flask",
        private=False,
        commit_message="إضافة خادم API مع التوثيق"
    )
    
    if success:
        print("✅ تم رفع مشروع API بنجاح!")

def example_create_repository_only():
    """مثال على إنشاء مستودع فقط"""
    print("=" * 50)
    print("مثال: إنشاء مستودع فارغ")
    print("=" * 50)
    
    token = "your_github_token_here"
    uploader = GitHubUploader(token)
    
    repo_data = uploader.create_repository(
        name="new-project-template",
        description="قالب مشروع جديد للبدء السريع",
        private=False,
        auto_init=True
    )
    
    if repo_data:
        print(f"✅ تم إنشاء المستودع: {repo_data['html_url']}")

def example_batch_upload():
    """مثال على رفع عدة مشاريع"""
    print("=" * 50)
    print("مثال: رفع عدة مشاريع")
    print("=" * 50)
    
    token = "your_github_token_here"
    uploader = GitHubUploader(token)
    
    projects = [
        {
            "path": "./bot1",
            "name": "telegram-news-bot",
            "description": "بوت أخبار Telegram"
        },
        {
            "path": "./bot2", 
            "name": "discord-music-bot",
            "description": "بوت موسيقى Discord"
        },
        {
            "path": "./scraper1",
            "name": "ecommerce-scraper", 
            "description": "أداة استخراج بيانات المتاجر"
        }
    ]
    
    for project in projects:
        print(f"\n🚀 رفع مشروع: {project['name']}")
        success = uploader.upload_project(
            project_path=project["path"],
            repo_name=project["name"],
            description=project["description"],
            private=False
        )
        
        if success:
            print(f"✅ تم رفع {project['name']} بنجاح!")
        else:
            print(f"❌ فشل في رفع {project['name']}")

def create_sample_project():
    """إنشاء مشروع تجريبي للاختبار"""
    print("=" * 50)
    print("إنشاء مشروع تجريبي")
    print("=" * 50)
    
    # إنشاء مجلد المشروع
    project_dir = "./sample_bot"
    os.makedirs(project_dir, exist_ok=True)
    
    # إنشاء ملف البوت الرئيسي
    bot_code = '''#!/usr/bin/env python3
"""
بوت تجريبي بسيط
"""

import time
import random

class SampleBot:
    def __init__(self, name="TestBot"):
        self.name = name
        self.responses = [
            "مرحباً! كيف يمكنني مساعدتك؟",
            "أهلاً وسهلاً!",
            "يسعدني التحدث معك!",
            "كيف حالك اليوم؟"
        ]
    
    def greet(self):
        return f"مرحباً، أنا {self.name}!"
    
    def get_random_response(self):
        return random.choice(self.responses)
    
    def run(self):
        print(self.greet())
        while True:
            user_input = input("أنت: ")
            if user_input.lower() in ['خروج', 'exit', 'quit']:
                print("وداعاً!")
                break
            
            print(f"{self.name}: {self.get_random_response()}")
            time.sleep(1)

if __name__ == "__main__":
    bot = SampleBot("بوت تجريبي")
    bot.run()
'''
    
    # حفظ ملف البوت
    with open(f"{project_dir}/main.py", "w", encoding="utf-8") as f:
        f.write(bot_code)
    
    # إنشاء ملف requirements.txt
    requirements = "# متطلبات المشروع\n# لا توجد متطلبات خارجية لهذا المشروع التجريبي\n"
    with open(f"{project_dir}/requirements.txt", "w", encoding="utf-8") as f:
        f.write(requirements)
    
    print(f"✅ تم إنشاء مشروع تجريبي في: {project_dir}")
    print("يمكنك الآن رفعه باستخدام الأداة!")

def main():
    """تشغيل الأمثلة"""
    print("🚀 أمثلة استخدام أداة GitHub Uploader")
    print("=" * 50)
    
    while True:
        print("\nاختر مثال للتشغيل:")
        print("1. إنشاء مشروع تجريبي")
        print("2. رفع مشروع Python")
        print("3. رفع بوت Discord")
        print("4. رفع بوت Telegram")
        print("5. رفع أداة Web Scraping")
        print("6. رفع مشروع API")
        print("7. إنشاء مستودع فارغ")
        print("8. رفع عدة مشاريع")
        print("9. خروج")
        
        choice = input("\nاختيارك (1-9): ").strip()
        
        if choice == "1":
            create_sample_project()
        elif choice == "2":
            example_upload_python_project()
        elif choice == "3":
            example_upload_discord_bot()
        elif choice == "4":
            example_upload_telegram_bot()
        elif choice == "5":
            example_upload_web_scraper()
        elif choice == "6":
            example_upload_api_project()
        elif choice == "7":
            example_create_repository_only()
        elif choice == "8":
            example_batch_upload()
        elif choice == "9":
            print("👋 شكراً لاستخدام الأمثلة!")
            break
        else:
            print("❌ اختيار غير صحيح")

if __name__ == "__main__":
    main()
