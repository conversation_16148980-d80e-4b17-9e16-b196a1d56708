<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Txtify</title>
    <link rel="icon" href="/static/favicon.ico" type="image/x-icon">
    <link rel="stylesheet" href="/static/styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Exo:wght@400;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <script async defer src="https://buttons.github.io/buttons.js"></script>
</head>

<body class="dark-theme">
    <div class="navbar">
        <div class="navbar-content">
            <!-- <a href="/" class="logo">Txtify</a> -->
            <a href="/" class="logo">
                <img src="/static/txtify_logo.png" alt="Txtify Logo" class="logo-image">
                <span>Txtify</span>
            </a>            
            <div class="menu-toggle" id="menuToggle">
                <i class="fa fa-bars"></i>
            </div>
            <div class="nav-links" id="navLinks">
                <a href="/">Application</a>
                <a href="/faq">FAQ</a>
                <a href="/contact">Contact</a>
                <a class="github-button" href="https://github.com/lkmeta/txtify"
                    data-color-scheme="no-preference: light; light: light; dark: dark;" data-size="large"
                    aria-label="Star lkmeta/txtify on GitHub">Star</a>
            </div>
            <div class="theme-toggle-wrapper">
                <label class="theme-toggle">
                    <input type="checkbox" id="themeToggle">
                    <div class="toggle-body"></div>
                    <div class="celestial-body"></div>
                </label>
            </div>
        </div>
    </div>

    <div class="container">
        <h1>Contact <span style="color: var(--text-color-dark); font-weight: bold;">Txt</span>ify</h1>
        <p>Please contact us directly at <a href="mailto:<EMAIL>"
                style="color: var(--primary-color); font-weight: bold;"><EMAIL></a> or through this form.
        </p>
        <form id="contact-form" action="/submit-contact" method="POST">
            <div class="input-group-contact">
                <label for="name">Full Name</label>
                <input type="text" id="name" name="name" placeholder="First & last name" required>
            </div>
            <div class="input-group-contact">
                <label for="email">Email</label>
                <input type="email" id="email" name="email" placeholder="For example: <EMAIL>" required>
            </div>
            <div class="input-group-contact">
                <label for="message">How can we help you?</label>
                <textarea id="message" name="message" placeholder="Describe your issue or question here..." rows="8"
                    required></textarea>
            </div>
            <button type="submit" class="submit-button">Submit <i class="fa-solid fa-paper-plane"></i></button>


            <br><br>

            <h2>Stay Updated</h2>
            <a>If you liked Txtify, consider giving it a star on GitHub to stay updated with the latest versions
                and features. Your support is appreciated!</a> <br>
            <a class="github-button" href="https://github.com/lkmeta/txtify"
                data-color-scheme="no-preference: light; light: light; dark: dark;" data-size="large"
                aria-label="Star lkmeta/txtify on GitHub">Star</a>

        </form>
    </div>

    <div class="footer">
        &copy; 2025 Txt<span style="color: var(--primary-color);">ify</span>. Created with <i
            class="fa-solid fa-heart heart"></i> by <a href="https://lkmeta.com" target="_blank">lkmeta</a>.
    </div>

    <!-- Alert Box Structure -->
    <div class="alert-overlay" id="alertOverlay">
        <div class="alert-box">
            <h2 id="alertTitle">Alert</h2>
            <p id="alertMessage">This is an alert message.</p>
            <button onclick="closeAlert()">OK</button>
        </div>
    </div>


    <script src="/static/contact_scripts.js"></script>
</body>

</html>