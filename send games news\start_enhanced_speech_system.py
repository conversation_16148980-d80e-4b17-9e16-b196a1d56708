# تشغيل سريع لنظام تحويل النص إلى صوت المحسن
import asyncio
import threading
import time
import webbrowser
from datetime import datetime

from modules.enhanced_speech_integration import enhanced_speech_integration
from modules.logger import logger

def print_banner():
    """طباعة شعار النظام"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║        🎤 نظام تحويل النص إلى صوت المحسن 🎤                ║
    ║                                                              ║
    ║  ✨ خدمات متعددة عالية الجودة                              ║
    ║  🧠 نظام أولوية ذكي                                        ║
    ║  💾 تخزين مؤقت متقدم                                       ║
    ║  🌐 واجهة ويب تفاعلية                                      ║
    ║  🔄 تبديل تلقائي للخدمات                                   ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def print_services_info():
    """طباعة معلومات الخدمات المتاحة"""
    print("\n🎯 الخدمات المتاحة:")
    print("=" * 50)
    
    services_info = [
        ("AssemblyAI", "416 ساعة مجانية", "⭐⭐⭐⭐⭐"),
        ("Speechmatics", "480 دقيقة شهرياً", "⭐⭐⭐⭐"),
        ("IBM Watson", "500 دقيقة شهرياً", "⭐⭐⭐⭐"),
        ("Microsoft Azure", "300 دقيقة شهرياً", "⭐⭐⭐"),
        ("Google Cloud", "60 دقيقة شهرياً", "⭐⭐⭐"),
        ("Wit.ai", "مجاني بلا حدود", "⭐⭐"),
        ("Whisper", "بديل احتياطي", "⭐⭐⭐")
    ]
    
    for service, limit, rating in services_info:
        print(f"  {rating} {service:<15} - {limit}")

def print_quick_start_guide():
    """طباعة دليل البدء السريع"""
    print("\n📖 دليل البدء السريع:")
    print("=" * 50)
    print("1. 🔑 تأكد من إعداد مفاتيح APIs في ملف .env")
    print("2. 🌐 افتح واجهة الويب: http://localhost:5001")
    print("3. 🧪 شغل الاختبار: python test_enhanced_speech_system.py")
    print("4. 📚 راجع الدليل: ENHANCED_SPEECH_TO_TEXT_GUIDE.md")

def start_web_dashboard():
    """تشغيل واجهة الويب"""
    try:
        from speech_to_text_dashboard import run_dashboard
        
        print("\n🌐 بدء تشغيل واجهة الويب...")
        
        # تشغيل في thread منفصل
        dashboard_thread = threading.Thread(
            target=run_dashboard,
            kwargs={'host': '0.0.0.0', 'port': 5001, 'debug': False},
            daemon=True
        )
        dashboard_thread.start()
        
        # انتظار قليل ثم فتح المتصفح
        time.sleep(3)
        try:
            webbrowser.open('http://localhost:5001')
            print("✅ تم فتح واجهة الويب في المتصفح")
        except:
            print("⚠️ لم يتمكن من فتح المتصفح تلقائياً")
            print("🔗 افتح المتصفح يدوياً على: http://localhost:5001")
            
        return True
        
    except Exception as e:
        print(f"❌ فشل في تشغيل واجهة الويب: {e}")
        return False

async def test_system_quickly():
    """اختبار سريع للنظام"""
    print("\n🧪 اختبار سريع للنظام...")
    print("-" * 30)
    
    try:
        # فحص تهيئة النظام
        status = enhanced_speech_integration.get_system_status()
        
        total_services = status['speech_services']['total_services']
        active_services = status['speech_services']['active_services']
        cache_size = status['usage_stats']['cache_size']
        
        print(f"✅ إجمالي الخدمات: {total_services}")
        print(f"✅ الخدمات النشطة: {active_services}")
        print(f"✅ حجم التخزين المؤقت: {cache_size}")
        
        # عرض حالة كل خدمة
        print("\n📊 حالة الخدمات:")
        for service_name, service_data in status['speech_services']['services'].items():
            status_icon = "✅" if service_data['status'] == 'active' else "❌"
            print(f"  {status_icon} {service_data['name']}: {service_data['status']}")
            
        return True
        
    except Exception as e:
        print(f"❌ فشل في الاختبار السريع: {e}")
        return False

async def run_demo():
    """تشغيل عرض توضيحي"""
    print("\n🎬 عرض توضيحي للنظام...")
    print("-" * 30)
    
    try:
        # إنشاء بيانات صوتية تجريبية
        test_audio = b"demo_audio_data" * 100
        
        print("🔄 محاولة تحويل ملف صوتي تجريبي...")
        
        result = await enhanced_speech_integration.transcribe_audio_enhanced(
            audio_data=test_audio,
            video_id="demo_video",
            video_title="Demo Audio File",
            language="en",
            duration_seconds=10.0
        )
        
        if result['success']:
            print(f"✅ نجح التحويل!")
            print(f"📝 النص: {result['text'][:100]}...")
            print(f"🎯 الخدمة: {result['service_used']}")
            print(f"⏱️ الوقت: {result['processing_time']:.1f}ث")
        else:
            print(f"❌ فشل التحويل: {result.get('error', 'خطأ غير محدد')}")
            
    except Exception as e:
        print(f"❌ خطأ في العرض التوضيحي: {e}")

def print_usage_tips():
    """طباعة نصائح الاستخدام"""
    print("\n💡 نصائح للاستخدام الأمثل:")
    print("=" * 50)
    print("• استخدم النظام المحسن بدلاً من Whisper مباشرة")
    print("• راقب استخدام الخدمات عبر واجهة الويب")
    print("• حدد اللغة بدقة لتحسين النتائج")
    print("• استفد من التخزين المؤقت لتوفير APIs")
    print("• شغل الاختبار الشامل دورياً")

def print_api_setup_reminder():
    """تذكير بإعداد مفاتيح APIs"""
    print("\n🔑 تذكير: إعداد مفاتيح APIs")
    print("=" * 50)
    print("تأكد من إضافة المفاتيح التالية إلى ملف .env:")
    print("• ASSEMBLYAI_API_KEY")
    print("• SPEECHMATICS_API_KEY") 
    print("• IBM_WATSON_API_KEY")
    print("• AZURE_SPEECH_KEY")
    print("• GOOGLE_CLOUD_SPEECH_KEY")
    print("• WITAI_ACCESS_TOKEN")
    print("\n📚 راجع الدليل للتفاصيل: ENHANCED_SPEECH_TO_TEXT_GUIDE.md")

async def main():
    """الدالة الرئيسية"""
    print_banner()
    print_services_info()
    print_quick_start_guide()
    print_api_setup_reminder()
    
    print(f"\n🕐 وقت البدء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # اختبار سريع للنظام
    system_ok = await test_system_quickly()
    
    if system_ok:
        print("\n✅ النظام جاهز للاستخدام!")
        
        # تشغيل واجهة الويب
        web_started = start_web_dashboard()
        
        if web_started:
            print("\n🎯 الخيارات المتاحة:")
            print("1. 🌐 واجهة الويب: http://localhost:5001")
            print("2. 🧪 اختبار شامل: python test_enhanced_speech_system.py")
            print("3. 🎬 عرض توضيحي: سيتم تشغيله الآن...")
            
            # تشغيل عرض توضيحي
            await run_demo()
            
            print_usage_tips()
            
            print("\n🎉 النظام يعمل بنجاح!")
            print("⏹️ اضغط Ctrl+C للإيقاف")
            
            try:
                # إبقاء النظام يعمل
                while True:
                    await asyncio.sleep(60)
                    print(f"💓 النظام يعمل - {datetime.now().strftime('%H:%M:%S')}")
                    
            except KeyboardInterrupt:
                print("\n🛑 تم إيقاف النظام بواسطة المستخدم")
                
        else:
            print("❌ فشل في تشغيل واجهة الويب")
            
    else:
        print("❌ النظام غير جاهز - راجع الإعدادات")
        
    print("\n👋 شكراً لاستخدام نظام تحويل النص إلى صوت المحسن!")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف النظام")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل النظام: {e}")
        print("📚 راجع الدليل للمساعدة: ENHANCED_SPEECH_TO_TEXT_GUIDE.md")
