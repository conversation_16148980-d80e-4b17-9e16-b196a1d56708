#!/usr/bin/env python3
"""
إنشاء ملف screenshot_downloader.py
"""

content = '''import json
import re
from pathlib import Path
from typing import Dict, Final

from utils import settings
from utils.console import print_step, print_substep
from utils.imagenarator import imagemaker

__all__ = ["get_screenshots_of_reddit_posts"]


def get_screenshots_of_reddit_posts(reddit_object: dict, screenshot_num: int):
    """Downloads screenshots of reddit posts as seen on the web. Downloads to assets/temp/png

    Args:
        reddit_object (Dict): Reddit object received from reddit/subreddit.py
        screenshot_num (int): Number of screenshots to download
    """
    
    print_step("Downloading screenshots of reddit posts...")
    
    # استخدام النظام البديل مباشرة لتجنب مشاكل Playwright
    print_substep("استخدام النظام البديل لالتقاط لقطات الشاشة...", style="yellow")
    
    try:
        from screenshot_fallback import create_fallback_screenshots
        if create_fallback_screenshots(reddit_object, screenshot_num):
            print_substep("تم إنشاء لقطات الشاشة بنجاح", style="green")
        else:
            raise Exception("فشل في إنشاء لقطات الشاشة")
    except Exception as e:
        print_substep(f"خطأ في إنشاء لقطات الشاشة: {str(e)}", style="red")
        
        # إرسال إشعار خطأ
        try:
            from automation.telegram_bot import send_error
            send_error(f"فشل في التقاط لقطات الشاشة: {str(e)}")
        except:
            pass
        
        raise e
'''

# إنشاء الملف
with open('video_creation/screenshot_downloader.py', 'w', encoding='utf-8') as f:
    f.write(content)

print("✅ تم إنشاء ملف screenshot_downloader.py بنجاح")
