# 📱 دليل التحكم عبر التيليجرام

## 🚀 البدء السريع

### 1. تشغيل مدير التيليجرام:
```bash
python start_telegram_manager.py
```

### 2. في التيليجرام، أرسل:
```
/start
```

### 3. ستحصل على إشعارات تلقائية:
- ⚠️ **مشاكل الإعدادات** مع حلول مفصلة
- 🔧 **الإصلاحات التلقائية** عند حدوثها
- 📊 **تحديثات حالة البوت** أثناء العمل
- ✅ **إشعارات النجاح** عند اكتمال الفيديوهات

## 🎛️ التحكم في البوت

### ▶️ تشغيل البوت:
- اضغط زر "▶️ تشغيل البوت" 
- أو أرسل `/run_bot`

### ⏹️ إيقاف البوت:
- اضغط زر "⏹️ إيقاف البوت"
- أو أرسل `/stop_bot`

### 📊 فحص الحالة:
- اضغط زر "🤖 البوت: حالة"
- أو أرسل `/bot_status`

## 🎤 إعداد ElevenLabs مجاني

### الخطوات:
1. اضغط "🎤 إعداد ElevenLabs مجاني"
2. اختر صوت من القائمة:

#### 🔥 الأصوات الأكثر شعبية:
- **Rachel**: `21m00Tcm4TlvDq8ikWAM` (أنثوي واضح)
- **Drew**: `29vD33N1CtxCmqQRPOHJ` (ذكوري شاب)
- **Paul**: `5Q0t7uMcjvnagumLfvZi` (ذكوري عميق)
- **Sarah**: `EXAVITQu4vr4xnSDxMaL` (أنثوي ناعم)

3. أرسل الـ Voice ID
4. ✅ تم! البوت سيستخدم ElevenLabs مجاناً

## 🔑 إدارة APIs

### إضافة مفتاح ElevenLabs مدفوع:
1. اضغط "🔑 إدارة APIs"
2. اضغط "🎤 ElevenLabs"
3. أرسل مفتاح API

### إضافة TikTok Session:
1. اضغط "🔑 إدارة APIs"
2. اضغط "🎵 TikTok"
3. أرسل Session ID

## 🧪 اختبار النظام

### اختبار جميع APIs:
- اضغط "🧪 اختبار APIs"
- أو أرسل `/test_key`

### فحص حالة النظام:
- اضغط "📊 حالة النظام"
- أو أرسل `/status`

## 📈 مراقبة الأداء

### معلومات APIs:
- أرسل `/apis`
- سيعرض جميع المفاتيح المتاحة

### الإشعارات التلقائية:
- 🚨 **مشاكل الإعدادات**: إشعار فوري مع الحلول
- 🔧 **الإصلاحات التلقائية**: تأكيد عند حل المشاكل
- 📊 **حالة البوت**: تحديثات أثناء تشغيل البوت
- ✅ **اكتمال الفيديو**: إشعار عند انتهاء الإنشاء
- ❌ **الأخطاء الحرجة**: تفاصيل المشاكل والحلول

### أمثلة الإشعارات:
```
⚠️ TikTok TTS يحتاج Session ID
🔧 الحل السريع: /start → 🔑 إدارة APIs → 🎵 TikTok
💡 أو استخدم ElevenLabs مجاني!
```

```
🎉 تم الإصلاح التلقائي بنجاح!
✅ تم التبديل إلى ElevenLabs المجاني
🎤 الصوت الحالي: Rachel (عالي الجودة)
🚀 البوت جاهز للعمل الآن!
```

## 🔧 استكشاف الأخطاء

### إذا لم يعمل البوت:
1. تحقق من `/bot_status`
2. جرب إيقاف وإعادة تشغيل: `/stop_bot` ثم `/run_bot`
3. فحص السجلات في مجلد `logs/`

### إذا فشل الصوت:
1. جرب `/test_key` لاختبار APIs
2. تحقق من إعدادات ElevenLabs المجاني
3. جرب تغيير Voice ID

### إذا فشلت لقطات الشاشة:
1. البوت سيحاول الإصلاح تلقائياً
2. ستحصل على إشعار إذا احتاج تدخل يدوي

## 💡 نصائح مفيدة

### للحصول على أفضل أداء:
- استخدم ElevenLabs المجاني للبداية
- أضف مفاتيح متعددة كنسخ احتياطية
- راقب الإشعارات للتدخل عند الحاجة

### للاستخدام المتقدم:
- شغل `start_smart_system.py` للمراقبة الكاملة
- استخدم مفاتيح APIs متعددة للموثوقية
- فعل النظام الذكي بـ `voice_choice = "auto"`

## 🆘 الحصول على المساعدة

### في حالة المشاكل:
1. أرسل `/status` للحصول على تقرير شامل
2. تحقق من ملف `logs/telegram_manager.log`
3. البوت سيرسل تقارير أخطاء تلقائية

### معلومات إضافية:
- راجع `SMART_SYSTEM_README.md` للتفاصيل الكاملة
- جميع العمليات مسجلة في مجلد `logs/`
- النظام يدعم الإصلاح التلقائي للمشاكل الشائعة

---

## 🎯 الخلاصة

مع مدير التيليجرام، يمكنك:
- ✅ تشغيل وإيقاف البوت عن بُعد
- ✅ استخدام ElevenLabs مجاناً بدون API key
- ✅ إدارة جميع مفاتيح APIs
- ✅ مراقبة النظام والحصول على تقارير
- ✅ إصلاح المشاكل تلقائياً

🚀 **استمتع بالتحكم الكامل في البوت من هاتفك!**
