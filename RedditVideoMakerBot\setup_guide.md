# 🚀 دليل الإعداد السريع

## 📋 قائمة المراجعة

### ✅ **الخطوات المطلوبة:**

#### 1. **إعداد Reddit API**
- [ ] إنشاء تطبيق Reddit على: https://www.reddit.com/prefs/apps
- [ ] نسخ `client_id` و `client_secret`
- [ ] تحديث ملف `config.toml`

#### 2. **إعداد Google Service Account**
- [ ] إنشاء مشروع على: https://console.cloud.google.com/
- [ ] تفعيل YouTube Data API v3
- [ ] إنشاء Service Account
- [ ] تحميل ملف JSON وتسميته `service_account.json`

#### 3. **تثبيت المتطلبات**
- [ ] تشغيل: `pip install -r requirements.txt`
- [ ] تشغيل: `python -m playwright install`

#### 4. **اختبار النظام**
- [ ] تشغيل: `python run_automated_system.py`
- [ ] فتح: http://localhost:5000
- [ ] التحقق من وصول إشعارات Telegram

---

## 🔧 إعداد Reddit API

### 1. إنشاء التطبيق
1. اذهب إلى: https://www.reddit.com/prefs/apps
2. انقر "Create App" أو "Create Another App"
3. املأ المعلومات:
   - **Name**: `VideoMakerBot` (أو أي اسم)
   - **App type**: اختر **"script"**
   - **Description**: اختياري
   - **About URL**: اتركه فارغ
   - **Redirect URI**: `https://example.com`

### 2. نسخ المعلومات
بعد إنشاء التطبيق، ستحصل على:
- **Client ID**: الرقم تحت اسم التطبيق
- **Client Secret**: الرقم الطويل المخفي

### 3. تحديث config.toml
```toml
[reddit.creds]
client_id = "ضع_الـClient_ID_هنا"
client_secret = "ضع_الـClient_Secret_هنا"
username = "اسم_المستخدم_في_Reddit"
password = "كلمة_المرور_في_Reddit"
```

---

## 🎬 إعداد Google Service Account

### 1. إنشاء المشروع
1. اذهب إلى: https://console.cloud.google.com/
2. انقر "Select a project" → "New Project"
3. أدخل اسم المشروع: `reddit-video-maker`
4. انقر "Create"

### 2. تفعيل YouTube API
1. في القائمة الجانبية: "APIs & Services" → "Library"
2. ابحث عن: "YouTube Data API v3"
3. انقر عليه ثم "Enable"

### 3. إنشاء Service Account
1. اذهب إلى: "APIs & Services" → "Credentials"
2. انقر "Create Credentials" → "Service Account"
3. أدخل:
   - **Service account name**: `youtube-uploader`
   - **Service account ID**: سيتم ملؤه تلقائياً
4. انقر "Create and Continue"
5. في "Grant this service account access to project":
   - اختر Role: "Editor" أو "YouTube API Service Agent"
6. انقر "Continue" ثم "Done"

### 4. تحميل ملف المفتاح
1. في صفحة Credentials، انقر على Service Account الذي أنشأته
2. اذهب إلى تبويب "Keys"
3. انقر "Add Key" → "Create new key"
4. اختر "JSON" ثم "Create"
5. سيتم تحميل ملف JSON
6. **مهم**: أعد تسمية الملف إلى `service_account.json`
7. ضع الملف في المجلد الرئيسي للمشروع

### 5. ربط القناة (مهم!)
لكي يتمكن Service Account من الرفع على قناتك:

**الطريقة الأولى - إضافة كمدير:**
1. اذهب إلى YouTube Studio
2. Settings → Permissions
3. انقر "Invite" وأدخل email الـ Service Account
4. اختر Role: "Manager" أو "Editor"

**الطريقة الثانية - OAuth (بديل):**
إذا لم تنجح الطريقة الأولى، يمكن استخدام OAuth بدلاً من Service Account.

---

## 📱 إعداد Telegram (اختياري)

البوت مُعد مسبقاً بالرمز: `**********:AAGsZ4qIfsNqEfTqGP0enZjZ78MR5JAKBXs`

### لتفعيل الإشعارات:
1. ابحث عن البوت في Telegram: `@YourBotName`
2. أرسل رسالة `/start` أو أي رسالة
3. سيتم اكتشاف معرف المحادثة تلقائياً

### لإنشاء بوت جديد (اختياري):
1. ابحث عن `@BotFather` في Telegram
2. أرسل `/newbot`
3. اتبع التعليمات
4. احصل على Bot Token
5. حدث الرمز في `automation/telegram_bot.py`

---

## 🧪 اختبار النظام

### 1. اختبار أساسي
```bash
python main.py
```
يجب أن ينشئ فيديو واحد ويحفظه في مجلد `results/`

### 2. اختبار النظام الكامل
```bash
python run_automated_system.py
```

### 3. اختبار واجهة المراقبة
افتح المتصفح على: http://localhost:5000

### 4. اختبار Telegram
يجب أن تصل رسالة "تم بدء تشغيل النظام"

---

## ❌ حل المشاكل الشائعة

### **"Invalid credentials" - Reddit**
- تأكد من صحة `client_id` و `client_secret`
- تأكد من صحة اسم المستخدم وكلمة المرور
- تأكد من أن نوع التطبيق "script"

### **"403 Forbidden" - YouTube**
- تأكد من تفعيل YouTube Data API v3
- تأكد من صحة ملف `service_account.json`
- تأكد من إضافة Service Account كمدير للقناة

### **"API key not valid" - Gemini**
- المفتاح مُعد مسبقاً، لكن تحقق من الاتصال بالإنترنت
- إذا انتهت صلاحية المفتاح، حدثه في `automation/gemini_content_generator.py`

### **لا تصل إشعارات Telegram**
- أرسل رسالة للبوت أولاً لتفعيل المحادثة
- تحقق من صحة رمز البوت
- تحقق من الاتصال بالإنترنت

### **"Module not found"**
```bash
pip install -r requirements.txt
```

### **مشاكل Playwright**
```bash
python -m playwright install
python -m playwright install-deps
```

---

## 🎯 نصائح للنجاح

1. **ابدأ بالاختبار**: جرب إنشاء فيديو واحد أولاً
2. **راقب السجلات**: تحقق من مجلد `logs/` عند حدوث مشاكل
3. **استخدم واجهة المراقبة**: http://localhost:5000 للمتابعة
4. **كن صبوراً**: قد يستغرق إنشاء الفيديو الأول وقتاً أطول
5. **احتفظ بنسخة احتياطية**: من ملفات الإعداد والمفاتيح

---

## ✅ قائمة التحقق النهائية

قبل التشغيل النهائي، تأكد من:

- [ ] ملف `config.toml` محدث ببيانات Reddit صحيحة
- [ ] ملف `service_account.json` موجود في المجلد الرئيسي
- [ ] تم تفعيل YouTube Data API v3
- [ ] تم إضافة Service Account كمدير للقناة
- [ ] تم تثبيت جميع المتطلبات
- [ ] تم اختبار إنشاء فيديو واحد بنجاح
- [ ] واجهة المراقبة تعمل على http://localhost:5000
- [ ] إشعارات Telegram تصل بنجاح

**🎉 إذا تم كل شيء، فأنت جاهز للتشغيل التلقائي!**

```bash
python run_automated_system.py
```
