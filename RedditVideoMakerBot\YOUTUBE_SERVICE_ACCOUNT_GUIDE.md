# 🎬 دليل إعداد YouTube Service Account

## 🎯 الهدف
إنشاء Service Account للنشر التلقائي على YouTube بدلاً من OAuth client الموجود حالياً.

---

## 🔍 **الفرق بين OAuth و Service Account:**

### **OAuth Client (الموجود حالياً):**
- ✅ يتطلب موافقة المستخدم في كل مرة
- ❌ لا يدعم النشر التلقائي الكامل
- ❌ يحتاج تفاعل بشري

### **Service Account (المطلوب):**
- ✅ نشر تلقائي بالكامل
- ✅ لا يحتاج تفاعل بشري
- ✅ مناسب للأتمتة

---

## 📋 **خطوات إنشاء Service Account:**

### **1. إنشاء مشروع Google Cloud:**
1. اذهب إلى: https://console.cloud.google.com/
2. انقر "Select a project" → "New Project"
3. أد<PERSON><PERSON> اسم المشروع: `reddit-video-maker`
4. انقر "Create"

### **2. تفعيل YouTube Data API v3:**
1. في القائمة الجانبية: "APIs & Services" → "Library"
2. ابحث عن: "YouTube Data API v3"
3. انقر عليه ثم "Enable"

### **3. إنشاء Service Account:**
1. اذهب إلى: "APIs & Services" → "Credentials"
2. انقر "Create Credentials" → "Service Account"
3. أدخل:
   - **Service account name**: `youtube-uploader`
   - **Service account ID**: سيتم ملؤه تلقائياً
   - **Description**: `Service account for automatic YouTube uploads`
4. انقر "Create and Continue"

### **4. تعيين الأذونات:**
1. في "Grant this service account access to project":
   - اختر Role: **"Editor"** أو **"YouTube API Service Agent"**
2. انقر "Continue" ثم "Done"

### **5. إنشاء وتحميل المفتاح:**
1. في صفحة Credentials، انقر على Service Account الذي أنشأته
2. اذهب إلى تبويب **"Keys"**
3. انقر "Add Key" → "Create new key"
4. اختر **"JSON"** ثم "Create"
5. سيتم تحميل ملف JSON تلقائياً

### **6. إعداد الملف:**
1. **أعد تسمية الملف** إلى: `service_account.json`
2. **انقل الملف** إلى المجلد الرئيسي للأداة
3. **تأكد من المسار**: `c:\Users\<USER>\Desktop\RedditVideoMakerBot\service_account.json`

---

## 🔗 **ربط Service Account بقناة YouTube:**

### **الطريقة الأولى - إضافة كمدير:**
1. اذهب إلى: https://studio.youtube.com/
2. Settings → Permissions
3. انقر "Invite"
4. أدخل **email الـ Service Account** (من ملف JSON)
5. اختر Role: **"Manager"** أو **"Editor"**
6. انقر "Invite"

### **الطريقة الثانية - إذا لم تنجح الأولى:**
1. اذهب إلى: https://console.cloud.google.com/iam-admin/iam
2. انقر "Add"
3. أدخل email الـ Service Account
4. اختر Role: "YouTube API Service Agent"
5. انقر "Save"

---

## 🧪 **اختبار Service Account:**

بعد إعداد الملف، شغل:

```bash
python check_all_apis.py
```

**النتيجة المتوقعة:**
```
✅ YouTube API - ملف service_account.json صحيح
```

---

## 📁 **مثال على محتوى service_account.json:**

```json
***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
```

---

## ⚠️ **تحذيرات مهمة:**

### **🔐 الأمان:**
- **لا تشارك** ملف service_account.json مع أي شخص
- **احتفظ بنسخة احتياطية** آمنة
- **لا ترفع الملف** على GitHub أو أي مكان عام

### **🎬 YouTube:**
- **تأكد من إضافة Service Account** كمدير للقناة
- **اختبر النشر** على فيديو تجريبي أولاً
- **راقب حدود YouTube API** (10,000 وحدة يومياً)

---

## 🆘 **استكشاف الأخطاء:**

### **خطأ: "403 Forbidden"**
```
السبب: Service Account غير مضاف للقناة
الحل: أضف Service Account كمدير في YouTube Studio
```

### **خطأ: "Invalid credentials"**
```
السبب: ملف service_account.json غير صحيح
الحل: تأكد من تحميل ملف JSON صحيح من Google Cloud
```

### **خطأ: "API not enabled"**
```
السبب: YouTube Data API v3 غير مفعل
الحل: فعّل API من Google Cloud Console
```

---

## ✅ **قائمة التحقق:**

- [ ] تم إنشاء مشروع Google Cloud
- [ ] تم تفعيل YouTube Data API v3
- [ ] تم إنشاء Service Account
- [ ] تم تحميل ملف JSON
- [ ] تم إعادة تسمية الملف إلى service_account.json
- [ ] تم وضع الملف في المجلد الرئيسي
- [ ] تم إضافة Service Account كمدير للقناة
- [ ] تم اختبار الإعداد بنجاح

---

## 🚀 **بعد الانتهاء:**

```bash
# اختبار جميع APIs
python check_all_apis.py

# إذا كان كل شيء جاهز، شغل النظام
python run_automated_system.py
```

---

## 📞 **الدعم:**

إذا واجهت مشاكل:
1. تأكد من اتباع الخطوات بالترتيب
2. تحقق من أن ملف JSON في المكان الصحيح
3. تأكد من إضافة Service Account للقناة
4. شغل اختبار التشخيص: `python check_all_apis.py`

**🎯 الهدف: الحصول على ✅ لجميع APIs في اختبار التشخيص**
