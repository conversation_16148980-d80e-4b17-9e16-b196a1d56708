#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل البوت مع ضمان instance واحد فقط - حل قوي
Single instance bot runner with strong conflict prevention
"""

import os
import sys
import time
import logging
import asyncio
import requests
import threading
import signal
import json
import fcntl
from pathlib import Path
from datetime import datetime

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger(__name__)

# ملف القفل العالمي
GLOBAL_LOCK_FILE = "/tmp/telegram_bot_global.lock"
INSTANCE_INFO_FILE = "/tmp/bot_instance_info.json"

class StrongInstanceLock:
    """قفل قوي لمنع تشغيل instances متعددة"""
    
    def __init__(self):
        self.lock_file = None
        self.lock_acquired = False
        self.current_pid = os.getpid()
        
    def acquire_lock(self):
        """الحصول على القفل بطريقة قوية"""
        try:
            # إنشاء ملف القفل
            self.lock_file = open(GLOBAL_LOCK_FILE, 'w')
            
            # محاولة الحصول على قفل حصري
            fcntl.flock(self.lock_file.fileno(), fcntl.LOCK_EX | fcntl.LOCK_NB)
            
            # كتابة معلومات Instance
            instance_info = {
                'pid': self.current_pid,
                'start_time': datetime.now().isoformat(),
                'hostname': os.getenv('HOSTNAME', 'unknown'),
                'render_instance': os.getenv('RENDER_INSTANCE_ID', 'unknown')
            }
            
            self.lock_file.write(json.dumps(instance_info, indent=2))
            self.lock_file.flush()
            
            # حفظ معلومات Instance في ملف منفصل
            with open(INSTANCE_INFO_FILE, 'w') as f:
                json.dump(instance_info, f, indent=2)
            
            self.lock_acquired = True
            logger.info(f"✅ تم الحصول على القفل القوي (PID: {self.current_pid})")
            return True
            
        except (IOError, OSError) as e:
            if self.lock_file:
                self.lock_file.close()
                self.lock_file = None
            
            # محاولة قراءة معلومات Instance الآخر
            try:
                with open(INSTANCE_INFO_FILE, 'r') as f:
                    other_instance = json.load(f)
                    other_pid = other_instance.get('pid')
                    start_time = other_instance.get('start_time')
                    logger.error(f"❌ البوت يعمل بالفعل (PID: {other_pid}, بدء: {start_time})")
            except:
                logger.error(f"❌ فشل في الحصول على القفل: {e}")
            
            return False
    
    def release_lock(self):
        """تحرير القفل"""
        if self.lock_acquired and self.lock_file:
            try:
                fcntl.flock(self.lock_file.fileno(), fcntl.LOCK_UN)
                self.lock_file.close()
                self.lock_file = None
                
                # حذف ملفات القفل
                try:
                    os.remove(GLOBAL_LOCK_FILE)
                    os.remove(INSTANCE_INFO_FILE)
                except:
                    pass
                
                self.lock_acquired = False
                logger.info("🔓 تم تحرير القفل القوي")
            except Exception as e:
                logger.error(f"❌ خطأ في تحرير القفل: {e}")

# إنشاء instance القفل العالمي
global_lock = StrongInstanceLock()

def setup_signal_handlers():
    """إعداد معالجات الإشارات للإغلاق الآمن"""
    def signal_handler(signum, frame):
        logger.info(f"📡 تم استلام إشارة {signum}، إغلاق آمن...")
        global_lock.release_lock()
        sys.exit(0)
    
    signal.signal(signal.SIGTERM, signal_handler)
    signal.signal(signal.SIGINT, signal_handler)

def aggressive_telegram_cleanup():
    """مسح شامل وقوي لتعارضات Telegram"""
    logger.info("🔧 مسح شامل لتعارضات Telegram...")
    
    try:
        bot_token = os.getenv('BOT_TOKEN')
        if not bot_token:
            logger.warning("⚠️ لم يتم العثور على BOT_TOKEN")
            return False
        
        # مسح webhook بقوة
        for attempt in range(3):
            try:
                response = requests.post(
                    f"https://api.telegram.org/bot{bot_token}/deleteWebhook",
                    json={"drop_pending_updates": True},
                    timeout=30
                )
                if response.status_code == 200 and response.json().get('ok'):
                    logger.info(f"✅ تم مسح webhook - المحاولة {attempt + 1}")
                    break
            except Exception as e:
                logger.warning(f"⚠️ فشل مسح webhook - المحاولة {attempt + 1}: {e}")
                time.sleep(5)
        
        # مسح جميع التحديثات المعلقة بطريقة عدوانية
        total_cleared = 0
        for main_attempt in range(10):  # 10 محاولات رئيسية
            try:
                response = requests.get(
                    f"https://api.telegram.org/bot{bot_token}/getUpdates",
                    params={"timeout": 0, "limit": 100},
                    timeout=30
                )
                
                if response.status_code == 200:
                    result = response.json()
                    if result.get('ok') and result.get('result'):
                        updates = result['result']
                        if updates:
                            last_update_id = updates[-1]['update_id']
                            # مسح التحديثات
                            clear_response = requests.get(
                                f"https://api.telegram.org/bot{bot_token}/getUpdates",
                                params={"offset": last_update_id + 1, "timeout": 0},
                                timeout=30
                            )
                            total_cleared += len(updates)
                            logger.info(f"✅ تم مسح {len(updates)} تحديث - المحاولة {main_attempt + 1}")
                            time.sleep(3)  # انتظار أطول
                        else:
                            logger.info("✅ لا توجد تحديثات معلقة")
                            break
                    else:
                        break
                else:
                    logger.warning(f"⚠️ فشل في جلب التحديثات: {response.status_code}")
                    break
                    
            except Exception as e:
                logger.warning(f"⚠️ خطأ في مسح التحديثات - المحاولة {main_attempt + 1}: {e}")
                time.sleep(5)
        
        logger.info(f"✅ تم مسح {total_cleared} تحديث إجمالي")
        
        # انتظار طويل للتأكد من تطبيق التغييرات
        logger.info("⏳ انتظار 15 ثانية للتأكد من تطبيق التغييرات...")
        time.sleep(15)
        
        logger.info("✅ تم المسح الشامل لتعارضات Telegram")
        return True
        
    except Exception as e:
        logger.error(f"❌ خطأ في المسح الشامل: {e}")
        return False

def setup_environment():
    """إعداد البيئة الأساسية"""
    logger.info("🔧 إعداد البيئة...")
    
    # متغيرات البيئة الأساسية
    env_vars = {
        'BOT_TOKEN': '**********************************************',
        'TELEGRAM_BOT_TOKEN': '**********************************************',
        'SUPABASE_URL': 'https://ytqxxodyecdeosnqoure.supabase.co',
        'SUPABASE_KEY': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4',
        'ADMIN_CHAT_ID': '7513880877',
        'USE_NGROK': 'false',
        'NGROK_ENABLED': 'false',
        'ENVIRONMENT': 'production',
        'DEBUG': 'false',
        'PYTHONUNBUFFERED': '1',
        'PYTHONIOENCODING': 'utf-8',
        'RENDER': 'true',
        'WEB_SERVER_URL': 'https://1c547fe5.sendaddons.pages.dev'
    }
    
    for key, value in env_vars.items():
        if not os.getenv(key):
            os.environ[key] = str(value)
    
    # إنشاء المجلدات والملفات المطلوبة
    required_dirs = ['logs', 'temp', 'cache', 'security/logs', 'security/quarantine', 'security_logs']
    for dir_path in required_dirs:
        Path(dir_path).mkdir(parents=True, exist_ok=True)
    
    required_files = [
        'user_channels.json', 'all_users.json', 'user_feedback.json',
        'user_mods_status.json', 'user_blocked_mods.json', 'user_invitations.json',
        'user_subscriptions.json', 'user_feature_activation.json',
        'admin_settings.json', 'admin_processed_mods.json', 'pending_publication.json'
    ]
    
    for file_path in required_files:
        if not Path(file_path).exists():
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write('{}')
    
    logger.info("✅ تم إعداد البيئة")

def start_health_server():
    """تشغيل خادم الصحة"""
    try:
        from flask import Flask, jsonify
        
        app = Flask(__name__)
        
        @app.route('/')
        def health():
            return jsonify({
                "status": "healthy",
                "service": "minecraft_mods_bot",
                "timestamp": str(datetime.now()),
                "platform": "render",
                "pid": os.getpid(),
                "lock_acquired": global_lock.lock_acquired
            })
        
        @app.route('/health')
        def health_check():
            return jsonify({"status": "ok", "pid": os.getpid()})
        
        @app.route('/instance-info')
        def instance_info():
            try:
                with open(INSTANCE_INFO_FILE, 'r') as f:
                    return jsonify(json.load(f))
            except:
                return jsonify({"error": "No instance info available"})
        
        port = int(os.getenv('PORT', 10000))
        
        def run_server():
            app.run(host='0.0.0.0', port=port, debug=False, use_reloader=False)
        
        server_thread = threading.Thread(target=run_server, daemon=True)
        server_thread.start()
        
        logger.info(f"✅ خادم الصحة يعمل على المنفذ {port}")
        return True
        
    except Exception as e:
        logger.error(f"❌ فشل في تشغيل خادم الصحة: {e}")
        return False

async def start_bot():
    """تشغيل البوت الرئيسي"""
    logger.info("🤖 بدء تشغيل البوت...")
    
    try:
        # استيراد البوت الرئيسي
        import main
        
        # تشغيل البوت
        if hasattr(main, 'main') and asyncio.iscoroutinefunction(main.main):
            await main.main()
        elif hasattr(main, 'main'):
            main.main()
        else:
            logger.error("❌ لم يتم العثور على دالة main في البوت")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ خطأ في تشغيل البوت: {e}")
        return False

async def main():
    """الدالة الرئيسية"""
    logger.info("🚀 بدء تشغيل البوت - وضع Instance واحد قوي")
    logger.info("=" * 60)
    
    try:
        # إعداد معالجات الإشارات
        setup_signal_handlers()
        
        # محاولة الحصول على القفل القوي
        if not global_lock.acquire_lock():
            logger.error("❌ لا يمكن تشغيل أكثر من instance واحد من البوت")
            logger.error("💡 إذا كنت متأكد من عدم وجود instance آخر، احذف الملفات:")
            logger.error(f"   - {GLOBAL_LOCK_FILE}")
            logger.error(f"   - {INSTANCE_INFO_FILE}")
            sys.exit(1)
        
        # إعداد البيئة
        setup_environment()
        
        # مسح شامل لتعارضات Telegram
        aggressive_telegram_cleanup()
        
        # تشغيل خادم الصحة
        start_health_server()
        
        # انتظار إضافي للتأكد
        logger.info("⏳ انتظار 10 ثواني إضافية للتأكد...")
        await asyncio.sleep(10)
        
        # تشغيل البوت
        await start_bot()
        
    except KeyboardInterrupt:
        logger.info("⏹️ تم إيقاف البوت بواسطة المستخدم")
    except Exception as e:
        logger.error(f"❌ خطأ في التشغيل: {e}")
        sys.exit(1)
    finally:
        global_lock.release_lock()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("⏹️ تم إيقاف البوت")
        global_lock.release_lock()
    except Exception as e:
        logger.error(f"❌ خطأ في التشغيل: {e}")
        global_lock.release_lock()
        sys.exit(1)
