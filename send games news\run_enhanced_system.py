#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل النظام المحسن لوكيل أخبار الألعاب
"""

import asyncio
import sys
import os
import argparse
from datetime import datetime

# إضافة المسار الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# استيراد الأنظمة المحسنة
from modules.smart_lifecycle_manager import lifecycle_manager, StartupMode, ShutdownReason
from modules.enhanced_web_interface import enhanced_web_interface
from modules.logger import logger

def print_banner():
    """طباعة شعار النظام"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║        🎮 وكيل أخبار الألعاب - النظام المحسن 🤖           ║
    ║                                                              ║
    ║                    نظام ذكي ومحسن                          ║
    ║                 إدارة متقدمة للعمليات                       ║
    ║                 مراقبة شاملة للأداء                        ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def print_system_info():
    """طباعة معلومات النظام"""
    print("📋 معلومات النظام:")
    print(f"   🕐 الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"   🐍 Python: {sys.version.split()[0]}")
    print(f"   📁 المجلد: {os.getcwd()}")
    print(f"   🌐 واجهة الويب: http://localhost:5000")
    print()

def print_startup_modes():
    """طباعة أوضاع التشغيل المتاحة"""
    print("🔧 أوضاع التشغيل المتاحة:")
    print("   • normal    - تشغيل عادي (افتراضي)")
    print("   • recovery  - وضع الاستعادة")
    print("   • safe      - الوضع الآمن")
    print("   • maintenance - وضع الصيانة")
    print()

def print_commands():
    """طباعة الأوامر المتاحة"""
    print("⌨️  الأوامر المتاحة أثناء التشغيل:")
    print("   • 'status'  - عرض حالة النظام")
    print("   • 'health'  - تقرير صحة النظام")
    print("   • 'stop'    - إيقاف آمن")
    print("   • 'emergency' - إيقاف طارئ")
    print("   • 'help'    - عرض المساعدة")
    print("   • 'quit'    - خروج")
    print()

async def start_enhanced_system(mode: StartupMode):
    """بدء النظام المحسن"""
    try:
        print(f"🚀 بدء تشغيل النظام المحسن في وضع: {mode.value}")
        print("=" * 60)
        
        # بدء النظام
        success = await lifecycle_manager.smart_startup(mode)
        
        if success:
            print("✅ تم بدء النظام بنجاح!")
            
            # بدء واجهة الويب
            print("🌐 بدء واجهة الويب المحسنة...")
            enhanced_web_interface.start_server(host='localhost', port=5000, debug=False)
            
            print("🎉 النظام جاهز للعمل!")
            print("📱 افتح المتصفح على: http://localhost:5000")
            print()
            
            return True
        else:
            print("❌ فشل في بدء النظام!")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في بدء النظام: {e}")
        return False

async def stop_enhanced_system(emergency=False):
    """إيقاف النظام المحسن"""
    try:
        if emergency:
            print("🚨 بدء الإيقاف الطارئ...")
            success = await lifecycle_manager.emergency_shutdown("طلب المستخدم")
        else:
            print("🛑 بدء الإيقاف الآمن...")
            success = await lifecycle_manager.graceful_shutdown(
                reason=ShutdownReason.USER_REQUEST,
                initiated_by="run_enhanced_system"
            )
        
        # إيقاف واجهة الويب
        enhanced_web_interface.stop_server()
        
        if success:
            print("✅ تم إيقاف النظام بنجاح!")
        else:
            print("⚠️ تم إيقاف النظام مع بعض المشاكل")
            
        return success
        
    except Exception as e:
        print(f"❌ خطأ في إيقاف النظام: {e}")
        return False

async def show_system_status():
    """عرض حالة النظام"""
    try:
        print("📊 حالة النظام:")
        print("-" * 40)
        
        # حالة دورة الحياة
        lifecycle_status = lifecycle_manager.get_lifecycle_status()
        print(f"🔄 حالة دورة الحياة: {lifecycle_status.get('current_state', 'غير محدد')}")
        print(f"🚀 جاري البدء: {'نعم' if lifecycle_status.get('is_starting_up') else 'لا'}")
        print(f"🛑 جاري الإيقاف: {'نعم' if lifecycle_status.get('is_shutting_down') else 'لا'}")
        
        # تقرير الصحة
        health_report = lifecycle_manager.get_health_report()
        print(f"💚 صحة النظام: {health_report.get('health_status', 'غير محدد')} ({health_report.get('health_score', 0)}%)")
        
        if health_report.get('issues'):
            print("⚠️ المشاكل:")
            for issue in health_report['issues']:
                print(f"   • {issue}")
        
        print()
        
    except Exception as e:
        print(f"❌ خطأ في عرض حالة النظام: {e}")

async def show_health_report():
    """عرض تقرير صحة مفصل"""
    try:
        print("🏥 تقرير صحة النظام المفصل:")
        print("=" * 50)
        
        health_report = lifecycle_manager.get_health_report()
        
        print(f"📊 نقاط الصحة: {health_report.get('health_score', 0)}/100")
        print(f"🎯 الحالة: {health_report.get('health_status', 'غير محدد')}")
        print(f"⏱️ وقت التشغيل: {health_report.get('uptime_hours', 0):.1f} ساعة")
        
        # حالة الوكيل
        agent_state = health_report.get('agent_state', {})
        print(f"🤖 حالة الوكيل: {agent_state.get('state', 'غير محدد')}")
        print(f"💾 استخدام الذاكرة: {agent_state.get('memory_usage_mb', 0):.1f} MB")
        print(f"🖥️ استخدام المعالج: {agent_state.get('cpu_usage_percent', 0):.1f}%")
        
        # قاعدة البيانات
        db_health = health_report.get('database_health', {})
        print(f"🗄️ صحة قاعدة البيانات: {db_health.get('integrity_check', 'غير محدد')}")
        print(f"📁 حجم قاعدة البيانات: {db_health.get('file_size_mb', 0):.1f} MB")
        
        # العمليات
        operation_stats = health_report.get('operation_stats', {})
        print(f"⚙️ العمليات النشطة: {operation_stats.get('running_operations', 0)}")
        print(f"📈 إجمالي العمليات: {operation_stats.get('total_operations', 0)}")
        
        # المشاكل
        if health_report.get('issues'):
            print("\n⚠️ المشاكل المكتشفة:")
            for issue in health_report['issues']:
                print(f"   • {issue}")
        
        # التوصيات
        if health_report.get('recommendations'):
            print("\n💡 التوصيات:")
            for rec in health_report['recommendations']:
                print(f"   • {rec}")
        
        print()
        
    except Exception as e:
        print(f"❌ خطأ في عرض تقرير الصحة: {e}")

async def interactive_mode():
    """الوضع التفاعلي"""
    print("🎮 الوضع التفاعلي - اكتب 'help' للمساعدة")
    print()
    
    while True:
        try:
            command = input("🤖 > ").strip().lower()
            
            if command == 'quit' or command == 'exit':
                break
            elif command == 'help':
                print_commands()
            elif command == 'status':
                await show_system_status()
            elif command == 'health':
                await show_health_report()
            elif command == 'stop':
                confirm = input("هل تريد إيقاف النظام بشكل آمن؟ (y/N): ")
                if confirm.lower() in ['y', 'yes', 'نعم']:
                    await stop_enhanced_system(emergency=False)
                    break
            elif command == 'emergency':
                confirm = input("هل تريد الإيقاف الطارئ؟ (قد يؤدي لفقدان البيانات) (y/N): ")
                if confirm.lower() in ['y', 'yes', 'نعم']:
                    await stop_enhanced_system(emergency=True)
                    break
            elif command == '':
                continue
            else:
                print(f"❓ أمر غير معروف: {command}")
                print("اكتب 'help' لعرض الأوامر المتاحة")
                
        except KeyboardInterrupt:
            print("\n🛑 تم الضغط على Ctrl+C")
            confirm = input("هل تريد إيقاف النظام؟ (y/N): ")
            if confirm.lower() in ['y', 'yes', 'نعم']:
                await stop_enhanced_system(emergency=False)
                break
        except EOFError:
            break
        except Exception as e:
            print(f"❌ خطأ: {e}")

async def main():
    """الدالة الرئيسية"""
    parser = argparse.ArgumentParser(description='تشغيل النظام المحسن لوكيل أخبار الألعاب')
    parser.add_argument('--mode', choices=['normal', 'recovery', 'safe', 'maintenance'], 
                       default='normal', help='وضع التشغيل')
    parser.add_argument('--no-interactive', action='store_true', 
                       help='تشغيل بدون الوضع التفاعلي')
    parser.add_argument('--test', action='store_true', 
                       help='تشغيل اختبارات النظام')
    
    args = parser.parse_args()
    
    # طباعة المعلومات
    print_banner()
    print_system_info()
    
    if args.test:
        print("🧪 تشغيل اختبارات النظام...")
        from test_enhanced_system import EnhancedSystemTester
        tester = EnhancedSystemTester()
        await tester.run_all_tests()
        return
    
    # تحديد وضع التشغيل
    mode_map = {
        'normal': StartupMode.NORMAL,
        'recovery': StartupMode.RECOVERY,
        'safe': StartupMode.SAFE_MODE,
        'maintenance': StartupMode.MAINTENANCE
    }
    
    startup_mode = mode_map[args.mode]
    
    print_startup_modes()
    
    # بدء النظام
    success = await start_enhanced_system(startup_mode)
    
    if not success:
        print("❌ فشل في بدء النظام!")
        return
    
    # الوضع التفاعلي
    if not args.no_interactive:
        try:
            await interactive_mode()
        except KeyboardInterrupt:
            print("\n🛑 تم إيقاف النظام بواسطة المستخدم")
        finally:
            await stop_enhanced_system(emergency=False)
    else:
        print("🔄 النظام يعمل في الخلفية...")
        print("استخدم Ctrl+C للإيقاف")
        try:
            # انتظار إشارة الإيقاف
            while True:
                await asyncio.sleep(1)
        except KeyboardInterrupt:
            print("\n🛑 إيقاف النظام...")
            await stop_enhanced_system(emergency=False)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 وداعاً!")
    except Exception as e:
        print(f"❌ خطأ حرج: {e}")
        sys.exit(1)
