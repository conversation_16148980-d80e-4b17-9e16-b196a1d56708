#!/usr/bin/env python3
"""
ملف تشغيل البوت على Render - نسخة Python
Render bot startup script - Python version
"""

import os
import sys
import subprocess
import logging
import requests

# إعداد السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def main():
    """الدالة الرئيسية لتشغيل البوت"""
    logger.info("🚀 بدء تشغيل البوت على Render...")
    logger.info("🚀 Starting bot on Render...")
    
    # تعيين متغيرات البيئة
    os.environ.setdefault('RENDER', 'true')
    os.environ.setdefault('PYTHONUNBUFFERED', '1')
    os.environ.setdefault('PYTHONIOENCODING', 'utf-8')
    
    # إنشاء المجلدات المطلوبة
    required_dirs = [
        'logs', 'temp', 'cache', 
        'security/logs', 'security/quarantine', 'security_logs'
    ]
    
    for dir_path in required_dirs:
        try:
            os.makedirs(dir_path, exist_ok=True)
            logger.info(f"✅ مجلد: {dir_path}")
        except Exception as e:
            logger.warning(f"⚠️ فشل في إنشاء مجلد {dir_path}: {e}")
    
    # تشغيل البوت
    logger.info("🤖 تشغيل البوت...")
    logger.info("🤖 Starting bot...")
    
    try:
        # تطبيق الإصلاحات أولاً
        apply_quick_fixes()

        # استيراد وتشغيل start_render مباشرة
        import start_render
        import asyncio
        asyncio.run(start_render.main())
    except Exception as e:
        logger.error(f"❌ خطأ في تشغيل البوت: {e}")
        sys.exit(1)

def apply_quick_fixes():
    """تطبيق الإصلاحات السريعة"""
    logger.info("🔧 تطبيق الإصلاحات السريعة...")

    # إعداد متغيرات البيئة
    env_vars = {
        'BOT_TOKEN': '**********************************************',
        'TELEGRAM_BOT_TOKEN': '**********************************************',
        'SUPABASE_URL': 'https://ytqxxodyecdeosnqoure.supabase.co',
        'SUPABASE_KEY': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4',
        'ADMIN_CHAT_ID': '7513880877',
        'USE_NGROK': 'false',
        'NGROK_ENABLED': 'false',
        'ENVIRONMENT': 'production',
        'DEBUG': 'false',
        'PYTHONUNBUFFERED': '1',
        'PYTHONIOENCODING': 'utf-8',
        'WEB_SERVER_URL': 'https://1c547fe5.sendaddons.pages.dev'
    }

    for key, value in env_vars.items():
        if not os.getenv(key):
            os.environ[key] = str(value)
            logger.info(f"✅ تم تعيين {key}")

    # إصلاح Telegram
    try:
        bot_token = os.getenv('BOT_TOKEN')
        if bot_token:
            logger.info("🔧 إصلاح مشكلة Telegram getUpdates conflicts...")

            # مسح webhook
            try:
                response = requests.post(f"https://api.telegram.org/bot{bot_token}/deleteWebhook",
                                       json={"drop_pending_updates": True}, timeout=10)
                if response.status_code == 200 and response.json().get('ok'):
                    logger.info("✅ تم مسح webhook للبوت")
            except Exception as e:
                logger.warning(f"⚠️ تحذير في مسح webhook: {e}")

            # مسح التحديثات المعلقة
            try:
                response = requests.get(f"https://api.telegram.org/bot{bot_token}/getUpdates",
                                      params={"offset": -1, "limit": 1, "timeout": 0}, timeout=10)

                if response.status_code == 200:
                    result = response.json()
                    if result.get('ok') and result.get('result'):
                        last_update_id = result['result'][-1]['update_id']
                        requests.get(f"https://api.telegram.org/bot{bot_token}/getUpdates",
                                   params={"offset": last_update_id + 1, "limit": 1, "timeout": 0}, timeout=10)
                        logger.info("✅ تم مسح التحديثات المعلقة")
            except Exception as e:
                logger.warning(f"⚠️ تحذير في مسح التحديثات: {e}")

    except Exception as e:
        logger.warning(f"⚠️ تحذير في إصلاح Telegram: {e}")

    logger.info("✅ تم تطبيق الإصلاحات")

if __name__ == "__main__":
    main()
