#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار نظام الاشتراك في القنوات
"""

import asyncio
import sys
import os
from dotenv import load_dotenv

# تحميل متغيرات البيئة
load_dotenv()

# إضافة المجلد الحالي للمسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from telegram import Bot
from telegram.ext import Application

# استيراد الدوال من main.py
from main import check_all_required_subscriptions, REQUIRED_CHANNELS

async def test_subscription_system():
    """اختبار نظام الاشتراك"""
    print("🧪 بدء اختبار نظام الاشتراك...")
    
    # إعداد البوت
    token = os.environ.get("BOT_TOKEN")
    if not token:
        print("❌ لم يتم العثور على BOT_TOKEN")
        return
    
    application = Application.builder().token(token).build()
    
    # معرف المستخدم للاختبار (يمكنك تغييره)
    test_user_id = 7513880877  # معرف المسؤول
    
    print(f"🔍 اختبار الاشتراك للمستخدم: {test_user_id}")
    print(f"📺 القنوات المطلوبة: {len(REQUIRED_CHANNELS.get('ads_system', []))}")
    
    for channel in REQUIRED_CHANNELS.get('ads_system', []):
        print(f"   - {channel['name']}: {channel['url']}")
    
    try:
        # اختبار التحقق من الاشتراك
        is_subscribed, unsubscribed_channels = await check_all_required_subscriptions(
            application, test_user_id, 'ads_system'
        )
        
        print(f"\n📊 نتائج الاختبار:")
        print(f"   - مشترك في جميع القنوات: {'✅ نعم' if is_subscribed else '❌ لا'}")
        print(f"   - عدد القنوات غير المشترك بها: {len(unsubscribed_channels)}")
        
        if unsubscribed_channels:
            print(f"   - القنوات غير المشترك بها:")
            for channel in unsubscribed_channels:
                print(f"     • {channel['name']}")
        
        print(f"\n🎯 النتيجة: نظام الاشتراك {'✅ يعمل' if True else '❌ لا يعمل'}")
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
    
    finally:
        await application.stop()

if __name__ == "__main__":
    asyncio.run(test_subscription_system())
