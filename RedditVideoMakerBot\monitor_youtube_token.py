#!/usr/bin/env python3
"""
مراقب توكن YouTube
يتحقق من صلاحية التوكن ويرسل إشعارات عند الحاجة
"""

import os
import sys
import time
import logging
import schedule
from datetime import datetime, timedelta
from pathlib import Path

# إضافة مجلد automation للمسار
sys.path.append(str(Path(__file__).parent / "automation"))

from automation.youtube_uploader import YouTubeTokenManager
from automation.telegram_bot import (
    send_token_expiry_warning, 
    send_token_refresh_success,
    send_token_refresh_failed,
    send_auth_setup_instructions
)

# إعداد السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('youtube_token_monitor.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class YouTubeTokenMonitor:
    """مراقب توكن YouTube"""
    
    def __init__(self):
        """إعداد المراقب"""
        self.token_manager = YouTubeTokenManager()
        self.last_warning_sent = None
        self.warning_interval_hours = 6  # إرسال تحذير كل 6 ساعات
        
    def check_token_status(self):
        """التحقق من حالة التوكن"""
        try:
            logger.info("🔍 فحص حالة توكن YouTube...")
            
            # تحميل التوكن
            credentials = self.token_manager.load_credentials()
            
            if not credentials:
                logger.warning("⚠️ لا يوجد توكن محفوظ")
                self._send_no_token_notification()
                return False
                
            # التحقق من صلاحية التوكن
            if not credentials.valid:
                if credentials.expired and credentials.refresh_token:
                    logger.info("🔄 محاولة تجديد التوكن...")
                    if self.token_manager.refresh_token():
                        logger.info("✅ تم تجديد التوكن بنجاح")
                        send_token_refresh_success()
                        return True
                    else:
                        logger.error("❌ فشل في تجديد التوكن")
                        send_token_refresh_failed("فشل في تجديد التوكن")
                        return False
                else:
                    logger.warning("⚠️ التوكن غير صالح ولا يوجد refresh token")
                    self._send_auth_required_notification()
                    return False
                    
            # التحقق من الوقت المتبقي
            if credentials.expiry:
                time_until_expiry = credentials.expiry - datetime.utcnow()
                hours_until_expiry = time_until_expiry.total_seconds() / 3600
                
                logger.info(f"⏰ الوقت المتبقي للتوكن: {hours_until_expiry:.1f} ساعة")
                
                # إرسال تحذير إذا كان التوكن سينتهي قريباً
                if hours_until_expiry <= 24:  # أقل من 24 ساعة
                    self._send_expiry_warning(int(hours_until_expiry))
                    
                # اعتبار التوكن منتهي إذا كان أقل من ساعة
                if hours_until_expiry < 1:
                    logger.warning("⚠️ التوكن سينتهي خلال أقل من ساعة")
                    return False
                    
            logger.info("✅ التوكن صالح ويعمل بشكل طبيعي")
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في فحص التوكن: {e}")
            return False
            
    def _send_expiry_warning(self, hours_until_expiry: int):
        """إرسال تحذير انتهاء التوكن"""
        try:
            # تجنب إرسال تحذيرات متكررة
            now = datetime.now()
            if (self.last_warning_sent and 
                (now - self.last_warning_sent).total_seconds() < 
                self.warning_interval_hours * 3600):
                return
                
            send_token_expiry_warning(hours_until_expiry)
            self.last_warning_sent = now
            logger.info(f"📤 تم إرسال تحذير انتهاء التوكن ({hours_until_expiry} ساعة)")
            
        except Exception as e:
            logger.error(f"خطأ في إرسال تحذير انتهاء التوكن: {e}")
            
    def _send_no_token_notification(self):
        """إرسال إشعار عدم وجود توكن"""
        try:
            auth_url = self.token_manager.get_authorization_url()
            send_auth_setup_instructions(auth_url)
            logger.info("📤 تم إرسال إشعار عدم وجود توكن")
            
        except Exception as e:
            logger.error(f"خطأ في إرسال إشعار عدم وجود توكن: {e}")
            
    def _send_auth_required_notification(self):
        """إرسال إشعار الحاجة لمصادقة جديدة"""
        try:
            auth_url = self.token_manager.get_authorization_url()
            send_auth_setup_instructions(auth_url)
            logger.info("📤 تم إرسال إشعار الحاجة لمصادقة جديدة")
            
        except Exception as e:
            logger.error(f"خطأ في إرسال إشعار المصادقة: {e}")
            
    def run_continuous_monitoring(self):
        """تشغيل المراقبة المستمرة"""
        logger.info("🚀 بدء مراقبة توكن YouTube...")
        
        # جدولة الفحص كل ساعة
        schedule.every().hour.do(self.check_token_status)
        
        # فحص أولي
        self.check_token_status()
        
        # تشغيل المراقبة
        while True:
            try:
                schedule.run_pending()
                time.sleep(60)  # انتظار دقيقة واحدة
                
            except KeyboardInterrupt:
                logger.info("⚠️ تم إيقاف المراقبة بواسطة المستخدم")
                break
            except Exception as e:
                logger.error(f"خطأ في المراقبة: {e}")
                time.sleep(300)  # انتظار 5 دقائق عند حدوث خطأ
                
    def run_single_check(self):
        """تشغيل فحص واحد"""
        logger.info("🔍 تشغيل فحص واحد للتوكن...")
        result = self.check_token_status()
        
        if result:
            print("✅ التوكن صالح ويعمل بشكل طبيعي")
        else:
            print("❌ التوكن غير صالح أو يحتاج تجديد")
            
        return result

def main():
    """الدالة الرئيسية"""
    import argparse
    
    parser = argparse.ArgumentParser(description="مراقب توكن YouTube")
    parser.add_argument("--check", action="store_true", 
                       help="فحص واحد للتوكن")
    parser.add_argument("--monitor", action="store_true", 
                       help="مراقبة مستمرة للتوكن")
    parser.add_argument("--refresh", action="store_true", 
                       help="محاولة تجديد التوكن")
    
    args = parser.parse_args()
    
    monitor = YouTubeTokenMonitor()
    
    if args.check:
        return 0 if monitor.run_single_check() else 1
        
    elif args.refresh:
        logger.info("🔄 محاولة تجديد التوكن...")
        if monitor.token_manager.refresh_token():
            print("✅ تم تجديد التوكن بنجاح")
            return 0
        else:
            print("❌ فشل في تجديد التوكن")
            return 1
            
    elif args.monitor:
        monitor.run_continuous_monitoring()
        return 0
        
    else:
        # تشغيل فحص واحد افتراضياً
        return 0 if monitor.run_single_check() else 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⚠️ تم إلغاء العملية بواسطة المستخدم")
        sys.exit(1)
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")
        sys.exit(1)
