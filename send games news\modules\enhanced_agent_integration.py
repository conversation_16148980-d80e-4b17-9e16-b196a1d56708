# نظام التكامل المحسن للوكيل الذكي
import asyncio
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from enum import Enum

from .logger import logger
from .advanced_rag_system import advanced_rag_system, RAGQuery, RAGMode, ContentType
from .multimodal_analyzer import multimodal_analyzer, MediaAnalysisRequest, MediaType
from .memory_system import memory_system, Memory, MemoryType, MemoryImportance

class EnhancementLevel(Enum):
    """مستويات التحسين"""
    BASIC = "basic"
    ADVANCED = "advanced"
    PREMIUM = "premium"

@dataclass
class EnhancedAnalysisResult:
    """نتيجة التحليل المحسن"""
    content: str
    confidence: float
    rag_results: List[Any]
    multimodal_results: List[Any]
    memory_insights: Dict[str, Any]
    enhancement_level: EnhancementLevel
    processing_time: float
    created_at: datetime = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()

class EnhancedAgentIntegration:
    """نظام التكامل المحسن للوكيل الذكي"""
    
    def __init__(self):
        self.enabled = True
        self.enhancement_level = EnhancementLevel.ADVANCED
        
        # إحصائيات التحسينات
        self.enhancement_stats = {
            'total_enhanced_analyses': 0,
            'rag_usage_count': 0,
            'multimodal_usage_count': 0,
            'memory_usage_count': 0,
            'avg_enhancement_time': 0,
            'success_rate': 0,
            'last_update': datetime.now()
        }
        
        # إعدادات التحسين
        self.config = {
            'enable_rag': True,
            'enable_multimodal': True,
            'enable_advanced_memory': True,
            'auto_memory_storage': True,
            'content_quality_threshold': 0.7,
            'multimodal_confidence_threshold': 0.6,
            'rag_similarity_threshold': 0.7
        }
        
        logger.info("🚀 تم تهيئة نظام التكامل المحسن")
    
    async def enhance_content_analysis(self, content: str, content_type: str = "text", 
                                     media_path: Optional[str] = None) -> EnhancedAnalysisResult:
        """تحليل محتوى محسن باستخدام جميع الأنظمة المتقدمة"""
        try:
            start_time = time.time()
            self.enhancement_stats['total_enhanced_analyses'] += 1
            
            logger.info(f"🔍 بدء التحليل المحسن للمحتوى: {content[:100]}...")
            
            # 1. تحليل RAG للسياق والمعلومات ذات الصلة
            rag_results = []
            if self.config['enable_rag'] and advanced_rag_system.enabled:
                rag_results = await self._perform_rag_analysis(content)
                self.enhancement_stats['rag_usage_count'] += 1
            
            # 2. تحليل متعدد الوسائط إذا توفرت وسائط
            multimodal_results = []
            if (self.config['enable_multimodal'] and media_path and 
                multimodal_analyzer.enabled):
                multimodal_results = await self._perform_multimodal_analysis(media_path)
                self.enhancement_stats['multimodal_usage_count'] += 1
            
            # 3. استرجاع رؤى الذاكرة
            memory_insights = {}
            if self.config['enable_advanced_memory']:
                memory_insights = await self._get_memory_insights(content)
                self.enhancement_stats['memory_usage_count'] += 1
            
            # 4. دمج النتائج وحساب الثقة الإجمالية
            enhanced_content, confidence = await self._merge_analysis_results(
                content, rag_results, multimodal_results, memory_insights)
            
            # 5. حفظ في الذاكرة إذا كانت الجودة عالية
            if (self.config['auto_memory_storage'] and 
                confidence >= self.config['content_quality_threshold']):
                await self._store_in_memory(enhanced_content, content_type, confidence)
            
            processing_time = time.time() - start_time
            self.enhancement_stats['avg_enhancement_time'] = (
                self.enhancement_stats['avg_enhancement_time'] + processing_time) / 2
            
            result = EnhancedAnalysisResult(
                content=enhanced_content,
                confidence=confidence,
                rag_results=rag_results,
                multimodal_results=multimodal_results,
                memory_insights=memory_insights,
                enhancement_level=self.enhancement_level,
                processing_time=processing_time
            )
            
            logger.info(f"✅ تم التحليل المحسن بنجاح - ثقة: {confidence:.2f}, وقت: {processing_time:.2f}s")
            return result
            
        except Exception as e:
            logger.error(f"❌ خطأ في التحليل المحسن: {e}")
            return EnhancedAnalysisResult(
                content=content,
                confidence=0.5,
                rag_results=[],
                multimodal_results=[],
                memory_insights={},
                enhancement_level=EnhancementLevel.BASIC,
                processing_time=0
            )
    
    async def _perform_rag_analysis(self, content: str) -> List[Any]:
        """تنفيذ تحليل RAG"""
        try:
            # إنشاء استعلام RAG
            rag_query = RAGQuery(
                query=content,
                mode=RAGMode.HYBRID,
                content_types=[ContentType.TEXT, ContentType.MIXED],
                max_results=5,
                similarity_threshold=self.config['rag_similarity_threshold']
            )
            
            # تنفيذ البحث
            results = await advanced_rag_system.search(rag_query)
            
            logger.debug(f"🔍 RAG: تم العثور على {len(results)} نتيجة ذات صلة")
            return results
            
        except Exception as e:
            logger.error(f"❌ خطأ في تحليل RAG: {e}")
            return []
    
    async def _perform_multimodal_analysis(self, media_path: str) -> List[Any]:
        """تنفيذ التحليل متعدد الوسائط"""
        try:
            # تحديد نوع الوسائط
            media_type = self._determine_media_type(media_path)
            
            # إنشاء طلب التحليل
            from .multimodal_analyzer import AnalysisType

            analysis_request = MediaAnalysisRequest(
                media_path=media_path,
                media_type=media_type,
                analysis_types=[
                    AnalysisType.OCR,
                    AnalysisType.SCENE_DESCRIPTION,
                    AnalysisType.OBJECT_DETECTION
                ],
                enhance_quality=True,
                extract_gaming_content=True
            )
            
            # تنفيذ التحليل
            results = await multimodal_analyzer.analyze_media(analysis_request)
            
            # فلترة النتائج عالية الثقة
            high_confidence_results = [
                r for r in results 
                if r.confidence >= self.config['multimodal_confidence_threshold']
            ]
            
            logger.debug(f"🖼️ Multimodal: تم تحليل {len(results)} عنصر، {len(high_confidence_results)} عالي الثقة")
            return high_confidence_results
            
        except Exception as e:
            logger.error(f"❌ خطأ في التحليل متعدد الوسائط: {e}")
            return []
    
    async def _get_memory_insights(self, content: str) -> Dict[str, Any]:
        """الحصول على رؤى الذاكرة"""
        try:
            # البحث في الذاكرة عن محتوى مشابه
            related_memories = await memory_system.smart_memory_retrieval(
                query=content,
                max_results=5,
                use_relations=True
            )
            
            # الحصول على رؤى عامة
            general_insights = await memory_system.get_memory_insights()
            
            insights = {
                'related_memories_count': len(related_memories),
                'related_memories': [
                    {
                        'content': mem.content[:200],
                        'type': mem.memory_type.value,
                        'confidence': mem.confidence_score,
                        'created_at': mem.created_at.isoformat()
                    }
                    for mem in related_memories
                ],
                'memory_health': general_insights.get('memory_health', {}),
                'trending_topics': general_insights.get('trending_topics', [])[:5]
            }
            
            logger.debug(f"🧠 Memory: تم العثور على {len(related_memories)} ذاكرة مرتبطة")
            return insights
            
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على رؤى الذاكرة: {e}")
            return {}
    
    async def _merge_analysis_results(self, original_content: str, rag_results: List[Any],
                                    multimodal_results: List[Any], memory_insights: Dict[str, Any]) -> Tuple[str, float]:
        """دمج نتائج التحليل"""
        try:
            enhanced_content = original_content
            confidence_scores = [0.5]  # ثقة أساسية
            
            # دمج نتائج RAG
            if rag_results:
                rag_content = []
                rag_confidences = []
                
                for result in rag_results:
                    rag_content.append(result.document.content[:100])
                    rag_confidences.append(result.final_score)
                
                if rag_content:
                    enhanced_content += f"\n\nمعلومات ذات صلة: {' | '.join(rag_content)}"
                    confidence_scores.append(sum(rag_confidences) / len(rag_confidences))
            
            # دمج نتائج التحليل متعدد الوسائط
            if multimodal_results:
                multimodal_content = []
                multimodal_confidences = []
                
                for result in multimodal_results:
                    multimodal_content.append(result.content)
                    multimodal_confidences.append(result.confidence)
                
                if multimodal_content:
                    enhanced_content += f"\n\nتحليل الوسائط: {' | '.join(multimodal_content)}"
                    confidence_scores.append(sum(multimodal_confidences) / len(multimodal_confidences))
            
            # دمج رؤى الذاكرة
            if memory_insights.get('related_memories'):
                memory_content = [mem['content'][:50] for mem in memory_insights['related_memories'][:2]]
                if memory_content:
                    enhanced_content += f"\n\nذكريات مرتبطة: {' | '.join(memory_content)}"
                    confidence_scores.append(0.7)  # ثقة متوسطة للذكريات
            
            # حساب الثقة الإجمالية
            final_confidence = sum(confidence_scores) / len(confidence_scores)
            
            return enhanced_content, min(1.0, final_confidence)
            
        except Exception as e:
            logger.error(f"❌ خطأ في دمج النتائج: {e}")
            return original_content, 0.5

    def _determine_media_type(self, media_path: str) -> MediaType:
        """تحديد نوع الوسائط من المسار"""
        try:
            extension = media_path.lower().split('.')[-1]

            image_extensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp']
            video_extensions = ['mp4', 'avi', 'mov', 'mkv', 'webm', 'flv']
            audio_extensions = ['mp3', 'wav', 'ogg', 'aac', 'm4a']

            if extension in image_extensions:
                return MediaType.IMAGE
            elif extension in video_extensions:
                return MediaType.VIDEO
            elif extension in audio_extensions:
                return MediaType.AUDIO
            else:
                return MediaType.IMAGE  # افتراضي

        except Exception as e:
            logger.error(f"❌ خطأ في تحديد نوع الوسائط: {e}")
            return MediaType.IMAGE

    async def _store_in_memory(self, content: str, content_type: str, confidence: float):
        """حفظ المحتوى في الذاكرة"""
        try:
            # تحديد نوع الذاكرة
            memory_type = MemoryType.ARTICLE if content_type == "article" else MemoryType.SEARCH_QUERY

            # تحديد الأهمية بناءً على الثقة
            if confidence >= 0.9:
                importance = MemoryImportance.HIGH
            elif confidence >= 0.7:
                importance = MemoryImportance.MEDIUM
            else:
                importance = MemoryImportance.LOW

            # إنشاء الذاكرة
            memory = Memory(
                id=memory_system._generate_memory_id(content, memory_type),
                content=content,
                memory_type=memory_type,
                importance=importance,
                metadata={
                    'content_type': content_type,
                    'confidence': confidence,
                    'source': 'enhanced_analysis',
                    'auto_stored': True
                }
            )

            # حفظ في النظام
            await memory_system.store_memory(memory)

            logger.debug(f"💾 تم حفظ المحتوى في الذاكرة بثقة {confidence:.2f}")

        except Exception as e:
            logger.error(f"❌ خطأ في حفظ الذاكرة: {e}")

    async def enhance_search_query(self, query: str) -> str:
        """تحسين استعلام البحث باستخدام الذاكرة والسياق"""
        try:
            logger.info(f"🔍 تحسين استعلام البحث: {query}")

            # البحث في الذاكرة عن استعلامات مشابهة
            related_memories = await memory_system.smart_memory_retrieval(
                query=query,
                max_results=3,
                use_relations=True
            )

            # استخراج كلمات مفتاحية إضافية
            additional_keywords = []
            for memory in related_memories:
                if memory.memory_type == MemoryType.SEARCH_QUERY:
                    # استخراج كلمات من الاستعلامات السابقة
                    words = memory.content.split()
                    for word in words:
                        if len(word) > 3 and word not in query:
                            additional_keywords.append(word)

            # إضافة كلمات مفتاحية من المواضيع الرائجة
            memory_insights = await memory_system.get_memory_insights()
            trending_topics = memory_insights.get('trending_topics', [])

            for topic, count in trending_topics[:3]:
                if topic not in query and len(additional_keywords) < 5:
                    additional_keywords.append(topic)

            # بناء الاستعلام المحسن
            enhanced_query = query
            if additional_keywords:
                unique_keywords = list(set(additional_keywords))[:3]
                enhanced_query += f" {' '.join(unique_keywords)}"

            logger.info(f"✅ استعلام محسن: {enhanced_query}")
            return enhanced_query

        except Exception as e:
            logger.error(f"❌ خطأ في تحسين الاستعلام: {e}")
            return query

    async def get_content_recommendations(self, current_content: str, max_recommendations: int = 5) -> List[Dict[str, Any]]:
        """الحصول على توصيات المحتوى بناءً على المحتوى الحالي"""
        try:
            logger.info("💡 توليد توصيات المحتوى...")

            recommendations = []

            # 1. توصيات من RAG
            if advanced_rag_system.enabled:
                rag_query = RAGQuery(
                    query=current_content,
                    mode=RAGMode.KNOWLEDGE_GRAPH,
                    content_types=[ContentType.TEXT],
                    max_results=max_recommendations
                )

                rag_results = await advanced_rag_system.search(rag_query)

                for result in rag_results:
                    recommendations.append({
                        'type': 'rag_related',
                        'content': result.document.content[:200],
                        'confidence': result.final_score,
                        'source': 'knowledge_graph'
                    })

            # 2. توصيات من الذاكرة
            related_memories = await memory_system.smart_memory_retrieval(
                query=current_content,
                max_results=max_recommendations,
                use_relations=True
            )

            for memory in related_memories:
                recommendations.append({
                    'type': 'memory_related',
                    'content': memory.content[:200],
                    'confidence': memory.confidence_score,
                    'source': 'memory_system',
                    'memory_type': memory.memory_type.value
                })

            # 3. ترتيب التوصيات حسب الثقة
            recommendations.sort(key=lambda x: x['confidence'], reverse=True)

            # 4. إزالة التكرار والحد من العدد
            unique_recommendations = []
            seen_content = set()

            for rec in recommendations:
                content_hash = hash(rec['content'][:100])
                if content_hash not in seen_content:
                    seen_content.add(content_hash)
                    unique_recommendations.append(rec)

                    if len(unique_recommendations) >= max_recommendations:
                        break

            logger.info(f"✅ تم توليد {len(unique_recommendations)} توصية")
            return unique_recommendations

        except Exception as e:
            logger.error(f"❌ خطأ في توليد التوصيات: {e}")
            return []

    async def get_enhancement_stats(self) -> Dict[str, Any]:
        """الحصول على إحصائيات التحسينات"""
        try:
            # إحصائيات الأنظمة الفرعية
            rag_stats = await advanced_rag_system.get_stats() if advanced_rag_system.enabled else {}
            multimodal_stats = await multimodal_analyzer.get_stats() if multimodal_analyzer.enabled else {}
            memory_stats = await memory_system.get_memory_insights() if memory_system else {}

            # حساب معدل النجاح
            total_analyses = self.enhancement_stats['total_enhanced_analyses']
            success_rate = 100 if total_analyses == 0 else (
                (total_analyses - self.enhancement_stats.get('failed_analyses', 0)) / total_analyses * 100
            )

            return {
                'integration_stats': {
                    **self.enhancement_stats,
                    'success_rate': success_rate,
                    'enhancement_level': self.enhancement_level.value,
                    'enabled_features': {
                        'rag': self.config['enable_rag'] and advanced_rag_system.enabled,
                        'multimodal': self.config['enable_multimodal'] and multimodal_analyzer.enabled,
                        'advanced_memory': self.config['enable_advanced_memory']
                    }
                },
                'rag_system': rag_stats,
                'multimodal_system': multimodal_stats,
                'memory_system': memory_stats
            }

        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على الإحصائيات: {e}")
            return {}

    async def optimize_all_systems(self):
        """تحسين جميع الأنظمة"""
        try:
            logger.info("⚡ بدء تحسين جميع الأنظمة...")

            # تحسين نظام الذاكرة
            if memory_system:
                await memory_system.optimize_memory_performance()

            # مسح التخزين المؤقت للأنظمة
            if advanced_rag_system.enabled:
                await advanced_rag_system.clear_cache()

            if multimodal_analyzer.enabled:
                await multimodal_analyzer.clear_temp_files()

            logger.info("✅ تم تحسين جميع الأنظمة بنجاح")

        except Exception as e:
            logger.error(f"❌ خطأ في تحسين الأنظمة: {e}")

# إنشاء مثيل عام
enhanced_agent = EnhancedAgentIntegration()
