#!/usr/bin/env python3
# نقطة دخول بديلة لتشغيل البوت
import sys
import os
import subprocess

def check_requirements():
    """التحقق من المتطلبات الأساسية"""
    print("🔍 فحص المتطلبات...")
    
    # التحقق من Python
    if sys.version_info < (3, 9):
        print("❌ يتطلب Python 3.9 أو أحدث")
        return False
    
    # التحقق من pip
    try:
        import pip
    except ImportError:
        print("❌ pip غير مثبت")
        return False
    
    print("✅ متطلبات النظام مستوفاة")
    return True

def install_dependencies():
    """تثبيت المتطلبات"""
    print("📦 تثبيت المتطلبات...")
    
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", 
            "-r", "requirements.txt", "--quiet"
        ])
        print("✅ تم تثبيت المتطلبات بنجاح")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في تثبيت المتطلبات: {e}")
        return False

def check_configuration():
    """التحقق من وجود التكوين"""
    config_files = [".env", "config/bot_config.json"]
    
    for config_file in config_files:
        if os.path.exists(config_file):
            print(f"✅ تم العثور على تكوين: {config_file}")
            return True
    
    print("⚠️ لم يتم العثور على تكوين البوت")
    return False

def run_setup():
    """تشغيل الإعداد التفاعلي"""
    print("🔧 تشغيل الإعداد التفاعلي...")
    
    try:
        subprocess.check_call([sys.executable, "setup_bot.py"])
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في الإعداد: {e}")
        return False

def run_bot():
    """تشغيل البوت الرئيسي"""
    print("🚀 تشغيل وكيل أخبار ماين كرافت...")
    
    try:
        subprocess.check_call([sys.executable, "main.py"])
    except subprocess.CalledProcessError as e:
        print(f"❌ توقف البوت بشكل غير متوقع: {e}")
        return False
    except KeyboardInterrupt:
        print("\n⌨️ تم إيقاف البوت بواسطة المستخدم")
        return True
    
    return True

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🎮 وكيل أخبار ماين كرافت الاحترافي")
    print("=" * 60)
    
    # فحص المتطلبات
    if not check_requirements():
        sys.exit(1)
    
    # تثبيت المتطلبات
    if not install_dependencies():
        sys.exit(1)
    
    # التحقق من التكوين
    if not check_configuration():
        print("🔧 بدء إعداد البوت...")
        if not run_setup():
            sys.exit(1)
    
    # تشغيل البوت
    success = run_bot()
    
    if success:
        print("✅ تم إنهاء البوت بنجاح")
    else:
        print("❌ توقف البوت بسبب خطأ")
        sys.exit(1)

if __name__ == "__main__":
    main()
