{"app_info": {"name": "GitHub Uploader Tool", "version": "1.0.0", "description": "أداة شاملة لرفع المشاريع على GitHub بشكل تلقائي", "author": "GitHub Uploader Team", "license": "MIT"}, "github_settings": {"api_base_url": "https://api.github.com", "api_version": "application/vnd.github.v3+json", "default_branch": "main", "timeout": 30}, "project_settings": {"auto_create_gitignore": true, "auto_create_readme": true, "auto_detect_project_type": true, "default_private": false, "default_commit_message": "Initial commit"}, "supported_project_types": {"python": {"extensions": [".py"], "config_files": ["requirements.txt", "setup.py", "pyproject.toml"], "description": "Python projects with automatic dependency detection"}, "javascript": {"extensions": [".js", ".jsx", ".ts", ".tsx"], "config_files": ["package.json", "yarn.lock", "package-lock.json"], "description": "JavaScript/TypeScript projects with npm/yarn support"}, "java": {"extensions": [".java"], "config_files": ["pom.xml", "build.gradle", "build.xml"], "description": "Java projects with Maven/Gradle support"}, "cpp": {"extensions": [".cpp", ".hpp", ".cc", ".h"], "config_files": ["CMakeLists.txt", "<PERSON><PERSON><PERSON>"], "description": "C++ projects with CMake/Make support"}, "csharp": {"extensions": [".cs"], "config_files": [".c<PERSON><PERSON>j", ".sln"], "description": "C# projects with .NET support"}, "go": {"extensions": [".go"], "config_files": ["go.mod", "go.sum"], "description": "Go projects with module support"}, "rust": {"extensions": [".rs"], "config_files": ["Cargo.toml", "Cargo.lock"], "description": "Rust projects with Cargo support"}, "php": {"extensions": [".php"], "config_files": ["composer.json", "composer.lock"], "description": "PHP projects with Composer support"}, "ruby": {"extensions": [".rb"], "config_files": ["Gem<PERSON>le", "Gemfile.lock"], "description": "Ruby projects with Bundler support"}}, "ui_settings": {"language": "ar", "show_progress": true, "colored_output": true, "confirm_actions": true}, "security_settings": {"save_token": false, "encrypt_token": true, "token_file_path": "~/.github-uploader/token.txt"}, "logging": {"enabled": true, "level": "INFO", "file_path": "~/.github-uploader/uploader.log", "max_file_size": "10MB", "backup_count": 5}, "templates": {"readme": {"include_badges": true, "include_installation": true, "include_usage": true, "include_contributing": true, "include_license": true, "language": "ar"}, "gitignore": {"include_os_files": true, "include_ide_files": true, "include_logs": true, "custom_patterns": []}}, "advanced_features": {"batch_upload": true, "auto_release": false, "create_issues": false, "setup_ci_cd": false, "add_collaborators": false}}