#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل سريع للوكيل مع الواجهة الويب
Quick Start for Gaming News Agent with Web Interface
"""

import os
import sys
import time
import threading
import webbrowser
from datetime import datetime

# إضافة المسار الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def check_requirements():
    """فحص المتطلبات الأساسية"""
    print("🔍 فحص المتطلبات...")
    
    required_packages = [
        'flask',
        'flask_cors',
        'requests',
        'beautifulsoup4'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"   ✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"   ❌ {package} - مفقود")
    
    if missing_packages:
        print(f"\n📦 تثبيت الحزم المفقودة...")
        import subprocess
        for package in missing_packages:
            try:
                subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
                print(f"   ✅ تم تثبيت {package}")
            except subprocess.CalledProcessError:
                print(f"   ❌ فشل في تثبيت {package}")
                return False
    
    return True

def setup_environment():
    """إعداد البيئة المحلية"""
    print("⚙️ إعداد البيئة...")
    
    # إنشاء المجلدات المطلوبة
    required_dirs = ['data', 'logs', 'cache', 'config', 'images', 'temp']
    for dir_name in required_dirs:
        if not os.path.exists(dir_name):
            os.makedirs(dir_name)
            print(f"   📁 تم إنشاء مجلد {dir_name}")
        else:
            print(f"   ✅ مجلد {dir_name} موجود")
    
    # إعداد متغيرات البيئة للتشغيل المحلي
    local_env = {
        'DEBUG': 'true',
        'LOG_LEVEL': 'DEBUG',
        'HOST': 'localhost',
        'PORT': '5000'
    }
    
    for key, value in local_env.items():
        if key not in os.environ:
            os.environ[key] = value
    
    print("✅ تم إعداد البيئة")

def start_web_server():
    """تشغيل خادم الواجهة الويب"""
    try:
        print("🌐 بدء تشغيل خادم الواجهة الويب...")
        
        # استيراد خادم الويب
        from web_api import run_server
        
        # تشغيل الخادم
        run_server(host='localhost', port=5000, debug=True)
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل خادم الواجهة: {e}")

def start_agent():
    """تشغيل الوكيل الرئيسي"""
    try:
        print("🤖 بدء تشغيل الوكيل...")
        
        # استيراد الوكيل
        import main
        
        # تشغيل الوكيل
        import asyncio
        asyncio.run(main.main())
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل الوكيل: {e}")

def open_browser():
    """فتح المتصفح تلقائياً"""
    time.sleep(3)  # انتظار بدء الخادم
    
    url = "http://localhost:5000"
    try:
        webbrowser.open(url)
        print(f"🌐 تم فتح المتصفح على: {url}")
    except Exception as e:
        print(f"⚠️ لم يتمكن من فتح المتصفح تلقائياً: {e}")
        print(f"📋 يرجى فتح المتصفح يدوياً والانتقال إلى: {url}")

def show_welcome():
    """عرض رسالة الترحيب"""
    print("=" * 60)
    print("🎮 وكيل أخبار الألعاب - التشغيل السريع")
    print("=" * 60)
    print("📅 التاريخ:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    print("🐍 Python:", sys.version.split()[0])
    print("📁 المجلد:", os.getcwd())
    print("=" * 60)

def show_instructions():
    """عرض التعليمات"""
    print("\n📋 التعليمات:")
    print("1. 🌐 الواجهة الويب: http://localhost:5000")
    print("2. 🔧 لوحة التحكم: http://localhost:5000")
    print("3. 📊 حالة النظام: http://localhost:5000/health")
    print("4. ⌨️ للإيقاف: اضغط Ctrl+C")
    print("=" * 60)

def main():
    """الدالة الرئيسية"""
    try:
        # عرض رسالة الترحيب
        show_welcome()
        
        # فحص المتطلبات
        if not check_requirements():
            print("❌ فشل في فحص المتطلبات")
            return
        
        # إعداد البيئة
        setup_environment()
        
        # عرض التعليمات
        show_instructions()
        
        # بدء خادم الواجهة الويب في thread منفصل
        web_thread = threading.Thread(target=start_web_server, daemon=True)
        web_thread.start()
        
        # فتح المتصفح في thread منفصل
        browser_thread = threading.Thread(target=open_browser, daemon=True)
        browser_thread.start()
        
        # انتظار قصير لبدء الخادم
        time.sleep(2)
        
        print("🚀 النظام جاهز!")
        print("🌐 الواجهة الويب تعمل على: http://localhost:5000")
        print("⌨️ اضغط Ctrl+C للإيقاف")
        
        # تشغيل الوكيل في الـ thread الرئيسي
        start_agent()
        
    except KeyboardInterrupt:
        print("\n⌨️ تم الإيقاف بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        import traceback
        traceback.print_exc()
    finally:
        print("\n👋 تم إنهاء النظام")

if __name__ == "__main__":
    main()
