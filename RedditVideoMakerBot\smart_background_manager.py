#!/usr/bin/env python3
"""
مدير ذكي لمقاطع الخلفية - يستخدم الملفات الموجودة أولاً
"""

import os
import json
from pathlib import Path
from typing import List, Dict, Tuple, Optional
from utils.console import print_step, print_substep

class SmartBackgroundManager:
    """مدير ذكي لمقاطع الخلفية"""
    
    def __init__(self):
        self.video_dir = Path("assets/backgrounds/video")
        self.audio_dir = Path("assets/backgrounds/audio")
        self.video_dir.mkdir(parents=True, exist_ok=True)
        self.audio_dir.mkdir(parents=True, exist_ok=True)
        
        # أنواع الملفات المدعومة
        self.video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.webm']
        self.audio_extensions = ['.mp3', '.wav', '.m4a', '.aac', '.ogg']
    
    def scan_existing_files(self) -> Dict[str, List[str]]:
        """فحص الملفات الموجودة في مجلدات الخلفية"""
        existing_files = {
            'video': [],
            'audio': []
        }
        
        # فحص ملفات الفيديو
        for file_path in self.video_dir.glob('*'):
            if file_path.is_file() and file_path.suffix.lower() in self.video_extensions:
                existing_files['video'].append(file_path.name)
        
        # فحص ملفات الصوت
        for file_path in self.audio_dir.glob('*'):
            if file_path.is_file() and file_path.suffix.lower() in self.audio_extensions:
                existing_files['audio'].append(file_path.name)
        
        return existing_files
    
    def find_parkour_videos(self) -> List[str]:
        """البحث عن مقاطع الباركور الموجودة"""
        parkour_keywords = [
            'parkour', 'minecraft', 'gta', 'rocket', 'surf', 'cluster',
            'motor', 'bike', 'stunt', 'race', 'fall-guys', 'simple'
        ]
        
        existing_videos = []
        
        for file_path in self.video_dir.glob('*'):
            if file_path.is_file() and file_path.suffix.lower() in self.video_extensions:
                filename_lower = file_path.name.lower()
                
                # فحص إذا كان الملف يحتوي على كلمات مفتاحية للباركور
                for keyword in parkour_keywords:
                    if keyword in filename_lower:
                        existing_videos.append(file_path.name)
                        break
        
        return existing_videos
    
    def get_best_available_video(self, preferred_type: str = None) -> Optional[str]:
        """الحصول على أفضل فيديو متاح"""
        parkour_videos = self.find_parkour_videos()
        
        if not parkour_videos:
            print_substep("لم يتم العثور على مقاطع باركور محلية", style="yellow")
            return None
        
        # إذا تم تحديد نوع مفضل، ابحث عنه أولاً
        if preferred_type:
            for video in parkour_videos:
                if preferred_type.lower() in video.lower():
                    print_substep(f"تم العثور على الفيديو المطلوب: {video}", style="green")
                    return video
        
        # إذا لم يتم العثور على النوع المفضل، استخدم أول فيديو متاح
        selected_video = parkour_videos[0]
        print_substep(f"استخدام فيديو متاح: {selected_video}", style="green")
        return selected_video
    
    def create_local_background_config(self, video_filename: str, audio_filename: str = None) -> Dict:
        """إنشاء إعدادات خلفية محلية"""
        config = {
            'video': [
                "local_file",  # لا يحتاج تحميل
                video_filename,
                "Local File",
                "center"
            ]
        }
        
        if audio_filename:
            config['audio'] = [
                "local_file",
                audio_filename,
                "Local File"
            ]
        else:
            # استخدام صوت صامت إذا لم يتم العثور على صوت
            config['audio'] = [
                "local_file",
                "silent.mp3",
                "Local Silent"
            ]
        
        return config
    
    def update_background_videos_json(self):
        """تحديث ملف background_videos.json بالملفات المحلية"""
        json_path = Path("utils/background_videos.json")
        
        # قراءة الملف الحالي
        if json_path.exists():
            with open(json_path, 'r', encoding='utf-8') as f:
                backgrounds = json.load(f)
        else:
            backgrounds = {"__comment": "Supported Backgrounds. Can add/remove background video here..."}
        
        # إضافة الملفات المحلية
        parkour_videos = self.find_parkour_videos()
        
        for video in parkour_videos:
            # إنشاء اسم مفتاح بناءً على اسم الملف
            key_name = video.replace('.mp4', '').replace('.avi', '').replace('.mov', '')
            key_name = key_name.replace('-', '_').replace(' ', '_').lower()
            
            # إضافة إلى القائمة إذا لم يكن موجوداً
            if key_name not in backgrounds:
                backgrounds[key_name] = [
                    "local_file",
                    video,
                    "Local File",
                    "center"
                ]
        
        # حفظ التحديث
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(backgrounds, f, indent=4, ensure_ascii=False)
        
        print_substep(f"تم تحديث قائمة الخلفيات بـ {len(parkour_videos)} ملف محلي", style="green")
    
    def get_smart_background_choice(self, requested_type: str = "minecraft") -> Tuple[str, bool]:
        """اختيار ذكي للخلفية - محلي أولاً، ثم تحميل"""
        print_step("🔍 البحث عن مقاطع خلفية متاحة...")
        
        # فحص الملفات المحلية أولاً
        parkour_videos = self.find_parkour_videos()
        
        if parkour_videos:
            print_substep(f"تم العثور على {len(parkour_videos)} مقطع محلي", style="green")
            
            # البحث عن النوع المطلوب
            best_video = self.get_best_available_video(requested_type)
            
            if best_video:
                # تحديث قائمة الخلفيات
                self.update_background_videos_json()
                
                # إرجاع اسم الملف بدون امتداد كمفتاح
                key_name = best_video.replace('.mp4', '').replace('.avi', '').replace('.mov', '')
                key_name = key_name.replace('-', '_').replace(' ', '_').lower()
                
                return key_name, False  # False = لا يحتاج تحميل
        
        # إذا لم يتم العثور على ملفات محلية، استخدم التحميل
        print_substep("لم يتم العثور على مقاطع محلية، سيتم التحميل...", style="yellow")
        return requested_type, True  # True = يحتاج تحميل
    
    def list_available_backgrounds(self):
        """عرض قائمة بالخلفيات المتاحة"""
        print_step("📋 الخلفيات المتاحة:")
        
        existing_files = self.scan_existing_files()
        
        print_substep("🎬 ملفات الفيديو المحلية:")
        if existing_files['video']:
            for video in existing_files['video']:
                size = (self.video_dir / video).stat().st_size / (1024 * 1024)
                print_substep(f"  • {video} ({size:.1f} MB)", style="green")
        else:
            print_substep("  لا توجد ملفات فيديو محلية", style="yellow")
        
        print_substep("🎵 ملفات الصوت المحلية:")
        if existing_files['audio']:
            for audio in existing_files['audio']:
                size = (self.audio_dir / audio).stat().st_size / (1024 * 1024)
                print_substep(f"  • {audio} ({size:.1f} MB)", style="green")
        else:
            print_substep("  لا توجد ملفات صوت محلية", style="yellow")

# إنشاء مثيل عام
smart_bg_manager = SmartBackgroundManager()

def get_smart_background_config(requested_type: str = "minecraft") -> Tuple[str, bool]:
    """دالة مساعدة للحصول على إعدادات الخلفية الذكية"""
    return smart_bg_manager.get_smart_background_choice(requested_type)

def list_backgrounds():
    """دالة مساعدة لعرض الخلفيات المتاحة"""
    smart_bg_manager.list_available_backgrounds()

if __name__ == "__main__":
    # اختبار النظام
    print("🧪 اختبار مدير الخلفيات الذكي")
    print("=" * 40)
    
    manager = SmartBackgroundManager()
    manager.list_available_backgrounds()
    
    # اختبار الاختيار الذكي
    choice, needs_download = manager.get_smart_background_choice("minecraft")
    print(f"\nالاختيار: {choice}")
    print(f"يحتاج تحميل: {'نعم' if needs_download else 'لا'}")
