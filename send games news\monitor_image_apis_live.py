#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مراقب مباشر لـ APIs الصور - تحليل الأداء الحالي
"""

import asyncio
import sys
import os
import json
from datetime import datetime
import time

# إضافة المسار الحالي لـ Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.logger import logger

class LiveImageAPIMonitor:
    """مراقب مباشر لأداء APIs الصور"""
    
    def __init__(self):
        self.stats_file = "cache/daily_image_stats.json"
        self.last_check = None
        
    def load_current_stats(self) -> dict:
        """تحميل إحصائيات اليوم الحالي"""
        try:
            if os.path.exists(self.stats_file):
                with open(self.stats_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return {}
        except Exception as e:
            print(f"خطأ في تحميل الإحصائيات: {e}")
            return {}
    
    def analyze_api_performance(self, stats: dict):
        """تحليل أداء APIs"""
        print("🔌 تحليل أداء APIs:")
        print("-" * 25)
        
        api_calls = stats.get('api_calls', {})
        api_attempts = stats.get('api_attempts', {})
        
        for api_name in ['freepik', 'fluxai']:
            calls = api_calls.get(api_name, 0)
            attempts = api_attempts.get(api_name, {'success': 0, 'failure': 0})
            
            total_attempts = attempts['success'] + attempts['failure']
            success_rate = (attempts['success'] / max(total_attempts, 1)) * 100
            
            print(f"\n📊 {api_name.upper()}:")
            print(f"   استدعاءات: {calls}")
            print(f"   محاولات ناجحة: {attempts['success']}")
            print(f"   محاولات فاشلة: {attempts['failure']}")
            print(f"   معدل النجاح: {success_rate:.1f}%")
            
            # تقييم الأداء
            if success_rate >= 80:
                print(f"   الحالة: 🟢 ممتاز")
            elif success_rate >= 50:
                print(f"   الحالة: 🟡 متوسط")
            else:
                print(f"   الحالة: 🔴 ضعيف")
    
    def analyze_fallback_usage(self, stats: dict):
        """تحليل استخدام الصور الاحتياطية"""
        print("\n🖼️ تحليل الصور الاحتياطية:")
        print("-" * 30)
        
        images_generated = stats.get('images_generated', 0)
        api_calls_total = stats.get('api_calls', {}).get('total', 0)
        
        # تقدير استخدام الصور الاحتياطية
        fallback_usage = max(0, images_generated - api_calls_total)
        fallback_rate = (fallback_usage / max(images_generated, 1)) * 100
        
        print(f"إجمالي الصور: {images_generated}")
        print(f"استدعاءات API ناجحة: {api_calls_total}")
        print(f"صور احتياطية (تقديري): {fallback_usage}")
        print(f"معدل الاعتماد على الاحتياطي: {fallback_rate:.1f}%")
        
        if fallback_rate > 70:
            print("⚠️ اعتماد عالي على الصور الاحتياطية - فحص APIs")
        elif fallback_rate > 30:
            print("🟡 اعتماد متوسط على الصور الاحتياطية")
        else:
            print("✅ اعتماد منخفض على الصور الاحتياطية")
    
    def show_quota_status(self, stats: dict):
        """عرض حالة الحصة"""
        print("\n📊 حالة الحصة اليومية:")
        print("-" * 25)
        
        images_generated = stats.get('images_generated', 0)
        max_daily = 50  # الحد الافتراضي
        
        remaining = max_daily - images_generated
        usage_percentage = (images_generated / max_daily) * 100
        
        print(f"المُستخدم: {images_generated}/{max_daily} صورة")
        print(f"المتبقي: {remaining} صورة")
        print(f"نسبة الاستخدام: {usage_percentage:.1f}%")
        
        # شريط تقدم بسيط
        bar_length = 20
        filled_length = int(bar_length * usage_percentage / 100)
        bar = '█' * filled_length + '░' * (bar_length - filled_length)
        print(f"التقدم: [{bar}] {usage_percentage:.1f}%")
        
        if usage_percentage >= 90:
            print("🔴 تحذير: اقتراب من الحد الأقصى")
        elif usage_percentage >= 70:
            print("🟡 تنبيه: استخدام عالي")
        else:
            print("🟢 استخدام طبيعي")
    
    def show_recommendations(self, stats: dict):
        """عرض توصيات التحسين"""
        print("\n💡 توصيات التحسين:")
        print("-" * 20)
        
        recommendations = []
        
        # تحليل أداء APIs
        api_attempts = stats.get('api_attempts', {})
        
        for api_name, attempts in api_attempts.items():
            total = attempts['success'] + attempts['failure']
            if total > 0:
                success_rate = (attempts['success'] / total) * 100
                
                if success_rate < 50:
                    recommendations.append(f"🔧 فحص إعدادات {api_name} - معدل نجاح منخفض ({success_rate:.1f}%)")
                elif success_rate < 80:
                    recommendations.append(f"⚠️ تحسين {api_name} - معدل نجاح متوسط ({success_rate:.1f}%)")
        
        # تحليل استخدام الحصة
        images_generated = stats.get('images_generated', 0)
        usage_rate = (images_generated / 50) * 100
        
        if usage_rate > 80:
            recommendations.append("📈 النظر في زيادة الحصة اليومية")
        elif usage_rate < 20:
            recommendations.append("📉 يمكن تقليل الحصة اليومية لتوفير الموارد")
        
        # تحليل التخزين المؤقت
        cache_hit_rate = stats.get('cache_hit_rate', 0)
        if cache_hit_rate < 20:
            recommendations.append("🗄️ تحسين نظام التخزين المؤقت")
        
        # عرض التوصيات
        if recommendations:
            for i, rec in enumerate(recommendations, 1):
                print(f"   {i}. {rec}")
        else:
            print("   ✅ النظام يعمل بكفاءة جيدة")
    
    def monitor_live(self, duration_minutes: int = 5):
        """مراقبة مباشرة لفترة محددة"""
        print(f"🔍 بدء المراقبة المباشرة لمدة {duration_minutes} دقائق...")
        print("=" * 50)
        
        start_time = time.time()
        end_time = start_time + (duration_minutes * 60)
        
        last_stats = None
        
        while time.time() < end_time:
            try:
                current_stats = self.load_current_stats()
                
                if current_stats != last_stats:
                    print(f"\n⏰ {datetime.now().strftime('%H:%M:%S')} - تحديث الإحصائيات")
                    print("-" * 30)
                    
                    # عرض الإحصائيات الأساسية
                    images_today = current_stats.get('images_generated', 0)
                    articles_processed = current_stats.get('articles_processed', 0)
                    
                    print(f"📊 الصور اليوم: {images_today}/50")
                    print(f"📰 المقالات المُعالجة: {articles_processed}")
                    
                    if last_stats:
                        # حساب التغيير
                        images_diff = images_today - last_stats.get('images_generated', 0)
                        articles_diff = articles_processed - last_stats.get('articles_processed', 0)
                        
                        if images_diff > 0:
                            print(f"🆕 صور جديدة: +{images_diff}")
                        if articles_diff > 0:
                            print(f"🆕 مقالات جديدة: +{articles_diff}")
                    
                    last_stats = current_stats.copy()
                
                # انتظار 30 ثانية قبل الفحص التالي
                time.sleep(30)
                
            except KeyboardInterrupt:
                print("\n⏹️ تم إيقاف المراقبة بواسطة المستخدم")
                break
            except Exception as e:
                print(f"❌ خطأ في المراقبة: {e}")
                time.sleep(30)
        
        print(f"\n✅ انتهت المراقبة المباشرة")

def main():
    """الدالة الرئيسية"""
    print("🔍 مراقب APIs الصور المباشر")
    print("=" * 35)
    
    monitor = LiveImageAPIMonitor()
    
    try:
        # تحليل الحالة الحالية
        current_stats = monitor.load_current_stats()
        
        if not current_stats:
            print("❌ لا توجد إحصائيات متاحة")
            print("💡 تأكد من تشغيل البوت أولاً لإنشاء الإحصائيات")
            return
        
        print(f"📅 تاريخ الإحصائيات: {current_stats.get('date', 'غير محدد')}")
        
        # عرض حالة الحصة
        monitor.show_quota_status(current_stats)
        
        # تحليل أداء APIs
        monitor.analyze_api_performance(current_stats)
        
        # تحليل الصور الاحتياطية
        monitor.analyze_fallback_usage(current_stats)
        
        # عرض التوصيات
        monitor.show_recommendations(current_stats)
        
        # سؤال المستخدم عن المراقبة المباشرة
        print("\n" + "=" * 35)
        response = input("هل تريد بدء المراقبة المباشرة؟ (y/n): ").lower().strip()
        
        if response in ['y', 'yes', 'نعم']:
            duration = input("كم دقيقة؟ (افتراضي: 5): ").strip()
            try:
                duration = int(duration) if duration else 5
            except ValueError:
                duration = 5
            
            monitor.monitor_live(duration)
        
        print("\n✅ تم إنجاز المراقبة بنجاح!")
        
    except Exception as e:
        print(f"\n❌ خطأ في المراقبة: {e}")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف المراقب بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
