// Discord Music Bot
// بوت Discord للموسيقى

const { Client, GatewayIntentBits, SlashCommandBuilder } = require('discord.js');
const { joinVoiceChannel, createAudioPlayer, createAudioResource } = require('@discordjs/voice');
const ytdl = require('ytdl-core');

class DiscordMusicBot {
    constructor(token) {
        this.token = token;
        this.client = new Client({
            intents: [
                GatewayIntentBits.Guilds,
                GatewayIntentBits.GuildMessages,
                GatewayIntentBits.GuildVoiceStates,
                GatewayIntentBits.MessageContent
            ]
        });
        
        this.queue = new Map();
        this.setupEvents();
        this.setupCommands();
    }
    
    setupEvents() {
        this.client.once('ready', () => {
            console.log(`🎵 ${this.client.user.tag} جاهز للعمل!`);
        });
        
        this.client.on('interactionCreate', async interaction => {
            if (!interaction.isChatInputCommand()) return;
            
            const { commandName } = interaction;
            
            switch (commandName) {
                case 'play':
                    await this.playMusic(interaction);
                    break;
                case 'stop':
                    await this.stopMusic(interaction);
                    break;
                case 'skip':
                    await this.skipMusic(interaction);
                    break;
            }
        });
    }
    
    setupCommands() {
        const commands = [
            new SlashCommandBuilder()
                .setName('play')
                .setDescription('تشغيل موسيقى من YouTube')
                .addStringOption(option =>
                    option.setName('url')
                        .setDescription('رابط YouTube')
                        .setRequired(true)
                ),
            new SlashCommandBuilder()
                .setName('stop')
                .setDescription('إيقاف الموسيقى'),
            new SlashCommandBuilder()
                .setName('skip')
                .setDescription('تخطي الأغنية الحالية')
        ];
        
        // تسجيل الأوامر...
    }
    
    async playMusic(interaction) {
        const url = interaction.options.getString('url');
        const voiceChannel = interaction.member.voice.channel;
        
        if (!voiceChannel) {
            return interaction.reply('❌ يجب أن تكون في قناة صوتية!');
        }
        
        try {
            const connection = joinVoiceChannel({
                channelId: voiceChannel.id,
                guildId: interaction.guild.id,
                adapterCreator: interaction.guild.voiceAdapterCreator,
            });
            
            const stream = ytdl(url, { filter: 'audioonly' });
            const resource = createAudioResource(stream);
            const player = createAudioPlayer();
            
            player.play(resource);
            connection.subscribe(player);
            
            await interaction.reply(`🎵 بدء تشغيل: ${url}`);
        } catch (error) {
            console.error('خطأ في تشغيل الموسيقى:', error);
            await interaction.reply('❌ حدث خطأ في تشغيل الموسيقى');
        }
    }
    
    async stopMusic(interaction) {
        // إيقاف الموسيقى
        await interaction.reply('⏹️ تم إيقاف الموسيقى');
    }
    
    async skipMusic(interaction) {
        // تخطي الأغنية
        await interaction.reply('⏭️ تم تخطي الأغنية');
    }
    
    start() {
        this.client.login(this.token);
    }
}

// تشغيل البوت
const bot = new DiscordMusicBot(process.env.DISCORD_TOKEN);
bot.start();

module.exports = DiscordMusicBot;
