#!/usr/bin/env python3
# تكامل النظام المحسن مع الملف الرئيسي
import asyncio
import sys
import os
from typing import List, Dict

# إضافة المسار الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.logger import logger
from modules.enhanced_search_integration import enhanced_search

class EnhancedContentCollector:
    """جامع المحتوى المحسن"""
    
    def __init__(self):
        self.fallback_enabled = True
        
    async def collect_content_enhanced(self) -> List[Dict]:
        """جمع المحتوى باستخدام النظام المحسن"""
        all_content = []
        
        try:
            logger.info("🧠 بدء جمع المحتوى باستخدام النظام المحسن...")
            
            # كلمات مفتاحية محسنة للبحث العميق
            advanced_keywords = [
                'gaming news today',
                'video game updates', 
                'new game releases',
                'gaming industry news',
                'latest gaming announcements',
                'breaking gaming news',
                'indie game updates',
                'AAA games 2025'
            ]

            enhanced_content = []
            
            # البحث باستخدام النظام المحسن
            for i, keyword in enumerate(advanced_keywords[:4]):  # أفضل 4 كلمات مفتاحية
                try:
                    logger.info(f"🔍 البحث المحسن {i+1}/4: '{keyword}'")
                    
                    # تحديد الأولوية بناءً على الكلمة المفتاحية
                    priority = self._determine_keyword_priority(keyword)
                    
                    # استخدام النظام المحسن
                    keyword_results = await enhanced_search.enhanced_search(
                        query=keyword,
                        max_results=8,
                        search_type="gaming_news",
                        priority=priority
                    )
                    
                    if keyword_results:
                        enhanced_content.extend(keyword_results)
                        logger.info(f"✅ النظام المحسن: {len(keyword_results)} نتيجة عالية الجودة لـ '{keyword}'")
                    else:
                        logger.info(f"📭 لم يتم العثور على نتائج لـ '{keyword}'")
                    
                    # تأخير ذكي بين الطلبات
                    await asyncio.sleep(2)
                    
                except Exception as e:
                    logger.warning(f"⚠️ فشل في البحث المحسن عن '{keyword}': {e}")
                    continue

            if enhanced_content:
                logger.info(f"🚀 النظام المحسن: تم العثور على {len(enhanced_content)} مقال عالي الجودة إجمالي")
                all_content.extend(enhanced_content)
                
                # إذا حصلنا على نتائج كافية، لا نحتاج للطريقة الاحتياطية
                if len(enhanced_content) >= 15:
                    logger.info("✅ تم الحصول على نتائج كافية من النظام المحسن")
                    return await self._filter_and_enhance_content(all_content)
            
            # إذا لم نحصل على نتائج كافية، استخدم البحث الاحتياطي
            if len(enhanced_content) < 10:
                logger.info("🔄 تفعيل البحث الاحتياطي للحصول على المزيد من النتائج...")
                backup_content = await self._backup_search(advanced_keywords[4:])
                all_content.extend(backup_content)
            
            # تصفية وتحسين النتائج النهائية
            final_content = await self._filter_and_enhance_content(all_content)
            
            logger.info(f"📊 إجمالي المحتوى المجمع: {len(final_content)} مقال عالي الجودة")
            return final_content
            
        except Exception as e:
            logger.error(f"❌ فشل في جمع المحتوى المحسن: {e}")
            
            # العودة للطريقة التقليدية في حالة الفشل
            if self.fallback_enabled:
                return await self._traditional_fallback()
            
            return []
    
    def _determine_keyword_priority(self, keyword: str) -> str:
        """تحديد أولوية الكلمة المفتاحية"""
        high_priority_keywords = ['breaking', 'urgent', 'exclusive', 'announcement']
        medium_priority_keywords = ['latest', 'new', 'update', 'release']
        
        keyword_lower = keyword.lower()
        
        if any(hp_keyword in keyword_lower for hp_keyword in high_priority_keywords):
            return "premium"
        elif any(mp_keyword in keyword_lower for mp_keyword in medium_priority_keywords):
            return "low_cost"
        else:
            return "auto"
    
    async def _backup_search(self, backup_keywords: List[str]) -> List[Dict]:
        """البحث الاحتياطي"""
        backup_content = []
        
        try:
            logger.info("🔄 بدء البحث الاحتياطي...")
            
            for keyword in backup_keywords[:2]:  # أفضل 2 كلمات احتياطية
                try:
                    # استخدام أولوية مجانية للبحث الاحتياطي
                    backup_results = await enhanced_search.enhanced_search(
                        query=keyword,
                        max_results=5,
                        search_type="gaming_news",
                        priority="free"
                    )
                    
                    if backup_results:
                        backup_content.extend(backup_results)
                        logger.info(f"🔄 البحث الاحتياطي: {len(backup_results)} نتيجة لـ '{keyword}'")
                    
                    await asyncio.sleep(1)
                    
                except Exception as e:
                    logger.debug(f"فشل في البحث الاحتياطي عن '{keyword}': {e}")
                    continue
            
            return backup_content
            
        except Exception as e:
            logger.error(f"❌ فشل في البحث الاحتياطي: {e}")
            return []
    
    async def _filter_and_enhance_content(self, content_list: List[Dict]) -> List[Dict]:
        """تصفية وتحسين المحتوى"""
        try:
            if not content_list:
                return []
            
            # إزالة المحتوى المكرر
            unique_content = []
            seen_titles = set()
            seen_urls = set()
            
            for content in content_list:
                title = content.get('title', '').lower().strip()
                url = content.get('url', '').strip()
                
                # تنظيف العنوان للمقارنة
                clean_title = self._clean_title_for_comparison(title)
                
                # فحص التكرار
                if clean_title in seen_titles or url in seen_urls:
                    continue
                
                # فحص جودة المحتوى
                if self._is_quality_content(content):
                    unique_content.append(content)
                    seen_titles.add(clean_title)
                    if url:
                        seen_urls.add(url)
            
            # ترتيب حسب الجودة
            sorted_content = sorted(
                unique_content,
                key=lambda x: x.get('total_quality_score', x.get('quality_score', 0)),
                reverse=True
            )
            
            logger.info(f"🔍 تم تصفية {len(sorted_content)} محتوى فريد من {len(content_list)}")
            return sorted_content
            
        except Exception as e:
            logger.error(f"❌ فشل في تصفية المحتوى: {e}")
            return content_list
    
    def _clean_title_for_comparison(self, title: str) -> str:
        """تنظيف العنوان للمقارنة"""
        import re
        
        # إزالة علامات الترقيم والمسافات الزائدة
        clean_title = re.sub(r'[^\w\s]', '', title)
        clean_title = re.sub(r'\s+', ' ', clean_title).strip()
        
        return clean_title
    
    def _is_quality_content(self, content: Dict) -> bool:
        """فحص جودة المحتوى"""
        try:
            title = content.get('title', '')
            content_text = content.get('content', '')
            
            # فحوصات الجودة الأساسية
            if len(title) < 10 or len(content_text) < 100:
                return False
            
            # فحص وجود كلمات مفتاحية متعلقة بالألعاب
            gaming_keywords = [
                'game', 'gaming', 'player', 'console', 'pc', 'mobile',
                'لعبة', 'ألعاب', 'لاعب', 'جهاز', 'كمبيوتر', 'موبايل',
                'video game', 'esports', 'indie', 'AAA', 'release', 'update'
            ]
            
            text_to_check = f"{title} {content_text}".lower()
            has_gaming_content = any(keyword in text_to_check for keyword in gaming_keywords)
            
            return has_gaming_content
            
        except Exception as e:
            logger.debug(f"فشل في فحص جودة المحتوى: {e}")
            return True  # في حالة الخطأ، اعتبره جيد
    
    async def _traditional_fallback(self) -> List[Dict]:
        """العودة للطريقة التقليدية"""
        try:
            logger.info("🔄 العودة للطريقة التقليدية...")
            
            # استيراد الطريقة التقليدية
            from modules.content_scraper import ContentScraper
            
            scraper = ContentScraper()
            
            # البحث التقليدي
            traditional_keywords = [
                'gaming news today',
                'video game updates',
                'new game releases'
            ]
            
            traditional_content = []
            
            for keyword in traditional_keywords:
                try:
                    results = scraper.search_and_extract_articles(keyword, 5)
                    if results:
                        traditional_content.extend(results)
                        logger.info(f"🔄 الطريقة التقليدية: {len(results)} نتيجة لـ '{keyword}'")
                    
                    await asyncio.sleep(2)
                    
                except Exception as e:
                    logger.debug(f"فشل في البحث التقليدي عن '{keyword}': {e}")
                    continue
            
            return traditional_content
            
        except Exception as e:
            logger.error(f"❌ فشل في الطريقة التقليدية: {e}")
            return []
    
    def get_system_status(self) -> Dict:
        """الحصول على حالة النظام"""
        try:
            return enhanced_search.get_system_status()
        except Exception as e:
            logger.error(f"❌ فشل في الحصول على حالة النظام: {e}")
            return {'error': str(e)}

# إنشاء مثيل عام
enhanced_collector = EnhancedContentCollector()

async def main():
    """اختبار النظام المحسن"""
    print("🧪 اختبار النظام المحسن...")
    
    try:
        # جمع المحتوى
        content = await enhanced_collector.collect_content_enhanced()
        
        print(f"✅ تم جمع {len(content)} مقال")
        
        # عرض عينة من النتائج
        for i, article in enumerate(content[:3]):
            print(f"\n📰 مقال {i+1}:")
            print(f"   العنوان: {article.get('title', 'غير محدد')[:100]}...")
            print(f"   المصدر: {article.get('source', 'غير محدد')}")
            print(f"   الجودة: {article.get('total_quality_score', article.get('quality_score', 0)):.1f}")
        
        # عرض حالة النظام
        status = enhanced_collector.get_system_status()
        print(f"\n📊 حالة النظام:")
        print(f"   وضع الطوارئ: {status.get('emergency_mode', 'غير محدد')}")
        print(f"   آخر بحث ناجح: {status.get('last_successful_search', 'غير محدد')}")
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")

if __name__ == "__main__":
    asyncio.run(main())
