-- SQL لإنشاء الجداول المفقودة
-- تاريخ الإنشاء: 2025-08-04 20:30:20.904755


                CREATE TABLE IF NOT EXISTS public.notification_user_logs (
                    id SERIAL PRIMARY KEY,
                    broadcast_id INTEGER,
                    user_id TEXT NOT NULL,
                    sent_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'sent', 'failed', 'blocked')),
                    error_message TEXT
                );
                CREATE INDEX IF NOT EXISTS idx_notification_user_logs_broadcast_id ON public.notification_user_logs(broadcast_id);
                CREATE INDEX IF NOT EXISTS idx_notification_user_logs_user_id ON public.notification_user_logs(user_id);
                CREATE INDEX IF NOT EXISTS idx_notification_user_logs_status ON public.notification_user_logs(status);
            