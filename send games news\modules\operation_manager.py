# نظام إدارة العمليات الذكي
import asyncio
import threading
import time
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable, Coroutine
from enum import Enum
from dataclasses import dataclass, field
import json
from .logger import logger
from .agent_state_manager import OperationState, OperationInfo

class OperationType(Enum):
    """أنواع العمليات"""
    CONTENT_COLLECTION = "content_collection"
    CONTENT_PROCESSING = "content_processing"
    ARTICLE_GENERATION = "article_generation"
    PUBLISHING = "publishing"
    ANALYTICS = "analytics"
    MAINTENANCE = "maintenance"
    BACKUP = "backup"
    MONITORING = "monitoring"

class OperationPriority(Enum):
    """أولويات العمليات"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4

@dataclass
class OperationTask:
    """مهمة العملية"""
    task_id: str
    operation_id: str
    operation_type: OperationType
    priority: OperationPriority
    func: Callable
    args: tuple = field(default_factory=tuple)
    kwargs: dict = field(default_factory=dict)
    dependencies: List[str] = field(default_factory=list)
    timeout_seconds: Optional[int] = None
    retry_count: int = 0
    max_retries: int = 3
    created_at: datetime = field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    state: OperationState = OperationState.PENDING
    result: Any = None
    error: Optional[str] = None
    progress: float = 0.0
    metadata: Dict[str, Any] = field(default_factory=dict)

class ResourceManager:
    """مدير الموارد"""
    
    def __init__(self):
        self.max_concurrent_operations = 5
        self.max_memory_mb = 1024  # 1GB
        self.max_cpu_percent = 80
        self._resource_lock = threading.Lock()
        self._active_operations: Dict[str, OperationTask] = {}
    
    def can_start_operation(self, task: OperationTask) -> bool:
        """فحص إمكانية بدء العملية"""
        with self._resource_lock:
            # فحص عدد العمليات المتزامنة
            active_count = len([
                op for op in self._active_operations.values()
                if op.state == OperationState.RUNNING
            ])
            
            if active_count >= self.max_concurrent_operations:
                return False
            
            # فحص الموارد (يمكن إضافة فحص الذاكرة والمعالج هنا)
            return True
    
    def register_operation(self, task: OperationTask):
        """تسجيل عملية نشطة"""
        with self._resource_lock:
            self._active_operations[task.task_id] = task
    
    def unregister_operation(self, task_id: str):
        """إلغاء تسجيل عملية"""
        with self._resource_lock:
            self._active_operations.pop(task_id, None)
    
    def get_resource_usage(self) -> Dict[str, Any]:
        """الحصول على استخدام الموارد"""
        import psutil
        
        try:
            process = psutil.Process()
            memory_mb = process.memory_info().rss / (1024 * 1024)
            cpu_percent = process.cpu_percent()
            
            return {
                'active_operations': len(self._active_operations),
                'max_concurrent': self.max_concurrent_operations,
                'memory_usage_mb': memory_mb,
                'max_memory_mb': self.max_memory_mb,
                'cpu_usage_percent': cpu_percent,
                'max_cpu_percent': self.max_cpu_percent,
                'memory_usage_ratio': memory_mb / self.max_memory_mb,
                'cpu_usage_ratio': cpu_percent / self.max_cpu_percent
            }
        except:
            return {
                'active_operations': len(self._active_operations),
                'max_concurrent': self.max_concurrent_operations,
                'memory_usage_mb': 0,
                'cpu_usage_percent': 0
            }

class OperationManager:
    """مدير العمليات الذكي"""
    
    def __init__(self):
        self._tasks: Dict[str, OperationTask] = {}
        self._operation_queue: List[OperationTask] = []
        self._running_tasks: Dict[str, asyncio.Task] = {}
        self._lock = threading.Lock()
        self._shutdown_event = asyncio.Event()
        self._worker_task: Optional[asyncio.Task] = None
        self.resource_manager = ResourceManager()
        
        # إحصائيات
        self.stats = {
            'total_operations': 0,
            'completed_operations': 0,
            'failed_operations': 0,
            'cancelled_operations': 0,
            'average_execution_time': 0.0
        }
    
    async def start(self):
        """بدء مدير العمليات"""
        try:
            logger.info("🚀 بدء مدير العمليات...")

            # التحقق من أن المدير لم يتم بدء تشغيله بالفعل
            if self._worker_task and not self._worker_task.done():
                logger.warning("⚠️ مدير العمليات يعمل بالفعل")
                return

            self._shutdown_event.clear()
            self._worker_task = asyncio.create_task(self._worker_loop())

            # انتظار قصير للتأكد من بدء العامل
            await asyncio.sleep(0.1)

            if self._worker_task.done():
                # إذا انتهت المهمة فوراً، فهناك خطأ
                exception = self._worker_task.exception()
                if exception:
                    raise exception
                else:
                    raise Exception("انتهت مهمة العامل بشكل غير متوقع")

            logger.info("✅ تم بدء مدير العمليات بنجاح")

        except Exception as e:
            logger.error(f"❌ فشل في بدء مدير العمليات: {e}")
            self._worker_task = None
            raise
    
    async def stop(self):
        """إيقاف مدير العمليات"""
        logger.info("🛑 إيقاف مدير العمليات...")
        
        # إشارة الإيقاف
        self._shutdown_event.set()
        
        # إيقاف العمليات الجارية
        await self._stop_running_operations()
        
        # إيقاف العامل الرئيسي
        if self._worker_task:
            self._worker_task.cancel()
            try:
                await self._worker_task
            except asyncio.CancelledError:
                pass
        
        logger.info("✅ تم إيقاف مدير العمليات")
    
    def submit_operation(
        self,
        operation_type: OperationType,
        func: Callable,
        args: tuple = (),
        kwargs: dict = None,
        priority: OperationPriority = OperationPriority.NORMAL,
        dependencies: List[str] = None,
        timeout_seconds: Optional[int] = None,
        metadata: Dict[str, Any] = None
    ) -> str:
        """إرسال عملية جديدة"""
        
        task_id = str(uuid.uuid4())
        operation_id = f"{operation_type.value}_{int(time.time() * 1000)}"
        
        task = OperationTask(
            task_id=task_id,
            operation_id=operation_id,
            operation_type=operation_type,
            priority=priority,
            func=func,
            args=args,
            kwargs=kwargs or {},
            dependencies=dependencies or [],
            timeout_seconds=timeout_seconds,
            metadata=metadata or {}
        )
        
        with self._lock:
            self._tasks[task_id] = task
            self._operation_queue.append(task)
            self._operation_queue.sort(key=lambda x: x.priority.value, reverse=True)
            self.stats['total_operations'] += 1
        
        logger.info(f"📋 تم إرسال عملية جديدة: {operation_type.value} (ID: {task_id})")
        return task_id
    
    def cancel_operation(self, task_id: str) -> bool:
        """إلغاء عملية"""
        with self._lock:
            task = self._tasks.get(task_id)
            if not task:
                return False
            
            if task.state == OperationState.RUNNING:
                # إلغاء المهمة الجارية
                if task_id in self._running_tasks:
                    self._running_tasks[task_id].cancel()
                    del self._running_tasks[task_id]
            
            elif task.state == OperationState.PENDING:
                # إزالة من القائمة
                if task in self._operation_queue:
                    self._operation_queue.remove(task)
            
            task.state = OperationState.CANCELLED
            task.completed_at = datetime.now()
            self.stats['cancelled_operations'] += 1
            
            self.resource_manager.unregister_operation(task_id)
        
        logger.info(f"🚫 تم إلغاء العملية: {task_id}")
        return True
    
    def pause_operation(self, task_id: str) -> bool:
        """إيقاف عملية مؤقتاً"""
        with self._lock:
            task = self._tasks.get(task_id)
            if not task or task.state != OperationState.RUNNING:
                return False
            
            task.state = OperationState.PAUSED
            # ملاحظة: الإيقاف المؤقت يتطلب تعاون من العملية نفسها
            
        logger.info(f"⏸️ تم إيقاف العملية مؤقتاً: {task_id}")
        return True
    
    def resume_operation(self, task_id: str) -> bool:
        """استئناف عملية متوقفة"""
        with self._lock:
            task = self._tasks.get(task_id)
            if not task or task.state != OperationState.PAUSED:
                return False
            
            task.state = OperationState.PENDING
            self._operation_queue.append(task)
            self._operation_queue.sort(key=lambda x: x.priority.value, reverse=True)
        
        logger.info(f"▶️ تم استئناف العملية: {task_id}")
        return True
    
    def get_operation_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """الحصول على حالة العملية"""
        task = self._tasks.get(task_id)
        if not task:
            return None
        
        duration = None
        if task.started_at:
            end_time = task.completed_at or datetime.now()
            duration = (end_time - task.started_at).total_seconds()
        
        return {
            'task_id': task.task_id,
            'operation_id': task.operation_id,
            'operation_type': task.operation_type.value,
            'priority': task.priority.value,
            'state': task.state.value,
            'progress': task.progress,
            'created_at': task.created_at.isoformat(),
            'started_at': task.started_at.isoformat() if task.started_at else None,
            'completed_at': task.completed_at.isoformat() if task.completed_at else None,
            'duration_seconds': duration,
            'retry_count': task.retry_count,
            'max_retries': task.max_retries,
            'error': task.error,
            'metadata': task.metadata
        }
    
    def get_all_operations(self) -> List[Dict[str, Any]]:
        """الحصول على جميع العمليات"""
        return [
            self.get_operation_status(task_id)
            for task_id in self._tasks.keys()
        ]
    
    def get_active_operations(self) -> List[Dict[str, Any]]:
        """الحصول على العمليات النشطة"""
        return [
            self.get_operation_status(task.task_id)
            for task in self._tasks.values()
            if task.state in [OperationState.RUNNING, OperationState.PENDING, OperationState.PAUSED]
        ]

    def get_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات العمليات"""
        resource_usage = self.resource_manager.get_resource_usage()

        return {
            **self.stats,
            'resource_usage': resource_usage,
            'queue_size': len(self._operation_queue),
            'running_operations': len(self._running_tasks),
            'total_tasks': len(self._tasks)
        }

    async def _worker_loop(self):
        """حلقة العامل الرئيسي"""
        logger.info("🔄 بدء حلقة العامل الرئيسي...")

        while not self._shutdown_event.is_set():
            try:
                # معالجة العمليات في القائمة
                await self._process_queue()

                # تنظيف العمليات المكتملة
                await self._cleanup_completed_tasks()

                # انتظار قصير قبل التكرار
                await asyncio.sleep(1)

            except Exception as e:
                logger.error(f"❌ خطأ في حلقة العامل الرئيسي: {e}")
                await asyncio.sleep(5)

        logger.info("✅ انتهت حلقة العامل الرئيسي")

    async def _process_queue(self):
        """معالجة قائمة العمليات"""
        with self._lock:
            # البحث عن العمليات الجاهزة للتنفيذ
            ready_tasks = []

            for task in self._operation_queue[:]:
                if task.state != OperationState.PENDING:
                    continue

                # فحص التبعيات
                if not self._are_dependencies_satisfied(task):
                    continue

                # فحص الموارد
                if not self.resource_manager.can_start_operation(task):
                    break  # لا توجد موارد كافية

                ready_tasks.append(task)
                self._operation_queue.remove(task)

                # حد أقصى للعمليات المتزامنة
                if len(ready_tasks) >= 3:
                    break

            # بدء العمليات الجاهزة
            for task in ready_tasks:
                await self._start_task(task)

    def _are_dependencies_satisfied(self, task: OperationTask) -> bool:
        """فحص ما إذا كانت التبعيات مكتملة"""
        for dep_id in task.dependencies:
            dep_task = self._tasks.get(dep_id)
            if not dep_task or dep_task.state != OperationState.COMPLETED:
                return False
        return True

    async def _start_task(self, task: OperationTask):
        """بدء تنفيذ مهمة"""
        task.state = OperationState.RUNNING
        task.started_at = datetime.now()

        self.resource_manager.register_operation(task)

        logger.info(f"🚀 بدء تنفيذ العملية: {task.operation_type.value} (ID: {task.task_id})")

        # إنشاء مهمة asyncio
        async_task = asyncio.create_task(self._execute_task(task))
        self._running_tasks[task.task_id] = async_task

    async def _execute_task(self, task: OperationTask):
        """تنفيذ المهمة"""
        try:
            # تطبيق المهلة الزمنية إذا كانت محددة
            if task.timeout_seconds:
                result = await asyncio.wait_for(
                    self._run_task_function(task),
                    timeout=task.timeout_seconds
                )
            else:
                result = await self._run_task_function(task)

            # نجح التنفيذ
            task.result = result
            task.state = OperationState.COMPLETED
            task.progress = 100.0
            task.completed_at = datetime.now()

            self.stats['completed_operations'] += 1

            # حساب متوسط وقت التنفيذ
            duration = (task.completed_at - task.started_at).total_seconds()
            self._update_average_execution_time(duration)

            logger.info(f"✅ اكتملت العملية بنجاح: {task.operation_type.value} (ID: {task.task_id})")

        except asyncio.TimeoutError:
            task.error = f"انتهت المهلة الزمنية ({task.timeout_seconds} ثانية)"
            task.state = OperationState.FAILED
            task.completed_at = datetime.now()
            self.stats['failed_operations'] += 1

            logger.error(f"⏰ انتهت المهلة الزمنية للعملية: {task.task_id}")

        except asyncio.CancelledError:
            task.state = OperationState.CANCELLED
            task.completed_at = datetime.now()
            self.stats['cancelled_operations'] += 1

            logger.info(f"🚫 تم إلغاء العملية: {task.task_id}")

        except Exception as e:
            task.error = str(e)
            task.state = OperationState.FAILED
            task.completed_at = datetime.now()

            # إعادة المحاولة إذا لم نصل للحد الأقصى
            if task.retry_count < task.max_retries:
                task.retry_count += 1
                task.state = OperationState.PENDING
                task.started_at = None
                task.completed_at = None
                task.error = None

                with self._lock:
                    self._operation_queue.append(task)
                    self._operation_queue.sort(key=lambda x: x.priority.value, reverse=True)

                logger.warning(f"🔄 إعادة محاولة العملية {task.task_id} (المحاولة {task.retry_count}/{task.max_retries})")
            else:
                self.stats['failed_operations'] += 1
                logger.error(f"❌ فشلت العملية نهائياً: {task.task_id} - {str(e)}")

        finally:
            # تنظيف
            self.resource_manager.unregister_operation(task.task_id)
            if task.task_id in self._running_tasks:
                del self._running_tasks[task.task_id]

    async def _run_task_function(self, task: OperationTask):
        """تشغيل دالة المهمة"""
        func = task.func

        # فحص ما إذا كانت الدالة غير متزامنة
        if asyncio.iscoroutinefunction(func):
            return await func(*task.args, **task.kwargs)
        else:
            # تشغيل الدالة المتزامنة في thread pool
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(None, lambda: func(*task.args, **task.kwargs))

    def _update_average_execution_time(self, duration: float):
        """تحديث متوسط وقت التنفيذ"""
        completed = self.stats['completed_operations']
        if completed == 1:
            self.stats['average_execution_time'] = duration
        else:
            current_avg = self.stats['average_execution_time']
            self.stats['average_execution_time'] = (current_avg * (completed - 1) + duration) / completed

    async def _cleanup_completed_tasks(self):
        """تنظيف المهام المكتملة القديمة"""
        cutoff_time = datetime.now() - timedelta(hours=1)

        with self._lock:
            tasks_to_remove = []

            for task_id, task in self._tasks.items():
                if (task.state in [OperationState.COMPLETED, OperationState.FAILED, OperationState.CANCELLED] and
                    task.completed_at and task.completed_at < cutoff_time):
                    tasks_to_remove.append(task_id)

            for task_id in tasks_to_remove:
                del self._tasks[task_id]

            if tasks_to_remove:
                logger.debug(f"🧹 تم تنظيف {len(tasks_to_remove)} مهمة مكتملة قديمة")

    async def _stop_running_operations(self):
        """إيقاف العمليات الجارية"""
        if not self._running_tasks:
            return

        logger.info(f"⏸️ إيقاف {len(self._running_tasks)} عملية جارية...")

        # إلغاء جميع المهام الجارية
        for task_id, async_task in self._running_tasks.items():
            async_task.cancel()

        # انتظار انتهاء المهام
        if self._running_tasks:
            await asyncio.gather(*self._running_tasks.values(), return_exceptions=True)

        self._running_tasks.clear()
        logger.info("✅ تم إيقاف جميع العمليات الجارية")

# إنشاء مثيل مدير العمليات
operation_manager = OperationManager()
