#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل البوت المحسن
يتضمن فحوصات أولية وإعداد البيئة قبل تشغيل البوت
"""

import os
import sys
import asyncio
import logging
import time
from datetime import datetime

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('bot.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

def check_python_version():
    """التحقق من إصدار Python"""
    if sys.version_info < (3, 8):
        logger.error("❌ يتطلب Python 3.8 أو أحدث")
        return False
    logger.info(f"✅ إصدار Python: {sys.version}")
    return True

def check_required_files():
    """التحقق من وجود الملفات المطلوبة"""
    required_files = [
        'main.py',
        'supabase_client.py',
        '.env'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        logger.error(f"❌ ملفات مفقودة: {missing_files}")
        return False
    
    logger.info("✅ جميع الملفات المطلوبة موجودة")
    return True

def check_environment_variables():
    """التحقق من متغيرات البيئة"""
    try:
        from dotenv import load_dotenv
        load_dotenv()
        
        required_vars = {
            'BOT_TOKEN': 'رمز البوت',
            'ADMIN_CHAT_ID': 'معرف المشرف',
            'SUPABASE_URL': 'رابط قاعدة البيانات',
            'SUPABASE_KEY': 'مفتاح قاعدة البيانات'
        }
        
        missing_vars = []
        for var, description in required_vars.items():
            if not os.getenv(var):
                missing_vars.append(f"{var} ({description})")
        
        if missing_vars:
            logger.error(f"❌ متغيرات بيئة مفقودة: {missing_vars}")
            logger.info("💡 تأكد من وجود ملف .env مع المتغيرات المطلوبة")
            return False
        
        logger.info("✅ جميع متغيرات البيئة موجودة")
        return True
        
    except ImportError:
        logger.error("❌ مكتبة python-dotenv غير مثبتة")
        logger.info("💡 قم بتثبيتها: pip install python-dotenv")
        return False
    except Exception as e:
        logger.error(f"❌ خطأ في فحص متغيرات البيئة: {e}")
        return False

def check_dependencies():
    """التحقق من المكتبات المطلوبة"""
    required_packages = [
        'telegram',
        'requests',
        'python-dotenv'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        logger.error(f"❌ مكتبات مفقودة: {missing_packages}")
        logger.info("💡 قم بتثبيتها: pip install " + " ".join(missing_packages))
        return False
    
    logger.info("✅ جميع المكتبات المطلوبة مثبتة")
    return True

def test_database_connection():
    """اختبار الاتصال بقاعدة البيانات"""
    try:
        from supabase_client import safe_supabase_request, SUPABASE_URL, SUPABASE_KEY
        
        if not SUPABASE_URL or not SUPABASE_KEY:
            logger.warning("⚠️ إعدادات قاعدة البيانات مفقودة")
            return False
        
        # اختبار بسيط للاتصال
        test_url = f"{SUPABASE_URL}/rest/v1/mods?limit=1"
        response = safe_supabase_request('GET', test_url)
        
        if response and response.status_code == 200:
            logger.info("✅ الاتصال بقاعدة البيانات يعمل")
            return True
        else:
            status = response.status_code if response else "لا يوجد استجابة"
            logger.warning(f"⚠️ مشكلة في الاتصال بقاعدة البيانات: {status}")
            return False
            
    except Exception as e:
        logger.warning(f"⚠️ خطأ في اختبار قاعدة البيانات: {e}")
        return False

def run_comprehensive_test():
    """تشغيل الاختبار الشامل"""
    try:
        logger.info("🧪 تشغيل الاختبار الشامل...")
        from comprehensive_bot_test import BotTester
        
        tester = BotTester()
        tester.run_all_tests()
        
        if tester.failed_tests == 0:
            logger.info("✅ جميع الاختبارات نجحت")
            return True
        else:
            logger.warning(f"⚠️ {tester.failed_tests} اختبار فشل")
            return False
            
    except Exception as e:
        logger.warning(f"⚠️ خطأ في الاختبار الشامل: {e}")
        return False

def show_startup_info():
    """عرض معلومات البدء"""
    logger.info("=" * 60)
    logger.info("🤖 بوت نشر المودات - الإصدار المحسن")
    logger.info("=" * 60)
    logger.info(f"📅 وقت البدء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info(f"🐍 Python: {sys.version}")
    logger.info(f"📁 مجلد العمل: {os.getcwd()}")
    logger.info("=" * 60)

async def main():
    """الدالة الرئيسية"""
    show_startup_info()
    
    logger.info("🔍 بدء الفحوصات الأولية...")
    
    # قائمة الفحوصات
    checks = [
        ("إصدار Python", check_python_version),
        ("الملفات المطلوبة", check_required_files),
        ("متغيرات البيئة", check_environment_variables),
        ("المكتبات المطلوبة", check_dependencies),
        ("اتصال قاعدة البيانات", test_database_connection)
    ]
    
    # تشغيل الفحوصات
    all_passed = True
    for check_name, check_func in checks:
        logger.info(f"🔍 فحص {check_name}...")
        if not check_func():
            all_passed = False
            logger.error(f"❌ فشل فحص {check_name}")
        else:
            logger.info(f"✅ نجح فحص {check_name}")
    
    if not all_passed:
        logger.error("❌ فشلت بعض الفحوصات الأولية")
        logger.info("💡 يرجى إصلاح المشاكل أعلاه قبل تشغيل البوت")
        return False
    
    logger.info("✅ جميع الفحوصات الأولية نجحت")
    
    # تشغيل الاختبار الشامل (اختياري)
    run_test = input("\n🧪 هل تريد تشغيل الاختبار الشامل؟ (y/n): ").lower().strip()
    if run_test in ['y', 'yes', 'نعم']:
        if not run_comprehensive_test():
            logger.warning("⚠️ بعض الاختبارات فشلت، لكن يمكن المتابعة")
    
    # تشغيل البوت
    logger.info("🚀 بدء تشغيل البوت...")
    logger.info("=" * 60)
    
    try:
        # استيراد وتشغيل البوت الرئيسي
        from main import main as bot_main
        await bot_main()
        
    except KeyboardInterrupt:
        logger.info("⏹️ تم إيقاف البوت بواسطة المستخدم")
    except Exception as e:
        logger.error(f"❌ خطأ في تشغيل البوت: {e}", exc_info=True)
        return False
    
    logger.info("👋 تم إنهاء البوت")
    return True

if __name__ == "__main__":
    try:
        # تشغيل البوت
        success = asyncio.run(main())
        
        if success:
            logger.info("✅ تم إنهاء البوت بنجاح")
            sys.exit(0)
        else:
            logger.error("❌ فشل في تشغيل البوت")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"❌ خطأ عام: {e}", exc_info=True)
        sys.exit(1)
