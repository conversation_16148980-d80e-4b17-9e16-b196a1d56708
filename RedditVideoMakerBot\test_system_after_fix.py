#!/usr/bin/env python3
"""
اختبار النظام بعد الإصلاحات
يتحقق من جميع المكونات ويعطي تقرير شامل
"""

import os
import sys
import logging
import subprocess
from pathlib import Path

# إعداد السجلات
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_video_creation_fix():
    """اختبار إصلاح إنشاء الفيديو"""
    print("🎬 اختبار إصلاح إنشاء الفيديو...")
    
    try:
        from video_creation.final_video import make_final_video
        print("✅ تم استيراد make_final_video بنجاح")
        
        # التحقق من وجود return statement
        import inspect
        source = inspect.getsource(make_final_video)
        if "return final_video_path" in source:
            print("✅ دالة make_final_video تُرجع مسار الفيديو")
        else:
            print("❌ دالة make_final_video لا تُرجع مسار الفيديو")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار إنشاء الفيديو: {e}")
        return False

def test_telegram_fix():
    """اختبار إصلاح Telegram"""
    print("\n📱 اختبار إصلاح Telegram...")
    
    try:
        from automation.telegram_bot import EnhancedTelegramBot
        print("✅ تم استيراد EnhancedTelegramBot بنجاح")
        
        # التحقق من وجود معالجة خطأ 400
        import inspect
        source = inspect.getsource(EnhancedTelegramBot.send_message)
        if "response.status_code == 400" in source:
            print("✅ تم إضافة معالجة خطأ HTTP 400")
        else:
            print("⚠️ معالجة خطأ HTTP 400 غير موجودة")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار Telegram: {e}")
        return False

def test_youtube_uploader():
    """اختبار رافع YouTube"""
    print("\n📤 اختبار رافع YouTube...")
    
    try:
        from automation.youtube_uploader import YouTubeUploader
        print("✅ تم استيراد YouTubeUploader بنجاح")
        
        # التحقق من وجود ملف التوكن
        if os.path.exists("youtube_token.pickle"):
            print("✅ ملف التوكن موجود")
        else:
            print("⚠️ ملف التوكن غير موجود (سيتم إنشاؤه عند الحاجة)")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار YouTube: {e}")
        return False

def test_gemini_ai():
    """اختبار Gemini AI"""
    print("\n🤖 اختبار Gemini AI...")
    
    try:
        from automation.gemini_content_generator import GeminiContentGenerator
        print("✅ تم استيراد GeminiContentGenerator بنجاح")
        
        # التحقق من مفتاح API
        try:
            from utils import settings
            api_key = settings.config.get("gemini", {}).get("api_key")
            if api_key and api_key != "YOUR_API_KEY_HERE":
                print("✅ مفتاح Gemini API موجود")
            else:
                print("⚠️ مفتاح Gemini API غير مُعرَّف")
        except:
            print("⚠️ لم يتم قراءة إعدادات Gemini")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار Gemini AI: {e}")
        return False

def test_reddit_connection():
    """اختبار اتصال Reddit"""
    print("\n🔗 اختبار اتصال Reddit...")
    
    try:
        from reddit.subreddit import get_subreddit_threads
        print("✅ تم استيراد get_subreddit_threads بنجاح")
        
        # التحقق من بيانات Reddit API
        try:
            from utils import settings
            creds = settings.config.get("reddit", {}).get("creds", {})
            if creds.get("client_id") and creds.get("client_secret"):
                print("✅ بيانات Reddit API موجودة")
            else:
                print("⚠️ بيانات Reddit API غير مكتملة")
        except:
            print("⚠️ لم يتم قراءة إعدادات Reddit")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار Reddit: {e}")
        return False

def check_video_files():
    """فحص ملفات الفيديو المنتجة"""
    print("\n📁 فحص ملفات الفيديو...")
    
    results_dir = Path("results")
    if not results_dir.exists():
        print("⚠️ مجلد results غير موجود")
        return False
    
    video_files = list(results_dir.rglob("*.mp4"))
    
    if video_files:
        print(f"✅ تم العثور على {len(video_files)} ملف فيديو:")
        for video_file in video_files[-3:]:  # آخر 3 ملفات
            size_mb = video_file.stat().st_size / (1024 * 1024)
            print(f"   📹 {video_file.name} ({size_mb:.1f} MB)")
        
        # فحص آخر فيديو
        latest_video = max(video_files, key=lambda x: x.stat().st_mtime)
        size_mb = latest_video.stat().st_size / (1024 * 1024)
        
        if size_mb > 1:  # أكبر من 1MB
            print(f"✅ آخر فيديو بحجم معقول: {size_mb:.1f} MB")
            return True
        else:
            print(f"⚠️ آخر فيديو صغير جداً: {size_mb:.1f} MB")
            return False
    else:
        print("⚠️ لا توجد ملفات فيديو")
        return False

def run_comprehensive_test():
    """تشغيل اختبار شامل"""
    print("🧪 اختبار شامل للنظام بعد الإصلاحات")
    print("=" * 60)
    
    tests = [
        ("إصلاح إنشاء الفيديو", test_video_creation_fix),
        ("إصلاح Telegram", test_telegram_fix),
        ("رافع YouTube", test_youtube_uploader),
        ("Gemini AI", test_gemini_ai),
        ("اتصال Reddit", test_reddit_connection),
        ("ملفات الفيديو", check_video_files),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {e}")
            results[test_name] = False
    
    # تلخيص النتائج
    print("\n" + "=" * 60)
    print("📊 ملخص نتائج الاختبار")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nالنتيجة النهائية: {passed}/{total} اختبارات نجحت")
    
    # تقييم الحالة العامة
    if passed == total:
        print("🎉 ممتاز! جميع الاختبارات نجحت")
        print("✅ النظام جاهز للاستخدام الكامل")
        status = "excellent"
    elif passed >= total * 0.8:
        print("👍 جيد! معظم الاختبارات نجحت")
        print("⚠️ بعض المكونات تحتاج إعداد إضافي")
        status = "good"
    elif passed >= total * 0.5:
        print("⚠️ متوسط. نصف الاختبارات نجحت")
        print("🔧 يحتاج إصلاحات إضافية")
        status = "average"
    else:
        print("❌ ضعيف. معظم الاختبارات فشلت")
        print("🚨 يحتاج إعداد شامل")
        status = "poor"
    
    return status, results

def show_next_steps(status: str, results: dict):
    """عرض الخطوات التالية"""
    print("\n" + "=" * 60)
    print("🎯 الخطوات التالية")
    print("=" * 60)
    
    if status == "excellent":
        print("""
🎉 النظام يعمل بشكل مثالي!

الخطوات التالية:
1. تشغيل البوت: python main.py
2. مراقبة الإشعارات في Telegram
3. التحقق من الفيديوهات في مجلد results/
4. مراقبة رفع الفيديوهات على YouTube
        """)
    
    elif status == "good":
        print("""
👍 النظام يعمل بشكل جيد!

قد تحتاج إلى:
1. إعداد مفاتيح API المفقودة
2. اختبار الاتصالات الخارجية
3. تشغيل: python main.py للاختبار
        """)
    
    else:
        print("""
🔧 النظام يحتاج إصلاحات:

1. إصلاح Telegram: python fix_telegram_issue.py
2. إصلاح عام: python fix_video_creation_issue.py
3. فحص الإعدادات: تأكد من config.toml
4. إعادة الاختبار: python test_system_after_fix.py
        """)
    
    # نصائح خاصة بالمكونات الفاشلة
    failed_components = [name for name, result in results.items() if not result]
    
    if failed_components:
        print(f"\nالمكونات التي تحتاج إصلاح: {', '.join(failed_components)}")

if __name__ == "__main__":
    try:
        status, results = run_comprehensive_test()
        show_next_steps(status, results)
        
        print(f"\n{'='*60}")
        print("🏁 انتهى الاختبار الشامل")
        print(f"{'='*60}")
        
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الاختبار بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ حرج في الاختبار: {e}")
        import traceback
        print(f"تفاصيل الخطأ:\n{traceback.format_exc()}")
