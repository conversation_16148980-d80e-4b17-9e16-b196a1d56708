# نظام تحسين الكتابة الطبيعية
import re
import random
from typing import Dict, List, Tuple
from .logger import logger

class NaturalWritingEnhancer:
    """محسن الكتابة الطبيعية لجعل النص يبدو مكتوب بواسطة إنسان"""
    
    def __init__(self):
        # أخطاء إملائية شائعة وطبيعية
        self.common_spelling_errors = {
            # أخطاء الهمزة
            'هذا': ['هاذا', 'هذا'],
            'هذه': ['هاذه', 'هذه'], 
            'التي': ['الي', 'اللي', 'التي'],
            'التى': ['الي', 'اللي'],
            'الذي': ['الي', 'اللي', 'الذي'],
            'الذى': ['الي', 'اللي'],
            'أن': ['ان', 'أن'],
            'أنا': ['انا', 'أنا'],
            'أنت': ['انت', 'أنت'],
            'أنتم': ['انتم', 'أنتم'],
            'أنتن': ['انتن', 'أنتن'],
            'إن': ['ان', 'إن'],
            'إذا': ['اذا', 'إذا'],
            'أيضا': ['كمان', 'برضو', 'أيضا'],
            'أيضاً': ['كمان', 'برضو', 'أيضاً'],
            
            # أخطاء التنوين والتشكيل
            'جداً': ['جدا', 'جداً'],
            'جدا': ['جدا'],
            'كثيراً': ['كتير', 'كثيراً'],
            'كثيرا': ['كتير', 'كثيرا'],
            'قليلاً': ['شوية', 'قليلاً'],
            'قليلا': ['شوية', 'قليلا'],
            
            # كلمات عامية شائعة
            'لأن': ['عشان', 'لان', 'لأن'],
            'لكن': ['بس', 'لكن'],
            'يمكن': ['ممكن', 'يمكن'],
            'يجب': ['لازم', 'يجب'],
            'سوف': ['هـ', 'حـ', 'سوف'],
            'الآن': ['دلوقتي', 'هلأ', 'الآن'],
            'الان': ['دلوقتي', 'هلأ', 'الان'],
            'كيف': ['ازاي', 'كيف'],
            'ماذا': ['ايه', 'شو', 'ماذا'],
            'مادا': ['ايه', 'شو', 'مادا'],
            'متى': ['امتى', 'متى'],
            'أين': ['فين', 'وين', 'أين'],
            'اين': ['فين', 'وين', 'اين'],
            
            # أخطاء الياء والألف المقصورة
            'على': ['علي', 'على'],
            'إلى': ['الي', 'إلى'],
            'الى': ['الي', 'الى'],
            'حتى': ['لحد', 'حتى'],
            'معنى': ['معني', 'معنى'],
            'معني': ['معني'],
            'مستوى': ['مستوي', 'مستوى'],
            'مستوي': ['مستوي'],
            
            # كلمات أخرى شائعة
            'شيء': ['حاجة', 'شي', 'شيء'],
            'شئ': ['حاجة', 'شي', 'شئ'],
            'بعض': ['شوية', 'بعض'],
            'كل': ['كل'],
            'جميع': ['كل', 'جميع'],
            'معظم': ['اغلب', 'معظم'],
            'أكثر': ['اكتر', 'أكثر'],
            'اكثر': ['اكتر', 'اكثر'],
            'أقل': ['اقل', 'أقل'],
            'اقل': ['اقل'],
            'أفضل': ['احسن', 'أفضل'],
            'افضل': ['احسن', 'افضل'],
            'أسوأ': ['اوحش', 'أسوأ'],
            'اسوأ': ['اوحش', 'اسوأ']
        }
        
        # تعبيرات عامية حسب اللهجة
        self.dialect_expressions = {
            'egyptian': {
                'نحن': 'احنا',
                'أنتم': 'انتو',
                'هم': 'هما',
                'هن': 'هما',
                'معهم': 'معاهم',
                'معهن': 'معاهم',
                'عندهم': 'عندهم',
                'عندهن': 'عندهم',
                'منهم': 'منهم',
                'منهن': 'منهم',
                'إليهم': 'ليهم',
                'إليهن': 'ليهم',
                'عليهم': 'عليهم',
                'عليهن': 'عليهم',
                'فيهم': 'فيهم',
                'فيهن': 'فيهم',
                'بهم': 'بيهم',
                'بهن': 'بيهم',
                'لهم': 'ليهم',
                'لهن': 'ليهم',
                'كيف': 'ازاي',
                'ماذا': 'ايه',
                'أين': 'فين',
                'متى': 'امتى',
                'لماذا': 'ليه',
                'كم': 'كام',
                'هكذا': 'كده',
                'هناك': 'هناك',
                'هنا': 'هنا',
                'الآن': 'دلوقتي',
                'بعد قليل': 'شوية كده',
                'قبل قليل': 'من شوية',
                'كثيراً': 'كتير',
                'قليلاً': 'شوية',
                'جداً': 'جدا',
                'أيضاً': 'كمان',
                'لكن': 'بس',
                'لأن': 'عشان',
                'يمكن': 'ممكن',
                'يجب': 'لازم',
                'أريد': 'عايز',
                'أحب': 'بحب',
                'أعرف': 'اعرف',
                'أفهم': 'افهم',
                'أعتقد': 'اعتقد',
                'أظن': 'اظن',
                'أشعر': 'احس',
                'أرى': 'اشوف',
                'أسمع': 'اسمع',
                'أقول': 'اقول',
                'أفعل': 'اعمل',
                'أذهب': 'اروح',
                'آتي': 'اجي',
                'آخذ': 'اخد',
                'أعطي': 'ادي',
                'أشتري': 'اشتري',
                'أبيع': 'ابيع',
                'أكل': 'اكل',
                'أشرب': 'اشرب',
                'أنام': 'انام',
                'أستيقظ': 'اصحى',
                'أعمل': 'اشتغل',
                'أدرس': 'ادرس',
                'ألعب': 'العب',
                'أقرأ': 'اقرا',
                'أكتب': 'اكتب',
                'أرسم': 'ارسم',
                'أغني': 'اغني',
                'أرقص': 'ارقص',
                'أضحك': 'اضحك',
                'أبكي': 'اعيط',
                'أفرح': 'افرح',
                'أحزن': 'ازعل',
                'أغضب': 'ازعل',
                'أخاف': 'اخاف',
                'أحب': 'بحب',
                'أكره': 'اكره'
            },
            'saudi': {
                'نحن': 'احنا',
                'أنتم': 'انتو',
                'هذا': 'ذا',
                'هذه': 'ذي',
                'ماذا': 'وش',
                'أين': 'وين',
                'كيف': 'كيف',
                'متى': 'متى',
                'لماذا': 'ليش',
                'الآن': 'الحين',
                'كثيراً': 'وايد',
                'جداً': 'مره',
                'أيضاً': 'بعد',
                'لكن': 'بس',
                'لأن': 'لان',
                'يمكن': 'يمكن',
                'يجب': 'لازم',
                'أريد': 'ابغى',
                'أحب': 'احب',
                'أعرف': 'اعرف',
                'أفهم': 'افهم',
                'أعتقد': 'اعتقد',
                'أظن': 'اظن',
                'أشعر': 'احس',
                'أرى': 'اشوف',
                'أسمع': 'اسمع',
                'أقول': 'اقول',
                'أفعل': 'اسوي',
                'أذهب': 'اروح',
                'آتي': 'اجي',
                'آخذ': 'اخذ',
                'أعطي': 'اعطي',
                'أشتري': 'اشتري',
                'أبيع': 'ابيع'
            },
            'standard': {
                # أخطاء بسيطة للعربية الفصحى المبسطة
                'هذا': 'هاذا',
                'هذه': 'هاذه',
                'التي': 'الي',
                'الذي': 'الي',
                'أن': 'ان',
                'إن': 'ان',
                'إذا': 'اذا',
                'جداً': 'جدا',
                'كثيراً': 'كتير',
                'أيضاً': 'كمان',
                'لأن': 'عشان',
                'لكن': 'بس',
                'يمكن': 'ممكن',
                'يجب': 'لازم'
            }
        }
        
        # تعبيرات طبيعية للانتقال
        self.natural_transitions = [
            'وبكده', 'وكده', 'يعني', 'بصراحة', 'الحقيقة', 'في الواقع',
            'بالمناسبة', 'عموماً', 'على فكرة', 'مش كده وبس', 'وده مش كل حاجة',
            'وفي الآخر', 'في النهاية', 'خلاصة الكلام', 'المهم', 'على العموم'
        ]
        
        # أخطاء في علامات الترقيم (طبيعية)
        self.punctuation_errors = {
            '،': [',', '،'],  # خلط بين الفاصلة العربية والإنجليزية
            '؟': ['?', '؟'],  # خلط بين علامة الاستفهام العربية والإنجليزية
            '!': ['!', '!'],  # تكرار علامة التعجب أحياناً
            '.': ['.', '..', '...']  # تنويع في النقاط
        }
    
    def enhance_natural_writing(self, content: str, dialect: str = 'standard', 
                              error_rate: float = 0.25) -> str:
        """تحسين الكتابة لتبدو طبيعية أكثر"""
        try:
            enhanced_content = content
            
            # إضافة أخطاء إملائية طبيعية
            enhanced_content = self._add_spelling_errors(enhanced_content, error_rate)
            
            # تطبيق تعبيرات اللهجة
            enhanced_content = self._apply_dialect_expressions(enhanced_content, dialect, error_rate)
            
            # إضافة تعبيرات انتقال طبيعية
            enhanced_content = self._add_natural_transitions(enhanced_content, error_rate / 2)
            
            # تنويع علامات الترقيم
            enhanced_content = self._vary_punctuation(enhanced_content, error_rate / 3)
            
            # إضافة تنويع في التعبير
            enhanced_content = self._add_expression_variety(enhanced_content, error_rate)
            
            logger.info("✅ تم تحسين الكتابة الطبيعية")
            return enhanced_content
            
        except Exception as e:
            logger.error("❌ فشل في تحسين الكتابة الطبيعية", e)
            return content
    
    def _add_spelling_errors(self, content: str, error_rate: float) -> str:
        """إضافة أخطاء إملائية طبيعية"""
        try:
            words = content.split()
            
            for i, word in enumerate(words):
                # إزالة علامات الترقيم للمقارنة
                clean_word = re.sub(r'[^\w]', '', word)
                
                if clean_word in self.common_spelling_errors and random.random() < error_rate:
                    # اختيار بديل عشوائي
                    alternatives = self.common_spelling_errors[clean_word]
                    chosen_alternative = random.choice(alternatives)
                    
                    # استبدال الكلمة مع الحفاظ على علامات الترقيم
                    words[i] = word.replace(clean_word, chosen_alternative)
            
            return ' '.join(words)
            
        except Exception as e:
            logger.warning(f"⚠️ خطأ في إضافة الأخطاء الإملائية: {e}")
            return content
    
    def _apply_dialect_expressions(self, content: str, dialect: str, error_rate: float) -> str:
        """تطبيق تعبيرات اللهجة"""
        try:
            if dialect not in self.dialect_expressions:
                dialect = 'standard'
            
            expressions = self.dialect_expressions[dialect]
            words = content.split()
            
            for i, word in enumerate(words):
                clean_word = re.sub(r'[^\w]', '', word)
                
                if clean_word in expressions and random.random() < error_rate:
                    dialect_word = expressions[clean_word]
                    words[i] = word.replace(clean_word, dialect_word)
            
            return ' '.join(words)
            
        except Exception as e:
            logger.warning(f"⚠️ خطأ في تطبيق تعبيرات اللهجة: {e}")
            return content
    
    def _add_natural_transitions(self, content: str, transition_rate: float) -> str:
        """إضافة تعبيرات انتقال طبيعية"""
        try:
            sentences = re.split(r'[.!?]', content)
            enhanced_sentences = []
            
            for i, sentence in enumerate(sentences):
                if sentence.strip():
                    # إضافة تعبير انتقال أحياناً
                    if i > 0 and random.random() < transition_rate:
                        transition = random.choice(self.natural_transitions)
                        sentence = f"{transition}، {sentence.strip()}"
                    
                    enhanced_sentences.append(sentence.strip())
            
            # إعادة تجميع الجمل مع علامات الترقيم
            result = '. '.join(enhanced_sentences)
            if result and not result.endswith('.'):
                result += '.'
            
            return result
            
        except Exception as e:
            logger.warning(f"⚠️ خطأ في إضافة التعبيرات الانتقالية: {e}")
            return content
    
    def _vary_punctuation(self, content: str, variation_rate: float) -> str:
        """تنويع علامات الترقيم"""
        try:
            for original, alternatives in self.punctuation_errors.items():
                if random.random() < variation_rate:
                    chosen_alternative = random.choice(alternatives)
                    # استبدال بعض الحالات فقط
                    content = re.sub(
                        re.escape(original), 
                        chosen_alternative, 
                        content, 
                        count=random.randint(1, 3)
                    )
            
            return content
            
        except Exception as e:
            logger.warning(f"⚠️ خطأ في تنويع علامات الترقيم: {e}")
            return content
    
    def _add_expression_variety(self, content: str, variety_rate: float) -> str:
        """إضافة تنويع في التعبير"""
        try:
            # استبدال بعض التعبيرات المتكررة
            repetitive_patterns = {
                r'\bيمكن أن\b': ['ممكن', 'من الممكن أن', 'يمكن أن'],
                r'\bيجب أن\b': ['لازم', 'من الضروري أن', 'يجب أن'],
                r'\bمن المهم\b': ['مهم جداً', 'من الضروري', 'من المهم'],
                r'\bفي الواقع\b': ['الحقيقة', 'بصراحة', 'في الواقع'],
                r'\bبالإضافة إلى ذلك\b': ['كمان', 'وبرضو', 'بالإضافة إلى ذلك']
            }
            
            for pattern, alternatives in repetitive_patterns.items():
                if random.random() < variety_rate:
                    chosen_alternative = random.choice(alternatives)
                    content = re.sub(pattern, chosen_alternative, content, count=1)
            
            return content
            
        except Exception as e:
            logger.warning(f"⚠️ خطأ في إضافة تنويع التعبير: {e}")
            return content

# إنشاء كائن محسن الكتابة الطبيعية
natural_writing_enhancer = NaturalWritingEnhancer()
