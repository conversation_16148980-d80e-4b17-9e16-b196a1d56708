# نظام تحليل الزوار والسلوك المتقدم
import sqlite3
import json
import requests
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import statistics
from urllib.parse import urlparse
from .logger import logger
from .database import db

class VisitorAnalytics:
    """نظام تحليل الزوار والسلوك المتقدم"""
    
    def __init__(self):
        self.db_path = "data/articles.db"
        self._init_analytics_tables()
        self.google_analytics_id = None  # سيتم تعيينه من الإعدادات
        self.facebook_pixel_id = None
        
    def _init_analytics_tables(self):
        """إنشاء جداول التحليلات المتقدمة"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # جدول تتبع الزوار
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS visitor_tracking (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        visitor_id TEXT NOT NULL,
                        session_id TEXT NOT NULL,
                        page_url TEXT NOT NULL,
                        referrer TEXT,
                        user_agent TEXT,
                        ip_address TEXT,
                        country TEXT,
                        city TEXT,
                        device_type TEXT,
                        browser TEXT,
                        visit_duration INTEGER DEFAULT 0,
                        page_views INTEGER DEFAULT 1,
                        bounce_rate REAL DEFAULT 0.0,
                        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # جدول تتبع النقرات
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS click_tracking (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        visitor_id TEXT NOT NULL,
                        article_id INTEGER,
                        element_type TEXT NOT NULL,
                        element_id TEXT,
                        click_position TEXT,
                        page_url TEXT NOT NULL,
                        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (article_id) REFERENCES published_articles (id)
                    )
                ''')
                
                # جدول تحليل المحتوى
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS content_performance (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        article_id INTEGER NOT NULL,
                        views INTEGER DEFAULT 0,
                        unique_views INTEGER DEFAULT 0,
                        avg_time_on_page REAL DEFAULT 0.0,
                        bounce_rate REAL DEFAULT 0.0,
                        social_shares INTEGER DEFAULT 0,
                        comments_count INTEGER DEFAULT 0,
                        click_through_rate REAL DEFAULT 0.0,
                        conversion_rate REAL DEFAULT 0.0,
                        engagement_score REAL DEFAULT 0.0,
                        last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (article_id) REFERENCES published_articles (id)
                    )
                ''')
                
                # جدول تحليل الكلمات المفتاحية
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS keyword_performance (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        keyword TEXT NOT NULL,
                        search_volume INTEGER DEFAULT 0,
                        ranking_position INTEGER DEFAULT 0,
                        click_through_rate REAL DEFAULT 0.0,
                        impressions INTEGER DEFAULT 0,
                        clicks INTEGER DEFAULT 0,
                        competition_level TEXT DEFAULT 'medium',
                        trend_direction TEXT DEFAULT 'stable',
                        last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # جدول تحليل المنافسين
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS competitor_analysis (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        competitor_name TEXT NOT NULL,
                        competitor_url TEXT NOT NULL,
                        content_frequency INTEGER DEFAULT 0,
                        avg_engagement REAL DEFAULT 0.0,
                        top_keywords TEXT,
                        content_strategy TEXT,
                        strengths TEXT,
                        weaknesses TEXT,
                        last_analyzed TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # جدول اتجاهات المحتوى
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS content_trends (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        trend_topic TEXT NOT NULL,
                        trend_score REAL DEFAULT 0.0,
                        search_volume INTEGER DEFAULT 0,
                        social_mentions INTEGER DEFAULT 0,
                        news_mentions INTEGER DEFAULT 0,
                        trend_direction TEXT DEFAULT 'rising',
                        category TEXT,
                        detected_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                conn.commit()
                logger.info("✅ تم إنشاء جداول التحليلات المتقدمة بنجاح")
                
        except Exception as e:
            logger.error("❌ فشل في إنشاء جداول التحليلات", e)
    
    def track_visitor(self, visitor_data: Dict) -> bool:
        """تتبع زائر جديد"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT INTO visitor_tracking 
                    (visitor_id, session_id, page_url, referrer, user_agent, 
                     ip_address, country, city, device_type, browser)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    visitor_data.get('visitor_id'),
                    visitor_data.get('session_id'),
                    visitor_data.get('page_url'),
                    visitor_data.get('referrer'),
                    visitor_data.get('user_agent'),
                    visitor_data.get('ip_address'),
                    visitor_data.get('country'),
                    visitor_data.get('city'),
                    visitor_data.get('device_type'),
                    visitor_data.get('browser')
                ))
                
                conn.commit()
                return True
                
        except Exception as e:
            logger.error("❌ فشل في تتبع الزائر", e)
            return False
    
    def track_click(self, click_data: Dict) -> bool:
        """تتبع النقرات"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT INTO click_tracking 
                    (visitor_id, article_id, element_type, element_id, 
                     click_position, page_url)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    click_data.get('visitor_id'),
                    click_data.get('article_id'),
                    click_data.get('element_type'),
                    click_data.get('element_id'),
                    click_data.get('click_position'),
                    click_data.get('page_url')
                ))
                
                conn.commit()
                return True
                
        except Exception as e:
            logger.error("❌ فشل في تتبع النقرة", e)
            return False
    
    def analyze_content_performance(self, article_id: int) -> Dict:
        """تحليل أداء المحتوى"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # جمع بيانات الأداء
                cursor.execute('''
                    SELECT COUNT(*) as total_views,
                           COUNT(DISTINCT visitor_id) as unique_views,
                           AVG(visit_duration) as avg_time,
                           AVG(bounce_rate) as bounce_rate
                    FROM visitor_tracking 
                    WHERE page_url LIKE ?
                ''', (f'%article_id={article_id}%',))
                
                performance_data = cursor.fetchone()
                
                # جمع بيانات النقرات
                cursor.execute('''
                    SELECT COUNT(*) as total_clicks,
                           COUNT(DISTINCT element_type) as interaction_types
                    FROM click_tracking 
                    WHERE article_id = ?
                ''', (article_id,))
                
                click_data = cursor.fetchone()
                
                # حساب معدل التفاعل
                total_views = performance_data[0] if performance_data[0] else 1
                total_clicks = click_data[0] if click_data[0] else 0
                engagement_rate = (total_clicks / total_views) * 100
                
                analysis = {
                    'article_id': article_id,
                    'total_views': performance_data[0] or 0,
                    'unique_views': performance_data[1] or 0,
                    'avg_time_on_page': round(performance_data[2] or 0, 2),
                    'bounce_rate': round(performance_data[3] or 0, 2),
                    'total_clicks': total_clicks,
                    'engagement_rate': round(engagement_rate, 2),
                    'performance_score': self._calculate_performance_score(performance_data, click_data)
                }
                
                # حفظ التحليل
                self._save_content_performance(analysis)
                
                return analysis
                
        except Exception as e:
            logger.error("❌ فشل في تحليل أداء المحتوى", e)
            return {}
    
    def _calculate_performance_score(self, performance_data: Tuple, click_data: Tuple) -> float:
        """حساب نقاط الأداء"""
        try:
            views = performance_data[0] or 0
            unique_views = performance_data[1] or 0
            avg_time = performance_data[2] or 0
            bounce_rate = performance_data[3] or 100
            clicks = click_data[0] or 0
            
            # حساب النقاط بناءً على معايير مختلفة
            views_score = min(views / 100 * 20, 20)  # حد أقصى 20 نقطة للمشاهدات
            time_score = min(avg_time / 60 * 15, 15)  # حد أقصى 15 نقطة للوقت
            bounce_score = max(0, (100 - bounce_rate) / 100 * 15)  # حد أقصى 15 نقطة لمعدل الارتداد
            engagement_score = min(clicks / views * 100 if views > 0 else 0, 25)  # حد أقصى 25 نقطة للتفاعل
            unique_score = min(unique_views / views * 25 if views > 0 else 0, 25)  # حد أقصى 25 نقطة للزوار الفريدين
            
            total_score = views_score + time_score + bounce_score + engagement_score + unique_score
            
            return round(total_score, 2)
            
        except Exception as e:
            logger.error("❌ فشل في حساب نقاط الأداء", e)
            return 0.0
    
    def _save_content_performance(self, analysis: Dict):
        """حفظ تحليل أداء المحتوى"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT OR REPLACE INTO content_performance 
                    (article_id, views, unique_views, avg_time_on_page, 
                     bounce_rate, click_through_rate, engagement_score)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (
                    analysis['article_id'],
                    analysis['total_views'],
                    analysis['unique_views'],
                    analysis['avg_time_on_page'],
                    analysis['bounce_rate'],
                    analysis['engagement_rate'],
                    analysis['performance_score']
                ))
                
                conn.commit()
                
        except Exception as e:
            logger.error("❌ فشل في حفظ تحليل الأداء", e)
    
    def get_visitor_insights(self, days: int = 7) -> Dict:
        """الحصول على رؤى الزوار"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                start_date = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')
                
                # إحصائيات الزوار
                cursor.execute('''
                    SELECT COUNT(*) as total_visits,
                           COUNT(DISTINCT visitor_id) as unique_visitors,
                           AVG(visit_duration) as avg_session_duration,
                           AVG(page_views) as avg_page_views
                    FROM visitor_tracking 
                    WHERE DATE(timestamp) >= ?
                ''', (start_date,))
                
                visitor_stats = cursor.fetchone()
                
                # توزيع الأجهزة
                cursor.execute('''
                    SELECT device_type, COUNT(*) as count
                    FROM visitor_tracking 
                    WHERE DATE(timestamp) >= ?
                    GROUP BY device_type
                    ORDER BY count DESC
                ''', (start_date,))
                
                device_distribution = dict(cursor.fetchall())
                
                # أهم المصادر
                cursor.execute('''
                    SELECT referrer, COUNT(*) as count
                    FROM visitor_tracking 
                    WHERE DATE(timestamp) >= ? AND referrer IS NOT NULL
                    GROUP BY referrer
                    ORDER BY count DESC
                    LIMIT 10
                ''', (start_date,))
                
                top_referrers = dict(cursor.fetchall())
                
                # التوزيع الجغرافي
                cursor.execute('''
                    SELECT country, COUNT(*) as count
                    FROM visitor_tracking 
                    WHERE DATE(timestamp) >= ? AND country IS NOT NULL
                    GROUP BY country
                    ORDER BY count DESC
                    LIMIT 10
                ''', (start_date,))
                
                geographic_distribution = dict(cursor.fetchall())
                
                insights = {
                    'period_days': days,
                    'total_visits': visitor_stats[0] or 0,
                    'unique_visitors': visitor_stats[1] or 0,
                    'avg_session_duration': round(visitor_stats[2] or 0, 2),
                    'avg_page_views': round(visitor_stats[3] or 0, 2),
                    'device_distribution': device_distribution,
                    'top_referrers': top_referrers,
                    'geographic_distribution': geographic_distribution,
                    'engagement_metrics': self._calculate_engagement_metrics(start_date)
                }
                
                return insights
                
        except Exception as e:
            logger.error("❌ فشل في الحصول على رؤى الزوار", e)
            return {}
    
    def _calculate_engagement_metrics(self, start_date: str) -> Dict:
        """حساب مقاييس التفاعل"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # معدل الارتداد
                cursor.execute('''
                    SELECT AVG(bounce_rate) as avg_bounce_rate
                    FROM visitor_tracking 
                    WHERE DATE(timestamp) >= ?
                ''', (start_date,))
                
                bounce_rate = cursor.fetchone()[0] or 0
                
                # معدل النقر
                cursor.execute('''
                    SELECT COUNT(*) as total_clicks
                    FROM click_tracking 
                    WHERE DATE(timestamp) >= ?
                ''', (start_date,))
                
                total_clicks = cursor.fetchone()[0] or 0
                
                cursor.execute('''
                    SELECT COUNT(*) as total_visits
                    FROM visitor_tracking 
                    WHERE DATE(timestamp) >= ?
                ''', (start_date,))
                
                total_visits = cursor.fetchone()[0] or 1
                
                click_through_rate = (total_clicks / total_visits) * 100
                
                return {
                    'bounce_rate': round(bounce_rate, 2),
                    'click_through_rate': round(click_through_rate, 2),
                    'total_interactions': total_clicks
                }
                
        except Exception as e:
            logger.error("❌ فشل في حساب مقاييس التفاعل", e)
            return {}
    
    def analyze_content_trends(self) -> Dict:
        """تحليل اتجاهات المحتوى"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # أفضل المقالات أداءً
                cursor.execute('''
                    SELECT pa.title, cp.engagement_score, cp.views
                    FROM content_performance cp
                    JOIN published_articles pa ON cp.article_id = pa.id
                    ORDER BY cp.engagement_score DESC
                    LIMIT 10
                ''')
                
                top_performing = cursor.fetchall()
                
                # الكلمات المفتاحية الأكثر فعالية
                cursor.execute('''
                    SELECT keyword, AVG(click_through_rate) as avg_ctr
                    FROM keyword_performance
                    WHERE last_updated >= date('now', '-30 days')
                    GROUP BY keyword
                    ORDER BY avg_ctr DESC
                    LIMIT 10
                ''')
                
                top_keywords = cursor.fetchall()
                
                # اتجاهات المحتوى الصاعدة
                cursor.execute('''
                    SELECT trend_topic, trend_score, search_volume
                    FROM content_trends
                    WHERE trend_direction = 'rising'
                    ORDER BY trend_score DESC
                    LIMIT 10
                ''')
                
                rising_trends = cursor.fetchall()
                
                return {
                    'top_performing_articles': [
                        {'title': row[0], 'engagement_score': row[1], 'views': row[2]}
                        for row in top_performing
                    ],
                    'top_keywords': [
                        {'keyword': row[0], 'ctr': row[1]}
                        for row in top_keywords
                    ],
                    'rising_trends': [
                        {'topic': row[0], 'score': row[1], 'volume': row[2]}
                        for row in rising_trends
                    ]
                }
                
        except Exception as e:
            logger.error("❌ فشل في تحليل اتجاهات المحتوى", e)
            return {}
    
    def generate_analytics_report(self, days: int = 7) -> Dict:
        """إنشاء تقرير تحليلي شامل"""
        try:
            visitor_insights = self.get_visitor_insights(days)
            content_trends = self.analyze_content_trends()
            
            # حساب معدلات النمو
            growth_metrics = self._calculate_growth_metrics(days)
            
            # توصيات التحسين
            recommendations = self._generate_optimization_recommendations(visitor_insights, content_trends)
            
            report = {
                'report_date': datetime.now().isoformat(),
                'period_days': days,
                'visitor_insights': visitor_insights,
                'content_trends': content_trends,
                'growth_metrics': growth_metrics,
                'recommendations': recommendations,
                'summary': self._generate_report_summary(visitor_insights, content_trends, growth_metrics)
            }
            
            logger.info(f"📊 تم إنشاء تقرير تحليلي لـ {days} أيام")
            
            return report
            
        except Exception as e:
            logger.error("❌ فشل في إنشاء التقرير التحليلي", e)
            return {}
    
    def _calculate_growth_metrics(self, days: int) -> Dict:
        """حساب معدلات النمو"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # مقارنة الفترة الحالية بالفترة السابقة
                current_start = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')
                previous_start = (datetime.now() - timedelta(days=days*2)).strftime('%Y-%m-%d')
                previous_end = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')
                
                # زوار الفترة الحالية
                cursor.execute('''
                    SELECT COUNT(DISTINCT visitor_id) as unique_visitors
                    FROM visitor_tracking 
                    WHERE DATE(timestamp) >= ?
                ''', (current_start,))
                
                current_visitors = cursor.fetchone()[0] or 0
                
                # زوار الفترة السابقة
                cursor.execute('''
                    SELECT COUNT(DISTINCT visitor_id) as unique_visitors
                    FROM visitor_tracking 
                    WHERE DATE(timestamp) >= ? AND DATE(timestamp) < ?
                ''', (previous_start, previous_end))
                
                previous_visitors = cursor.fetchone()[0] or 1
                
                # حساب معدل النمو
                growth_rate = ((current_visitors - previous_visitors) / previous_visitors) * 100
                
                return {
                    'visitor_growth_rate': round(growth_rate, 2),
                    'current_period_visitors': current_visitors,
                    'previous_period_visitors': previous_visitors
                }
                
        except Exception as e:
            logger.error("❌ فشل في حساب معدلات النمو", e)
            return {}
    
    def _generate_optimization_recommendations(self, visitor_insights: Dict, content_trends: Dict) -> List[str]:
        """توليد توصيات التحسين"""
        recommendations = []
        
        # تحليل معدل الارتداد
        bounce_rate = visitor_insights.get('engagement_metrics', {}).get('bounce_rate', 0)
        if bounce_rate > 70:
            recommendations.append("معدل الارتداد مرتفع - حسن سرعة التحميل وجودة المحتوى")
        
        # تحليل مدة الجلسة
        avg_duration = visitor_insights.get('avg_session_duration', 0)
        if avg_duration < 60:
            recommendations.append("مدة الجلسة قصيرة - أضف محتوى أكثر تفاعلية")
        
        # تحليل التوزيع الجغرافي
        geo_dist = visitor_insights.get('geographic_distribution', {})
        if len(geo_dist) < 3:
            recommendations.append("التنوع الجغرافي محدود - استهدف أسواق جديدة")
        
        # تحليل الأجهزة
        device_dist = visitor_insights.get('device_distribution', {})
        mobile_percentage = device_dist.get('mobile', 0) / sum(device_dist.values()) * 100 if device_dist else 0
        if mobile_percentage < 60:
            recommendations.append("حسن تجربة الهاتف المحمول - معظم الزوار يستخدمون الجوال")
        
        return recommendations
    
    def _generate_report_summary(self, visitor_insights: Dict, content_trends: Dict, growth_metrics: Dict) -> str:
        """توليد ملخص التقرير"""
        total_visitors = visitor_insights.get('unique_visitors', 0)
        growth_rate = growth_metrics.get('visitor_growth_rate', 0)
        top_article = content_trends.get('top_performing_articles', [{}])[0] if content_trends.get('top_performing_articles') else {}
        
        summary = f"""
        📊 ملخص الأداء:
        • إجمالي الزوار الفريدين: {total_visitors:,}
        • معدل النمو: {growth_rate:+.1f}%
        • أفضل مقال: {top_article.get('title', 'غير متوفر')}
        • نقاط التفاعل: {top_article.get('engagement_score', 0):.1f}
        """
        
        return summary.strip()

# إنشاء مثيل عام لتحليلات الزوار
visitor_analytics = VisitorAnalytics()
