# نظام إدارة دورة حياة الوكيل الذكي
import asyncio
import signal
import threading
import time
import json
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
from enum import Enum
from dataclasses import dataclass, asdict
from .logger import logger
from .agent_state_manager import agent_state_manager, AgentState
from .operation_manager import operation_manager
from .smart_database_manager import smart_db_manager

class ShutdownReason(Enum):
    """أسباب الإيقاف"""
    USER_REQUEST = "user_request"
    SYSTEM_SIGNAL = "system_signal"
    ERROR = "error"
    MAINTENANCE = "maintenance"
    RESOURCE_EXHAUSTION = "resource_exhaustion"
    SCHEDULED = "scheduled"

class StartupMode(Enum):
    """أوضاع البدء"""
    NORMAL = "normal"
    RECOVERY = "recovery"
    MAINTENANCE = "maintenance"
    SAFE_MODE = "safe_mode"

@dataclass
class ShutdownInfo:
    """معلومات الإيقاف"""
    reason: ShutdownReason
    timestamp: datetime
    initiated_by: str
    graceful: bool
    cleanup_completed: bool
    operations_saved: int
    errors: List[str]
    metadata: Dict[str, Any]

@dataclass
class StartupInfo:
    """معلومات البدء"""
    mode: StartupMode
    timestamp: datetime
    previous_shutdown: Optional[ShutdownInfo]
    recovery_needed: bool
    operations_restored: int
    database_healthy: bool
    errors: List[str]
    metadata: Dict[str, Any]

class SmartLifecycleManager:
    """مدير دورة حياة الوكيل الذكي"""
    
    def __init__(self):
        self._shutdown_callbacks: List[Callable] = []
        self._startup_callbacks: List[Callable] = []
        self._cleanup_callbacks: List[Callable] = []
        self._is_shutting_down = False
        self._is_starting_up = False
        self._shutdown_timeout = 60  # 60 ثانية
        self._startup_timeout = 120  # 120 ثانية
        self._last_shutdown_info: Optional[ShutdownInfo] = None
        self._last_startup_info: Optional[StartupInfo] = None
        
        # ملف حفظ معلومات دورة الحياة
        self.lifecycle_file = "agent_lifecycle.json"
        
        # تحميل معلومات دورة الحياة السابقة
        self._load_lifecycle_info()
        
        # إعداد معالجات الإشارات
        self._setup_signal_handlers()
    
    def _setup_signal_handlers(self):
        """إعداد معالجات إشارات النظام"""
        try:
            def signal_handler(signum, frame):
                logger.info(f"📡 تم استلام إشارة النظام {signum}")
                asyncio.create_task(self.graceful_shutdown(
                    reason=ShutdownReason.SYSTEM_SIGNAL,
                    initiated_by=f"system_signal_{signum}"
                ))
            
            signal.signal(signal.SIGINT, signal_handler)
            signal.signal(signal.SIGTERM, signal_handler)
            
            logger.info("✅ تم إعداد معالجات إشارات النظام")
            
        except ValueError as e:
            logger.warning(f"⚠️ لا يمكن إعداد معالجات الإشارات: {e}")
    
    def _load_lifecycle_info(self):
        """تحميل معلومات دورة الحياة السابقة"""
        try:
            if os.path.exists(self.lifecycle_file):
                with open(self.lifecycle_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                if 'last_shutdown' in data:
                    shutdown_data = data['last_shutdown']
                    shutdown_data['reason'] = ShutdownReason(shutdown_data['reason'])
                    shutdown_data['timestamp'] = datetime.fromisoformat(shutdown_data['timestamp'])
                    self._last_shutdown_info = ShutdownInfo(**shutdown_data)
                
                if 'last_startup' in data:
                    startup_data = data['last_startup']
                    startup_data['mode'] = StartupMode(startup_data['mode'])
                    startup_data['timestamp'] = datetime.fromisoformat(startup_data['timestamp'])
                    if startup_data.get('previous_shutdown'):
                        prev_shutdown = startup_data['previous_shutdown']
                        prev_shutdown['reason'] = ShutdownReason(prev_shutdown['reason'])
                        prev_shutdown['timestamp'] = datetime.fromisoformat(prev_shutdown['timestamp'])
                        startup_data['previous_shutdown'] = ShutdownInfo(**prev_shutdown)
                    self._last_startup_info = StartupInfo(**startup_data)
                
                logger.info("✅ تم تحميل معلومات دورة الحياة السابقة")
                
        except Exception as e:
            logger.warning(f"⚠️ فشل في تحميل معلومات دورة الحياة: {e}")
    
    def _save_lifecycle_info(self):
        """حفظ معلومات دورة الحياة"""
        try:
            data = {}
            
            if self._last_shutdown_info:
                shutdown_data = asdict(self._last_shutdown_info)
                shutdown_data['reason'] = self._last_shutdown_info.reason.value
                shutdown_data['timestamp'] = self._last_shutdown_info.timestamp.isoformat()
                data['last_shutdown'] = shutdown_data
            
            if self._last_startup_info:
                startup_data = asdict(self._last_startup_info)
                startup_data['mode'] = self._last_startup_info.mode.value
                startup_data['timestamp'] = self._last_startup_info.timestamp.isoformat()
                if startup_data.get('previous_shutdown'):
                    prev_shutdown = startup_data['previous_shutdown']
                    if isinstance(prev_shutdown, ShutdownInfo):
                        prev_shutdown_data = asdict(prev_shutdown)
                        prev_shutdown_data['reason'] = prev_shutdown.reason.value
                        prev_shutdown_data['timestamp'] = prev_shutdown.timestamp.isoformat()
                        startup_data['previous_shutdown'] = prev_shutdown_data
                data['last_startup'] = startup_data
            
            with open(self.lifecycle_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            logger.debug("✅ تم حفظ معلومات دورة الحياة")
            
        except Exception as e:
            logger.error(f"❌ فشل في حفظ معلومات دورة الحياة: {e}")
    
    def register_shutdown_callback(self, callback: Callable):
        """تسجيل دالة استدعاء للإيقاف"""
        self._shutdown_callbacks.append(callback)
    
    def register_startup_callback(self, callback: Callable):
        """تسجيل دالة استدعاء للبدء"""
        self._startup_callbacks.append(callback)
    
    def register_cleanup_callback(self, callback: Callable):
        """تسجيل دالة استدعاء للتنظيف"""
        self._cleanup_callbacks.append(callback)
    
    async def smart_startup(self, mode: StartupMode = StartupMode.NORMAL) -> bool:
        """بدء ذكي للوكيل"""
        if self._is_starting_up:
            logger.warning("⚠️ عملية البدء جارية بالفعل")
            return False

        # حماية من الحلقة اللا نهائية
        if hasattr(self, '_startup_attempts'):
            self._startup_attempts += 1
            if self._startup_attempts > 3:
                logger.error("❌ تم تجاوز الحد الأقصى لمحاولات البدء (3 محاولات)")
                return False
        else:
            self._startup_attempts = 1

        self._is_starting_up = True
        start_time = datetime.now()
        errors = []

        logger.info(f"🚀 بدء تشغيل الوكيل في وضع: {mode.value} (المحاولة {self._startup_attempts}/3)")

        try:
            # إضافة timeout أقصر للعملية الكاملة
            result = await asyncio.wait_for(
                self._perform_startup(mode, start_time, errors),
                timeout=30.0  # 30 ثانية فقط
            )

            if result:
                # إعادة تعيين العداد عند النجاح
                self._startup_attempts = 0

            return result

        except asyncio.TimeoutError:
            logger.error(f"❌ انتهت مهلة بدء التشغيل (30 ثانية)")
            self._is_starting_up = False
            return False
        except Exception as e:
            logger.error(f"❌ خطأ في بدء التشغيل: {e}")
            self._is_starting_up = False
            return False
        finally:
            self._is_starting_up = False

    async def _perform_startup(self, mode: StartupMode, start_time: datetime, errors: List[str]) -> bool:
        """تنفيذ عملية البدء الفعلية"""
        try:
            # تحديد ما إذا كانت الاستعادة مطلوبة
            recovery_needed = self._is_recovery_needed()
            
            if recovery_needed and mode == StartupMode.NORMAL:
                logger.info("🔄 تم اكتشاف الحاجة للاستعادة، التحويل لوضع الاستعادة")
                mode = StartupMode.RECOVERY
            
            # تعيين حالة البدء
            agent_state_manager.set_state(AgentState.STARTING, f"بدء التشغيل - {mode.value}")
            
            # 1. فحص وتهيئة قاعدة البيانات (مع timeout قصير)
            logger.info("🔍 فحص وتهيئة قاعدة البيانات...")
            try:
                # تشغيل فحص قاعدة البيانات مع timeout قصير
                logger.info("⏱️ بدء فحص قاعدة البيانات مع timeout 3 ثوان...")
                db_healthy = await asyncio.wait_for(
                    asyncio.to_thread(smart_db_manager.check_and_initialize_database),
                    timeout=3.0  # 3 ثوان فقط
                )
                logger.info("✅ تم فحص قاعدة البيانات بنجاح")
            except asyncio.TimeoutError:
                logger.warning("⚠️ انتهت مهلة فحص قاعدة البيانات (3 ثوان)، استخدام النظام التقليدي")
                db_healthy = True  # متابعة مع النظام التقليدي
            except Exception as e:
                logger.error(f"❌ خطأ في فحص قاعدة البيانات: {e}")
                db_healthy = True  # متابعة مع النظام التقليدي

            if not db_healthy:
                errors.append("فشل في تهيئة قاعدة البيانات")
                if mode != StartupMode.SAFE_MODE:
                    logger.warning("⚠️ مشكلة في قاعدة البيانات، التحويل للوضع الآمن")
                    # منع إعادة الاستدعاء اللا نهائية
                    if hasattr(self, '_safe_mode_attempted') and self._safe_mode_attempted:
                        logger.error("❌ فشل في الوضع الآمن أيضاً، إيقاف المحاولات")
                        return False
                    self._safe_mode_attempted = True
                    return await self.smart_startup(StartupMode.SAFE_MODE)
            
            # 2. بدء مدير العمليات (مع timeout وإعادة المحاولة)
            logger.info("🔄 بدء مدير العمليات...")
            operation_manager_started = False

            for attempt in range(3):  # 3 محاولات
                try:
                    logger.info(f"🔄 محاولة بدء مدير العمليات - المحاولة {attempt + 1}/3...")
                    await asyncio.wait_for(operation_manager.start(), timeout=5.0)  # timeout أقصر
                    operation_manager_started = True
                    logger.info("✅ تم بدء مدير العمليات بنجاح")
                    break
                except asyncio.TimeoutError:
                    logger.warning(f"⚠️ انتهت مهلة بدء مدير العمليات (5 ثوان) - المحاولة {attempt + 1}/3")
                    if attempt < 2:  # ليس المحاولة الأخيرة
                        logger.info("🔄 إعادة المحاولة خلال ثانيتين...")
                        await asyncio.sleep(2)  # انتظار قبل إعادة المحاولة
                except Exception as e:
                    logger.error(f"❌ خطأ في بدء مدير العمليات - المحاولة {attempt + 1}/3: {e}")
                    if attempt < 2:  # ليس المحاولة الأخيرة
                        logger.info("🔄 إعادة المحاولة خلال ثانيتين...")
                        await asyncio.sleep(2)  # انتظار قبل إعادة المحاولة

            if not operation_manager_started:
                logger.error("❌ فشل في بدء مدير العمليات بعد 3 محاولات")
                errors.append("فشل في بدء مدير العمليات")
                # في هذه الحالة، يجب التحويل للوضع الآمن أو التقليدي
                if mode != StartupMode.SAFE_MODE:
                    logger.warning("⚠️ التحويل للوضع الآمن بسبب فشل مدير العمليات")
                    # منع إعادة الاستدعاء اللا نهائية
                    if hasattr(self, '_safe_mode_attempted') and self._safe_mode_attempted:
                        logger.error("❌ فشل في الوضع الآمن أيضاً، إيقاف المحاولات")
                        return False
                    self._safe_mode_attempted = True
                    return await self.smart_startup(StartupMode.SAFE_MODE)
            
            # 3. استعادة العمليات المتوقفة (في وضع الاستعادة)
            operations_restored = 0
            if mode == StartupMode.RECOVERY:
                operations_restored = await self._restore_operations()
            
            # 4. تشغيل دوال الاستدعاء للبدء (مع timeout)
            logger.info("📞 تشغيل دوال الاستدعاء للبدء...")
            for i, callback in enumerate(self._startup_callbacks):
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await asyncio.wait_for(callback(), timeout=10.0)
                    else:
                        callback()
                except asyncio.TimeoutError:
                    logger.warning(f"⚠️ انتهت مهلة دالة الاستدعاء {i+1}")
                    errors.append(f"انتهت مهلة دالة الاستدعاء {i+1}")
                except Exception as e:
                    error_msg = f"فشل في دالة الاستدعاء للبدء: {str(e)}"
                    errors.append(error_msg)
                    logger.error(f"❌ {error_msg}")
            
            # 5. التحضير النهائي
            success = agent_state_manager.prepare_for_startup()
            
            if success:
                agent_state_manager.set_state(AgentState.RUNNING, "الوكيل جاهز للعمل")
                logger.info("✅ تم بدء تشغيل الوكيل بنجاح")
            else:
                errors.append("فشل في التحضير النهائي للبدء")
                agent_state_manager.set_state(AgentState.ERROR, "فشل في البدء")
            
            # حفظ معلومات البدء
            self._last_startup_info = StartupInfo(
                mode=mode,
                timestamp=start_time,
                previous_shutdown=self._last_shutdown_info,
                recovery_needed=recovery_needed,
                operations_restored=operations_restored,
                database_healthy=db_healthy,
                errors=errors,
                metadata={
                    'startup_duration': (datetime.now() - start_time).total_seconds(),
                    'callbacks_executed': len(self._startup_callbacks)
                }
            )
            
            self._save_lifecycle_info()
            
            return success
            
        except Exception as e:
            error_msg = f"خطأ حرج في بدء التشغيل: {str(e)}"
            errors.append(error_msg)
            logger.critical(f"❌ {error_msg}")
            
            agent_state_manager.set_state(AgentState.ERROR, error_msg)
            
            return False
            
        finally:
            self._is_starting_up = False
    
    async def graceful_shutdown(
        self,
        reason: ShutdownReason = ShutdownReason.USER_REQUEST,
        initiated_by: str = "system",
        timeout: Optional[int] = None
    ) -> bool:
        """إيقاف آمن للوكيل"""
        
        if self._is_shutting_down:
            logger.warning("⚠️ عملية الإيقاف جارية بالفعل")
            return False
        
        self._is_shutting_down = True
        start_time = datetime.now()
        errors = []
        operations_saved = 0
        
        timeout = timeout or self._shutdown_timeout
        
        logger.info(f"🛑 بدء الإيقاف الآمن - السبب: {reason.value}")
        
        try:
            # تعيين حالة الإيقاف
            agent_state_manager.set_state(AgentState.STOPPING, f"إيقاف الوكيل - {reason.value}")
            
            # 1. حفظ العمليات الجارية
            logger.info("💾 حفظ العمليات الجارية...")
            operations_saved = await self._save_running_operations()
            
            # 2. تشغيل دوال الاستدعاء للإيقاف
            logger.info("📞 تشغيل دوال الاستدعاء للإيقاف...")
            for callback in self._shutdown_callbacks:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await asyncio.wait_for(callback(), timeout=10)
                    else:
                        callback()
                except Exception as e:
                    error_msg = f"فشل في دالة الاستدعاء للإيقاف: {str(e)}"
                    errors.append(error_msg)
                    logger.error(f"❌ {error_msg}")
            
            # 3. إيقاف مدير العمليات
            logger.info("⏹️ إيقاف مدير العمليات...")
            await asyncio.wait_for(operation_manager.stop(), timeout=30)
            
            # 4. التحضير للإيقاف في مدير الحالة
            logger.info("🔄 التحضير للإيقاف...")
            agent_state_manager.prepare_for_shutdown()
            
            # 5. تشغيل دوال التنظيف
            logger.info("🧹 تشغيل دوال التنظيف...")
            for callback in self._cleanup_callbacks:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await asyncio.wait_for(callback(), timeout=10)
                    else:
                        callback()
                except Exception as e:
                    error_msg = f"فشل في دالة التنظيف: {str(e)}"
                    errors.append(error_msg)
                    logger.error(f"❌ {error_msg}")
            
            # 6. إنشاء نسخة احتياطية نهائية
            logger.info("💾 إنشاء نسخة احتياطية نهائية...")
            try:
                smart_db_manager.backup_database()
            except Exception as e:
                error_msg = f"فشل في إنشاء النسخة الاحتياطية: {str(e)}"
                errors.append(error_msg)
                logger.warning(f"⚠️ {error_msg}")
            
            # تعيين الحالة النهائية
            agent_state_manager.set_state(AgentState.STOPPED, "تم إيقاف الوكيل")
            
            cleanup_completed = len(errors) == 0
            
            logger.info(f"✅ تم الإيقاف الآمن {'بنجاح' if cleanup_completed else 'مع بعض الأخطاء'}")
            
            # حفظ معلومات الإيقاف
            self._last_shutdown_info = ShutdownInfo(
                reason=reason,
                timestamp=start_time,
                initiated_by=initiated_by,
                graceful=True,
                cleanup_completed=cleanup_completed,
                operations_saved=operations_saved,
                errors=errors,
                metadata={
                    'shutdown_duration': (datetime.now() - start_time).total_seconds(),
                    'callbacks_executed': len(self._shutdown_callbacks),
                    'cleanup_callbacks_executed': len(self._cleanup_callbacks)
                }
            )
            
            self._save_lifecycle_info()
            
            return cleanup_completed
            
        except asyncio.TimeoutError:
            logger.error(f"⏰ انتهت المهلة الزمنية للإيقاف ({timeout} ثانية)")
            errors.append("انتهت المهلة الزمنية للإيقاف")
            return False
            
        except Exception as e:
            error_msg = f"خطأ حرج في الإيقاف الآمن: {str(e)}"
            errors.append(error_msg)
            logger.critical(f"❌ {error_msg}")
            return False
            
        finally:
            self._is_shutting_down = False

    def _is_recovery_needed(self) -> bool:
        """فحص ما إذا كانت الاستعادة مطلوبة"""
        if not self._last_shutdown_info:
            return False

        # إذا كان الإيقاف السابق غير آمن
        if not self._last_shutdown_info.graceful:
            return True

        # إذا كان هناك عمليات محفوظة
        if self._last_shutdown_info.operations_saved > 0:
            return True

        # إذا كان الإيقاف بسبب خطأ
        if self._last_shutdown_info.reason == ShutdownReason.ERROR:
            return True

        return False

    async def _save_running_operations(self) -> int:
        """حفظ العمليات الجارية"""
        try:
            active_operations = operation_manager.get_active_operations()

            if not active_operations:
                return 0

            # حفظ العمليات في ملف
            operations_file = "saved_operations.json"
            operations_data = {
                'timestamp': datetime.now().isoformat(),
                'operations': active_operations
            }

            with open(operations_file, 'w', encoding='utf-8') as f:
                json.dump(operations_data, f, ensure_ascii=False, indent=2)

            logger.info(f"💾 تم حفظ {len(active_operations)} عملية جارية")
            return len(active_operations)

        except Exception as e:
            logger.error(f"❌ فشل في حفظ العمليات الجارية: {e}")
            return 0

    async def _restore_operations(self) -> int:
        """استعادة العمليات المحفوظة"""
        try:
            operations_file = "saved_operations.json"

            if not os.path.exists(operations_file):
                return 0

            with open(operations_file, 'r', encoding='utf-8') as f:
                operations_data = json.load(f)

            operations = operations_data.get('operations', [])

            if not operations:
                return 0

            # استعادة العمليات القابلة للاستئناف
            restored_count = 0

            for op_data in operations:
                try:
                    # فقط العمليات المتوقفة أو المعلقة
                    if op_data.get('state') in ['paused', 'pending']:
                        # هنا يمكن إضافة منطق استعادة العمليات
                        # حسب نوع العملية
                        restored_count += 1
                        logger.info(f"🔄 تم استعادة العملية: {op_data.get('operation_type')}")

                except Exception as e:
                    logger.warning(f"⚠️ فشل في استعادة العملية: {e}")

            # حذف ملف العمليات المحفوظة بعد الاستعادة
            os.remove(operations_file)

            logger.info(f"✅ تم استعادة {restored_count} عملية")
            return restored_count

        except Exception as e:
            logger.error(f"❌ فشل في استعادة العمليات: {e}")
            return 0

    async def emergency_shutdown(self, reason: str = "طوارئ") -> bool:
        """إيقاف طارئ سريع"""
        logger.warning(f"🚨 إيقاف طارئ: {reason}")

        try:
            # إيقاف سريع بدون حفظ مفصل
            agent_state_manager.set_state(AgentState.STOPPING, f"إيقاف طارئ - {reason}")

            # إيقاف العمليات فوراً
            await operation_manager.stop()

            # حفظ الحالة الأساسية
            agent_state_manager.save_state()

            agent_state_manager.set_state(AgentState.STOPPED, "تم الإيقاف الطارئ")

            # حفظ معلومات الإيقاف الطارئ
            self._last_shutdown_info = ShutdownInfo(
                reason=ShutdownReason.ERROR,
                timestamp=datetime.now(),
                initiated_by="emergency_system",
                graceful=False,
                cleanup_completed=False,
                operations_saved=0,
                errors=[f"إيقاف طارئ: {reason}"],
                metadata={'emergency': True}
            )

            self._save_lifecycle_info()

            logger.warning("⚠️ تم الإيقاف الطارئ")
            return True

        except Exception as e:
            logger.critical(f"❌ فشل في الإيقاف الطارئ: {e}")
            return False

    def get_lifecycle_status(self) -> Dict[str, Any]:
        """الحصول على حالة دورة الحياة"""
        return {
            'is_starting_up': self._is_starting_up,
            'is_shutting_down': self._is_shutting_down,
            'current_state': agent_state_manager.get_current_state().value,
            'last_startup': asdict(self._last_startup_info) if self._last_startup_info else None,
            'last_shutdown': asdict(self._last_shutdown_info) if self._last_shutdown_info else None,
            'recovery_needed': self._is_recovery_needed(),
            'registered_callbacks': {
                'startup': len(self._startup_callbacks),
                'shutdown': len(self._shutdown_callbacks),
                'cleanup': len(self._cleanup_callbacks)
            }
        }

    def get_health_report(self) -> Dict[str, Any]:
        """تقرير صحة النظام"""
        current_state = agent_state_manager.get_current_state_info()
        db_health = smart_db_manager.get_database_health_report()
        operation_stats = operation_manager.get_statistics()

        # تقييم الصحة العامة
        health_score = 100
        issues = []

        # فحص حالة الوكيل
        if current_state.state == AgentState.ERROR:
            health_score -= 50
            issues.append("الوكيل في حالة خطأ")
        elif current_state.state == AgentState.STOPPED:
            health_score -= 30
            issues.append("الوكيل متوقف")

        # فحص قاعدة البيانات
        if not db_health.get('integrity_check') == 'ok':
            health_score -= 20
            issues.append("مشكلة في سلامة قاعدة البيانات")

        # فحص الذاكرة
        if current_state.memory_usage_mb > 512:  # أكثر من 512 MB
            health_score -= 10
            issues.append("استخدام ذاكرة مرتفع")

        # فحص المعالج
        if current_state.cpu_usage_percent > 80:
            health_score -= 10
            issues.append("استخدام معالج مرتفع")

        # فحص العمليات الفاشلة
        if operation_stats.get('failed_operations', 0) > 5:
            health_score -= 15
            issues.append("عدد كبير من العمليات الفاشلة")

        health_status = "ممتاز" if health_score >= 90 else \
                       "جيد" if health_score >= 70 else \
                       "متوسط" if health_score >= 50 else \
                       "ضعيف"

        return {
            'health_score': max(0, health_score),
            'health_status': health_status,
            'issues': issues,
            'agent_state': current_state.to_dict(),
            'database_health': db_health,
            'operation_stats': operation_stats,
            'uptime_hours': current_state.uptime_seconds / 3600,
            'last_error': current_state.last_error,
            'recommendations': self._generate_health_recommendations(health_score, issues)
        }

    def _generate_health_recommendations(self, health_score: int, issues: List[str]) -> List[str]:
        """إنشاء توصيات لتحسين الصحة"""
        recommendations = []

        if health_score < 70:
            recommendations.append("إعادة تشغيل الوكيل لتحسين الأداء")

        if "مشكلة في سلامة قاعدة البيانات" in issues:
            recommendations.append("تشغيل فحص وإصلاح قاعدة البيانات")

        if "استخدام ذاكرة مرتفع" in issues:
            recommendations.append("تنظيف الذاكرة وإعادة تشغيل العمليات")

        if "عدد كبير من العمليات الفاشلة" in issues:
            recommendations.append("فحص سجل الأخطاء وإصلاح المشاكل")

        if not recommendations:
            recommendations.append("النظام يعمل بشكل جيد")

        return recommendations

# إنشاء مثيل مدير دورة الحياة
lifecycle_manager = SmartLifecycleManager()
