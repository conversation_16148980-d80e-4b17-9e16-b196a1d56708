#!/usr/bin/env python3
"""
ملف بدء التشغيل المحسن لـ Render
يشغل البوت مع إصلاح جميع المشاكل
"""

import os
import sys
import logging
import threading
import time
import requests
from pathlib import Path

# إعداد المسارات
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# تطبيق الإصلاحات في البداية
try:
    from render_fixes import apply_all_fixes
    from port_fix import fix_port_conflicts, create_single_server_config

    # تطبيق جميع الإصلاحات
    apply_all_fixes()
    fix_port_conflicts()
    create_single_server_config()

    print("✅ تم تطبيق جميع الإصلاحات بنجاح")
except Exception as e:
    print(f"⚠️ تحذير: لم يتم تطبيق بعض الإصلاحات: {e}")

# إعداد السجلات المحسن
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

def fix_telegram_conflicts():
    """إصلاح مشكلة Telegram getUpdates conflicts"""
    try:
        # الحصول على token البوت
        bot_token = os.getenv('BOT_TOKEN') or os.getenv('TELEGRAM_BOT_TOKEN')
        if not bot_token:
            logger.warning("⚠️ لم يتم العثور على token البوت")
            return False

        logger.info("🔧 إصلاح مشكلة Telegram getUpdates conflicts...")

        # مسح webhook إذا كان موجود
        try:
            url = f"https://api.telegram.org/bot{bot_token}/deleteWebhook"
            response = requests.post(url, json={"drop_pending_updates": True}, timeout=10)
            if response.status_code == 200 and response.json().get('ok'):
                logger.info("✅ تم مسح webhook للبوت")
        except Exception as e:
            logger.warning(f"⚠️ تحذير في مسح webhook: {e}")

        # مسح التحديثات المعلقة
        try:
            url = f"https://api.telegram.org/bot{bot_token}/getUpdates"
            params = {"offset": -1, "limit": 1, "timeout": 0}
            response = requests.get(url, params=params, timeout=10)

            if response.status_code == 200:
                result = response.json()
                if result.get('ok') and result.get('result'):
                    last_update_id = result['result'][-1]['update_id']
                    params['offset'] = last_update_id + 1
                    requests.get(url, params=params, timeout=10)
                    logger.info("✅ تم مسح التحديثات المعلقة")
        except Exception as e:
            logger.warning(f"⚠️ تحذير في مسح التحديثات: {e}")

        return True

    except Exception as e:
        logger.error(f"❌ خطأ في إصلاح Telegram: {e}")
        return False

def remove_ngrok_dependencies():
    """إزالة الاعتماد على ngrok"""
    try:
        logger.info("🔧 إزالة الاعتماد على ngrok...")

        # تعطيل ngrok في متغيرات البيئة
        os.environ['USE_NGROK'] = 'false'
        os.environ['NGROK_ENABLED'] = 'false'

        logger.info("✅ تم تعطيل ngrok")
        return True

    except Exception as e:
        logger.error(f"❌ خطأ في إزالة ngrok: {e}")
        return False

def setup_environment():
    """إعداد البيئة للعمل على Render مع إصلاح جميع المشاكل"""
    logger.info("🚀 بدء إعداد البيئة المحسنة لـ Render...")

    # إنشاء المجلدات المطلوبة
    required_dirs = [
        'logs',
        'results',
        'assets/backgrounds/video',
        'assets/backgrounds/audio',
        'assets/temp',
        'data',
        'cache',
        'config'
    ]

    for dir_path in required_dirs:
        Path(dir_path).mkdir(parents=True, exist_ok=True)
        logger.info(f"✅ تم إنشاء المجلد: {dir_path}")

    # إصلاح مشكلة Supabase - استخدام الإعدادات الصحيحة
    supabase_config = {
        'SUPABASE_URL': 'https://ytqxxodyecdeosnqoure.supabase.co',
        'SUPABASE_KEY': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4'
    }

    for key, value in supabase_config.items():
        if not os.environ.get(key):
            os.environ[key] = value
            logger.info(f"✅ تم تعيين {key}")

    # إصلاح مشكلة Telegram - إعداد متغيرات البوت
    telegram_config = {
        'BOT_TOKEN': '**********************************************',
        'TELEGRAM_BOT_TOKEN': '**********************************************',
        'ADMIN_CHAT_ID': '7513880877'
    }

    for key, value in telegram_config.items():
        if not os.environ.get(key):
            os.environ[key] = value
            logger.info(f"✅ تم تعيين {key}")

    # تعيين متغيرات البيئة الافتراضية المحسنة
    default_env_vars = {
        'PYTHONUNBUFFERED': '1',
        'PYTHONIOENCODING': 'utf-8',
        'TZ': 'UTC',
        'RENDER': 'true',
        'DEPLOYMENT_ENV': 'render',
        'ENVIRONMENT': 'production',
        'DEBUG': 'false',
        'LOG_LEVEL': 'INFO',
        'USE_NGROK': 'false',  # تعطيل ngrok
        'HOSTING_PLATFORM': 'render'
    }

    for key, value in default_env_vars.items():
        if key not in os.environ:
            os.environ[key] = value
            logger.info(f"✅ تم تعيين متغير البيئة: {key}={value}")

    # إصلاح مشكلة Telegram getUpdates conflict
    fix_telegram_conflicts()


    # إزالة الاعتماد على ngrok
    remove_ngrok_dependencies()

    logger.info("✅ تم إعداد البيئة بنجاح")

def check_configuration():
    """فحص الإعدادات المطلوبة"""
    logger.info("🔍 فحص الإعدادات...")

    try:
        # استيراد وتشغيل إعداد الإعدادات
        from config_render import setup_render_environment
        config_ok = setup_render_environment()

        if config_ok:
            logger.info("✅ جميع الإعدادات صحيحة")
            return True
        else:
            logger.warning("⚠️ بعض الإعدادات مفقودة، سيتم المتابعة مع الإعدادات الافتراضية")
            return False

    except Exception as e:
        logger.error(f"❌ خطأ في إعداد الإعدادات: {e}")
        return False

def run_bot():
    """تشغيل البوت الرئيسي"""
    logger.info("🤖 بدء تشغيل البوت...")

    try:
        # استيراد وتشغيل البوت الرئيسي
        import main as bot_main

        # تشغيل البوت إذا كان له دالة main
        if hasattr(bot_main, 'main'):
            bot_main.main()
        elif hasattr(bot_main, 'run'):
            bot_main.run()
        else:
            logger.info("✅ تم استيراد البوت بنجاح")

        logger.info("✅ تم تشغيل البوت بنجاح")

    except Exception as e:
        logger.error(f"❌ خطأ في تشغيل البوت: {e}")
        raise

def run_web_server():
    """تشغيل خادم الويب للمراقبة"""
    logger.info("🌐 بدء تشغيل خادم الويب...")
    
    try:
        from web_server import run_web_server
        run_web_server()
        
    except Exception as e:
        logger.error(f"❌ خطأ في تشغيل خادم الويب: {e}")
        # لا نوقف البوت إذا فشل خادم الويب
        pass

def main():
    """الدالة الرئيسية"""
    logger.info("=" * 50)
    logger.info("🚀 Reddit Video Maker Bot - Render Deployment")
    logger.info("=" * 50)
    
    try:
        # إعداد البيئة
        setup_environment()
        
        # فحص الإعدادات
        config_ok = check_configuration()
        if not config_ok:
            logger.warning("⚠️ بعض الإعدادات مفقودة، سيتم المتابعة مع الإعدادات الافتراضية")
        
        # تحديد نوع الخدمة
        service_type = os.getenv('RENDER_SERVICE_TYPE', 'worker')
        logger.info(f"📋 نوع الخدمة: {service_type}")
        
        if service_type == 'web':
            # إذا كانت خدمة ويب، نشغل خادم الويب مع البوت
            logger.info("🌐 تشغيل كخدمة ويب...")
            
            # تشغيل البوت في خيط منفصل
            bot_thread = threading.Thread(target=run_bot, daemon=True)
            bot_thread.start()
            
            # تشغيل خادم الويب في الخيط الرئيسي
            run_web_server()
            
        else:
            # إذا كانت خدمة عامل، نشغل البوت فقط
            logger.info("⚙️ تشغيل كخدمة عامل...")
            run_bot()
            
    except KeyboardInterrupt:
        logger.info("⏹️ تم إيقاف البوت بواسطة المستخدم")
        
    except Exception as e:
        logger.error(f"❌ خطأ حرج: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
