#!/usr/bin/env python3
"""
فحص صحة الخدمة لـ Render
"""

import requests
import sys
import os

def check_health():
    """فحص صحة الخدمة"""
    try:
        port = os.environ.get('PORT', '10000')
        url = f"http://localhost:{port}/health"
        
        response = requests.get(url, timeout=10)
        if response.status_code == 200:
            print("✅ الخدمة تعمل بشكل طبيعي")
            return True
        else:
            print(f"❌ الخدمة تعيد كود خطأ: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في فحص الصحة: {e}")
        return False

if __name__ == "__main__":
    if check_health():
        sys.exit(0)
    else:
        sys.exit(1)
