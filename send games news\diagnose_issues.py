#!/usr/bin/env python3
"""
سكريبت تشخيص المشاكل
يفحص ويشخص المشاكل الشائعة في البوت
"""

import sys
import os
import subprocess
import importlib
import logging
from datetime import datetime

# إعداد التسجيل
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_python_version():
    """فحص إصدار Python"""
    logger.info("🐍 فحص إصدار Python...")
    version = sys.version_info
    logger.info(f"📝 إصدار Python: {version.major}.{version.minor}.{version.micro}")
    
    if version.major >= 3 and version.minor >= 8:
        logger.info("✅ إصدار Python مناسب")
        return True
    else:
        logger.error("❌ إصدار Python قديم - يُنصح بـ Python 3.8+")
        return False

def check_package_installation(package_name, import_name=None):
    """فحص تثبيت حزمة معينة"""
    if import_name is None:
        import_name = package_name
    
    try:
        importlib.import_module(import_name)
        logger.info(f"✅ {package_name} مثبت")
        return True
    except ImportError:
        logger.error(f"❌ {package_name} غير مثبت")
        return False

def check_critical_packages():
    """فحص الحزم الحرجة"""
    logger.info("📦 فحص الحزم الحرجة...")
    
    critical_packages = [
        ("python-telegram-bot", "telegram"),
        ("google-generativeai", "google.generativeai"),
        ("aiohttp", "aiohttp"),
        ("beautifulsoup4", "bs4"),
        ("requests", "requests"),
        ("yt-dlp", "yt_dlp"),
    ]
    
    all_good = True
    for package, import_name in critical_packages:
        if not check_package_installation(package, import_name):
            all_good = False
    
    return all_good

def check_telegram_bot_version():
    """فحص إصدار python-telegram-bot"""
    logger.info("📱 فحص إصدار python-telegram-bot...")
    
    try:
        import telegram
        version = telegram.__version__
        logger.info(f"📝 إصدار python-telegram-bot: {version}")
        
        # فحص الإصدار المطلوب
        if version.startswith("20.6"):
            logger.info("✅ إصدار python-telegram-bot صحيح")
            return True
        else:
            logger.warning(f"⚠️ إصدار python-telegram-bot غير مطلوب - الحالي: {version}, المطلوب: 20.6.x")
            return False
    except ImportError:
        logger.error("❌ python-telegram-bot غير مثبت")
        return False

def check_ffmpeg():
    """فحص FFmpeg"""
    logger.info("🎬 فحص FFmpeg...")
    
    try:
        result = subprocess.run("ffmpeg -version", shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            logger.info("✅ FFmpeg متوفر")
            # استخراج إصدار FFmpeg
            lines = result.stdout.split('\n')
            if lines:
                version_line = lines[0]
                logger.info(f"📝 {version_line}")
            return True
        else:
            logger.error("❌ FFmpeg غير متوفر")
            return False
    except Exception as e:
        logger.error(f"❌ خطأ في فحص FFmpeg: {e}")
        return False

def check_environment_variables():
    """فحص متغيرات البيئة المهمة"""
    logger.info("🔑 فحص متغيرات البيئة...")
    
    important_vars = [
        "GEMINI_API_KEY",
        "TELEGRAM_BOT_TOKEN",
        "BLOGGER_CLIENT_ID",
        "BLOGGER_CLIENT_SECRET",
        "BLOGGER_BLOG_ID"
    ]
    
    all_good = True
    for var in important_vars:
        value = os.getenv(var)
        if value:
            logger.info(f"✅ {var}: موجود")
        else:
            logger.error(f"❌ {var}: غير موجود")
            all_good = False
    
    return all_good

def check_file_permissions():
    """فحص صلاحيات الملفات"""
    logger.info("📁 فحص صلاحيات الملفات...")
    
    important_files = [
        "main.py",
        "requirements.txt",
        "config/settings.py"
    ]
    
    all_good = True
    for file_path in important_files:
        if os.path.exists(file_path):
            if os.access(file_path, os.R_OK):
                logger.info(f"✅ {file_path}: قابل للقراءة")
            else:
                logger.error(f"❌ {file_path}: غير قابل للقراءة")
                all_good = False
        else:
            logger.error(f"❌ {file_path}: غير موجود")
            all_good = False
    
    return all_good

def check_database():
    """فحص قاعدة البيانات"""
    logger.info("🗄️ فحص قاعدة البيانات...")
    
    db_path = "data/articles.db"
    if os.path.exists(db_path):
        logger.info(f"✅ قاعدة البيانات موجودة: {db_path}")
        
        # فحص حجم قاعدة البيانات
        size = os.path.getsize(db_path)
        logger.info(f"📝 حجم قاعدة البيانات: {size} بايت")
        return True
    else:
        logger.warning(f"⚠️ قاعدة البيانات غير موجودة: {db_path}")
        return False

def generate_report():
    """إنشاء تقرير شامل"""
    logger.info("📋 إنشاء تقرير التشخيص...")
    
    report = {
        "timestamp": datetime.now().isoformat(),
        "python_version": check_python_version(),
        "critical_packages": check_critical_packages(),
        "telegram_bot_version": check_telegram_bot_version(),
        "ffmpeg": check_ffmpeg(),
        "environment_variables": check_environment_variables(),
        "file_permissions": check_file_permissions(),
        "database": check_database()
    }
    
    # حساب النتيجة الإجمالية
    total_checks = len(report) - 1  # استثناء timestamp
    passed_checks = sum(1 for key, value in report.items() if key != "timestamp" and value)
    
    score = (passed_checks / total_checks) * 100
    
    logger.info("=" * 50)
    logger.info("📊 تقرير التشخيص النهائي")
    logger.info("=" * 50)
    logger.info(f"🎯 النتيجة الإجمالية: {score:.1f}% ({passed_checks}/{total_checks})")
    
    if score >= 80:
        logger.info("✅ النظام في حالة جيدة")
    elif score >= 60:
        logger.warning("⚠️ النظام يحتاج بعض الإصلاحات")
    else:
        logger.error("❌ النظام يحتاج إصلاحات كبيرة")
    
    return report

def main():
    """الدالة الرئيسية"""
    logger.info("🔍 بدء تشخيص المشاكل...")
    logger.info("=" * 50)
    
    report = generate_report()
    
    logger.info("=" * 50)
    logger.info("💡 التوصيات:")
    
    if not report["telegram_bot_version"]:
        logger.info("   • تشغيل: python fix_dependencies.py")
    
    if not report["ffmpeg"]:
        logger.info("   • تثبيت FFmpeg من الموقع الرسمي")
    
    if not report["environment_variables"]:
        logger.info("   • إعداد متغيرات البيئة في ملف .env")
    
    logger.info("🔄 بعد الإصلاحات، شغل البوت مرة أخرى")

if __name__ == "__main__":
    main()
