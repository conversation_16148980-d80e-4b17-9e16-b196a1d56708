#!/usr/bin/env python3
"""
اختبارات أداة GitHub Uploader
"""

import unittest
import tempfile
import os
import shutil
from pathlib import Path
from unittest.mock import patch, MagicMock

from github_uploader import GitHubUploader

class TestGitHubUploader(unittest.TestCase):
    """اختبارات فئة GitHubUploader"""
    
    def setUp(self):
        """إعداد الاختبارات"""
        self.token = "test_token_123"
        self.uploader = GitHubUploader(self.token)
        self.test_dir = tempfile.mkdtemp()
        
    def tearDown(self):
        """تنظيف بعد الاختبارات"""
        if os.path.exists(self.test_dir):
            shutil.rmtree(self.test_dir)
    
    def test_init(self):
        """اختبار تهيئة الكلاس"""
        self.assertEqual(self.uploader.token, self.token)
        self.assertIn('Authorization', self.uploader.headers)
        self.assertEqual(self.uploader.base_url, 'https://api.github.com')
    
    def test_detect_project_type_python(self):
        """اختبار تحديد نوع مشروع Python"""
        # إنشاء ملف Python
        python_file = Path(self.test_dir) / "main.py"
        python_file.write_text("print('Hello World')")
        
        project_type = self.uploader.detect_project_type(self.test_dir)
        self.assertEqual(project_type, "python")
    
    def test_detect_project_type_javascript(self):
        """اختبار تحديد نوع مشروع JavaScript"""
        # إنشاء ملف package.json
        package_json = Path(self.test_dir) / "package.json"
        package_json.write_text('{"name": "test"}')
        
        project_type = self.uploader.detect_project_type(self.test_dir)
        self.assertEqual(project_type, "javascript")
    
    def test_detect_project_type_java(self):
        """اختبار تحديد نوع مشروع Java"""
        # إنشاء ملف Java
        java_file = Path(self.test_dir) / "Main.java"
        java_file.write_text("public class Main {}")
        
        project_type = self.uploader.detect_project_type(self.test_dir)
        self.assertEqual(project_type, "java")
    
    def test_detect_project_type_general(self):
        """اختبار تحديد نوع مشروع عام"""
        # مجلد فارغ
        project_type = self.uploader.detect_project_type(self.test_dir)
        self.assertEqual(project_type, "general")
    
    def test_generate_gitignore_python(self):
        """اختبار إنشاء .gitignore لـ Python"""
        gitignore = self.uploader.generate_gitignore("python")
        self.assertIn("__pycache__/", gitignore)
        self.assertIn("*.pyc", gitignore)
        self.assertIn(".env", gitignore)
    
    def test_generate_gitignore_javascript(self):
        """اختبار إنشاء .gitignore لـ JavaScript"""
        gitignore = self.uploader.generate_gitignore("javascript")
        self.assertIn("node_modules/", gitignore)
        self.assertIn("*.log", gitignore)
        self.assertIn(".env", gitignore)
    
    def test_generate_readme(self):
        """اختبار إنشاء README"""
        readme = self.uploader.generate_readme(
            "test-project", "python", "مشروع اختبار"
        )
        self.assertIn("# test-project", readme)
        self.assertIn("مشروع اختبار", readme)
        self.assertIn("pip install", readme)
        self.assertIn("python main.py", readme)
    
    @patch('subprocess.run')
    def test_init_git_repo_success(self, mock_run):
        """اختبار تهيئة Git بنجاح"""
        mock_run.return_value = MagicMock()
        
        # تغيير المجلد الحالي للاختبار
        original_dir = os.getcwd()
        try:
            result = self.uploader.init_git_repo(self.test_dir)
            self.assertTrue(result)
            mock_run.assert_called_with(['git', 'init'], check=True, capture_output=True)
        finally:
            os.chdir(original_dir)
    
    @patch('subprocess.run')
    def test_init_git_repo_failure(self, mock_run):
        """اختبار فشل تهيئة Git"""
        mock_run.side_effect = Exception("Git error")
        
        original_dir = os.getcwd()
        try:
            result = self.uploader.init_git_repo(self.test_dir)
            self.assertFalse(result)
        finally:
            os.chdir(original_dir)
    
    @patch('requests.post')
    def test_create_repository_success(self, mock_post):
        """اختبار إنشاء مستودع بنجاح"""
        # محاكاة استجابة ناجحة
        mock_response = MagicMock()
        mock_response.status_code = 201
        mock_response.json.return_value = {
            'name': 'test-repo',
            'html_url': 'https://github.com/user/test-repo',
            'clone_url': 'https://github.com/user/test-repo.git'
        }
        mock_post.return_value = mock_response
        
        result = self.uploader.create_repository("test-repo", "Test repository")
        
        self.assertIsInstance(result, dict)
        self.assertEqual(result['name'], 'test-repo')
        mock_post.assert_called_once()
    
    @patch('requests.post')
    def test_create_repository_failure(self, mock_post):
        """اختبار فشل إنشاء مستودع"""
        # محاكاة استجابة فاشلة
        mock_response = MagicMock()
        mock_response.status_code = 400
        mock_response.json.return_value = {'message': 'Repository already exists'}
        mock_post.return_value = mock_response
        
        result = self.uploader.create_repository("existing-repo")
        
        self.assertEqual(result, {})
        mock_post.assert_called_once()
    
    def test_project_type_detection_comprehensive(self):
        """اختبار شامل لتحديد أنواع المشاريع"""
        test_cases = [
            ("requirements.txt", "python"),
            ("package.json", "javascript"),
            ("pom.xml", "java"),
            ("Cargo.toml", "rust"),
            ("go.mod", "go"),
            ("main.cpp", "cpp"),
            ("Program.cs", "csharp"),
        ]
        
        for filename, expected_type in test_cases:
            with self.subTest(filename=filename):
                # إنشاء مجلد جديد لكل اختبار
                test_dir = tempfile.mkdtemp()
                try:
                    test_file = Path(test_dir) / filename
                    test_file.write_text("test content")
                    
                    detected_type = self.uploader.detect_project_type(test_dir)
                    self.assertEqual(detected_type, expected_type)
                finally:
                    shutil.rmtree(test_dir)

class TestProjectStructure(unittest.TestCase):
    """اختبارات هيكل المشروع"""
    
    def test_required_files_exist(self):
        """اختبار وجود الملفات المطلوبة"""
        required_files = [
            "github_uploader.py",
            "requirements.txt",
            "README.md",
            "LICENSE",
            "setup.py"
        ]
        
        for filename in required_files:
            with self.subTest(filename=filename):
                self.assertTrue(
                    os.path.exists(filename),
                    f"الملف المطلوب غير موجود: {filename}"
                )
    
    def test_readme_content(self):
        """اختبار محتوى README"""
        with open("README.md", "r", encoding="utf-8") as f:
            content = f.read()
        
        required_sections = [
            "# 🚀 GitHub Project Uploader",
            "## ✨ المميزات",
            "## 📋 المتطلبات",
            "## 🔧 التثبيت",
            "## 🚀 الاستخدام"
        ]
        
        for section in required_sections:
            with self.subTest(section=section):
                self.assertIn(section, content)

def run_tests():
    """تشغيل جميع الاختبارات"""
    print("🧪 بدء تشغيل الاختبارات...")
    print("=" * 50)
    
    # تشغيل الاختبارات
    unittest.main(verbosity=2, exit=False)
    
    print("=" * 50)
    print("✅ انتهت الاختبارات")

if __name__ == "__main__":
    run_tests()
