#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير قفل Instance - يمنع تشغيل أكثر من instance واحد من البوت
Instance Lock Manager - prevents multiple bot instances from running
"""

import os
import sys
import time
import json
import logging
import threading
import signal
from pathlib import Path
from datetime import datetime, timedelta
from typing import Optional, Dict, Any

logger = logging.getLogger(__name__)

class InstanceLockManager:
    """مدير قفل Instance لمنع التشغيل المتعدد"""
    
    def __init__(self, lock_name: str = "telegram_bot", lock_dir: str = "/tmp"):
        self.lock_name = lock_name
        self.lock_dir = Path(lock_dir)
        self.lock_file = self.lock_dir / f"{lock_name}.lock"
        self.heartbeat_file = self.lock_dir / f"{lock_name}_heartbeat.json"
        
        self.current_pid = os.getpid()
        self.lock_acquired = False
        self.heartbeat_thread = None
        self.heartbeat_interval = 30  # 30 ثانية
        self.lock_timeout = 300  # 5 دقائق
        self.shutdown_requested = False
        
        # إنشاء مجلد القفل إذا لم يكن موجوداً
        self.lock_dir.mkdir(parents=True, exist_ok=True)
        
        # إعداد معالج الإشارات
        self._setup_signal_handlers()
    
    def _setup_signal_handlers(self):
        """إعداد معالجات الإشارات للإغلاق الآمن"""
        def signal_handler(signum, frame):
            logger.info(f"📡 تم استلام إشارة {signum}، تحرير القفل...")
            self.shutdown_requested = True
            self.release_lock()
            sys.exit(0)
        
        signal.signal(signal.SIGTERM, signal_handler)
        signal.signal(signal.SIGINT, signal_handler)
    
    def _is_process_running(self, pid: int) -> bool:
        """فحص إذا كان العملية لا تزال تعمل"""
        try:
            os.kill(pid, 0)
            return True
        except (OSError, ProcessLookupError):
            return False
    
    def _read_lock_file(self) -> Optional[Dict[str, Any]]:
        """قراءة ملف القفل"""
        try:
            if self.lock_file.exists():
                with open(self.lock_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except (json.JSONDecodeError, IOError) as e:
            logger.warning(f"⚠️ خطأ في قراءة ملف القفل: {e}")
            # حذف ملف القفل التالف
            try:
                self.lock_file.unlink()
            except:
                pass
        return None
    
    def _write_lock_file(self, data: Dict[str, Any]) -> bool:
        """كتابة ملف القفل"""
        try:
            with open(self.lock_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            return True
        except IOError as e:
            logger.error(f"❌ خطأ في كتابة ملف القفل: {e}")
            return False
    
    def _read_heartbeat_file(self) -> Optional[Dict[str, Any]]:
        """قراءة ملف نبضات القلب"""
        try:
            if self.heartbeat_file.exists():
                with open(self.heartbeat_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except (json.JSONDecodeError, IOError):
            pass
        return None
    
    def _write_heartbeat_file(self) -> bool:
        """كتابة ملف نبضات القلب"""
        try:
            heartbeat_data = {
                'pid': self.current_pid,
                'timestamp': datetime.now().isoformat(),
                'lock_name': self.lock_name
            }
            
            with open(self.heartbeat_file, 'w', encoding='utf-8') as f:
                json.dump(heartbeat_data, f, indent=2, ensure_ascii=False)
            return True
        except IOError as e:
            logger.warning(f"⚠️ خطأ في كتابة ملف نبضات القلب: {e}")
            return False
    
    def _is_lock_stale(self, lock_data: Dict[str, Any]) -> bool:
        """فحص إذا كان القفل قديم ولا يستخدم"""
        try:
            old_pid = lock_data.get('pid')
            lock_time_str = lock_data.get('timestamp')
            
            # فحص إذا كان PID لا يزال نشطاً
            if old_pid and self._is_process_running(old_pid):
                # فحص نبضات القلب
                heartbeat_data = self._read_heartbeat_file()
                if heartbeat_data and heartbeat_data.get('pid') == old_pid:
                    heartbeat_time_str = heartbeat_data.get('timestamp')
                    if heartbeat_time_str:
                        try:
                            heartbeat_time = datetime.fromisoformat(heartbeat_time_str)
                            time_diff = datetime.now() - heartbeat_time
                            
                            # إذا كانت آخر نبضة قلب أقل من timeout، فالقفل نشط
                            if time_diff.total_seconds() < self.lock_timeout:
                                return False
                        except ValueError:
                            pass
                
                # إذا لم توجد نبضات قلب حديثة، فحص وقت القفل
                if lock_time_str:
                    try:
                        lock_time = datetime.fromisoformat(lock_time_str)
                        time_diff = datetime.now() - lock_time
                        
                        # إذا كان القفل أقل من timeout، فهو نشط
                        if time_diff.total_seconds() < self.lock_timeout:
                            return False
                    except ValueError:
                        pass
            
            # القفل قديم أو العملية لا تعمل
            return True
            
        except Exception as e:
            logger.warning(f"⚠️ خطأ في فحص القفل: {e}")
            return True  # افتراض أن القفل قديم في حالة الخطأ
    
    def acquire_lock(self) -> bool:
        """محاولة الحصول على القفل"""
        try:
            # فحص إذا كان القفل موجود
            lock_data = self._read_lock_file()
            
            if lock_data:
                old_pid = lock_data.get('pid')
                lock_time_str = lock_data.get('timestamp', '')
                
                # فحص إذا كان القفل قديم
                if self._is_lock_stale(lock_data):
                    logger.info(f"🔄 إزالة قفل قديم (PID: {old_pid})")
                    try:
                        self.lock_file.unlink()
                        if self.heartbeat_file.exists():
                            self.heartbeat_file.unlink()
                    except:
                        pass
                else:
                    logger.warning(f"⚠️ البوت يعمل بالفعل (PID: {old_pid}, وقت القفل: {lock_time_str})")
                    return False
            
            # إنشاء قفل جديد
            lock_data = {
                'pid': self.current_pid,
                'timestamp': datetime.now().isoformat(),
                'lock_name': self.lock_name,
                'hostname': os.getenv('HOSTNAME', 'unknown'),
                'render_instance': os.getenv('RENDER_INSTANCE_ID', 'unknown')
            }
            
            if self._write_lock_file(lock_data):
                self.lock_acquired = True
                logger.info(f"✅ تم الحصول على القفل (PID: {self.current_pid})")
                
                # بدء نبضات القلب
                self._start_heartbeat()
                return True
            else:
                logger.error("❌ فشل في كتابة ملف القفل")
                return False
                
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على القفل: {e}")
            return False
    
    def _start_heartbeat(self):
        """بدء نبضات القلب"""
        if self.heartbeat_thread and self.heartbeat_thread.is_alive():
            return
        
        def heartbeat_worker():
            while self.lock_acquired and not self.shutdown_requested:
                self._write_heartbeat_file()
                time.sleep(self.heartbeat_interval)
        
        self.heartbeat_thread = threading.Thread(target=heartbeat_worker, daemon=True)
        self.heartbeat_thread.start()
        logger.debug("💓 تم بدء نبضات القلب")
    
    def release_lock(self):
        """تحرير القفل"""
        if not self.lock_acquired:
            return
        
        try:
            self.lock_acquired = False
            
            # إيقاف نبضات القلب
            if self.heartbeat_thread and self.heartbeat_thread.is_alive():
                # انتظار انتهاء thread نبضات القلب
                self.heartbeat_thread.join(timeout=2)
            
            # حذف ملفات القفل
            try:
                if self.lock_file.exists():
                    self.lock_file.unlink()
                if self.heartbeat_file.exists():
                    self.heartbeat_file.unlink()
            except Exception as e:
                logger.warning(f"⚠️ خطأ في حذف ملفات القفل: {e}")
            
            logger.info("🔓 تم تحرير القفل")
            
        except Exception as e:
            logger.error(f"❌ خطأ في تحرير القفل: {e}")
    
    def is_locked(self) -> bool:
        """فحص إذا كان القفل نشط"""
        return self.lock_acquired
    
    def get_lock_info(self) -> Optional[Dict[str, Any]]:
        """الحصول على معلومات القفل الحالي"""
        lock_data = self._read_lock_file()
        if lock_data:
            heartbeat_data = self._read_heartbeat_file()
            lock_data['heartbeat'] = heartbeat_data
        return lock_data
    
    def force_release_lock(self):
        """تحرير القفل بالقوة (للاستخدام في حالات الطوارئ)"""
        try:
            if self.lock_file.exists():
                self.lock_file.unlink()
            if self.heartbeat_file.exists():
                self.heartbeat_file.unlink()
            logger.warning("⚠️ تم تحرير القفل بالقوة")
        except Exception as e:
            logger.error(f"❌ خطأ في تحرير القفل بالقوة: {e}")

# إنشاء instance عام للاستخدام
bot_lock_manager = InstanceLockManager("telegram_bot")

def ensure_single_instance():
    """ضمان تشغيل instance واحد فقط"""
    if not bot_lock_manager.acquire_lock():
        logger.error("❌ لا يمكن تشغيل أكثر من instance واحد من البوت")
        sys.exit(1)
    
    logger.info("✅ تم ضمان تشغيل instance واحد فقط")

def release_instance_lock():
    """تحرير قفل Instance"""
    bot_lock_manager.release_lock()

def get_instance_info():
    """الحصول على معلومات Instance الحالي"""
    return bot_lock_manager.get_lock_info()

# تصدير الدوال المهمة
__all__ = [
    'InstanceLockManager',
    'bot_lock_manager',
    'ensure_single_instance',
    'release_instance_lock',
    'get_instance_info'
]
