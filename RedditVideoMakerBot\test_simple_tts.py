#!/usr/bin/env python3
"""
اختبار بسيط لمحركات TTS
"""

import os
import sys
from pathlib import Path

def test_google_translate_tts():
    """اختب<PERSON>ر Google Translate TTS"""
    print("🔍 اختبار Google Translate TTS...")
    
    try:
        from TTS.GTTS import GTTS
        
        # إنشاء مجلد temp
        os.makedirs("assets/temp", exist_ok=True)
        
        # إنشاء محرك GTTS
        tts_engine = GTTS()
        
        # نص تجريبي
        test_text = "Hello, this is a test message."
        test_file = "assets/temp/test_gtts.mp3"
        
        print(f"📝 النص: {test_text}")
        print(f"📁 الملف: {test_file}")
        
        # إنشاء الصوت
        tts_engine.run(test_text, test_file)
        
        # فحص النتيجة
        if os.path.exists(test_file):
            file_size = os.path.getsize(test_file)
            print(f"✅ تم إنشاء الملف بنجاح! الحجم: {file_size} بايت")
            
            # حذف الملف
            os.remove(test_file)
            print("🗑️ تم حذف الملف التجريبي")
            return True
        else:
            print("❌ لم يتم إنشاء الملف")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في Google Translate TTS: {e}")
        return False

def test_pyttsx():
    """اختبار pyttsx TTS"""
    print("\n🔍 اختبار pyttsx TTS...")
    
    try:
        from TTS.pyttsx import pyttsx
        
        # إنشاء مجلد temp
        os.makedirs("assets/temp", exist_ok=True)
        
        # إنشاء محرك pyttsx
        tts_engine = pyttsx()
        
        # نص تجريبي
        test_text = "Hello, this is a test message."
        test_file = "assets/temp/test_pyttsx.mp3"
        
        print(f"📝 النص: {test_text}")
        print(f"📁 الملف: {test_file}")
        
        # إنشاء الصوت
        tts_engine.run(test_text, test_file)
        
        # فحص النتيجة
        if os.path.exists(test_file):
            file_size = os.path.getsize(test_file)
            print(f"✅ تم إنشاء الملف بنجاح! الحجم: {file_size} بايت")
            
            # حذف الملف
            os.remove(test_file)
            print("🗑️ تم حذف الملف التجريبي")
            return True
        else:
            print("❌ لم يتم إنشاء الملف")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في pyttsx TTS: {e}")
        return False

def test_priority_order():
    """اختبار ترتيب الأولوية من config.toml"""
    print("\n🔍 فحص ترتيب الأولوية...")
    
    try:
        import toml
        
        with open("config.toml", "r", encoding="utf-8") as f:
            config = toml.load(f)
        
        voice_choice = config["settings"]["tts"]["voice_choice"]
        priority_order = config["settings"]["tts"]["priority_order"]["primary"]
        
        print(f"🎵 محرك الصوت: {voice_choice}")
        print(f"🎯 ترتيب الأولوية: {priority_order}")
        
        # التحقق من الترتيب
        expected_first = "GoogleTranslate"
        if priority_order[0] == expected_first:
            print(f"✅ المحرك الأول صحيح: {expected_first}")
            
            if "ElevenLabs" in priority_order:
                elevenlabs_position = priority_order.index("ElevenLabs") + 1
                print(f"✅ ElevenLabs في المرتبة: {elevenlabs_position}")
            
            return True
        else:
            print(f"❌ المحرك الأول خطأ. متوقع: {expected_first}, فعلي: {priority_order[0]}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في فحص الترتيب: {e}")
        return False

def main():
    """تشغيل الاختبارات"""
    print("🚀 اختبار محركات TTS البديلة")
    print("=" * 40)
    
    tests = [
        ("ترتيب الأولوية", test_priority_order),
        ("Google Translate TTS", test_google_translate_tts),
        ("pyttsx TTS", test_pyttsx)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                print(f"✅ {test_name}: نجح")
                passed += 1
            else:
                print(f"❌ {test_name}: فشل")
        except Exception as e:
            print(f"💥 {test_name}: خطأ - {e}")
    
    print("\n" + "=" * 40)
    print(f"📊 النتائج: {passed}/{total} اختبارات نجحت")
    
    if passed >= 1:  # على الأقل اختبار واحد نجح
        print("🎉 النظام جاهز! سيستخدم البدائل قبل ElevenLabs")
    else:
        print("⚠️ يرجى التحقق من إعداد محركات TTS")

if __name__ == "__main__":
    main()
