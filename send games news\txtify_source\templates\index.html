<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Txtify</title>
    <link rel="icon" href="/static/favicon.ico" type="image/x-icon">
    <link rel="stylesheet" href="/static/styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Exo:wght@400;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <script async defer src="https://buttons.github.io/buttons.js"></script>

</head>


<body class="dark-theme">
    <div class="navbar">
        <div class="navbar-content">
            <!-- <a href="/" class="logo">Txtify</a> -->
            <a href="/" class="logo">
                <img src="/static/txtify_logo.png" alt="Txtify Logo" class="logo-image">
                <span>Txtify</span>
            </a>            
            <div class="menu-toggle" id="menuToggle">
                <i class="fa fa-bars"></i>
            </div>
            <div class="nav-links" id="navLinks">
                <a href="/">Application</a>
                <a href="/faq">FAQ</a>
                <a href="/contact">Contact</a>
                <a class="github-button" href="https://github.com/lkmeta/txtify"
                    data-color-scheme="no-preference: light; light: light; dark: dark;" data-size="large"
                    aria-label="Star lkmeta/txtify on GitHub">Star</a>
            </div>
            <div class="theme-toggle-wrapper">
                <label class="theme-toggle">
                    <input type="checkbox" id="themeToggle">
                    <div class="toggle-body"></div>
                    <div class="celestial-body"></div>
                </label>
            </div>
        </div>
    </div>



    <div class="container">
        <h1>Audio and Video to Text for FREE using AI</h1>

        <div class="description">
            Welcome to Txtify! Easily turn your 
            <a href="#" style="color: var(--primary-color); font-weight: bold;">audio or video to text for free</a>. 
            Provide a YouTube URL or upload your own files and leverage AI, powered by the new 
            <a href="https://github.com/jianfch/stable-ts" target="_blank" style="color: var(--primary-color); font-weight: bold;">stable-ts</a> 
            library for fast and accurate transcriptions.
            <br><br>
            Transcribe audio and video in 
            <span id="transcription-languages" style="color: var(--primary-color); font-weight: bold;"></span> 
            languages and translate transcriptions to 
            <span id="translation-languages" style="color: var(--primary-color); font-weight: bold;"></span> 
            languages. Export in multiple formats including 
            <span id="file-types" style="color: var(--primary-color); font-weight: bold;"></span>.
            Txtify supports files up to 
            <span style="color: var(--primary-color); font-weight: bold;">100MB</span> 
            and YouTube videos up to 
            <span style="color: var(--primary-color); font-weight: bold;">10 minutes</span>.
            <br><br>
            Please note that this website is a 
            <span style="color: var(--primary-color); font-weight: bold;">demo version</span> 
            intended to showcase the functionality of Txtify. To utilize the full features of Txtify, including real data transcription, we recommend self-hosting the application. For detailed instructions on how to set up and run Txtify locally, visit our 
            <a href="https://github.com/lkmeta/txtify" target="_blank" style="color: var(--primary-color); font-weight: bold;">GitHub repository</a>.
        </div>
        
        <!-- <div class="description">
            Welcome to Txtify! Easily turn your <a style="color: var(--primary-color); font-weight: bold;">audio or
                video to text for free</a>. Provide a YouTube URL or upload your own files and use AI, powered by the new 
            <a href="https://github.com/jianfch/stable-ts" target="_blank" style="color: var(--primary-color); font-weight: bold;">stable-ts</a>
                 library, for fast and accurate transcriptions.
            <br><br>
            Transcribe audio and video in <span id="transcription-languages"
                style="color: var(--primary-color); font-weight: bold;"></span> languages and translate transcriptions
            to <span id="translation-languages" style="color: var(--primary-color); font-weight: bold;"></span>
            languages.
            Export in multiple formats including <span id="file-types"
                style="color: var(--primary-color); font-weight: bold;"></span>.
            Txtify supports files up to <span style="color: var(--primary-color); font-weight: bold;">100MB</span>
            and YouTube videos up to <span style="color: var(--primary-color); font-weight: bold;">10 minutes</span>.
            <br><br>
            Please note that this website is a <span style="color: var(--primary-color); font-weight: bold;">demo
                version</span> intended to showcase the functionality of Txtify. To utilize the full features of Txtify,
            including real data transcription, we recommend self-hosting the application. For detailed instructions on
            how to set up and run Txtify locally, visit our <a href="https://github.com/lkmeta/txtify" target="_blank"
                style="color: var(--primary-color); font-weight: bold;">GitHub repository</a>.

        </div> -->

        <div class="switch-case">
            <button id="youtube-button" class="active" onclick="showInput('youtube')">YouTube URL</button>
            <button id="upload-button" onclick="showInput('upload')">Upload Audio/Video</button>
        </div>
        <div id="youtube-input" class="input-group">
            <label for="youtube-url">YouTube URL:</label>
            <small>Enter the URL of the YouTube video you want to transcribe (max 10 minutes trascription).</small>
            <input type="text" id="youtube-url" placeholder="Enter YouTube URL">
        </div>
        <div id="upload-input" class="input-group" style="display: none;">
            <label for="media-upload">Upload Audio/Video:</label>
            <small>Upload an audio or video file from your device to transcribe (.mp4, .mp3 -- max 100MB).</small>
            <input type="file" id="media-upload" accept=".mp4,.mp3">
        </div>

        <div class="input-group">
            <label for="stt-model">Speech-to-Text AI Model:</label>
            <small>Select the AI model to use for speech recognition.</small>
            <select id="stt-model">
                <option value="whisper_tiny">Whisper Tiny (Fastest - Low Accuracy - English Only)</option>
                <option value="whisper_base" selected>Whisper Base (Fast - Balanced - English Only)</span>
                </option>
                <option value="whisper_small">Whisper Small (Fast - Accurate - English Only)</option>
                <option value="whisper_medium">Whisper Medium (Fast - High Accuracy - Multi-Language - RECOMMENDED)</option>
                </option>
                <option value="whisper_large">Whisper Large (Slow - Highest Accuracy - Multi-Language)
                </option>
            </select>
        </div>


        <div class="input-group">
            <label for="language-choice">Choose Language:</label>
            <small>Select the language spoken in the media.</small>
            <select id="language-choice"></select>
        </div>
        <div class="input-group">
            <label for="translation">Translation:</label>
            <small>Enable translation of the transcribed text.</small>
            <select id="translation">
                <option value="none">Don't Translate</option>
                <!-- <option value="whisper">Whisper </option> -->
                <option value="deepl">DeepL (requires DeepL API key - Highest Accuracy - RECOMMENDED)</option>
            </select>
        </div>

        <div class="input-group">
            <label for="language-translation">Choose Language Translation:</label>
            <small>Select the language to translate the transcribed text.</small>
            <select id="language-translation"></select>
        </div>
        <!-- <div class="input-group">
            <label for="file-export">File Export Type:</label>
            <small>Choose the format for the transcribed text.</small>
            <select id="file-export">
                <option value="txt">Text File (.txt)</option>
                <option value="pdf">PDF (.pdf)</option>
                <option value="srt">SubRip Subtitle (.srt)</option>
                <option value="vtt">WebVTT (.vtt)</option>
                <option value="sbv">YouTube Captions (.sbv)</option>
            </select>
        </div> -->

        <div class="actions">
            <button id="transcribeButton">Transcribe</button>
        </div>
    </div>
    <div class="footer">
        &copy; 2025 Txt<span style="color: var(--primary-color);">ify</span>. Created with <i
            class="fa-solid fa-heart heart"></i> by <a href="https://lkmeta.com" target="_blank">lkmeta</a>.
    </div>




    <!-- Alert Box Structure -->
    <div class="alert-overlay" id="alertOverlay">
        <div class="alert-box">
            <h2 id="alertTitle">Alert</h2>
            <p id="alertMessage">This is an alert message.</p>
            <button onclick="closeAlert()">OK</button>
        </div>
    </div>




    <!-- Progress Window Structure -->
    <div class="progress-overlay" id="progressOverlay">
        <div class="progress-box">
            <h2>Transcription Progress</h2>
            <div class="progress-animation">
                <div class="spinner"></div>
                <div class="progress-percentage" id="progressPercentage">0%</div>
            </div>
            <p id="progressPhase">Downloading audio locally...Transcription will begin shortly.</p>
            <div class="stats">
                <p id="statsModel"></p>
                <p id="statsLanguage"></p>
                <p id="statsTranslation"></p>
                <p id="statsTime"></p>
            </div>
            <div class="preview-container hidden" id="previewContainer">
                <div class="tabs">
                    <button class="tab-button active" onclick="showPreview('txt')">.txt</button>
                    <button class="tab-button" onclick="showPreview('srt')">.srt</button>
                    <button class="tab-button" onclick="showPreview('vtt')">.vtt</button>
                    <button class="tab-button" onclick="showPreview('sbv')">.sbv</button>
                </div>
                <div class="preview-content" id="previewContent">
                    <button class="download-emoji" onclick="downloadCurrentPreview()"><i
                            class="fa-solid fa-download"></i></button>

                    <pre id="previewText" class="hidden"></pre>
                    <pre id="previewSRT" class="hidden"></pre>
                    <pre id="previewVTT" class="hidden"></pre>
                    <pre id="previewSBV" class="hidden"></pre>
                </div>
            </div>
            <div>
                <br>
                <button class="cancel-button hidden">Cancel</button>
                <button class="download-button hidden">Download</button>
                <button class="close-button hidden">Close</button>
            </div>
        </div>
    </div>




    <script src="/static/scripts.js"></script>
</body>

</html>