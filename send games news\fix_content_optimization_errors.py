#!/usr/bin/env python3
"""
إصلاح شامل لمشاكل TypeError في content optimization
"""

import sys
import os
import logging
import json
from pathlib import Path
from datetime import datetime

# إضافة مسار المشروع
sys.path.append(str(Path(__file__).parent))

# إعداد التسجيل
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def fix_content_optimizer_none_errors():
    """إصلاح أخطاء NoneType في content_optimizer"""
    try:
        content_optimizer_path = Path("modules/content_optimizer.py")
        if not content_optimizer_path.exists():
            logger.error("❌ ملف content_optimizer.py غير موجود")
            return False
        
        with open(content_optimizer_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # إضافة دوال حماية من NoneType
        protection_functions = '''
    def _safe_get_article_data(self, article_id: str) -> Optional[Dict]:
        """الحصول على بيانات المقال مع حماية من NoneType"""
        try:
            article = self._get_full_article_data(article_id)
            if article is None:
                logger.warning(f"⚠️ لم يتم العثور على المقال {article_id}")
                return None
            
            # التأكد من وجود الحقول المطلوبة
            required_fields = ['title', 'content']
            for field in required_fields:
                if field not in article or article[field] is None:
                    logger.warning(f"⚠️ حقل مفقود أو None في المقال {article_id}: {field}")
                    article[field] = ""  # قيمة افتراضية
            
            # التأكد من وجود keywords
            if 'keywords' not in article or article['keywords'] is None:
                article['keywords'] = []
            elif isinstance(article['keywords'], str):
                article['keywords'] = [kw.strip() for kw in article['keywords'].split(',') if kw.strip()]
            
            return article
            
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على بيانات المقال {article_id}: {e}")
            return None
    
    def _safe_optimize_content(self, article: Dict) -> Optional[str]:
        """تحسين المحتوى مع حماية من NoneType"""
        try:
            if not article or not isinstance(article, dict):
                logger.warning("⚠️ بيانات المقال غير صالحة")
                return None
            
            title = article.get('title', '')
            content = article.get('content', '')
            
            if not title or not content:
                logger.warning("⚠️ العنوان أو المحتوى فارغ")
                return None
            
            # استدعاء الدالة الأصلية مع حماية
            return self._optimize_content(article)
            
        except Exception as e:
            logger.error(f"❌ خطأ في تحسين المحتوى: {e}")
            return None
    
    def _safe_optimize_keywords(self, article: Dict) -> Optional[str]:
        """تحسين الكلمات المفتاحية مع حماية من NoneType"""
        try:
            if not article or not isinstance(article, dict):
                logger.warning("⚠️ بيانات المقال غير صالحة")
                return None
            
            title = article.get('title', '')
            content = article.get('content', '')
            keywords = article.get('keywords', [])
            
            if not title:
                logger.warning("⚠️ العنوان فارغ")
                return None
            
            # التأكد من أن keywords قائمة
            if keywords is None:
                keywords = []
            elif isinstance(keywords, str):
                keywords = [kw.strip() for kw in keywords.split(',') if kw.strip()]
            
            # تحديث المقال
            article['keywords'] = keywords
            
            # استدعاء الدالة الأصلية مع حماية
            return self._optimize_keywords(article)
            
        except Exception as e:
            logger.error(f"❌ خطأ في تحسين الكلمات المفتاحية: {e}")
            return None
'''
        
        # إضافة الدوال الآمنة إذا لم تكن موجودة
        if "_safe_get_article_data" not in content:
            # البحث عن مكان مناسب للإضافة
            if "def _get_full_article_data" in content:
                content = content.replace(
                    "def _get_full_article_data",
                    protection_functions + "\n    def _get_full_article_data"
                )
            else:
                # إضافة في نهاية الكلاس
                content = content.replace(
                    "class ContentOptimizer:",
                    f"class ContentOptimizer:{protection_functions}"
                )
        
        # تحديث دالة optimize_article_automatically لاستخدام الدوال الآمنة
        safe_optimization_update = '''
    async def safe_optimize_article_automatically(self, article_data: Dict) -> Dict:
        """تحسين مقال تلقائياً مع حماية من الأخطاء"""
        try:
            article_id = article_data.get('article_id')
            if not article_id:
                logger.error("❌ معرف المقال مفقود")
                return {}
            
            logger.info(f"🔧 بدء التحسين الآمن للمقال: {article_data.get('title', 'غير محدد')[:50]}...")
            
            # الحصول على بيانات المقال بطريقة آمنة
            full_article = self._safe_get_article_data(article_id)
            if not full_article:
                logger.error(f"❌ لم يتم العثور على بيانات المقال {article_id}")
                return {}
            
            optimization_results = {}
            
            # التحقق من وجود performance_metrics
            performance_metrics = article_data.get('performance_metrics')
            if not performance_metrics:
                logger.warning("⚠️ مقاييس الأداء غير متوفرة، سيتم تخطي بعض التحسينات")
                performance_metrics = type('obj', (object,), {
                    'ctr': 0.0,
                    'avg_read_time': 0.0,
                    'seo_score': 0.0
                })()
            
            # 1. تحسين العنوان (إذا كان CTR منخفض)
            try:
                ctr = getattr(performance_metrics, 'ctr', 0.0) or 0.0
                if ctr < self.poor_performance_thresholds.get('ctr', 2.0):
                    optimized_title = await self._optimize_title(full_article)
                    if optimized_title:
                        optimization_results['title'] = {
                            'original': full_article.get('title', ''),
                            'optimized': optimized_title,
                            'improvement_type': 'title_optimization'
                        }
            except Exception as e:
                logger.warning(f"⚠️ فشل في تحسين العنوان: {e}")
            
            # 2. تحسين المحتوى (إذا كان وقت القراءة منخفض)
            try:
                avg_read_time = getattr(performance_metrics, 'avg_read_time', 0.0) or 0.0
                if avg_read_time < self.poor_performance_thresholds.get('avg_read_time', 60.0):
                    optimized_content = self._safe_optimize_content(full_article)
                    if optimized_content:
                        optimization_results['content'] = {
                            'original': full_article.get('content', '')[:200] + "...",
                            'optimized': optimized_content[:200] + "...",
                            'improvement_type': 'content_enhancement'
                        }
            except Exception as e:
                logger.warning(f"⚠️ فشل في تحسين المحتوى: {e}")
            
            # 3. تحسين الكلمات المفتاحية (إذا كان SEO منخفض)
            try:
                seo_score = getattr(performance_metrics, 'seo_score', 0.0) or 0.0
                if seo_score < self.poor_performance_thresholds.get('seo_score', 60.0):
                    optimized_keywords = self._safe_optimize_keywords(full_article)
                    if optimized_keywords:
                        optimization_results['keywords'] = {
                            'original': full_article.get('keywords', []),
                            'optimized': optimized_keywords,
                            'improvement_type': 'keyword_optimization'
                        }
            except Exception as e:
                logger.warning(f"⚠️ فشل في تحسين الكلمات المفتاحية: {e}")
            
            # 4. تحسين التصنيف والوسوم
            try:
                optimized_category = await self._optimize_category_and_tags(full_article)
                if optimized_category:
                    optimization_results['category'] = {
                        'original': full_article.get('category', ''),
                        'optimized': optimized_category,
                        'improvement_type': 'category_optimization'
                    }
            except Exception as e:
                logger.warning(f"⚠️ فشل في تحسين التصنيف: {e}")
            
            # حفظ نتائج التحسين
            if optimization_results:
                try:
                    self._save_optimization_results(article_id, optimization_results, performance_metrics)
                    logger.info(f"✅ تم تحسين {len(optimization_results)} عنصر في المقال")
                except Exception as e:
                    logger.warning(f"⚠️ فشل في حفظ نتائج التحسين: {e}")
            
            return optimization_results
            
        except Exception as e:
            logger.error(f"❌ فشل في التحسين الآمن للمقال {article_data.get('article_id', 'غير محدد')}: {e}")
            return {}
'''
        
        # إضافة الدالة الآمنة
        if "safe_optimize_article_automatically" not in content:
            content = content.replace(
                "async def optimize_article_automatically",
                safe_optimization_update + "\n    async def optimize_article_automatically"
            )
        
        # حفظ الملف
        with open(content_optimizer_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        logger.info("✅ تم إصلاح أخطاء NoneType في content_optimizer")
        return True
        
    except Exception as e:
        logger.error(f"❌ فشل في إصلاح content_optimizer: {e}")
        return False

def fix_keyword_optimization_errors():
    """إصلاح أخطاء تحسين الكلمات المفتاحية"""
    try:
        content_optimizer_path = Path("modules/content_optimizer.py")
        if not content_optimizer_path.exists():
            logger.error("❌ ملف content_optimizer.py غير موجود")
            return False
        
        with open(content_optimizer_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # إصلاح دالة _optimize_keywords
        fixed_optimize_keywords = '''
    async def _optimize_keywords_fixed(self, article: Dict) -> Optional[str]:
        """تحسين الكلمات المفتاحية مع حماية شاملة من الأخطاء"""
        try:
            # التحقق من صحة البيانات
            if not article or not isinstance(article, dict):
                logger.warning("⚠️ بيانات المقال غير صالحة")
                return self._optimize_keywords_basic(article) if article else None
            
            title = article.get('title', '')
            content = article.get('content', '')
            current_keywords = article.get('keywords', [])
            
            # التحقق من وجود العنوان
            if not title or not isinstance(title, str):
                logger.warning("⚠️ العنوان غير صالح")
                return self._optimize_keywords_basic(article)
            
            # التحقق من المحتوى
            if not content or not isinstance(content, str):
                logger.warning("⚠️ المحتوى غير صالح")
                content = title  # استخدام العنوان كبديل
            
            # تنظيف الكلمات المفتاحية الحالية
            if current_keywords is None:
                current_keywords = []
            elif isinstance(current_keywords, str):
                current_keywords = [kw.strip() for kw in current_keywords.split(',') if kw.strip()]
            elif not isinstance(current_keywords, list):
                current_keywords = []
            
            # محاولة استخدام AI إذا كان متوفراً
            if self.model:
                try:
                    prompt = f"""
                    أنت خبير SEO متخصص في ألعاب الفيديو.

                    العنوان: "{title}"
                    المحتوى: "{content[:500]}"
                    الكلمات المفتاحية الحالية: "{', '.join(current_keywords)}"

                    اقترح كلمات مفتاحية محسنة:
                    1. ذات صلة بالمحتوى
                    2. شائعة البحث في مجال الألعاب
                    3. متوسطة المنافسة
                    4. باللغة العربية والإنجليزية
                    5. 8-12 كلمة مفتاحية

                    اكتب الكلمات المفتاحية مفصولة بفواصل:
                    """

                    response = self.model.generate_content(prompt)
                    
                    if response and response.text:
                        optimized_keywords = response.text.strip()
                        
                        # تنظيف الكلمات المفتاحية
                        import re
                        optimized_keywords = re.sub(r'["\']', '', optimized_keywords)
                        
                        # التحقق من صحة النتيجة
                        if optimized_keywords and len(optimized_keywords) > 10:
                            return optimized_keywords
                        else:
                            logger.warning("⚠️ نتيجة AI غير مناسبة، استخدام الطريقة الأساسية")
                            return self._optimize_keywords_basic(article)
                    else:
                        logger.warning("⚠️ لم يتم الحصول على استجابة من AI")
                        return self._optimize_keywords_basic(article)
                        
                except Exception as ai_error:
                    logger.warning(f"⚠️ فشل في استخدام AI لتحسين الكلمات المفتاحية: {ai_error}")
                    return self._optimize_keywords_basic(article)
            else:
                logger.info("ℹ️ AI غير متوفر، استخدام الطريقة الأساسية")
                return self._optimize_keywords_basic(article)

        except Exception as e:
            logger.error(f"❌ خطأ في تحسين الكلمات المفتاحية: {e}")
            return self._optimize_keywords_basic(article) if article else None
'''
        
        # إضافة الدالة المحسنة
        if "_optimize_keywords_fixed" not in content:
            content = content.replace(
                "async def _optimize_keywords",
                fixed_optimize_keywords + "\n    async def _optimize_keywords"
            )
        
        # حفظ الملف
        with open(content_optimizer_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        logger.info("✅ تم إصلاح أخطاء تحسين الكلمات المفتاحية")
        return True
        
    except Exception as e:
        logger.error(f"❌ فشل في إصلاح تحسين الكلمات المفتاحية: {e}")
        return False

def create_content_optimizer_tester():
    """إنشاء أداة اختبار محسن المحتوى"""
    tester_code = '''#!/usr/bin/env python3
"""
أداة اختبار محسن المحتوى
"""

import sys
import json
from pathlib import Path

# إضافة مسار المشروع
sys.path.append(str(Path(__file__).parent))

def test_content_optimizer():
    """اختبار محسن المحتوى"""
    print("🧪 بدء اختبار محسن المحتوى...")
    
    try:
        from modules.content_optimizer import ContentOptimizer
        
        # إنشاء محسن المحتوى
        optimizer = ContentOptimizer()
        print("✅ تم إنشاء محسن المحتوى بنجاح")
        
        # بيانات اختبار
        test_article_data = {
            'article_id': 'test_123',
            'title': 'اختبار تحسين المحتوى',
            'performance_metrics': type('obj', (object,), {
                'ctr': 1.0,  # منخفض
                'avg_read_time': 30.0,  # منخفض
                'seo_score': 40.0  # منخفض
            })()
        }
        
        # اختبار الدالة الآمنة إذا كانت متوفرة
        if hasattr(optimizer, 'safe_optimize_article_automatically'):
            print("✅ استخدام الدالة الآمنة للتحسين")
            import asyncio
            result = asyncio.run(optimizer.safe_optimize_article_automatically(test_article_data))
        else:
            print("⚠️ الدالة الآمنة غير متوفرة، استخدام الدالة التقليدية")
            import asyncio
            result = asyncio.run(optimizer.optimize_article_automatically(test_article_data))
        
        print(f"\\n📊 نتائج التحسين:")
        if result:
            print(f"عدد العناصر المحسنة: {len(result)}")
            for key, value in result.items():
                print(f"  - {key}: {value.get('improvement_type', 'غير محدد')}")
        else:
            print("لم يتم تحسين أي عناصر")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار محسن المحتوى: {e}")
        return False

if __name__ == "__main__":
    success = test_content_optimizer()
    if success:
        print("\\n✅ اختبار محسن المحتوى نجح!")
    else:
        print("\\n❌ اختبار محسن المحتوى فشل!")
'''
    
    try:
        with open("test_content_optimizer.py", 'w', encoding='utf-8') as f:
            f.write(tester_code)
        logger.info("✅ تم إنشاء أداة اختبار محسن المحتوى")
        return True
    except Exception as e:
        logger.error(f"❌ فشل في إنشاء أداة الاختبار: {e}")
        return False

def main():
    """الدالة الرئيسية لإصلاح مشاكل content optimization"""
    logger.info("🚀 بدء إصلاح مشاكل TypeError في content optimization...")
    
    success_count = 0
    total_steps = 3
    
    # 1. إصلاح أخطاء NoneType
    logger.info("\\n🛡️ الخطوة 1: إصلاح أخطاء NoneType في content_optimizer...")
    if fix_content_optimizer_none_errors():
        success_count += 1
    
    # 2. إصلاح أخطاء تحسين الكلمات المفتاحية
    logger.info("\\n🔑 الخطوة 2: إصلاح أخطاء تحسين الكلمات المفتاحية...")
    if fix_keyword_optimization_errors():
        success_count += 1
    
    # 3. إنشاء أداة اختبار
    logger.info("\\n🧪 الخطوة 3: إنشاء أداة اختبار محسن المحتوى...")
    if create_content_optimizer_tester():
        success_count += 1
    
    # النتيجة النهائية
    logger.info(f"\\n🎯 اكتمل الإصلاح: {success_count}/{total_steps} خطوات نجحت")
    
    if success_count == total_steps:
        logger.info("✅ تم إصلاح جميع مشاكل content optimization بنجاح!")
        logger.info("🔄 يرجى إعادة تشغيل الوكيل لتطبيق التحديثات")
        logger.info("🧪 يمكنك اختبار التحسينات باستخدام: python test_content_optimizer.py")
    else:
        logger.warning(f"⚠️ تم إصلاح {success_count} من أصل {total_steps} مشاكل")
        logger.info("📋 يرجى مراجعة الأخطاء أعلاه وإصلاحها يدوياً")

if __name__ == "__main__":
    main()
