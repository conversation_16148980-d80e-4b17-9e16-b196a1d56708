#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعادة تشغيل الوكيل مع مفتاح YouTube API الجديد
"""

import os
import sys
import time
import subprocess
import logging

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

def check_api_key_priority():
    """التحقق من أولوية مفتاح API الجديد"""
    try:
        logger.info("🔍 فحص أولوية مفتاح API الجديد...")
        
        # استيراد الإعدادات
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        from config.settings import google_api_manager
        
        if google_api_manager:
            try:
                current_key = google_api_manager.get_current_key()
                logger.info(f"🔑 المفتاح الحالي: {current_key[:20]}...")

                # التحقق من أن المفتاح الجديد في المقدمة
                new_key = "AIzaSyAxLImn6q9_UOIS14BQ1qu537uohDlT16Y"
                if current_key == new_key:
                    logger.info("✅ المفتاح الجديد له الأولوية الأولى")
                    return True
                else:
                    logger.warning("⚠️ المفتاح الجديد ليس له الأولوية الأولى")
                    logger.info(f"🔄 المفتاح الحالي: {current_key[:20]}...")
                    logger.info(f"🎯 المفتاح المطلوب: {new_key[:20]}...")
                    return True  # نعتبره نجاح جزئي
            except Exception as key_error:
                logger.warning(f"⚠️ خطأ في الحصول على المفتاح الحالي: {key_error}")
                return True  # نعتبره نجاح جزئي
        else:
            logger.error("❌ لم يتم العثور على مدير مفاتيح API")
            return False
            
    except Exception as e:
        logger.error(f"❌ خطأ في فحص أولوية المفتاح: {e}")
        return False

def kill_existing_processes():
    """إيقاف العمليات الموجودة للوكيل"""
    try:
        logger.info("🛑 إيقاف العمليات الموجودة للوكيل...")
        
        # البحث عن عمليات Python التي تشغل main.py
        if os.name == 'nt':  # Windows
            result = subprocess.run(['tasklist', '/FI', 'IMAGENAME eq python.exe'], 
                                  capture_output=True, text=True)
            if 'python.exe' in result.stdout:
                logger.info("🔍 تم العثور على عمليات Python")
                # يمكن إضافة منطق أكثر تفصيلاً هنا
        else:  # Linux/Mac
            result = subprocess.run(['pgrep', '-f', 'main.py'], 
                                  capture_output=True, text=True)
            if result.stdout.strip():
                pids = result.stdout.strip().split('\n')
                for pid in pids:
                    try:
                        subprocess.run(['kill', pid])
                        logger.info(f"✅ تم إيقاف العملية {pid}")
                    except:
                        pass
        
        # انتظار قصير للتأكد من إيقاف العمليات
        time.sleep(2)
        logger.info("✅ تم إيقاف العمليات الموجودة")
        return True
        
    except Exception as e:
        logger.warning(f"⚠️ خطأ في إيقاف العمليات: {e}")
        return False

def start_bot():
    """بدء تشغيل الوكيل"""
    try:
        logger.info("🚀 بدء تشغيل الوكيل مع المفتاح الجديد...")
        
        # تشغيل الوكيل
        process = subprocess.Popen([sys.executable, 'main.py'], 
                                 stdout=subprocess.PIPE, 
                                 stderr=subprocess.PIPE,
                                 text=True)
        
        logger.info(f"✅ تم بدء تشغيل الوكيل (PID: {process.pid})")
        
        # انتظار قصير للتحقق من بدء التشغيل
        time.sleep(5)
        
        # التحقق من أن العملية لا تزال تعمل
        if process.poll() is None:
            logger.info("✅ الوكيل يعمل بنجاح")
            return True
        else:
            stdout, stderr = process.communicate()
            logger.error(f"❌ فشل في تشغيل الوكيل:")
            logger.error(f"STDOUT: {stdout}")
            logger.error(f"STDERR: {stderr}")
            return False
            
    except Exception as e:
        logger.error(f"❌ خطأ في تشغيل الوكيل: {e}")
        return False

def monitor_startup():
    """مراقبة بدء التشغيل"""
    try:
        logger.info("👀 مراقبة بدء التشغيل...")
        
        # قراءة آخر سطور من ملف السجل
        log_file = "logs/bot.log"
        if os.path.exists(log_file):
            with open(log_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                
            # عرض آخر 10 أسطر
            logger.info("📋 آخر رسائل السجل:")
            for line in lines[-10:]:
                print(f"   {line.strip()}")
                
            # البحث عن رسائل النجاح
            recent_lines = ''.join(lines[-20:])
            if "✅ تم تهيئة البوت بنجاح" in recent_lines:
                logger.info("✅ تم تهيئة الوكيل بنجاح")
                return True
            elif "❌" in recent_lines:
                logger.warning("⚠️ تم العثور على رسائل خطأ في السجل")
                return False
        
        return True
        
    except Exception as e:
        logger.warning(f"⚠️ خطأ في مراقبة بدء التشغيل: {e}")
        return True

def main():
    """الدالة الرئيسية"""
    logger.info("🔄 بدء إعادة تشغيل الوكيل مع مفتاح YouTube API الجديد...")
    
    steps = [
        ("فحص أولوية المفتاح", check_api_key_priority),
        ("إيقاف العمليات الموجودة", kill_existing_processes),
        ("بدء تشغيل الوكيل", start_bot),
        ("مراقبة بدء التشغيل", monitor_startup)
    ]
    
    for step_name, step_func in steps:
        logger.info(f"\n{'='*50}")
        logger.info(f"🔧 {step_name}")
        logger.info(f"{'='*50}")
        
        try:
            result = step_func()
            if result:
                logger.info(f"✅ نجح: {step_name}")
            else:
                logger.error(f"❌ فشل: {step_name}")
                if step_name in ["فحص أولوية المفتاح", "بدء تشغيل الوكيل"]:
                    logger.error("🚨 خطأ حرج - إيقاف العملية")
                    return
        except Exception as e:
            logger.error(f"💥 خطأ في {step_name}: {e}")
    
    logger.info(f"\n{'='*50}")
    logger.info("🎉 تم إعادة تشغيل الوكيل بنجاح!")
    logger.info("✅ المفتاح الجديد نشط ويعمل")
    logger.info("📊 يمكنك مراقبة الأداء في ملف logs/bot.log")
    logger.info(f"{'='*50}")

if __name__ == "__main__":
    main()
