import os
import random
import requests
import json
import logging
from typing import List, Optional

# from elevenlabs import save
from elevenlabs.client import ElevenLabs

from utils import settings

logger = logging.getLogger(__name__)

class elevenlabs:
    def __init__(self):
        self.max_chars = 5000  # زيادة الحد الأقصى لتقليل التقسيم
        self.client: ElevenLabs = None

        # مفاتيح ElevenLabs المحدثة (بترتيب الأولوية)
        self.api_keys = [
            "***************************************************",
            "***************************************************",
            "***************************************************"  # المفتاح الموجود مسبقاً
        ]

        # الصوت المجاني المحدد
        self.free_voice_id = "ErXwobaYiN019PkySvjV"
        self.default_voice_name = "Bella"

    def run(self, text, filepath, random_voice: bool = False):
        """
        تشغيل ElevenLabs مع نظام fallback ذكي
        يجرب المفاتيح بالترتيب ويستخدم الصوت المجاني أولاً
        """
        logger.info("🎤 بدء تشغيل ElevenLabs TTS...")

        # محاولة استخدام المفاتيح بالترتيب
        for i, api_key in enumerate(self.api_keys):
            try:
                logger.info(f"🔑 محاولة المفتاح {i+1}/{len(self.api_keys)}: {api_key[:10]}...")

                if self._try_with_api_key(api_key, text, filepath, random_voice):
                    logger.info(f"✅ نجح ElevenLabs مع المفتاح {i+1}")
                    return True

            except Exception as e:
                logger.warning(f"⚠️ فشل المفتاح {i+1}: {str(e)}")
                continue

        # إذا فشلت جميع المفاتيح، استخدم البديل
        logger.warning("❌ فشلت جميع مفاتيح ElevenLabs، التبديل إلى البديل...")
        self._run_fallback(text, filepath)
        return True

    def _try_with_api_key(self, api_key: str, text: str, filepath: str, random_voice: bool = False) -> bool:
        """محاولة استخدام مفتاح API محدد"""
        try:
            # إنشاء عميل ElevenLabs مع المفتاح
            client = ElevenLabs(api_key=api_key)

            # تحديد الصوت
            if random_voice:
                voice = self._get_random_voice(client)
            else:
                # استخدام الصوت المجاني المحدد أولاً
                voice = self.free_voice_id

            logger.info(f"🎵 استخدام الصوت: {voice}")

            # إنشاء الصوت
            audio = client.text_to_speech.convert(
                text=text,
                voice_id=voice,
                model_id="eleven_multilingual_v1"
            )

            # حفظ الملف
            with open(filepath, 'wb') as f:
                for chunk in audio:
                    f.write(chunk)

            # التحقق من أن الملف تم إنشاؤه بنجاح
            if os.path.exists(filepath) and os.path.getsize(filepath) > 0:
                logger.info(f"✅ تم إنشاء الصوت بنجاح: {filepath}")
                return True
            else:
                logger.warning(f"⚠️ الملف الصوتي فارغ أو غير موجود: {filepath}")
                return False

        except Exception as e:
            error_msg = str(e).lower()

            # فحص نوع الخطأ
            if any(keyword in error_msg for keyword in ["quota", "limit", "insufficient", "exceeded"]):
                logger.warning(f"💳 انتهى رصيد المفتاح: {api_key[:10]}...")
            elif "unauthorized" in error_msg or "invalid" in error_msg:
                logger.warning(f"🔑 مفتاح غير صحيح: {api_key[:10]}...")
            else:
                logger.warning(f"❌ خطأ غير متوقع: {str(e)}")

            raise e

    def _run_fallback(self, text: str, filepath: str):
        """تشغيل البديل عند فشل ElevenLabs"""
        logger.info("🔄 تشغيل البديل: GoogleTranslate...")

        try:
            from TTS.GTTS import GTTS
            gtts_engine = GTTS()
            gtts_engine.run(text, filepath)
            logger.info("✅ تم استخدام GoogleTranslate بنجاح")

        except Exception as e:
            logger.error(f"❌ فشل البديل أيضاً: {str(e)}")
            # محاولة أخيرة مع pyttsx
            try:
                from TTS.pyttsx import pyttsx
                pyttsx_engine = pyttsx()
                pyttsx_engine.run(text, filepath)
                logger.info("✅ تم استخدام pyttsx كبديل أخير")
            except Exception as e2:
                logger.error(f"❌ فشل جميع البدائل: {str(e2)}")
                raise Exception("فشل جميع محركات TTS المتاحة")

    def _get_random_voice(self, client: ElevenLabs) -> str:
        """الحصول على صوت عشوائي"""
        try:
            voices = client.voices.get_all()
            if voices and hasattr(voices, 'voices') and voices.voices:
                return random.choice(voices.voices).voice_id
            else:
                logger.warning("⚠️ لم يتم العثور على أصوات، استخدام الصوت الافتراضي")
                return self.free_voice_id
        except Exception as e:
            logger.warning(f"⚠️ فشل في الحصول على أصوات عشوائية: {str(e)}")
            return self.free_voice_id

    def get_available_voices(self, api_key: str) -> List[dict]:
        """الحصول على قائمة الأصوات المتاحة"""
        try:
            client = ElevenLabs(api_key=api_key)
            voices_response = client.voices.get_all()

            voice_list = []
            if hasattr(voices_response, 'voices') and voices_response.voices:
                for voice in voices_response.voices:
                    voice_list.append({
                        "voice_id": voice.voice_id,
                        "name": voice.name,
                        "category": getattr(voice, 'category', 'Unknown')
                    })

            return voice_list

        except Exception as e:
            logger.error(f"❌ فشل في الحصول على الأصوات: {str(e)}")
            return []

    def test_api_key(self, api_key: str) -> bool:
        """اختبار صحة مفتاح API"""
        try:
            client = ElevenLabs(api_key=api_key)
            # محاولة الحصول على معلومات المستخدم
            user = client.user.get()
            logger.info(f"✅ مفتاح صحيح: {api_key[:10]}... - المستخدم: {getattr(user, 'name', 'Unknown')}")
            return True
        except Exception as e:
            logger.warning(f"❌ مفتاح غير صحيح: {api_key[:10]}... - {str(e)}")
            return False
