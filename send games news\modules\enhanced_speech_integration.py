# تكامل نظام تحويل النص إلى صوت المحسن مع الوكيل الحالي
import asyncio
import time
from typing import Dict, Optional, Any
from datetime import datetime

from modules.logger import logger
from modules.speech_to_text_manager import speech_to_text_manager, TranscriptionResult
from modules.speech_priority_manager import speech_priority_manager
from config.settings import BotConfig

class EnhancedSpeechIntegration:
    """تكامل محسن لخدمات تحويل النص إلى صوت مع النظام الحالي"""
    
    def __init__(self):
        self.speech_manager = speech_to_text_manager
        self.priority_manager = speech_priority_manager
        self.fallback_whisper = None
        self._initialize_services()
        
    def _initialize_services(self):
        """تهيئة جميع الخدمات مع حصصها"""
        services_config = [
            ("assemblyai", BotConfig.ASSEMBLYAI_FREE_HOURS * 60, 0, 0),
            ("speechmatics", BotConfig.SPEECHMATICS_FREE_MINUTES_MONTHLY, 0, 0),
            ("ibm_watson", BotConfig.IBM_WATSON_FREE_MINUTES_MONTHLY, 0, 0),
            ("azure_speech", BotConfig.AZURE_FREE_MINUTES_MONTHLY, 0, 0),
            ("google_cloud", BotConfig.GOOGLE_CLOUD_FREE_MINUTES_MONTHLY, 0, 0),
            ("witai", 999999, 0, 0),  # مجاني بلا حدود
            ("whisper", 999999, 0, 0)  # البديل الاحتياطي
        ]
        
        for service_name, monthly_limit, daily_limit, hourly_limit in services_config:
            self.priority_manager.initialize_service(
                service_name, monthly_limit, daily_limit, hourly_limit
            )
            
        logger.info("✅ تم تهيئة جميع خدمات تحويل النص إلى صوت")
        
    async def transcribe_audio_enhanced(self, audio_data: bytes, video_id: str = "",
                                      video_title: str = "", language: str = "auto",
                                      duration_seconds: float = 0.0) -> Dict[str, Any]:
        """تحويل الصوت إلى نص باستخدام النظام المحسن"""
        
        start_time = time.time()
        duration_minutes = max(1.0, duration_seconds / 60.0)
        
        logger.info(f"🎤 بدء تحويل الصوت إلى نص المحسن - المدة: {duration_minutes:.1f} دقيقة")
        logger.info(f"📹 الفيديو: {video_id} - {video_title[:50]}...")
        
        # الحصول على أفضل خدمة متاحة
        best_service = self.priority_manager.get_best_service(duration_minutes)
        
        if not best_service:
            logger.warning("⚠️ لا توجد خدمات متاحة، استخدام Whisper مباشرة")
            return await self._fallback_to_whisper(audio_data, video_id, video_title, language)
            
        logger.info(f"🎯 استخدام الخدمة: {best_service}")
        
        # محاولة التحويل مع الخدمة المختارة
        result = await self._try_transcription_with_fallback(
            audio_data, video_id, video_title, language, duration_seconds, best_service
        )
        
        processing_time = time.time() - start_time
        
        # تحويل النتيجة للتنسيق المطلوب
        return self._format_result(result, processing_time)
        
    async def _try_transcription_with_fallback(self, audio_data: bytes, video_id: str,
                                             video_title: str, language: str, 
                                             duration_seconds: float, preferred_service: str) -> TranscriptionResult:
        """محاولة التحويل مع البدائل"""
        
        # الحصول على قائمة الخدمات مرتبة حسب الأولوية
        available_services = self.priority_manager.fallback_chain
        
        # وضع الخدمة المفضلة في المقدمة
        if preferred_service in available_services:
            services_to_try = [preferred_service] + [s for s in available_services if s != preferred_service]
        else:
            services_to_try = available_services
            
        # محاولة كل خدمة
        for service_name in services_to_try:
            if not self.priority_manager._is_service_available(service_name, duration_seconds / 60.0):
                logger.debug(f"⏭️ تخطي {service_name} - غير متاح")
                continue
                
            try:
                logger.info(f"🔄 محاولة {service_name}...")
                start_time = time.time()
                
                # استخدام مدير تحويل النص إلى صوت
                result = await self.speech_manager.transcribe_audio(
                    audio_data, video_id, video_title, language, duration_seconds
                )
                
                response_time = time.time() - start_time
                
                # تسجيل النتيجة في مدير الأولوية
                self.priority_manager.record_request_result(
                    service_name, result.success, response_time, 
                    duration_seconds / 60.0, result.error_message or ""
                )
                
                if result.success:
                    logger.info(f"✅ نجح {service_name} - {len(result.text)} حرف")
                    return result
                else:
                    logger.warning(f"❌ فشل {service_name}: {result.error_message}")
                    
            except Exception as e:
                logger.error(f"❌ خطأ في {service_name}: {e}")
                # تسجيل الفشل
                self.priority_manager.record_request_result(
                    service_name, False, 0.0, 0.0, str(e)
                )
                continue
                
        # إذا فشلت جميع الخدمات، استخدم Whisper
        logger.warning("⚠️ فشلت جميع الخدمات، استخدام Whisper كبديل أخير")
        return await self._fallback_to_whisper_direct(audio_data, video_id, video_title, language)
        
    async def _fallback_to_whisper(self, audio_data: bytes, video_id: str, 
                                 video_title: str, language: str) -> Dict[str, Any]:
        """العودة لـ Whisper كبديل"""
        try:
            if not self.fallback_whisper:
                from modules.advanced_youtube_analyzer import AdvancedYouTubeAnalyzer
                self.fallback_whisper = AdvancedYouTubeAnalyzer()
                
            # استخدام نظام Whisper الموجود
            result = await self.fallback_whisper.extract_text_with_whisper(
                audio_data, video_id, video_title, language
            )
            
            if result and result.get('success'):
                return {
                    'success': True,
                    'text': result.get('text', ''),
                    'confidence': result.get('confidence', 0.8),
                    'language': language,
                    'service_used': 'Whisper (Fallback)',
                    'processing_time': 0.0,
                    'word_count': len(result.get('text', '').split()),
                    'enhanced_system': True
                }
            else:
                return {
                    'success': False,
                    'text': '',
                    'confidence': 0.0,
                    'language': language,
                    'service_used': 'Whisper (Fallback)',
                    'processing_time': 0.0,
                    'error': result.get('error', 'فشل Whisper') if result else 'فشل في تهيئة Whisper',
                    'enhanced_system': True
                }
                
        except Exception as e:
            logger.error(f"❌ خطأ في Whisper الاحتياطي: {e}")
            return {
                'success': False,
                'text': '',
                'confidence': 0.0,
                'language': language,
                'service_used': 'Whisper (Fallback)',
                'processing_time': 0.0,
                'error': f'خطأ في Whisper: {str(e)}',
                'enhanced_system': True
            }
            
    async def _fallback_to_whisper_direct(self, audio_data: bytes, video_id: str,
                                        video_title: str, language: str) -> TranscriptionResult:
        """العودة المباشرة لـ Whisper"""
        try:
            result = await self.speech_manager._transcribe_with_whisper(
                audio_data, video_id, video_title, language
            )
            return result
            
        except Exception as e:
            logger.error(f"❌ خطأ في Whisper المباشر: {e}")
            return TranscriptionResult(
                success=False, text="", confidence=0.0, language=language,
                service_used="Whisper (Direct Fallback)", processing_time=0.0,
                error_message=f"خطأ في Whisper: {str(e)}"
            )
            
    def _format_result(self, result: TranscriptionResult, processing_time: float) -> Dict[str, Any]:
        """تنسيق النتيجة للتوافق مع النظام الحالي"""
        return {
            'success': result.success,
            'text': result.text,
            'confidence': result.confidence,
            'language': result.language,
            'service_used': result.service_used,
            'processing_time': processing_time,
            'word_count': result.word_count,
            'duration_seconds': result.duration_seconds,
            'error': result.error_message if not result.success else None,
            'enhanced_system': True,  # علامة للنظام المحسن
            'timestamp': datetime.now().isoformat()
        }
        
    def get_system_status(self) -> Dict[str, Any]:
        """الحصول على حالة النظام المحسن"""
        return {
            'speech_services': self.priority_manager.get_service_statistics(),
            'usage_stats': self.speech_manager.get_usage_statistics(),
            'cache_size': len(self.speech_manager.cache),
            'system_health': 'healthy',
            'last_updated': datetime.now().isoformat()
        }
        
    def reset_service_health(self, service_name: str = None):
        """إعادة تعيين صحة الخدمات"""
        if service_name:
            self.priority_manager.reset_service_health(service_name)
        else:
            # إعادة تعيين جميع الخدمات
            for service in self.priority_manager.health_data.keys():
                self.priority_manager.reset_service_health(service)
                
        logger.info("🔄 تم إعادة تعيين صحة الخدمات")
        
    def clear_cache(self):
        """مسح التخزين المؤقت"""
        self.speech_manager.clear_cache()
        logger.info("🗑️ تم مسح التخزين المؤقت")


# إنشاء مثيل عام للاستخدام
enhanced_speech_integration = EnhancedSpeechIntegration()
