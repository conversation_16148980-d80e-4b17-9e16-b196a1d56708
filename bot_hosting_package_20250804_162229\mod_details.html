<!DOCTYPE html>
<html id="html-root" lang="{{ lang }}" dir="{{ 'rtl' if lang == 'ar' else 'ltr' }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; font-src 'self' data: https://cdn.jsdelivr.net https://fonts.googleapis.com https://fonts.gstatic.com; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://fonts.googleapis.com; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://telegram.org; img-src 'self' data: blob: https: http:; connect-src 'self' https: http: ws: wss:;">
    <title id="page-title">{{ mod_title }} - {{ 'تفاصيل المود' if lang == 'ar' else 'Mod Details' }}</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Press+Start+2P&display=swap" rel="stylesheet">
    <style>
        {% if page_customization and page_customization.page_theme %}
            {% if page_customization.page_theme == 'telegram' %}
                body {
                    font-family: 'Press Start 2P', cursive;
                    background-color: #0088cc;
                    color: white;
                }
                .header-bg {
                    background-color: #0088cc !important;
                }
                .mod-container {
                    background-color: #005580;
                }
                .mod-header {
                    background-color: #005580;
                    color: white;
                }
            {% elif page_customization.page_theme == 'dark' %}
                body {
                    font-family: 'Press Start 2P', cursive;
                    background-color: #000000;
                    color: #ffffff;
                }
                .header-bg {
                    background-color: #1a1a1a !important;
                }
                .mod-container {
                    background-color: #1a1a1a;
                }
                .mod-header {
                    background-color: #1a1a1a;
                    color: white;
                }
            {% elif page_customization.page_theme == 'light' %}
                body {
                    font-family: 'Press Start 2P', cursive;
                    background-color: #f5f5f5;
                    color: #333333;
                }
                .header-bg {
                    background-color: #ffffff !important;
                    color: #333333 !important;
                }
                .mod-container {
                    background-color: #ffffff;
                    color: #333333;
                }
                .mod-header {
                    background-color: #ffffff;
                    color: #333333;
                }
            {% elif page_customization.page_theme == 'custom' %}
                body {
                    font-family: 'Press Start 2P', cursive;
                    background-color: {{ page_customization.custom_bg_color or '#1a1a1a' }};
                    color: {{ page_customization.custom_text_color or 'white' }};
                }
                .header-bg {
                    background-color: {{ page_customization.custom_header_color or '#FFA500' }} !important;
                    color: {{ page_customization.custom_text_color or 'white' }} !important;
                }
                .mod-container {
                    background-color: {{ page_customization.custom_bg_color or '#2D2D2D' }};
                    color: {{ page_customization.custom_text_color or 'white' }};
                }
                .mod-header {
                    background-color: {{ page_customization.custom_header_color or '#2D2D2D' }};
                    color: {{ page_customization.custom_text_color or 'white' }};
                }
                .pixel-button {
                    background-color: {{ page_customization.custom_button_color or '#FFA500' }};
                    border-color: {{ page_customization.custom_border_color or '#FFD700' }};
                }
            {% else %}
                body {
                    font-family: 'Press Start 2P', cursive;
                    background-color: #1a1a1a;
                    color: white;
                }
                .header-bg {
                    background-color: #FFA500;
                }
            {% endif %}
        {% else %}
            body {
                font-family: 'Press Start 2P', cursive;
                background-color: #1a1a1a;
                color: white;
            }
            .header-bg {
                background-color: #FFA500;
            }
        {% endif %}

        .pixel-border {
            box-shadow: inset 0 0 0 1px #AAAAAA;
            border: 1px solid #AAAAAA;
        }

        .mod-header {
            background-color: #2D2D2D;
            color: white;
            padding: 15px;
            text-align: center;
        }

        .mod-container {
            background-color: #2D2D2D;
        }

        .nav-button {
            background-color: #FFA500;
            color: white;
            width: 40px; /* Slightly smaller buttons */
            height: 40px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 20px; /* Smaller font size */
            flex-shrink: 0; /* Prevent shrinking */
        }

        .thumbnail {
            width: 80px;
            height: 60px;
            object-fit: cover;
            cursor: pointer;
            border: 3px solid #FFA500;
            border-radius: 8px;
            transition: all 0.3s ease;
            background-color: #FFA500;
            box-shadow: 0 2px 8px rgba(255, 165, 0, 0.3);
            opacity: 0.8;
        }

        .thumbnail:hover {
            opacity: 1;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(255, 165, 0, 0.5);
        }

        .thumbnail.active {
            border-color: #FF8C00;
            opacity: 1;
            transform: scale(1.1);
            box-shadow: 0 4px 16px rgba(255, 140, 0, 0.7);
        }

        .pixel-button {
            image-rendering: pixelated;
            background-color: #FFA500;
            color: white;
            border: 2px solid #FFD700;
            padding: 10px 20px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            cursor: pointer;
            box-shadow: inset -4px -4px 0 0 #d27e00,
                       inset 4px 4px 0 0 #ffcb6b,
                       0 0 10px #FFD700,
                       0 0 20px #FFD700;
            transition: all 0.1s;
            position: relative;
            overflow: hidden;
            animation: pulse-animation 1.5s infinite;
            min-width: 200px;
            min-height: 50px;
        }

        .pixel-button.downloading {
            background-color: #4CAF50;
            border-color: #45a049;
            animation: none;
            cursor: not-allowed;
        }

        .pixel-button.downloaded {
            background-color: #2196F3;
            border-color: #1976D2;
            animation: none;
        }

        .progress-bar {
            position: absolute;
            bottom: 0;
            left: 0;
            height: 4px;
            background-color: #4CAF50;
            width: 0%;
            transition: width 0.3s ease;
            z-index: 1;
        }

        .download-icon {
            display: inline-block;
            margin-right: 8px;
            font-size: 18px;
        }

        .spinner {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid #ffffff;
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s ease-in-out infinite;
            margin-right: 8px;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* تحسينات إضافية للتصميم */
        .download-success-animation {
            animation: downloadSuccess 0.6s ease-out;
        }

        @keyframes downloadSuccess {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); box-shadow: 0 0 20px #4CAF50; }
            100% { transform: scale(1); }
        }

        .progress-bar {
            transition: width 0.3s ease, background-color 0.3s ease;
        }

        .pixel-button:hover {
            transform: translateY(-2px);
            box-shadow: inset -4px -4px 0 0 #d27e00,
                       inset 4px 4px 0 0 #ffcb6b,
                       0 0 15px #FFD700,
                       0 0 25px #FFD700;
        }

        .pixel-button.downloading:hover {
            transform: none;
        }

        .pixel-button.downloaded:hover {
            box-shadow: 0 0 15px #2196F3,
                       0 0 25px #2196F3;
        }

        /* تأثيرات الإشعارات */
        .notification-enter {
            animation: slideInRight 0.3s ease-out;
        }

        .notification-exit {
            animation: slideOutRight 0.3s ease-in;
        }

        @keyframes slideInRight {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        @keyframes slideOutRight {
            from { transform: translateX(0); opacity: 1; }
            to { transform: translateX(100%); opacity: 0; }
        }

        @keyframes pulse-animation {
            0% { transform: scale(1); box-shadow: inset -4px -4px 0 0 #d27e00, inset 4px 4px 0 0 #ffcb6b, 0 0 10px #FFD700, 0 0 20px #FFD700; }
            50% { transform: scale(1.05); box-shadow: inset -4px -4px 0 0 #d27e00, inset 4px 4px 0 0 #ffcb6b, 0 0 15px #FFD700, 0 0 30px #FFD700; }
            100% { transform: scale(1); box-shadow: inset -4px -4px 0 0 #d27e00, inset 4px 4px 0 0 #ffcb6b, 0 0 10px #FFD700, 0 0 20px #FFD700; }
        }

        .pixel-button::before,
        .pixel-button::after {
            content: '';
            position: absolute;
            width: 6px;
            height: 6px;
            background-color: #FFFF00;
            box-shadow: 0 0 5px #FFFF00, 0 0 10px #FFFF00;
            border-radius: 50%;
            opacity: 0;
            animation: particle-effect 2s infinite ease-out;
        }

        /* Thumbnail styles are defined above */

        .info-label {
            color: #FFA500;
            font-size: 14px;
        }

        .mod-info {
            font-size: 18px;
            color: white;
            letter-spacing: -1px;
        }

        .image-glow-effect {
            position: relative;
            overflow: hidden;
            background-color: #FFC300;
        }

        /* Thumbnail styles are defined above */

        .image-glow-effect .particle {
            position: absolute;
            width: 6px;
            height: 6px;
            background-color: #FFD700;
            box-shadow: 0 0 3px #FFD700, 0 0 2px #FFA500;
            opacity: 0;
            animation: emanate-squares-from-bottom 3s infinite ease-out;
        }
        
        /* Enhanced Thumbnail Container Styles */
        #thumbnail-container {
            background-color: transparent;
            padding: 0;
            border: none;
            margin: 20px 0;
            min-height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .thumbnails-wrapper {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 15px;
            flex-wrap: wrap;
            width: 100%;
        }

        @keyframes emanate-squares-from-bottom {
            0% { opacity: 0.9; transform: translate(0, 0) scale(0.5) rotate(0deg); }
            20% { opacity: 1; transform: translate(var(--tx, 0px), var(--ty, 0px)) scale(1) rotate(45deg); }
            80% { opacity: 0.5; transform: translate(calc(var(--tx, 0px) * 2.5), calc(var(--ty, 0px) * 2.5)) scale(0.8) rotate(90deg); }
            100% { opacity: 0; transform: translate(calc(var(--tx, 0px) * 3), calc(var(--ty, 0px) * 3)) scale(0) rotate(135deg); }
        }
    </style>

<script>
// إضافة headers لتجنب ngrok browser warning
if (window.location.hostname.includes('ngrok')) {
    // تعيين headers للطلبات
    const originalFetch = window.fetch;
    window.fetch = function(...args) {
        if (args[1]) {
            args[1].headers = {
                ...args[1].headers,
                'ngrok-skip-browser-warning': 'true',
                'User-Agent': 'TelegramBot/1.0'
            };
        } else {
            args[1] = {
                headers: {
                    'ngrok-skip-browser-warning': 'true',
                    'User-Agent': 'TelegramBot/1.0'
                }
            };
        }
        return originalFetch.apply(this, args);
    };
}
</script>

</head>
<body>
    <!-- Header -->
    <header class="header-bg text-white p-4 flex justify-center items-center">
        {% if page_customization and page_customization.channel_logo_url %}
            {% if page_customization.logo_position == 'left' %}
                <div class="flex items-center">
                    <img src="{{ page_customization.channel_logo_url }}" alt="Channel Logo" class="w-12 h-12 rounded-full mr-4 border-2 border-white shadow-lg">
                    <div class="font-bold text-2xl">{{ site_name }}</div>
                </div>
            {% else %}
                <div class="flex items-center">
                    <div class="font-bold text-2xl">{{ site_name }}</div>
                    <img src="{{ page_customization.channel_logo_url }}" alt="Channel Logo" class="w-12 h-12 rounded-full ml-4 border-2 border-white shadow-lg">
                </div>
            {% endif %}
        {% else %}
            <div class="font-bold text-2xl">{{ site_name }}</div>
        {% endif %}
    </header>

    <div class="container mx-auto p-4 pb-24">
        <!-- Mod Title -->
        <div class="mod-header pixel-border mb-4">
            <h1 class="text-2xl mod-title">{{ mod_title }}</h1>
        </div>

        <!-- Main Mod Image Display -->
        <div class="mod-container mb-4 p-1 image-glow-effect">
            <div class="relative w-full bg-gray-700" style="padding-top: 56.25%;">
                <img id="main-mod-image" class="absolute inset-0 w-full h-full object-cover" src="{{ mod_image_url }}" alt="{{ 'صورة المود' if lang == 'ar' else 'Mod Image' }}">
            </div>
            <div class="particle p-bottom1" style="--tx: 2px; --ty: -25px;"></div>
            <div class="particle p-bottom2" style="--tx: 0px; --ty: -30px;"></div>
            <div class="particle p-bottom3" style="--tx: -2px; --ty: -28px;"></div>
        </div>

        <!-- Thumbnail Navigation and Controls -->
        <div class="flex items-center justify-center mb-4">
            <button id="prev-image" class="nav-button pixel-border mr-2"><</button>
            <div id="thumbnail-container" class="flex-grow">
                <!-- Thumbnails will be inserted here by JavaScript -->
            </div>
            <button id="next-image" class="nav-button pixel-border ml-2">></button>
        </div>

        <!-- Mod Info -->
        <div class="grid grid-cols-2 gap-4 mb-6">
            <div class="mod-container pixel-border p-4">
                <p class="info-label version-label">{{ version_label }}</p>
                <p class="mod-info mod-version">{{ mod_version }}</p>
            </div>
            <div class="mod-container pixel-border p-4">
                <p class="info-label loader-label">{{ category_label }}</p>
                <p class="mod-info mod-category">{{ mod_category }}</p>
            </div>
        </div>

        <!-- Mod Description -->
        <div class="mod-container pixel-border p-4 mb-6">
            <p class="info-label text-center description-label">{{ description_label }}</p>
            <p class="mod-info mt-2 text-center mod-description">{{ mod_description | safe }}</p>
        </div>

        <!-- Tasks Modal -->
        <div id="tasks-modal" class="fixed inset-0 bg-black bg-opacity-75 z-60 hidden flex items-center justify-center">
            <div class="bg-white rounded-lg p-6 max-w-md mx-4 text-center relative max-h-96 overflow-y-auto">
                <h3 class="text-lg font-bold text-gray-800 mb-4">{{ 'أكمل المهام للتحميل' if lang == 'ar' else 'Complete Tasks to Download' }}</h3>
                <div id="tasks-list" class="space-y-3 mb-4">
                    <!-- المهام ستُضاف هنا بواسطة JavaScript -->
                </div>
                <div id="tasks-progress" class="mb-4">
                    <div class="w-full bg-gray-200 rounded-full h-2.5">
                        <div id="progress-bar" class="bg-blue-600 h-2.5 rounded-full" style="width: 0%"></div>
                    </div>
                    <p id="progress-text" class="text-sm text-gray-600 mt-2">0 / 0 {{ 'مكتملة' if lang == 'ar' else 'completed' }}</p>
                </div>
                <button id="download-after-tasks" class="bg-green-500 text-white px-6 py-2 rounded hidden" onclick="proceedToDownload()">
                    {{ 'تحميل الآن' if lang == 'ar' else 'Download Now' }}
                </button>
            </div>
        </div>

        <!-- Download Button -->
        <div class="fixed bottom-0 left-0 right-0 p-4 z-50 flex justify-center">
            <button id="download-button" class="pixel-button text-xl py-3 px-10 new-feature" style="position: relative; overflow: hidden;" onclick="handleDownload()">
                <div class="progress-bar" id="progress-bar"></div>
                <span class="download-icon" id="download-icon">📥</span>
                <span id="download-text">{{ download_button_text }}</span>
                <span class="new-badge" style="position: absolute; top: -5px; right: -25px; background-color: #dc3545; color: white; transform: rotate(45deg); padding: 20px 25px 5px 25px; font-size: 10px; font-weight: bold; box-shadow: 0 2px 4px rgba(0,0,0,0.3);" id="new-badge-text">{{ 'ميزة جديدة!' if lang == 'ar' else 'New Feature!' }}</span>
            </button>
        </div>

        <!-- Ad Overlay -->
        <div id="ad-overlay" class="fixed inset-0 bg-black bg-opacity-75 z-60 hidden flex items-center justify-center">
            <div class="bg-white rounded-lg p-6 max-w-md mx-4 text-center relative">
                <div id="ad-content" class="mb-4">
                    <h3 class="text-lg font-bold text-gray-800 mb-2">{{ 'إعلان' if lang == 'ar' else 'Advertisement' }}</h3>
                    <p class="text-gray-600 mb-4">{{ 'سيتم فتح الإعلان في نافذة جديدة...' if lang == 'ar' else 'Ad will open in a new window...' }}</p>
                </div>
                <button id="close-ad-btn" class="bg-red-500 text-white px-4 py-2 rounded hidden" onclick="closeAd()">
                    {{ 'إغلاق' if lang == 'ar' else 'Close' }}
                </button>
                <div id="countdown" class="text-sm text-gray-500 mt-2"></div>
            </div>
        </div>


        <style>
            .new-feature {
                animation: pulse 1.5s infinite;
            }

            @keyframes pulse {
                0% { transform: scale(1); }
                50% { transform: scale(1.05); box-shadow: 0 0 15px rgba(255, 165, 0, 0.7); }
                100% { transform: scale(1); }
            }
        </style>
    </div>

    <script>
        const mainModImage = document.getElementById('main-mod-image'); // Changed ID
        const prevButton = document.getElementById('prev-image');
        const nextButton = document.getElementById('next-image');
        const thumbnailContainer = document.getElementById('thumbnail-container');

        // This will be populated by Flask with the actual image URLs
        const imageUrls = JSON.parse('{{ mod_image_urls_json | safe }}');
        let currentImageIndex = 0;

        // دالة تحميل بيانات المود من API (للتوافق مع الأخطاء المحتملة)
        async function loadModData() {
            try {
                const urlParams = new URLSearchParams(window.location.search);
                const modId = urlParams.get('id') || '{{ mod_id }}';
                const lang = urlParams.get('lang') || '{{ lang }}';
                const userId = urlParams.get('user_id') || '{{ user_id }}';
                const channel = urlParams.get('channel') || '{{ channel_id }}';

                const apiUrl = `/api/mod/${modId}?lang=${lang}&user_id=${userId}&channel=${channel}`;

                const response = await fetch(apiUrl, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json',
                        'User-Agent': 'TelegramBot/1.0',
                        'ngrok-skip-browser-warning': 'true'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const contentType = response.headers.get('content-type');
                if (!contentType || !contentType.includes('application/json')) {
                    throw new Error(`Expected JSON, got ${contentType}`);
                }

                const modData = await response.json();
                console.log('Mod data loaded successfully:', modData);
                return modData;

            } catch (error) {
                console.error('Error loading mod data:', error);

                // في حالة الخطأ، استخدم البيانات المتاحة من template
                return {
                    id: '{{ mod_id }}',
                    title: '{{ mod_title }}',
                    description: '{{ mod_description | safe }}',
                    version: '{{ mod_version }}',
                    category: '{{ mod_category }}',
                    download_link: '{{ mod_download_url }}',
                    image_urls: imageUrls
                };
            }
        }

        // استدعاء دالة تحميل البيانات عند تحميل الصفحة (اختياري)
        document.addEventListener('DOMContentLoaded', function() {
            // يمكن استدعاء loadModData هنا إذا لزم الأمر
            // loadModData().then(data => console.log('Mod data:', data));
        });

        // إعدادات الإعلانات - Ads Settings (Jinja2 Template - IDE warnings are normal)
        // eslint-disable-next-line
        const adsSettings = {% if ads_settings %}{{ ads_settings | tojson }}{% else %}null{% endif %};
        const modDownloadUrl = '{{ mod_download_url }}';
        const userId = '{{ user_id }}';
        let adShown = false;

        // إعدادات نظام المهام - Tasks Settings (Jinja2 Template - IDE warnings are normal)
        // eslint-disable-next-line
        const tasksSettings = {% if tasks_settings %}{{ tasks_settings | tojson }}{% else %}null{% endif %};
        // eslint-disable-next-line
        const availableTasks = {% if available_tasks %}{{ available_tasks | tojson }}{% else %}[]{% endif %};
        // eslint-disable-next-line
        const userCompletedTasks = {% if user_completed_tasks %}{{ user_completed_tasks | tojson }}{% else %}[]{% endif %};
        // eslint-disable-next-line
        const userIsCompleted = {% if user_is_completed %}{{ user_is_completed | tojson }}{% else %}false{% endif %};
        const channelOwnerId = '{{ channel_id }}'; // سنحتاج لإيجاد صاحب القناة

        let tasksCompleted = false;

        // متغيرات حالة التحميل
        let isDownloading = false;
        let isDownloaded = false;
        let downloadProgress = 0;
        let modFileSize = 0;
        let downloadedFileName = '';
        let downloadStartTime = 0;

        // معلومات المود
        const modId = '{{ mod_id }}';
        const modTitle = '{{ mod_title }}';
        const modFileExtension = modDownloadUrl.includes('.mcaddon') ? '.mcaddon' : '.mcpack';

        // إعدادات التخصيص
        const customDownloadText = '{{ download_button_text }}';
        const customOpenText = '{{ open_button_text }}';
        const enableModOpening = {{ 'true' if enable_mod_opening else 'false' }};
        const showAllImages = {{ 'true' if page_customization and page_customization.show_all_images else 'true' }};

        function updateImage() {
            console.log('updateImage called, currentImageIndex:', currentImageIndex);

            if (imageUrls && imageUrls.length > 0 && mainModImage) {
                // Ensure index is within bounds
                if (currentImageIndex < 0) currentImageIndex = 0;
                if (currentImageIndex >= imageUrls.length) currentImageIndex = imageUrls.length - 1;

                // Update main image
                mainModImage.src = imageUrls[currentImageIndex];
                console.log('Main image updated to:', imageUrls[currentImageIndex]);

                // Update thumbnails to reflect current selection
                updateThumbnails();
            } else {
                console.log('Cannot update image: missing data or elements');
            }
        }

        function updateThumbnails() {
            console.log('updateThumbnails called, imageUrls:', imageUrls);

            // Clear existing thumbnails
            thumbnailContainer.innerHTML = '';

            // Check if we have images
            if (!imageUrls || imageUrls.length === 0) {
                console.log('No images available for thumbnails');
                return;
            }

            // Create wrapper with the new class
            const thumbnailsWrapper = document.createElement('div');
            thumbnailsWrapper.className = 'thumbnails-wrapper';

            // Create thumbnails for all images
            for (let i = 0; i < imageUrls.length; i++) {
                const thumbnail = document.createElement('img');
                thumbnail.src = imageUrls[i];
                thumbnail.className = 'thumbnail';
                thumbnail.alt = `صورة ${i + 1}`;

                // Mark active thumbnail
                if (i === currentImageIndex) {
                    thumbnail.classList.add('active');
                }

                // Add click event
                thumbnail.dataset.index = i;
                thumbnail.addEventListener('click', (event) => {
                    const newIndex = parseInt(event.target.dataset.index);
                    console.log('Thumbnail clicked, changing to index:', newIndex);
                    currentImageIndex = newIndex;
                    updateImage();
                });

                // Add error handling for images
                thumbnail.addEventListener('error', () => {
                    console.error('Failed to load thumbnail:', imageUrls[i]);
                    thumbnail.style.display = 'none';
                });

                thumbnailsWrapper.appendChild(thumbnail);
            }

            thumbnailContainer.appendChild(thumbnailsWrapper);
            console.log('Thumbnails updated, count:', imageUrls.length);
        }

        // Navigation button event listeners
        if (prevButton) {
            prevButton.addEventListener('click', () => {
                if (imageUrls && imageUrls.length > 0) {
                    currentImageIndex = (currentImageIndex - 1 + imageUrls.length) % imageUrls.length;
                    console.log('Previous button clicked, new index:', currentImageIndex);
                    updateImage();
                }
            });
        }

        if (nextButton) {
            nextButton.addEventListener('click', () => {
                if (imageUrls && imageUrls.length > 0) {
                    currentImageIndex = (currentImageIndex + 1) % imageUrls.length;
                    console.log('Next button clicked, new index:', currentImageIndex);
                    updateImage();
                }
            });
        }

        // دوال إدارة التحميل والمهام
        function handleDownload() {
            // التحقق من نظام المهام أولاً
            if (tasksSettings && tasksSettings.tasks_enabled && !userIsCompleted) {
                if (availableTasks.length > 0) {
                    showTasksModal();
                    return;
                }
            }

            // إذا لم تكن هناك مهام أو تم إكمالها، تحقق من الإعلانات
            if (adsSettings && adsSettings.ads_enabled && !adShown) {
                const displayMode = adsSettings.ad_display_mode;

                if (displayMode === 'on_download') {
                    showAd();
                } else {
                    // تحميل مباشر إذا لم تكن الإعلانات مفعلة أو تم عرضها بالفعل
                    downloadMod();
                }
            } else {
                downloadMod();
            }
        }

        // دوال نظام المهام
        function showTasksModal() {
            const modal = document.getElementById('tasks-modal');
            const tasksList = document.getElementById('tasks-list');

            // مسح المحتوى السابق
            tasksList.innerHTML = '';

            // إضافة المهام
            availableTasks.forEach((task, index) => {
                const isCompleted = userCompletedTasks.some(ct => ct.task_id === task.id);
                const taskElement = createTaskElement(task, isCompleted, index);
                tasksList.appendChild(taskElement);
            });

            updateTasksProgress();
            modal.classList.remove('hidden');
        }

        function createTaskElement(task, isCompleted, index) {
            const taskDiv = document.createElement('div');
            taskDiv.className = `task-item p-3 border rounded ${isCompleted ? 'bg-green-100 border-green-300' : 'bg-gray-50 border-gray-300'}`;

            const taskTypeIcon = getTaskTypeIcon(task.task_type);
            const statusIcon = isCompleted ? '✅' : '⏳';

            taskDiv.innerHTML = `
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-2">
                        <span class="text-lg">${taskTypeIcon}</span>
                        <div>
                            <h4 class="font-semibold text-sm">${task.task_title}</h4>
                            <p class="text-xs text-gray-600">${task.task_description || ''}</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2">
                        <span class="text-lg">${statusIcon}</span>
                        ${!isCompleted ? `<button onclick="completeTask(${task.id}, ${index})" class="bg-blue-500 text-white px-3 py-1 rounded text-xs">{{ 'إكمال' if lang == 'ar' else 'Complete' }}</button>` : ''}
                    </div>
                </div>
            `;

            return taskDiv;
        }

        function getTaskTypeIcon(taskType) {
            const icons = {
                'youtube': '📺',
                'telegram': '💬',
                'twitter': '🐦',
                'discord': '🎮',
                'instagram': '📸',
                'website': '🌐'
            };
            return icons[taskType] || '📋';
        }

        function completeTask(taskId, taskIndex) {
            // فتح المهمة في نافذة جديدة
            const task = availableTasks.find(t => t.id === taskId);
            if (task && task.task_url) {
                window.open(task.task_url, '_blank');
            }

            // تسجيل إكمال المهمة
            fetch(`/complete-task?user_id=${userId}&task_id=${taskId}&channel_owner_id=${channelOwnerId}&channel_id={{ channel_id }}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // تحديث حالة المهمة
                        userCompletedTasks.push({task_id: taskId});

                        // تحديث العرض
                        const taskElement = document.querySelectorAll('.task-item')[taskIndex];
                        taskElement.className = 'task-item p-3 border rounded bg-green-100 border-green-300';
                        taskElement.querySelector('button').remove();
                        taskElement.querySelector('.text-lg:last-child').textContent = '✅';

                        updateTasksProgress();

                        // إذا تم إكمال جميع المهام
                        if (data.all_completed) {
                            tasksCompleted = true;
                            userIsCompleted = true;
                            document.getElementById('download-after-tasks').classList.remove('hidden');
                        }
                    }
                })
                .catch(error => {
                    console.error('Error completing task:', error);
                });
        }

        function updateTasksProgress() {
            const totalTasks = availableTasks.length;
            const completedTasks = userCompletedTasks.length;
            const progressPercentage = totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0;

            document.getElementById('progress-bar').style.width = `${progressPercentage}%`;
            document.getElementById('progress-text').textContent = `${completedTasks} / ${totalTasks} {{ 'مكتملة' if lang == 'ar' else 'completed' }}`;

            if (completedTasks === totalTasks && totalTasks > 0) {
                document.getElementById('download-after-tasks').classList.remove('hidden');
            }
        }

        function proceedToDownload() {
            // إغلاق نافذة المهام
            document.getElementById('tasks-modal').classList.add('hidden');

            // المتابعة للتحميل مع الإعلانات إن وجدت
            if (adsSettings && adsSettings.ads_enabled && !adShown) {
                const displayMode = adsSettings.ad_display_mode;

                if (displayMode === 'on_download') {
                    showAd();
                } else {
                    downloadMod();
                }
            } else {
                downloadMod();
            }
        }

        function showAd() {
            if (!adsSettings || !adsSettings.ad_direct_link) {
                downloadMod();
                return;
            }

            const overlay = document.getElementById('ad-overlay');
            const closeBtn = document.getElementById('close-ad-btn');
            const countdown = document.getElementById('countdown');

            overlay.classList.remove('hidden');
            adShown = true;

            // فتح الإعلان في نافذة جديدة
            const adUrl = `/ad-click?user_id=${userId}&url=${encodeURIComponent(adsSettings.ad_direct_link)}`;
            window.open(adUrl, '_blank');

            // إظهار زر الإغلاق بعد التأخير المحدد
            const closeDelay = adsSettings.close_button_delay || 5;
            let timeLeft = closeDelay;

            const countdownInterval = setInterval(() => {
                countdown.textContent = `{{ 'يمكن الإغلاق خلال' if lang == 'ar' else 'Can close in' }} ${timeLeft} {{ 'ثانية' if lang == 'ar' else 'seconds' }}`;
                timeLeft--;

                if (timeLeft < 0) {
                    clearInterval(countdownInterval);
                    closeBtn.classList.remove('hidden');
                    countdown.textContent = '';
                }
            }, 1000);

            // بدء التحميل تلقائياً
            setTimeout(() => {
                downloadMod();
            }, 1000);
        }

        function closeAd() {
            const overlay = document.getElementById('ad-overlay');
            overlay.classList.add('hidden');
        }

        function downloadMod() {
            if (isDownloading || isDownloaded) {
                if (isDownloaded) {
                    // فتح الملف المحمل
                    openDownloadedFile();
                }
                return;
            }

            startDownload();
        }

        function startDownload() {
            isDownloading = true;
            downloadStartTime = Date.now();
            updateDownloadButton();

            // محاكاة التحميل مع مؤشر التقدم
            simulateDownload();
        }

        async function simulateDownload() {
            const button = document.getElementById('download-button');
            const progressBar = document.getElementById('progress-bar');
            const downloadText = document.getElementById('download-text');
            const downloadIcon = document.getElementById('download-icon');

            try {
                // جلب معلومات الملف من الخادم
                const response = await fetch(`/get-file-info?mod_id=${modId}`);
                const fileInfo = await response.json();

                if (fileInfo.success) {
                    modFileSize = fileInfo.size_mb;
                    downloadedFileName = fileInfo.filename;
                } else {
                    // استخدام قيم افتراضية في حالة الفشل
                    modFileSize = Math.random() * 50 + 10;
                    downloadedFileName = `${modTitle.replace(/[^a-zA-Z0-9]/g, '_')}${modFileExtension}`;
                }
            } catch (error) {
                console.error('Error fetching file info:', error);
                // استخدام قيم افتراضية
                modFileSize = Math.random() * 50 + 10;
                downloadedFileName = `${modTitle.replace(/[^a-zA-Z0-9]/g, '_')}${modFileExtension}`;
            }

            // محاكاة سرعة التحميل
            const downloadSpeed = Math.random() * 5 + 2; // بين 2-7 ميجابايت/ثانية
            const totalTime = (modFileSize / downloadSpeed) * 1000; // بالميلي ثانية

            let startTime = Date.now();

            const updateProgress = () => {
                const elapsed = Date.now() - startTime;
                downloadProgress = Math.min((elapsed / totalTime) * 100, 100);

                // تحديث شريط التقدم
                progressBar.style.width = downloadProgress + '%';

                // تحديث النص والأيقونة
                if (downloadProgress < 100) {
                    const downloadingText = '{{ "جاري التحميل..." if lang == "ar" else "Downloading..." }}';
                    const downloadedMB = (modFileSize * downloadProgress / 100).toFixed(1);
                    const speedMBps = downloadSpeed.toFixed(1);

                    // حساب الوقت المتبقي
                    const remainingMB = modFileSize - (modFileSize * downloadProgress / 100);
                    const remainingSeconds = Math.ceil(remainingMB / downloadSpeed);
                    const remainingTime = remainingSeconds > 60 ?
                        `${Math.ceil(remainingSeconds / 60)}{{ "د" if lang == "ar" else "m" }}` :
                        `${remainingSeconds}{{ "ث" if lang == "ar" else "s" }}`;

                    downloadText.textContent = `${downloadingText} ${Math.round(downloadProgress)}%`;

                    // إضافة معلومات إضافية في tooltip
                    const button = document.getElementById('download-button');
                    button.title = `{{ "المحمل:" if lang == "ar" else "Downloaded:" }} ${downloadedMB}/${modFileSize.toFixed(1)} MB\n{{ "السرعة:" if lang == "ar" else "Speed:" }} ${speedMBps} MB/s\n{{ "الوقت المتبقي:" if lang == "ar" else "Time remaining:" }} ${remainingTime}`;

                    if (downloadProgress < 30) {
                        downloadIcon.innerHTML = '<div class="spinner"></div>';
                    } else if (downloadProgress < 70) {
                        downloadIcon.textContent = '⬇️';
                    } else {
                        downloadIcon.textContent = '📦';
                    }

                    requestAnimationFrame(updateProgress);
                } else {
                    // التحميل مكتمل
                    completeDownload();
                }
            };

            // بدء التحميل الفعلي في نافذة جديدة
            try {
                // إنشاء رابط تحميل مؤقت
                const downloadLink = document.createElement('a');
                downloadLink.href = modDownloadUrl;
                downloadLink.download = downloadedFileName || 'mod_file';
                downloadLink.target = '_blank';
                downloadLink.style.display = 'none';

                document.body.appendChild(downloadLink);
                downloadLink.click();
                document.body.removeChild(downloadLink);

                // إظهار إشعار بدء التحميل
                showDownloadStartNotification();

            } catch (error) {
                console.error('Error starting download:', error);
                // fallback إلى window.open
                window.open(modDownloadUrl, '_blank');
            }

            // بدء محاكاة التقدم
            updateProgress();
        }

        function completeDownload() {
            isDownloading = false;
            isDownloaded = true;
            downloadProgress = 100;

            // حساب إحصائيات التحميل
            const downloadEndTime = Date.now();
            const totalDownloadTime = (downloadEndTime - downloadStartTime) / 1000; // بالثواني
            const averageSpeed = (modFileSize / totalDownloadTime).toFixed(1);

            // تحديث اسم الملف المحمل
            if (!downloadedFileName) {
                downloadedFileName = `${modTitle.replace(/[^a-zA-Z0-9]/g, '_')}${modFileExtension}`;
            }

            updateDownloadButton();

            // حفظ إحصائيات التحميل
            const downloadStats = {
                fileSize: modFileSize,
                downloadTime: totalDownloadTime,
                averageSpeed: averageSpeed,
                fileName: downloadedFileName,
                completedAt: new Date().toISOString()
            };

            localStorage.setItem(`download_stats_${modId}`, JSON.stringify(downloadStats));

            // إظهار إشعار النجاح مع الإحصائيات
            showDownloadCompleteNotification(downloadStats);
        }

        function updateDownloadButton() {
            const button = document.getElementById('download-button');
            const downloadText = document.getElementById('download-text');
            const downloadIcon = document.getElementById('download-icon');
            const progressBar = document.getElementById('progress-bar');

            if (isDownloading) {
                button.classList.add('downloading');
                button.classList.remove('downloaded');
                downloadText.textContent = `{{ 'جاري التحميل...' if lang == 'ar' else 'Downloading...' }} ${Math.round(downloadProgress)}%`;
                progressBar.style.width = downloadProgress + '%';
            } else if (isDownloaded) {
                button.classList.remove('downloading');
                button.classList.add('downloaded');
                // استخدام النص المخصص لفتح المود أو إخفاء الزر إذا كان الفتح معطل
                if (enableModOpening) {
                    downloadText.textContent = customOpenText;
                    downloadIcon.textContent = '📂';
                } else {
                    downloadText.textContent = customDownloadText + ' ✓';
                    downloadIcon.textContent = '✅';
                }
                progressBar.style.width = '100%';
                progressBar.style.backgroundColor = '#2196F3';
            } else {
                button.classList.remove('downloading', 'downloaded');
                downloadText.textContent = customDownloadText;
                downloadIcon.textContent = '📥';
                progressBar.style.width = '0%';
            }
        }

        function openDownloadedFile() {
            // التحقق من إعدادات فتح المود
            if (!enableModOpening) {
                // إذا كان فتح المود معطل، عرض رسالة فقط
                const message = '{{ "تم التحميل بنجاح!" if lang == "ar" else "Download completed successfully!" }}';
                showDownloadCompleteNotification();
                return;
            }

            // محاولة فتح الملف باستخدام Minecraft
            const minecraftUrl = `minecraft://import?url=${encodeURIComponent(modDownloadUrl)}`;

            try {
                // محاولة فتح Minecraft مباشرة
                window.location.href = minecraftUrl;

                // إظهار إشعار محاولة الفتح
                showOpeningNotification();

                // إظهار تعليمات بعد تأخير قصير
                setTimeout(() => {
                    showMinecraftInstructions();
                }, 2000);

            } catch (error) {
                console.error('Error opening Minecraft:', error);
                showMinecraftInstructions();
            }
        }

        function showDownloadStartNotification() {
            const notification = document.createElement('div');
            notification.className = 'fixed top-4 left-4 bg-blue-500 text-white p-3 rounded-lg shadow-lg z-50 notification-enter';
            notification.innerHTML = `
                <div class="flex items-center space-x-2">
                    <span class="text-lg">📥</span>
                    <span>{{ "بدء التحميل..." if lang == "ar" else "Download started..." }}</span>
                </div>
            `;

            document.body.appendChild(notification);

            // إخفاء الإشعار بعد 2 ثانية
            setTimeout(() => {
                if (document.body.contains(notification)) {
                    notification.classList.remove('notification-enter');
                    notification.classList.add('notification-exit');
                    setTimeout(() => {
                        if (document.body.contains(notification)) {
                            document.body.removeChild(notification);
                        }
                    }, 300);
                }
            }, 2000);
        }

        function showOpeningNotification() {
            const notification = document.createElement('div');
            notification.className = 'fixed top-4 left-4 bg-blue-500 text-white p-3 rounded-lg shadow-lg z-50 notification-enter';
            notification.innerHTML = `
                <div class="flex items-center space-x-2">
                    <div class="spinner"></div>
                    <span>{{ "جاري فتح ماين كرافت..." if lang == "ar" else "Opening Minecraft..." }}</span>
                </div>
            `;

            document.body.appendChild(notification);

            // إخفاء الإشعار بعد 3 ثوان
            setTimeout(() => {
                if (document.body.contains(notification)) {
                    notification.classList.remove('notification-enter');
                    notification.classList.add('notification-exit');
                    setTimeout(() => {
                        if (document.body.contains(notification)) {
                            document.body.removeChild(notification);
                        }
                    }, 300);
                }
            }, 3000);
        }

        function showDownloadCompleteNotification(stats = null) {
            // إضافة تأثير النجاح للزر
            const button = document.getElementById('download-button');
            button.classList.add('download-success-animation');

            // إنشاء إشعار النجاح
            const notification = document.createElement('div');
            notification.className = 'fixed top-4 right-4 bg-green-500 text-white p-4 rounded-lg shadow-lg z-70 notification-enter max-w-sm';
            const successText = '{{ "تم التحميل بنجاح!" if lang == "ar" else "Download Complete!" }}';

            let statsHtml = '';
            if (stats) {
                const timeText = stats.downloadTime < 60 ?
                    `${stats.downloadTime.toFixed(1)}{{ "ث" if lang == "ar" else "s" }}` :
                    `${(stats.downloadTime / 60).toFixed(1)}{{ "د" if lang == "ar" else "m" }}`;

                statsHtml = `
                    <div class="text-xs opacity-75 mt-2 space-y-1">
                        <div>{{ "الحجم:" if lang == "ar" else "Size:" }} ${stats.fileSize.toFixed(1)} MB</div>
                        <div>{{ "الوقت:" if lang == "ar" else "Time:" }} ${timeText}</div>
                        <div>{{ "السرعة:" if lang == "ar" else "Speed:" }} ${stats.averageSpeed} MB/s</div>
                    </div>
                `;
            }

            notification.innerHTML = `
                <div class="flex items-start space-x-2">
                    <span class="text-2xl">✅</span>
                    <div class="flex-1">
                        <h4 class="font-bold">${successText}</h4>
                        <p class="text-sm">${downloadedFileName}</p>
                        <p class="text-xs opacity-75 mt-1">{{ "انقر على الزر لفتح المود" if lang == "ar" else "Click button to open mod" }}</p>
                        ${statsHtml}
                    </div>
                </div>
            `;

            document.body.appendChild(notification);

            // إزالة تأثير النجاح من الزر بعد انتهاء الأنيميشن
            setTimeout(() => {
                button.classList.remove('download-success-animation');
            }, 600);

            // إخفاء الإشعار بعد 7 ثوان (أطول لعرض الإحصائيات)
            setTimeout(() => {
                notification.classList.remove('notification-enter');
                notification.classList.add('notification-exit');
                setTimeout(() => {
                    if (document.body.contains(notification)) {
                        document.body.removeChild(notification);
                    }
                }, 300);
            }, 7000);

            // إضافة زر إعادة التعيين
            setTimeout(() => {
                addResetButton();
            }, 1000);
        }

        function showMinecraftInstructions() {
            // إنشاء نافذة التعليمات
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-75 z-80 flex items-center justify-center';
            modal.innerHTML = `
                <div class="bg-white rounded-lg p-6 max-w-md mx-4 text-center">
                    <div class="text-6xl mb-4">🎮</div>
                    <h3 class="text-lg font-bold text-gray-800 mb-4">{{ 'تعليمات التثبيت' if lang == 'ar' else 'Installation Instructions' }}</h3>
                    <div class="text-sm text-gray-600 mb-6 text-right" dir="{{ 'rtl' if lang == 'ar' else 'ltr' }}">
                        {{ 'إذا لم يفتح ماين كرافت تلقائياً:' if lang == 'ar' else 'If Minecraft did not open automatically:' }}
                        <br><br>
                        {{ '1. افتح ماين كرافت' if lang == 'ar' else '1. Open Minecraft' }}
                        <br>
                        {{ '2. اذهب إلى الإعدادات' if lang == 'ar' else '2. Go to Settings' }}
                        <br>
                        {{ '3. اختر "التخزين"' if lang == 'ar' else '3. Select "Storage"' }}
                        <br>
                        {{ '4. اختر "استيراد"' if lang == 'ar' else '4. Select "Import"' }}
                        <br>
                        {{ '5. اختر الملف المحمل' if lang == 'ar' else '5. Select the downloaded file' }}
                    </div>
                    <div class="flex space-x-2 justify-center">
                        <button onclick="tryOpenMinecraftAgain()" class="bg-green-500 text-white px-4 py-2 rounded">
                            {{ 'فتح ماين كرافت' if lang == 'ar' else 'Open Minecraft' }}
                        </button>
                        <button onclick="closeInstructionsModal()" class="bg-gray-500 text-white px-4 py-2 rounded">
                            {{ 'إغلاق' if lang == 'ar' else 'Close' }}
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            window.currentInstructionsModal = modal;
        }

        function tryOpenMinecraftAgain() {
            const minecraftUrl = `minecraft://import?url=${encodeURIComponent(modDownloadUrl)}`;
            window.open(minecraftUrl, '_blank');
            closeInstructionsModal();
        }

        function closeInstructionsModal() {
            if (window.currentInstructionsModal) {
                document.body.removeChild(window.currentInstructionsModal);
                window.currentInstructionsModal = null;
            }
        }

        // عرض الإعلان حسب الوضع المحدد
        function checkAdDisplay() {
            if (adsSettings && adsSettings.ads_enabled && !adShown) {
                const displayMode = adsSettings.ad_display_mode;
                const delay = adsSettings.ad_delay_seconds || 3;

                if (displayMode === 'after_page_load') {
                    setTimeout(() => {
                        showAd();
                    }, delay * 1000);
                } else if (displayMode === 'after_delay') {
                    setTimeout(() => {
                        showAd();
                    }, delay * 1000);
                }
            }
        }

        // دوال إضافية لتحسين تجربة المستخدم
        function saveDownloadState() {
            const state = {
                isDownloaded: isDownloaded,
                downloadedFileName: downloadedFileName,
                timestamp: Date.now()
            };
            localStorage.setItem(`mod_download_${modId}`, JSON.stringify(state));
        }

        function loadDownloadState() {
            try {
                const saved = localStorage.getItem(`mod_download_${modId}`);
                if (saved) {
                    const state = JSON.parse(saved);
                    // التحقق من أن الحالة المحفوظة ليست قديمة جداً (24 ساعة)
                    const hoursPassed = (Date.now() - state.timestamp) / (1000 * 60 * 60);
                    if (hoursPassed < 24) {
                        isDownloaded = state.isDownloaded;
                        downloadedFileName = state.downloadedFileName;
                        if (isDownloaded) {
                            updateDownloadButton();
                        }
                    }
                }
            } catch (error) {
                console.error('Error loading download state:', error);
            }
        }

        function resetDownloadState() {
            isDownloading = false;
            isDownloaded = false;
            downloadProgress = 0;
            downloadedFileName = '';
            localStorage.removeItem(`mod_download_${modId}`);
            updateDownloadButton();
        }

        // تحديث دالة completeDownload لحفظ الحالة
        function completeDownload() {
            isDownloading = false;
            isDownloaded = true;
            downloadProgress = 100;

            // تحديث اسم الملف المحمل إذا لم يكن محدداً
            if (!downloadedFileName) {
                downloadedFileName = `${modTitle.replace(/[^a-zA-Z0-9]/g, '_')}${modFileExtension}`;
            }

            updateDownloadButton();
            saveDownloadState();

            // إظهار إشعار النجاح
            showDownloadCompleteNotification();
        }

        // إضافة زر إعادة تعيين (مخفي افتراضياً)
        function addResetButton() {
            if (isDownloaded && !document.getElementById('reset-download-btn')) {
                const resetBtn = document.createElement('button');
                resetBtn.id = 'reset-download-btn';
                resetBtn.className = 'absolute top-2 right-2 bg-red-500 text-white text-xs px-2 py-1 rounded opacity-50 hover:opacity-100';
                resetBtn.innerHTML = '🔄';
                resetBtn.title = '{{ "إعادة تعيين حالة التحميل" if lang == "ar" else "Reset download state" }}';
                resetBtn.onclick = (e) => {
                    e.stopPropagation();
                    resetDownloadState();
                };

                const downloadButton = document.getElementById('download-button');
                downloadButton.appendChild(resetBtn);
            }
        }

        // Initialize thumbnails immediately when DOM is ready
        function initializeThumbnails() {
            console.log('Initializing thumbnails...');
            console.log('imageUrls available:', imageUrls);
            console.log('imageUrls length:', imageUrls ? imageUrls.length : 0);

            if (imageUrls && imageUrls.length > 0) {
                // Set the main image
                if (mainModImage) {
                    mainModImage.src = imageUrls[0];
                    console.log('Main image set to:', imageUrls[0]);
                }

                // Create thumbnails immediately
                updateThumbnails();

                // Show navigation buttons if more than one image
                if (imageUrls.length > 1) {
                    if (prevButton) prevButton.style.display = 'block';
                    if (nextButton) nextButton.style.display = 'block';
                } else {
                    if (prevButton) prevButton.style.display = 'none';
                    if (nextButton) nextButton.style.display = 'none';
                }
            } else {
                console.log('No images available, hiding navigation');
                if (prevButton) prevButton.style.display = 'none';
                if (nextButton) nextButton.style.display = 'none';
                if (thumbnailContainer) thumbnailContainer.style.display = 'none';
            }
        }

        // Initial load
        document.addEventListener('DOMContentLoaded', () => {
            console.log('DOM Content Loaded');

            // Initialize thumbnails immediately
            initializeThumbnails();

            // Initialize other features
            checkAdDisplay();
            loadDownloadState();

            // إضافة زر إعادة التعيين إذا كان المود محملاً
            if (isDownloaded) {
                setTimeout(addResetButton, 1000);
            }
        });
    </script>
</body>
</html>
