# إعدادات نظام البحث الذكي المتقدم
from dataclasses import dataclass
from typing import Dict, List, Any, Optional
from enum import Enum

class ConfigLevel(Enum):
    """مستويات الإعدادات"""
    BASIC = "basic"
    ADVANCED = "advanced"
    EXPERT = "expert"
    CUSTOM = "custom"

class PerformanceMode(Enum):
    """أنماط الأداء"""
    SPEED_OPTIMIZED = "speed_optimized"      # محسن للسرعة
    QUALITY_OPTIMIZED = "quality_optimized"  # محسن للجودة
    BALANCED = "balanced"                    # متوازن
    RESOURCE_SAVING = "resource_saving"      # توفير الموارد

@dataclass
class IntelligentSearchConfig:
    """إعدادات شاملة لنظام البحث الذكي"""
    
    # إعدادات عامة
    config_level: ConfigLevel = ConfigLevel.BALANCED
    performance_mode: PerformanceMode = PerformanceMode.BALANCED
    enable_logging: bool = True
    log_level: str = "INFO"
    
    # إعدادات البحث الأساسية
    default_max_results: int = 10
    default_timeout: float = 30.0
    max_concurrent_searches: int = 3
    enable_caching: bool = True
    cache_ttl: int = 3600  # ثانية
    
    # إعدادات التعلم التكيفي
    enable_learning: bool = True
    learning_rate: float = 0.1
    exploration_rate: float = 0.2
    confidence_threshold: float = 0.7
    pattern_discovery_threshold: int = 5
    max_learning_events: int = 1000
    
    # إعدادات التحليل السياقي
    enable_context_analysis: bool = True
    enable_entity_extraction: bool = True
    enable_intent_detection: bool = True
    enable_sentiment_analysis: bool = True
    context_analysis_threshold: float = 0.7
    
    # إعدادات البحث الدلالي
    enable_semantic_search: bool = True
    semantic_similarity_threshold: float = 0.6
    concept_expansion_limit: int = 10
    semantic_confidence_threshold: float = 0.5
    enable_concept_learning: bool = True
    
    # إعدادات التنسيق بين المحركات
    enable_engine_coordination: bool = True
    max_parallel_engines: int = 3
    engine_timeout: float = 20.0
    enable_load_balancing: bool = True
    enable_fallback: bool = True
    
    # إعدادات التقييم الذكي
    enable_intelligent_evaluation: bool = True
    enable_ai_evaluation: bool = True
    quality_threshold: float = 0.6
    relevance_threshold: float = 0.7
    authority_boost: float = 0.1
    
    # أوزان معايير التقييم
    evaluation_weights: Dict[str, float] = None
    
    # إعدادات الذكاء الاصطناعي
    enable_ai_enhancement: bool = True
    ai_timeout: float = 10.0
    ai_fallback_enabled: bool = True
    ai_confidence_threshold: float = 0.6
    
    # إعدادات المحركات
    engine_preferences: Dict[str, Dict[str, Any]] = None
    engine_limits: Dict[str, int] = None
    
    # إعدادات الأمان والحدود
    rate_limit_per_minute: int = 60
    max_query_length: int = 500
    enable_query_sanitization: bool = True
    blocked_terms: List[str] = None
    
    # إعدادات المراقبة والإحصائيات
    enable_performance_monitoring: bool = True
    enable_detailed_stats: bool = True
    stats_retention_days: int = 30
    enable_health_checks: bool = True
    
    def __post_init__(self):
        """تهيئة الإعدادات الافتراضية"""
        if self.evaluation_weights is None:
            self.evaluation_weights = {
                'relevance': 0.25,
                'quality': 0.20,
                'freshness': 0.15,
                'authority': 0.15,
                'completeness': 0.10,
                'uniqueness': 0.05,
                'engagement': 0.05,
                'semantic_match': 0.05
            }
        
        if self.engine_preferences is None:
            self.engine_preferences = {
                'news': {'preferred': ['tavily', 'serpapi'], 'weight': 1.0},
                'reviews': {'preferred': ['serpapi', 'enhanced'], 'weight': 0.9},
                'guides': {'preferred': ['enhanced', 'smart'], 'weight': 0.8}
            }
        
        if self.engine_limits is None:
            self.engine_limits = {
                'tavily': 35,      # حد يومي
                'serpapi': 100,    # حد يومي
                'enhanced': 1000,  # حد يومي
                'smart': 500       # حد يومي
            }
        
        if self.blocked_terms is None:
            self.blocked_terms = []
        
        # تطبيق إعدادات نمط الأداء
        self._apply_performance_mode()
    
    def _apply_performance_mode(self):
        """تطبيق إعدادات نمط الأداء"""
        if self.performance_mode == PerformanceMode.SPEED_OPTIMIZED:
            self.default_timeout = 15.0
            self.engine_timeout = 10.0
            self.max_parallel_engines = 2
            self.enable_ai_enhancement = False
            self.semantic_similarity_threshold = 0.7
            
        elif self.performance_mode == PerformanceMode.QUALITY_OPTIMIZED:
            self.default_timeout = 60.0
            self.engine_timeout = 30.0
            self.max_parallel_engines = 4
            self.enable_ai_enhancement = True
            self.semantic_similarity_threshold = 0.5
            self.concept_expansion_limit = 15
            
        elif self.performance_mode == PerformanceMode.RESOURCE_SAVING:
            self.default_timeout = 20.0
            self.max_concurrent_searches = 2
            self.max_parallel_engines = 2
            self.enable_ai_enhancement = False
            self.enable_detailed_stats = False
            self.cache_ttl = 7200  # تخزين مؤقت أطول
            
        # النمط المتوازن يستخدم الإعدادات الافتراضية

class ConfigManager:
    """مدير الإعدادات"""
    
    def __init__(self):
        self.current_config = IntelligentSearchConfig()
        self.config_history = []
        self.custom_configs = {}
    
    def get_config(self) -> IntelligentSearchConfig:
        """الحصول على الإعدادات الحالية"""
        return self.current_config
    
    def update_config(self, **kwargs) -> bool:
        """تحديث الإعدادات"""
        try:
            # حفظ الإعدادات الحالية في التاريخ
            self.config_history.append(self.current_config)
            
            # تطبيق التحديثات
            for key, value in kwargs.items():
                if hasattr(self.current_config, key):
                    setattr(self.current_config, key, value)
                else:
                    raise ValueError(f"إعداد غير معروف: {key}")
            
            # إعادة تطبيق نمط الأداء
            self.current_config._apply_performance_mode()
            
            return True
            
        except Exception as e:
            # استرجاع الإعدادات السابقة في حالة الخطأ
            if self.config_history:
                self.current_config = self.config_history.pop()
            raise e
    
    def set_performance_mode(self, mode: PerformanceMode) -> bool:
        """تغيير نمط الأداء"""
        return self.update_config(performance_mode=mode)
    
    def save_custom_config(self, name: str, config: IntelligentSearchConfig) -> bool:
        """حفظ إعدادات مخصصة"""
        try:
            self.custom_configs[name] = config
            return True
        except Exception:
            return False
    
    def load_custom_config(self, name: str) -> bool:
        """تحميل إعدادات مخصصة"""
        if name in self.custom_configs:
            self.current_config = self.custom_configs[name]
            return True
        return False
    
    def get_preset_configs(self) -> Dict[str, IntelligentSearchConfig]:
        """الحصول على إعدادات جاهزة"""
        presets = {}
        
        # إعدادات سريعة
        speed_config = IntelligentSearchConfig(
            performance_mode=PerformanceMode.SPEED_OPTIMIZED,
            default_timeout=10.0,
            enable_ai_enhancement=False,
            max_parallel_engines=2
        )
        presets['speed'] = speed_config
        
        # إعدادات جودة عالية
        quality_config = IntelligentSearchConfig(
            performance_mode=PerformanceMode.QUALITY_OPTIMIZED,
            default_timeout=45.0,
            enable_ai_enhancement=True,
            max_parallel_engines=4,
            concept_expansion_limit=15
        )
        presets['quality'] = quality_config
        
        # إعدادات توفير الموارد
        saving_config = IntelligentSearchConfig(
            performance_mode=PerformanceMode.RESOURCE_SAVING,
            max_concurrent_searches=1,
            enable_ai_enhancement=False,
            enable_detailed_stats=False
        )
        presets['saving'] = saving_config
        
        # إعدادات متوازنة
        balanced_config = IntelligentSearchConfig()
        presets['balanced'] = balanced_config
        
        return presets
    
    def optimize_for_usage_pattern(self, usage_stats: Dict[str, Any]) -> bool:
        """تحسين الإعدادات بناءً على نمط الاستخدام"""
        try:
            avg_response_time = usage_stats.get('avg_response_time', 0)
            success_rate = usage_stats.get('success_rate', 0)
            resource_usage = usage_stats.get('resource_usage', 0)
            
            # تحسين بناءً على الأداء
            if avg_response_time > 20.0:
                # الأداء بطيء - تحسين للسرعة
                self.update_config(
                    default_timeout=max(15.0, self.current_config.default_timeout * 0.8),
                    max_parallel_engines=max(2, self.current_config.max_parallel_engines - 1),
                    enable_ai_enhancement=False
                )
            
            elif avg_response_time < 5.0 and success_rate > 0.9:
                # الأداء ممتاز - يمكن تحسين الجودة
                self.update_config(
                    enable_ai_enhancement=True,
                    concept_expansion_limit=min(15, self.current_config.concept_expansion_limit + 2),
                    max_parallel_engines=min(4, self.current_config.max_parallel_engines + 1)
                )
            
            # تحسين بناءً على معدل النجاح
            if success_rate < 0.7:
                # معدل نجاح منخفض - تحسين الجودة
                self.update_config(
                    quality_threshold=max(0.4, self.current_config.quality_threshold - 0.1),
                    relevance_threshold=max(0.5, self.current_config.relevance_threshold - 0.1),
                    enable_semantic_search=True
                )
            
            # تحسين بناءً على استخدام الموارد
            if resource_usage > 0.8:
                # استخدام عالي للموارد - تقليل الاستهلاك
                self.update_config(
                    max_concurrent_searches=max(1, self.current_config.max_concurrent_searches - 1),
                    cache_ttl=min(7200, self.current_config.cache_ttl * 2),
                    enable_detailed_stats=False
                )
            
            return True
            
        except Exception as e:
            print(f"فشل في تحسين الإعدادات: {e}")
            return False
    
    def validate_config(self, config: IntelligentSearchConfig = None) -> List[str]:
        """التحقق من صحة الإعدادات"""
        if config is None:
            config = self.current_config
        
        issues = []
        
        # فحص القيم الأساسية
        if config.default_timeout <= 0:
            issues.append("timeout يجب أن يكون أكبر من 0")
        
        if config.default_max_results <= 0:
            issues.append("max_results يجب أن يكون أكبر من 0")
        
        if not (0 <= config.learning_rate <= 1):
            issues.append("learning_rate يجب أن يكون بين 0 و 1")
        
        if not (0 <= config.exploration_rate <= 1):
            issues.append("exploration_rate يجب أن يكون بين 0 و 1")
        
        # فحص أوزان التقييم
        if config.evaluation_weights:
            total_weight = sum(config.evaluation_weights.values())
            if abs(total_weight - 1.0) > 0.01:
                issues.append(f"مجموع أوزان التقييم يجب أن يكون 1.0 (حالياً: {total_weight:.2f})")
        
        # فحص حدود المحركات
        if config.engine_limits:
            for engine, limit in config.engine_limits.items():
                if limit <= 0:
                    issues.append(f"حد المحرك {engine} يجب أن يكون أكبر من 0")
        
        return issues
    
    def export_config(self, format: str = "json") -> str:
        """تصدير الإعدادات"""
        if format == "json":
            import json
            from dataclasses import asdict
            return json.dumps(asdict(self.current_config), indent=2, ensure_ascii=False)
        else:
            raise ValueError(f"تنسيق غير مدعوم: {format}")
    
    def import_config(self, config_data: str, format: str = "json") -> bool:
        """استيراد الإعدادات"""
        try:
            if format == "json":
                import json
                data = json.loads(config_data)
                
                # التحقق من صحة البيانات
                new_config = IntelligentSearchConfig(**data)
                issues = self.validate_config(new_config)
                
                if issues:
                    raise ValueError(f"إعدادات غير صحيحة: {', '.join(issues)}")
                
                self.current_config = new_config
                return True
            else:
                raise ValueError(f"تنسيق غير مدعوم: {format}")
                
        except Exception as e:
            print(f"فشل في استيراد الإعدادات: {e}")
            return False

# إنشاء مدير الإعدادات العام
config_manager = ConfigManager()

# دوال مساعدة سريعة
def get_config() -> IntelligentSearchConfig:
    """الحصول على الإعدادات الحالية"""
    return config_manager.get_config()

def set_performance_mode(mode: PerformanceMode) -> bool:
    """تغيير نمط الأداء"""
    return config_manager.set_performance_mode(mode)

def update_config(**kwargs) -> bool:
    """تحديث الإعدادات"""
    return config_manager.update_config(**kwargs)

def optimize_config(usage_stats: Dict[str, Any]) -> bool:
    """تحسين الإعدادات تلقائياً"""
    return config_manager.optimize_for_usage_pattern(usage_stats)
