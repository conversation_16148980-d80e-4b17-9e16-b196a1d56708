#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إصلاح شامل لجميع مشاكل قاعدة البيانات
"""

import sqlite3
import os
import sys
from datetime import datetime

# إضافة المسار الحالي لـ Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def fix_all_database_issues():
    """إصلاح شامل لجميع مشاكل قاعدة البيانات"""
    db_path = "data/articles.db"
    
    # إنشاء مجلد البيانات إذا لم يكن موجوداً
    os.makedirs(os.path.dirname(db_path), exist_ok=True)
    
    print("🔧 بدء الإصلاح الشامل لقاعدة البيانات...")
    
    try:
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            # فحص الجداول الموجودة
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            existing_tables = [row[0] for row in cursor.fetchall()]
            print(f"📋 الجداول الموجودة: {existing_tables}")
            
            # إنشاء جدول المقالات المنشورة
            print("📝 إنشاء جدول published_articles...")
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS published_articles (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    title TEXT NOT NULL,
                    content TEXT,
                    content_hash TEXT UNIQUE NOT NULL,
                    semantic_hash TEXT NOT NULL,
                    source_url TEXT,
                    source_type TEXT,
                    blogger_url TEXT,
                    telegram_message_id INTEGER,
                    keywords TEXT,
                    category TEXT,
                    dialect TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    published_at TIMESTAMP,
                    view_count INTEGER DEFAULT 0,
                    engagement_score REAL DEFAULT 0.0
                )
            ''')
            
            # إضافة عمود content إذا لم يكن موجوداً
            try:
                cursor.execute('ALTER TABLE published_articles ADD COLUMN content TEXT')
                print("✅ تم إضافة عمود content")
            except sqlite3.OperationalError:
                print("ℹ️ عمود content موجود بالفعل")
            
            # إنشاء جدول المصادر المراقبة
            print("📝 إنشاء جدول monitored_sources...")
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS monitored_sources (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    source_url TEXT UNIQUE NOT NULL,
                    source_type TEXT NOT NULL,
                    last_checked TIMESTAMP,
                    success_count INTEGER DEFAULT 0,
                    error_count INTEGER DEFAULT 0,
                    is_active BOOLEAN DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # إنشاء جدول سجل الأخطاء
            print("📝 إنشاء جدول error_log...")
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS error_log (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    error_type TEXT NOT NULL,
                    error_message TEXT,
                    stack_trace TEXT,
                    source_url TEXT,
                    retry_count INTEGER DEFAULT 0,
                    resolved BOOLEAN DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # إنشاء جدول إحصائيات الأداء
            print("📝 إنشاء جدول performance_stats...")
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS performance_stats (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    date DATE UNIQUE NOT NULL,
                    articles_processed INTEGER DEFAULT 0,
                    articles_published INTEGER DEFAULT 0,
                    api_calls_gemini INTEGER DEFAULT 0,
                    api_calls_telegram INTEGER DEFAULT 0,
                    api_calls_blogger INTEGER DEFAULT 0,
                    errors_count INTEGER DEFAULT 0,
                    uptime_hours REAL DEFAULT 0.0
                )
            ''')
            
            # إنشاء جدول تحليل المحتوى
            print("📝 إنشاء جدول content_analytics...")
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS content_analytics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    article_id INTEGER,
                    content_type TEXT,
                    quality_score INTEGER DEFAULT 0,
                    seo_score INTEGER DEFAULT 0,
                    word_count INTEGER DEFAULT 0,
                    keyword_count INTEGER DEFAULT 0,
                    readability_score REAL DEFAULT 0.0,
                    engagement_prediction REAL DEFAULT 0.0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (article_id) REFERENCES published_articles (id)
                )
            ''')
            
            # إنشاء جدول Whisper Quality Logs
            print("📝 إنشاء جدول whisper_quality_logs...")
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS whisper_quality_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    video_id TEXT NOT NULL,
                    video_title TEXT,
                    video_duration REAL,
                    transcription_method TEXT,
                    transcription_quality REAL DEFAULT 0.0,
                    word_count INTEGER DEFAULT 0,
                    confidence_score REAL DEFAULT 0.0,
                    processing_time REAL DEFAULT 0.0,
                    error_message TEXT,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    success BOOLEAN DEFAULT 1
                )
            ''')
            
            # إنشاء جدول مقاييس الوكيل
            print("📝 إنشاء جدول agent_metrics...")
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS agent_metrics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    articles_published_today INTEGER,
                    articles_published_hour INTEGER,
                    avg_processing_time REAL,
                    success_rate REAL,
                    error_count INTEGER,
                    api_calls_count INTEGER,
                    cache_hit_rate REAL
                )
            ''')
            
            # إنشاء جدول التنبيهات
            print("📝 إنشاء جدول monitoring_alerts...")
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS monitoring_alerts (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    alert_type TEXT,
                    severity TEXT,
                    message TEXT,
                    metric_value REAL,
                    threshold_value REAL,
                    resolved BOOLEAN DEFAULT FALSE,
                    resolved_at TIMESTAMP
                )
            ''')
            
            # إنشاء الفهارس المصححة (بدون content_hashes المفقود)
            print("📝 إنشاء الفهارس المصححة...")
            indexes = [
                "CREATE INDEX IF NOT EXISTS idx_published_articles_hash ON published_articles(content_hash)",
                "CREATE INDEX IF NOT EXISTS idx_published_articles_semantic ON published_articles(semantic_hash)",
                "CREATE INDEX IF NOT EXISTS idx_published_articles_date ON published_articles(published_at)",
                "CREATE INDEX IF NOT EXISTS idx_published_articles_category ON published_articles(category)",
                "CREATE INDEX IF NOT EXISTS idx_published_articles_engagement ON published_articles(engagement_score)",
                "CREATE INDEX IF NOT EXISTS idx_error_log_date ON error_log(created_at)",
                "CREATE INDEX IF NOT EXISTS idx_performance_stats_date ON performance_stats(date)",
                "CREATE INDEX IF NOT EXISTS idx_whisper_quality_video_id ON whisper_quality_logs(video_id)",
                "CREATE INDEX IF NOT EXISTS idx_whisper_quality_timestamp ON whisper_quality_logs(timestamp)"
            ]
            
            for index_sql in indexes:
                try:
                    cursor.execute(index_sql)
                    index_name = index_sql.split('idx_')[1].split(' ')[0]
                    print(f"✅ تم إنشاء فهرس: {index_name}")
                except Exception as e:
                    print(f"⚠️ تحذير في إنشاء فهرس: {e}")
            
            conn.commit()
            
            # فحص الجداول بعد الإنشاء
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            final_tables = [row[0] for row in cursor.fetchall()]
            print(f"✅ الجداول النهائية: {final_tables}")
            
            print("✅ تم إصلاح جميع مشاكل قاعدة البيانات بنجاح")
            return True
            
    except Exception as e:
        print(f"❌ خطأ في إصلاح قاعدة البيانات: {e}")
        return False

def fix_performance_optimizer():
    """إصلاح مشكلة الفهارس في performance_optimizer"""
    print("🔧 إصلاح مشكلة الفهارس في performance_optimizer...")
    
    # قراءة الملف الحالي
    optimizer_path = "modules/performance_optimizer.py"
    
    if not os.path.exists(optimizer_path):
        print(f"⚠️ الملف غير موجود: {optimizer_path}")
        return False
    
    try:
        with open(optimizer_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # استبدال الفهرس المشكل
        old_index = '"CREATE INDEX IF NOT EXISTS idx_content_hashes_hash ON content_hashes(content_hash)",'
        new_index = '"CREATE INDEX IF NOT EXISTS idx_published_articles_content_hash ON published_articles(content_hash)",'
        
        if old_index in content:
            content = content.replace(old_index, new_index)
            
            with open(optimizer_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print("✅ تم إصلاح مشكلة الفهارس في performance_optimizer")
            return True
        else:
            print("ℹ️ لم يتم العثور على الفهرس المشكل")
            return True
            
    except Exception as e:
        print(f"❌ خطأ في إصلاح performance_optimizer: {e}")
        return False

if __name__ == "__main__":
    print("🚀 بدء الإصلاح الشامل...")
    
    # إصلاح قاعدة البيانات
    db_success = fix_all_database_issues()
    
    # إصلاح performance_optimizer
    optimizer_success = fix_performance_optimizer()
    
    if db_success and optimizer_success:
        print("\n✅ تم إكمال جميع الإصلاحات بنجاح!")
        print("🔄 يمكنك الآن تشغيل البرنامج مرة أخرى")
    else:
        print("\n❌ فشل في بعض الإصلاحات")
        print("🔍 يرجى مراجعة الأخطاء أعلاه")
