/*
 * ملف التصميم لصفحة عرض المودات - محسن للهواتف
 * CSS Styles for Mod Details Page - Mobile Optimized
 * Cloudflare Pages Version - READY FOR DEPLOYMENT
 */

:root {
    --bg-color: #1a1a1a;
    --header-color: #FFA500;
    --text-color: #ffffff;
    --button-color: #FFA500;
    --border-color: #333333;
    --accent-color: #FFD700;
    --card-color: #2D2D2D;
    --shadow-color: rgba(0,0,0,0.3);
    --font-family: 'Press Start 2P', monospace;
    --border-radius: 8px;
}

body {
    font-family: var(--font-family);
    background-color: var(--bg-color);
    color: var(--text-color);
    margin: 0;
    padding: 0;
    overflow-x: hidden;
    line-height: 1.6;
}

/* Header */
header {
    background: linear-gradient(135deg, #FFA500 0%, #FF8C00 100%);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    position: relative;
    padding: 1rem;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1200px;
    margin: 0 auto;
}

header h1 {
    margin: 0;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    letter-spacing: 1px;
    text-align: center;
    font-size: 1.5rem;
    flex: 1;
}

.logo-container {
    width: 60px;
    height: 60px;
}

.channel-logo {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.channel-logo:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
}

.spacer {
    width: 60px;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--bg-color);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-content {
    text-align: center;
    color: var(--text-color);
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255, 165, 0, 0.3);
    border-top: 4px solid var(--button-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Error Screen */
.error-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--bg-color);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9998;
}

.error-content {
    text-align: center;
    color: var(--text-color);
    padding: 40px;
    background: var(--card-color);
    border-radius: var(--border-radius);
    box-shadow: 0 10px 30px var(--shadow-color);
}

.error-icon {
    font-size: 4rem;
    margin-bottom: 20px;
}

/* Preview Notice */
.preview-notice {
    background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
    color: white;
    padding: 15px;
    text-align: center;
    margin-bottom: 20px;
    border-radius: var(--border-radius);
    box-shadow: 0 4px 12px rgba(33, 150, 243, 0.3);
}

.preview-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.preview-icon {
    font-size: 1.5rem;
}

/* Mod Info Card */
.mod-info-card {
    background: var(--card-color);
    padding: 30px;
    border-radius: var(--border-radius);
    margin-bottom: 30px;
    box-shadow: 0 8px 25px var(--shadow-color);
    border: 1px solid var(--border-color);
}

.mod-title {
    font-size: 2rem;
    margin: 0 0 20px 0;
    color: var(--accent-color);
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    text-align: center;
}

.mod-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background: rgba(255, 165, 0, 0.1);
    border-radius: 5px;
    border-left: 4px solid var(--button-color);
}

.detail-label {
    font-weight: bold;
    color: var(--accent-color);
}

.detail-value {
    color: var(--text-color);
}

/* Image Gallery */
.image-gallery {
    margin-bottom: 30px;
}

.main-image-container {
    position: relative;
    margin-bottom: 20px;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: 0 10px 30px var(--shadow-color);
}

.main-mod-image {
    width: 100%;
    height: auto;
    max-height: 500px;
    object-fit: cover;
    display: block;
    transition: all 0.3s ease;
}

.main-mod-image.fade-in {
    opacity: 0.7;
}

/* Navigation Buttons */
.nav-button {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(0, 0, 0, 0.7);
    color: white;
    border: none;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    font-size: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

.nav-button:hover {
    background: rgba(0, 0, 0, 0.9);
    transform: translateY(-50%) scale(1.1);
}

.prev-button {
    left: 15px;
}

.next-button {
    right: 15px;
}

/* Thumbnails */
.thumbnail-container {
    display: flex;
    gap: 10px;
    overflow-x: auto;
    padding: 10px 0;
    scrollbar-width: thin;
    scrollbar-color: var(--button-color) var(--card-color);
}

.thumbnail {
    width: 100px;
    height: 60px;
    object-fit: cover;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    flex-shrink: 0;
}

.thumbnail:hover {
    transform: scale(1.05);
    border-color: var(--button-color);
}

.thumbnail.active {
    border-color: var(--accent-color);
    box-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
}

/* Description Card */
.mod-description-card {
    background: var(--card-color);
    padding: 30px;
    border-radius: var(--border-radius);
    margin-bottom: 30px;
    box-shadow: 0 8px 25px var(--shadow-color);
    border: 1px solid var(--border-color);
}

.mod-description-card h2 {
    color: var(--accent-color);
    margin-top: 0;
    margin-bottom: 20px;
    font-size: 1.5rem;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.mod-description-card p {
    line-height: 1.8;
    font-size: 1rem;
    color: #e0e0e0;
}

/* Download Section */
.download-section {
    text-align: center;
    margin-bottom: 30px;
}

.download-button {
    background: linear-gradient(135deg, var(--button-color) 0%, #FF8C00 100%);
    color: white;
    border: none;
    padding: 20px 40px;
    border-radius: var(--border-radius);
    font-size: 1.2rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 8px 25px rgba(255, 165, 0, 0.3);
    position: relative;
    overflow: hidden;
    min-width: 250px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.download-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(255, 165, 0, 0.4);
}

.download-button:active {
    transform: translateY(-1px);
}

.download-button.downloading {
    background: linear-gradient(135deg, #666 0%, #555 100%);
    cursor: not-allowed;
}

.download-button.downloaded {
    background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
}

.download-icon {
    font-size: 1.2rem;
}

.download-text {
    font-family: 'Roboto', sans-serif;
}

.progress-bar {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 4px;
    background: var(--accent-color);
    width: 0%;
    transition: width 0.3s ease;
}

/* Ad Overlay */
.ad-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
}

.ad-content {
    background: var(--card-color);
    padding: 40px;
    border-radius: var(--border-radius);
    text-align: center;
    max-width: 400px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
}

.ad-header h3 {
    color: var(--accent-color);
    margin-top: 0;
}

.close-ad-btn {
    background: var(--button-color);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 1rem;
    margin-top: 20px;
    transition: all 0.3s ease;
}

.close-ad-btn:hover {
    background: #FF8C00;
    transform: translateY(-2px);
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* تحسينات للأخطاء وإعادة المحاولة */
.retry-button {
    background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    cursor: pointer;
    font-family: inherit;
    font-size: 14px;
    margin-top: 15px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.retry-button:hover {
    background: linear-gradient(135deg, #45a049 0%, #4CAF50 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0,0,0,0.3);
}

.retry-button:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

/* تحسينات للإشعارات */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 8px;
    color: white;
    font-weight: bold;
    z-index: 10000;
    max-width: 300px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    transition: all 0.3s ease;
    font-family: 'Roboto', sans-serif;
    font-size: 14px;
}

.notification.success {
    background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
}

.notification.error {
    background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
}

.notification.warning {
    background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
}

.notification.info {
    background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
}

.notification-enter {
    opacity: 0;
    transform: translateX(100%);
    animation: slideIn 0.3s ease forwards;
}

.notification-exit {
    animation: slideOut 0.3s ease forwards;
}

@keyframes slideIn {
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideOut {
    to {
        opacity: 0;
        transform: translateX(100%);
    }
}

/* تحسينات للتحميل */
.spinner {
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top: 3px solid var(--button-color, #FFA500);
    border-radius: 50%;
    width: 20px;
    height: 20px;
    animation: spin 1s linear infinite;
    display: inline-block;
}

/* تحسينات للهواتف */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }

    .mod-title {
        font-size: 1.5rem;
    }

    .mod-info-card,
    .mod-description-card {
        padding: 20px;
    }

    .mod-details {
        grid-template-columns: 1fr;
    }

    .download-button {
        width: 100%;
        padding: 18px;
        font-size: 1.1rem;
    }

    .nav-button {
        width: 40px;
        height: 40px;
        font-size: 1.2rem;
    }

    .prev-button {
        left: 10px;
    }

    .next-button {
        right: 10px;
    }

    .notification {
        right: 10px;
        left: 10px;
        max-width: none;
        font-size: 13px;
    }

    .retry-button {
        width: 100%;
        padding: 15px;
        font-size: 16px;
    }

    .header-content {
        padding: 0 10px;
    }

    header h1 {
        font-size: 1.2rem;
    }

    .logo-container {
        width: 50px;
        height: 50px;
    }

    .spacer {
        width: 50px;
    }
}

/* تحسينات للشاشات الصغيرة جداً */
@media (max-width: 480px) {
    .mod-title {
        font-size: 1.2rem;
    }

    .mod-info-card,
    .mod-description-card {
        padding: 15px;
    }

    .download-button {
        min-width: auto;
        padding: 15px 20px;
    }

    .thumbnail {
        width: 80px;
        height: 50px;
    }

    header h1 {
        font-size: 1rem;
    }
}

/* تحسينات للوضع المظلم */
@media (prefers-color-scheme: dark) {
    :root {
        --bg-color: #0a0a0a;
        --card-color: #1a1a1a;
        --border-color: #404040;
    }
}

/* تحسينات للحركة المخفضة */
@media (prefers-reduced-motion: reduce) {
    .notification,
    .retry-button,
    .spinner,
    .main-mod-image,
    .thumbnail,
    .download-button,
    .nav-button {
        animation: none;
        transition: none;
    }

    .notification-enter,
    .notification-exit {
        animation: none;
    }

    .fade-in {
        animation: none;
    }
}

/* تحسينات للطباعة */
@media print {
    .nav-button,
    .download-button,
    .ad-overlay,
    .loading-screen,
    .error-screen {
        display: none !important;
    }

    body {
        background: white;
        color: black;
    }

    .mod-info-card,
    .mod-description-card {
        background: white;
        border: 1px solid #ccc;
        box-shadow: none;
    }
}
