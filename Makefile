# Makefile for GitHub Uploader Tool
# أداة رفع المشاريع على GitHub

.PHONY: help install test clean run examples lint format check-deps setup dev-install

# متغيرات
PYTHON := python
PIP := pip
VENV := venv
VENV_BIN := $(VENV)/bin
VENV_PYTHON := $(VENV_BIN)/python
VENV_PIP := $(VENV_BIN)/pip

# الهدف الافتراضي
help: ## عرض هذه الرسالة
	@echo "🚀 GitHub Uploader Tool - أداة رفع المشاريع على GitHub"
	@echo "=================================================="
	@echo "الأهداف المتاحة:"
	@echo ""
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "  \033[36m%-15s\033[0m %s\n", $$1, $$2}'
	@echo ""
	@echo "أمثلة الاستخدام:"
	@echo "  make setup     # إعداد البيئة التطويرية"
	@echo "  make test      # تشغيل الاختبارات"
	@echo "  make run       # تشغيل الأداة"
	@echo "  make clean     # تنظيف الملفات المؤقتة"

setup: ## إعداد البيئة التطويرية
	@echo "🔧 إعداد البيئة التطويرية..."
	$(PYTHON) -m venv $(VENV)
	$(VENV_PIP) install --upgrade pip
	$(VENV_PIP) install -r requirements.txt
	@echo "✅ تم إعداد البيئة بنجاح!"
	@echo "لتفعيل البيئة الافتراضية:"
	@echo "  source $(VENV_BIN)/activate  # Linux/Mac"
	@echo "  $(VENV)\\Scripts\\activate     # Windows"

install: ## تثبيت المتطلبات
	@echo "📦 تثبيت المتطلبات..."
	$(PIP) install -r requirements.txt
	@echo "✅ تم تثبيت المتطلبات بنجاح!"

dev-install: ## تثبيت متطلبات التطوير
	@echo "🛠️ تثبيت متطلبات التطوير..."
	$(PIP) install -r requirements.txt
	$(PIP) install pytest coverage flake8 black isort
	@echo "✅ تم تثبيت متطلبات التطوير!"

test: ## تشغيل الاختبارات
	@echo "🧪 تشغيل الاختبارات..."
	$(PYTHON) test_uploader.py
	@echo "✅ انتهت الاختبارات!"

test-coverage: ## تشغيل الاختبارات مع قياس التغطية
	@echo "📊 تشغيل الاختبارات مع قياس التغطية..."
	coverage run test_uploader.py
	coverage report
	coverage html
	@echo "✅ تم إنشاء تقرير التغطية في htmlcov/"

run: ## تشغيل الأداة
	@echo "🚀 تشغيل أداة GitHub Uploader..."
	$(PYTHON) github_uploader.py

examples: ## تشغيل الأمثلة
	@echo "📚 تشغيل أمثلة الاستخدام..."
	$(PYTHON) examples.py

lint: ## فحص جودة الكود
	@echo "🔍 فحص جودة الكود..."
	flake8 github_uploader.py config.py examples.py test_uploader.py
	@echo "✅ فحص الكود مكتمل!"

format: ## تنسيق الكود
	@echo "✨ تنسيق الكود..."
	black github_uploader.py config.py examples.py test_uploader.py
	isort github_uploader.py config.py examples.py test_uploader.py
	@echo "✅ تم تنسيق الكود!"

check-deps: ## فحص المتطلبات
	@echo "🔍 فحص المتطلبات..."
	$(PIP) check
	@echo "✅ جميع المتطلبات سليمة!"

clean: ## تنظيف الملفات المؤقتة
	@echo "🧹 تنظيف الملفات المؤقتة..."
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	rm -rf build/
	rm -rf dist/
	rm -rf .coverage
	rm -rf htmlcov/
	rm -rf .pytest_cache/
	rm -rf sample_bot/
	@echo "✅ تم التنظيف!"

build: ## بناء الحزمة
	@echo "📦 بناء الحزمة..."
	$(PYTHON) setup.py sdist bdist_wheel
	@echo "✅ تم بناء الحزمة في dist/"

install-package: build ## تثبيت الحزمة محلياً
	@echo "📥 تثبيت الحزمة محلياً..."
	$(PIP) install dist/*.whl
	@echo "✅ تم تثبيت الحزمة!"

uninstall: ## إلغاء تثبيت الحزمة
	@echo "🗑️ إلغاء تثبيت الحزمة..."
	$(PIP) uninstall github-uploader -y
	@echo "✅ تم إلغاء التثبيت!"

docs: ## إنشاء التوثيق
	@echo "📖 إنشاء التوثيق..."
	@echo "التوثيق متوفر في:"
	@echo "  - README.md"
	@echo "  - QUICK_START.md"
	@echo "  - CONTRIBUTING.md"
	@echo "  - CHANGELOG.md"

create-sample: ## إنشاء مشروع تجريبي
	@echo "🎯 إنشاء مشروع تجريبي..."
	$(PYTHON) -c "from examples import create_sample_project; create_sample_project()"
	@echo "✅ تم إنشاء مشروع تجريبي!"

check-git: ## فحص إعداد Git
	@echo "🔍 فحص إعداد Git..."
	@git --version || (echo "❌ Git غير مثبت" && exit 1)
	@git config user.name || (echo "⚠️ اسم المستخدم غير مُعرَّف في Git" && echo "استخدم: git config --global user.name 'Your Name'")
	@git config user.email || (echo "⚠️ البريد الإلكتروني غير مُعرَّف في Git" && echo "استخدم: git config --global user.email '<EMAIL>'")
	@echo "✅ إعداد Git سليم!"

check-python: ## فحص إصدار Python
	@echo "🐍 فحص إصدار Python..."
	@$(PYTHON) --version
	@$(PYTHON) -c "import sys; exit(0 if sys.version_info >= (3, 6) else 1)" || (echo "❌ يتطلب Python 3.6 أو أحدث" && exit 1)
	@echo "✅ إصدار Python مناسب!"

check-all: check-python check-git check-deps ## فحص جميع المتطلبات
	@echo "✅ جميع الفحوصات مكتملة!"

release: clean test build ## إعداد إصدار جديد
	@echo "🚀 إعداد إصدار جديد..."
	@echo "تأكد من:"
	@echo "  1. تحديث رقم الإصدار في setup.py"
	@echo "  2. تحديث CHANGELOG.md"
	@echo "  3. عمل commit للتغييرات"
	@echo "  4. إنشاء tag للإصدار"
	@echo "✅ الحزمة جاهزة في dist/"

# أهداف Windows
run-windows: ## تشغيل الأداة على Windows
	@echo "🚀 تشغيل الأداة على Windows..."
	run_uploader.bat

# أهداف Linux/Mac
run-unix: ## تشغيل الأداة على Linux/Mac
	@echo "🚀 تشغيل الأداة على Linux/Mac..."
	chmod +x run_uploader.sh
	./run_uploader.sh

# معلومات النظام
info: ## عرض معلومات النظام
	@echo "ℹ️ معلومات النظام:"
	@echo "  OS: $$(uname -s 2>/dev/null || echo 'Windows')"
	@echo "  Python: $$($(PYTHON) --version)"
	@echo "  Pip: $$($(PIP) --version)"
	@echo "  Git: $$(git --version 2>/dev/null || echo 'غير مثبت')"
	@echo "  المجلد الحالي: $$(pwd)"

# تحديث المتطلبات
update-deps: ## تحديث المتطلبات
	@echo "🔄 تحديث المتطلبات..."
	$(PIP) install --upgrade pip
	$(PIP) install --upgrade -r requirements.txt
	@echo "✅ تم تحديث المتطلبات!"

# إنشاء requirements.txt جديد
freeze: ## إنشاء requirements.txt من البيئة الحالية
	@echo "❄️ إنشاء requirements.txt..."
	$(PIP) freeze > requirements-freeze.txt
	@echo "✅ تم إنشاء requirements-freeze.txt"

# مساعدة سريعة
quick-help: ## مساعدة سريعة
	@echo "🚀 مساعدة سريعة:"
	@echo "  make setup    # إعداد أولي"
	@echo "  make run      # تشغيل الأداة"
	@echo "  make test     # اختبار"
	@echo "  make clean    # تنظيف"
