#!/bin/bash

# تعيين الترميز
export LANG=en_US.UTF-8

echo "========================================"
echo "🚀 رفع وكيل أخبار الألعاب إلى GitHub"
echo "========================================"
echo ""

# التحقق من وجود Git
if ! command -v git &> /dev/null; then
    echo "❌ Git غير مثبت. يرجى تثبيت Git أولاً"
    echo "Ubuntu/Debian: sudo apt install git"
    echo "macOS: brew install git"
    exit 1
fi

echo "✅ Git مثبت ومتاح"
echo ""

# إعداد Git إذا لم يكن معداً
echo "🔧 إعداد Git..."
git config --global user.name "vandal324" 2>/dev/null
git config --global user.email "<EMAIL>" 2>/dev/null
echo "✅ تم إعداد Git"

echo ""
echo "📁 إعداد المستودع المحلي..."

# تهيئة Git إذا لم يكن مهيئاً
if [ ! -d ".git" ]; then
    git init
    echo "✅ تم تهيئة مستودع Git محلي"
else
    echo "✅ مستودع Git موجود بالفعل"
fi

echo ""
echo "📝 إضافة الملفات..."

# نسخ README الجديد
cp "README_GITHUB.md" "README.md" 2>/dev/null
echo "✅ تم تحديث README.md"

# إضافة جميع الملفات
git add .
echo "✅ تم إضافة جميع الملفات"

echo ""
echo "💾 إنشاء commit..."
git commit -m "🎮 Gaming News Agent - Ready for deployment

✨ Features:
- Smart news collection with AI
- Interactive web interface  
- Ready for hosting (Render, Heroku, Railway)
- Real-time monitoring and control
- Secure API key management

🚀 Ready to deploy on multiple platforms
🌐 Web interface at localhost:5000
📖 Complete deployment guide included"

if [ $? -ne 0 ]; then
    echo "⚠️ لا توجد تغييرات جديدة للـ commit أو تم إنشاء commit بالفعل"
else
    echo "✅ تم إنشاء commit بنجاح"
fi

echo ""
echo "🌐 إضافة المستودع البعيد..."

# إضافة المستودع البعيد
git remote remove origin 2>/dev/null
git remote add origin https://github.com/vandal324/gaming-news-agent.git
echo "✅ تم إضافة المستودع البعيد"

echo ""
echo "📤 رفع الملفات إلى GitHub..."
echo "⚠️ ستحتاج إلى إدخال اسم المستخدم وكلمة المرور أو Personal Access Token"

# إنشاء branch main إذا لم يكن موجوداً
git branch -M main

# رفع الملفات
git push -u origin main

if [ $? -ne 0 ]; then
    echo ""
    echo "❌ فشل في رفع الملفات"
    echo ""
    echo "💡 نصائح لحل المشكلة:"
    echo "1. تأكد من أن المستودع موجود على GitHub"
    echo "2. تأكد من صحة اسم المستخدم وكلمة المرور"
    echo "3. استخدم Personal Access Token بدلاً من كلمة المرور"
    echo "4. تأكد من أن لديك صلاحيات الكتابة على المستودع"
    echo ""
    echo "🔗 لإنشاء Personal Access Token:"
    echo "https://github.com/settings/tokens"
    echo ""
    exit 1
else
    echo ""
    echo "🎉 تم رفع المشروع بنجاح!"
    echo ""
    echo "🔗 رابط المشروع: https://github.com/vandal324/gaming-news-agent"
    echo ""
    echo "📋 الخطوات التالية:"
    echo "1. اذهب إلى GitHub وتأكد من رفع الملفات"
    echo "2. أضف وصف للمشروع"
    echo "3. أضف Topics مثل: python, flask, ai, gaming, news"
    echo "4. اجعل المشروع عام أو خاص حسب رغبتك"
    echo ""
    echo "🚀 للنشر على Render:"
    echo "1. اذهب إلى render.com"
    echo "2. أنشئ Web Service جديد"
    echo "3. اربط مستودع GitHub هذا"
    echo "4. أضف متغيرات البيئة"
    echo "5. انشر!"
    echo ""
fi

read -p "اضغط Enter للمتابعة..."
