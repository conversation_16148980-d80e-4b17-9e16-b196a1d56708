#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط لـ Gemini 2.5 Pro
"""

import asyncio
import sys
import os

# إضافة المسار الحالي لـ Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.logger import logger
from modules.content_scraper import ContentScraper

async def test_simple_gemini():
    """اختبار بسيط لـ Gemini"""
    
    logger.info("🧪 اختبار بسيط لـ Gemini 2.5 Pro...")
    
    scraper = ContentScraper()
    
    try:
        # اختبار البحث المحسن مع النماذج الاحتياطية
        results = await scraper.enhanced_search_with_ai_fallbacks(
            keyword="gaming news",
            max_results=3,
            priority="balanced"
        )
        
        if results:
            logger.info(f"✅ نجح الاختبار! تم العثور على {len(results)} نتيجة")
            
            for i, result in enumerate(results):
                logger.info(f"📄 النتيجة {i+1}:")
                logger.info(f"  - العنوان: {result.get('title', 'غير محدد')[:100]}...")
                logger.info(f"  - المصدر: {result.get('source', 'غير محدد')}")
                logger.info(f"  - طول المحتوى: {len(result.get('content', ''))}")
                logger.info(f"  - معزز بالذكاء الاصطناعي: {result.get('ai_enhanced', False)}")
                
        else:
            logger.error("❌ فشل الاختبار - لم يتم العثور على نتائج")
            
    except Exception as e:
        logger.error(f"❌ خطأ في الاختبار: {e}")
        import traceback
        logger.error(f"📋 تفاصيل الخطأ: {traceback.format_exc()}")

if __name__ == "__main__":
    asyncio.run(test_simple_gemini())
