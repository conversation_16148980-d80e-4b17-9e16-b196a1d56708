#!/usr/bin/env python3
"""
اختبار ترتيب أولوية محركات TTS
للتأكد من أن النظام يستخدم البدائل قبل ElevenLabs
"""

import sys
import os
from pathlib import Path

# إضافة المجلد الجذر للمسار
sys.path.append(str(Path(__file__).parent))

def test_config_priority():
    """اختبار ترتيب الأولوية في الإعدادات"""
    print("🔍 فحص ترتيب الأولوية في config.toml...")

    try:
        import toml

        # قراءة الإعدادات مباشرة من الملف
        with open("config.toml", "r", encoding="utf-8") as f:
            config = toml.load(f)

        # فحص voice_choice
        voice_choice = config["settings"]["tts"]["voice_choice"]
        print(f"🎵 محرك الصوت المختار: {voice_choice}")

        # فحص ترتيب الأولوية
        priority_order = config["settings"]["tts"]["priority_order"]["primary"]
        print(f"🎯 ترتيب الأولوية: {priority_order}")

        # التحقق من أن ElevenLabs ليس الأول
        if priority_order[0] != "ElevenLabs":
            print(f"✅ ممتاز! المحرك الأول هو: {priority_order[0]}")
            if "ElevenLabs" in priority_order:
                print(f"✅ ElevenLabs في المرتبة: {priority_order.index('ElevenLabs') + 1}")
            else:
                print("✅ ElevenLabs غير موجود في القائمة")
        else:
            print("❌ ElevenLabs ما زال الأول في الترتيب")
            return False

        return True

    except Exception as e:
        print(f"❌ خطأ في فحص الإعدادات: {e}")
        return False

def test_smart_tts_manager():
    """اختبار مدير TTS الذكي"""
    print("\n🧠 فحص مدير TTS الذكي...")
    
    try:
        from TTS.smart_tts_manager import SmartTTSManager
        
        manager = SmartTTSManager()
        
        # فحص ترتيب الأولوية
        priority_engines = manager._get_priority_engines()
        print(f"🎯 ترتيب المحركات من المدير الذكي: {priority_engines}")
        
        # التحقق من أن ElevenLabs ليس الأول
        if priority_engines[0] != "ElevenLabs":
            print(f"✅ ممتاز! المحرك الأول هو: {priority_engines[0]}")
            print(f"✅ ElevenLabs في المرتبة: {priority_engines.index('ElevenLabs') + 1}")
        else:
            print("❌ ElevenLabs ما زال الأول في المدير الذكي")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فحص المدير الذكي: {e}")
        return False

def test_tts_generation():
    """اختبار إنشاء صوت فعلي"""
    print("\n🎤 اختبار إنشاء صوت فعلي...")
    
    try:
        from TTS.smart_tts_manager import SmartTTSManager
        
        manager = SmartTTSManager()
        
        # نص تجريبي
        test_text = "This is a test message to check TTS priority order."
        test_file = "assets/temp/test_priority.mp3"
        
        # إنشاء مجلد temp إذا لم يكن موجوداً
        os.makedirs("assets/temp", exist_ok=True)
        
        print(f"📝 النص التجريبي: {test_text}")
        print(f"📁 ملف الإخراج: {test_file}")
        
        # محاولة إنشاء الصوت
        success = manager.generate_speech(test_text, test_file)
        
        if success:
            print(f"✅ تم إنشاء الصوت بنجاح!")
            print(f"🎵 المحرك المستخدم: {manager.current_engine}")
            
            # فحص وجود الملف
            if os.path.exists(test_file):
                file_size = os.path.getsize(test_file)
                print(f"📊 حجم الملف: {file_size} بايت")
                
                # حذف الملف التجريبي
                os.remove(test_file)
                print("🗑️ تم حذف الملف التجريبي")
                
            return True
        else:
            print("❌ فشل في إنشاء الصوت")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار إنشاء الصوت: {e}")
        return False

def main():
    """تشغيل جميع الاختبارات"""
    print("🚀 بدء اختبار ترتيب أولوية محركات TTS")
    print("=" * 50)
    
    tests = [
        ("فحص الإعدادات", test_config_priority),
        ("فحص المدير الذكي", test_smart_tts_manager),
        ("اختبار إنشاء الصوت", test_tts_generation)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}...")
        try:
            if test_func():
                print(f"✅ {test_name}: نجح")
                passed += 1
            else:
                print(f"❌ {test_name}: فشل")
        except Exception as e:
            print(f"💥 {test_name}: خطأ - {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 النتائج: {passed}/{total} اختبارات نجحت")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! ترتيب الأولوية يعمل بشكل صحيح")
        print("✅ النظام سيستخدم البدائل قبل ElevenLabs")
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة الإعدادات")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
