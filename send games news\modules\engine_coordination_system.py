# نظام التنسيق بين محركات البحث
import asyncio
import time
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any, Set
from dataclasses import dataclass, asdict
from enum import Enum
from collections import defaultdict
import concurrent.futures

from .logger import logger
from .api_usage_manager import api_usage_manager, APIProvider, UsagePriority, can_use_api, get_cached_result, cache_result, record_usage
from .tavily_search import tavily_search
from .serpapi_search import serpapi_search
from .enhanced_search_manager import enhanced_search_manager
from .smart_search_manager import smart_search_manager
from .fallback_ai_manager import fallback_ai_manager

class CoordinationStrategy(Enum):
    """استراتيجيات التنسيق"""
    PARALLEL = "parallel"           # تشغيل متوازي
    SEQUENTIAL = "sequential"       # تشغيل متتالي
    ADAPTIVE = "adaptive"          # تكيفي حسب الحاجة
    REDUNDANT = "redundant"        # تكرار للتأكد
    SPECIALIZED = "specialized"     # تخصص حسب النوع
    HYBRID = "hybrid"              # مزيج من الطرق

class EngineCapability(Enum):
    """قدرات المحركات"""
    NEWS_SEARCH = "news_search"
    DEEP_ANALYSIS = "deep_analysis"
    REAL_TIME = "real_time"
    SEMANTIC_SEARCH = "semantic_search"
    MULTIMEDIA = "multimedia"
    ACADEMIC = "academic"
    SOCIAL_MEDIA = "social_media"

@dataclass
class EngineProfile:
    """ملف تعريف المحرك"""
    name: str
    capabilities: List[EngineCapability]
    strengths: List[str]
    weaknesses: List[str]
    avg_response_time: float
    reliability_score: float
    cost_per_request: float
    daily_limit: int
    current_usage: int
    last_reset: datetime

@dataclass
class CoordinationTask:
    """مهمة تنسيق"""
    task_id: str
    query: str
    required_capabilities: List[EngineCapability]
    priority: int  # 1-5
    max_engines: int
    timeout: float
    strategy: CoordinationStrategy
    context: Dict[str, Any]

@dataclass
class EngineResult:
    """نتيجة محرك"""
    engine_name: str
    results: List[Dict]
    execution_time: float
    success: bool
    error_message: Optional[str]
    quality_score: float
    relevance_score: float
    metadata: Dict[str, Any]

class EngineCoordinationSystem:
    """نظام التنسيق بين محركات البحث"""
    
    def __init__(self):
        # ملفات تعريف المحركات
        self.engine_profiles = self._initialize_engine_profiles()
        
        # إحصائيات التنسيق
        self.coordination_stats = {
            'total_coordinations': 0,
            'successful_coordinations': 0,
            'failed_coordinations': 0,
            'average_response_time': 0.0,
            'engine_usage': defaultdict(int),
            'strategy_performance': defaultdict(list)
        }
        
        # إعدادات التنسيق
        self.coordination_config = {
            'max_parallel_engines': 3,
            'default_timeout': 30.0,
            'quality_threshold': 0.6,
            'redundancy_threshold': 0.8,
            'load_balancing': True,
            'adaptive_timeout': True
        }
        
        # مجموعة المحركات النشطة
        self.active_engines = set()
        self._update_active_engines()
        
        logger.info("🔗 تم تهيئة نظام التنسيق بين المحركات")
    
    def _initialize_engine_profiles(self) -> Dict[str, EngineProfile]:
        """تهيئة ملفات تعريف المحركات"""
        profiles = {}
        
        # Tavily - محرك البحث العميق
        profiles['tavily'] = EngineProfile(
            name='tavily',
            capabilities=[
                EngineCapability.NEWS_SEARCH,
                EngineCapability.DEEP_ANALYSIS,
                EngineCapability.REAL_TIME,
                EngineCapability.SEMANTIC_SEARCH
            ],
            strengths=['بحث عميق', 'تحليل ذكي', 'نتائج حديثة'],
            weaknesses=['حدود يومية', 'بطء نسبي'],
            avg_response_time=3.5,
            reliability_score=0.95,
            cost_per_request=0.0,
            daily_limit=35,
            current_usage=0,
            last_reset=datetime.now()
        )
        
        # SerpAPI - محرك Google المتقدم
        profiles['serpapi'] = EngineProfile(
            name='serpapi',
            capabilities=[
                EngineCapability.NEWS_SEARCH,
                EngineCapability.REAL_TIME,
                EngineCapability.MULTIMEDIA
            ],
            strengths=['تغطية واسعة', 'نتائج متنوعة', 'سرعة عالية'],
            weaknesses=['تكلفة', 'حدود API'],
            avg_response_time=2.0,
            reliability_score=0.90,
            cost_per_request=0.0,
            daily_limit=100,
            current_usage=0,
            last_reset=datetime.now()
        )
        
        # Enhanced Search Manager - النظام المحسن
        profiles['enhanced'] = EngineProfile(
            name='enhanced',
            capabilities=[
                EngineCapability.NEWS_SEARCH,
                EngineCapability.SEMANTIC_SEARCH,
                EngineCapability.DEEP_ANALYSIS
            ],
            strengths=['تحليل متقدم', 'تنوع المصادر', 'ذكاء اصطناعي'],
            weaknesses=['تعقيد', 'استهلاك موارد'],
            avg_response_time=4.0,
            reliability_score=0.85,
            cost_per_request=0.0,
            daily_limit=1000,
            current_usage=0,
            last_reset=datetime.now()
        )
        
        # Smart Search Manager - النظام الذكي
        profiles['smart'] = EngineProfile(
            name='smart',
            capabilities=[
                EngineCapability.NEWS_SEARCH,
                EngineCapability.ADAPTIVE,
                EngineCapability.REAL_TIME
            ],
            strengths=['تكيف ذكي', 'تحسين تلقائي', 'كفاءة'],
            weaknesses=['تعلم تدريجي', 'تعقيد إعداد'],
            avg_response_time=3.0,
            reliability_score=0.88,
            cost_per_request=0.0,
            daily_limit=500,
            current_usage=0,
            last_reset=datetime.now()
        )
        
        return profiles
    
    def _update_active_engines(self):
        """تحديث قائمة المحركات النشطة"""
        self.active_engines.clear()
        
        # فحص توفر كل محرك
        if hasattr(tavily_search, 'enabled') and tavily_search.enabled:
            self.active_engines.add('tavily')
        
        if hasattr(serpapi_search, 'enabled') and serpapi_search.enabled:
            self.active_engines.add('serpapi')
        
        if enhanced_search_manager:
            self.active_engines.add('enhanced')
        
        if smart_search_manager:
            self.active_engines.add('smart')
        
        logger.info(f"🔧 المحركات النشطة: {', '.join(self.active_engines)}")
    
    async def coordinate_search(self, 
                              query: str,
                              required_capabilities: List[EngineCapability] = None,
                              strategy: CoordinationStrategy = CoordinationStrategy.ADAPTIVE,
                              max_results: int = 10,
                              priority: int = 3,
                              context: Dict[str, Any] = None) -> List[EngineResult]:
        """تنسيق البحث عبر محركات متعددة"""
        
        start_time = time.time()
        self.coordination_stats['total_coordinations'] += 1
        
        try:
            # إنشاء مهمة التنسيق
            task = CoordinationTask(
                task_id=f"coord_{int(time.time())}_{hash(query)}",
                query=query,
                required_capabilities=required_capabilities or [EngineCapability.NEWS_SEARCH],
                priority=priority,
                max_engines=self.coordination_config['max_parallel_engines'],
                timeout=self.coordination_config['default_timeout'],
                strategy=strategy,
                context=context or {}
            )
            
            # اختيار المحركات المناسبة
            selected_engines = await self._select_engines(task)
            
            if not selected_engines:
                logger.warning("⚠️ لم يتم العثور على محركات مناسبة")
                return []
            
            # تنفيذ البحث حسب الاستراتيجية
            results = await self._execute_coordination_strategy(task, selected_engines, max_results)
            
            # تقييم وترتيب النتائج
            evaluated_results = await self._evaluate_and_rank_results(results, task)
            
            # تحديث الإحصائيات
            execution_time = time.time() - start_time
            await self._update_coordination_stats(task, evaluated_results, execution_time)
            
            # تحديث ملفات المحركات
            self._update_engine_profiles(evaluated_results)
            
            logger.info(f"🔗 تنسيق مكتمل: {len(evaluated_results)} محرك، {execution_time:.2f}ث")
            
            self.coordination_stats['successful_coordinations'] += 1
            return evaluated_results
            
        except Exception as e:
            logger.error(f"❌ فشل في تنسيق البحث: {e}")
            self.coordination_stats['failed_coordinations'] += 1
            return []
    
    async def _select_engines(self, task: CoordinationTask) -> List[str]:
        """اختيار المحركات المناسبة للمهمة"""
        try:
            suitable_engines = []
            
            # فحص كل محرك نشط
            for engine_name in self.active_engines:
                profile = self.engine_profiles.get(engine_name)
                if not profile:
                    continue
                
                # فحص القدرات المطلوبة
                has_required_capabilities = any(
                    cap in profile.capabilities 
                    for cap in task.required_capabilities
                )
                
                if not has_required_capabilities:
                    continue
                
                # فحص الحدود اليومية
                if profile.current_usage >= profile.daily_limit:
                    logger.warning(f"⚠️ تم تجاوز الحد اليومي لـ {engine_name}")
                    continue
                
                # فحص موثوقية المحرك
                if profile.reliability_score < 0.5:
                    continue
                
                suitable_engines.append(engine_name)
            
            # ترتيب المحركات حسب الأولوية
            suitable_engines = self._prioritize_engines(suitable_engines, task)
            
            # تحديد العدد الأقصى
            max_engines = min(task.max_engines, len(suitable_engines))
            selected = suitable_engines[:max_engines]
            
            logger.info(f"🎯 تم اختيار المحركات: {', '.join(selected)}")
            return selected
            
        except Exception as e:
            logger.error(f"❌ فشل في اختيار المحركات: {e}")
            return []
    
    def _prioritize_engines(self, engines: List[str], task: CoordinationTask) -> List[str]:
        """ترتيب المحركات حسب الأولوية"""
        try:
            engine_scores = {}
            
            for engine_name in engines:
                profile = self.engine_profiles[engine_name]
                score = 0.0
                
                # نقاط الموثوقية
                score += profile.reliability_score * 0.3
                
                # نقاط السرعة (عكسية)
                speed_score = max(0, 1.0 - (profile.avg_response_time / 10.0))
                score += speed_score * 0.2
                
                # نقاط التخصص
                capability_match = len(set(task.required_capabilities) & set(profile.capabilities))
                score += capability_match * 0.3
                
                # نقاط الاستخدام (تفضيل المحركات الأقل استخداماً)
                usage_ratio = profile.current_usage / max(profile.daily_limit, 1)
                score += (1.0 - usage_ratio) * 0.2
                
                engine_scores[engine_name] = score
            
            # ترتيب حسب النقاط
            sorted_engines = sorted(engines, key=lambda x: engine_scores.get(x, 0), reverse=True)
            
            return sorted_engines
            
        except Exception as e:
            logger.error(f"❌ فشل في ترتيب المحركات: {e}")
            return engines

    async def _execute_coordination_strategy(self,
                                           task: CoordinationTask,
                                           engines: List[str],
                                           max_results: int) -> List[EngineResult]:
        """تنفيذ استراتيجية التنسيق"""
        try:
            if task.strategy == CoordinationStrategy.PARALLEL:
                return await self._parallel_execution(task, engines, max_results)
            elif task.strategy == CoordinationStrategy.SEQUENTIAL:
                return await self._sequential_execution(task, engines, max_results)
            elif task.strategy == CoordinationStrategy.ADAPTIVE:
                return await self._adaptive_execution(task, engines, max_results)
            elif task.strategy == CoordinationStrategy.REDUNDANT:
                return await self._redundant_execution(task, engines, max_results)
            elif task.strategy == CoordinationStrategy.SPECIALIZED:
                return await self._specialized_execution(task, engines, max_results)
            else:  # HYBRID
                return await self._hybrid_execution(task, engines, max_results)

        except Exception as e:
            logger.error(f"❌ فشل في تنفيذ استراتيجية {task.strategy.value}: {e}")
            return []

    async def _parallel_execution(self, task: CoordinationTask, engines: List[str], max_results: int) -> List[EngineResult]:
        """تنفيذ متوازي لجميع المحركات"""
        try:
            # إنشاء مهام متوازية
            tasks = []
            for engine_name in engines:
                task_coro = self._execute_single_engine(engine_name, task.query, max_results // len(engines))
                tasks.append(task_coro)

            # تنفيذ متوازي مع timeout
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # فلترة النتائج الناجحة
            successful_results = []
            for i, result in enumerate(results):
                if isinstance(result, EngineResult):
                    successful_results.append(result)
                else:
                    logger.error(f"❌ فشل محرك {engines[i]}: {result}")

            return successful_results

        except Exception as e:
            logger.error(f"❌ فشل في التنفيذ المتوازي: {e}")
            return []

    async def _sequential_execution(self, task: CoordinationTask, engines: List[str], max_results: int) -> List[EngineResult]:
        """تنفيذ متتالي للمحركات"""
        try:
            results = []
            remaining_results = max_results

            for engine_name in engines:
                if remaining_results <= 0:
                    break

                # تنفيذ المحرك
                result = await self._execute_single_engine(engine_name, task.query, remaining_results)

                if result and result.success:
                    results.append(result)
                    remaining_results -= len(result.results)

                    # إذا حصلنا على نتائج كافية، توقف
                    if remaining_results <= 0:
                        break
                else:
                    logger.warning(f"⚠️ فشل محرك {engine_name}, الانتقال للتالي")

            return results

        except Exception as e:
            logger.error(f"❌ فشل في التنفيذ المتتالي: {e}")
            return []

    async def _adaptive_execution(self, task: CoordinationTask, engines: List[str], max_results: int) -> List[EngineResult]:
        """تنفيذ تكيفي حسب الأداء"""
        try:
            # بدء بأفضل محرك
            best_engine = engines[0]
            result = await self._execute_single_engine(best_engine, task.query, max_results)

            results = []
            if result and result.success:
                results.append(result)

                # فحص جودة النتائج
                if result.quality_score < self.coordination_config['quality_threshold']:
                    # إذا كانت الجودة منخفضة، جرب محرك إضافي
                    logger.info("🔄 جودة منخفضة، تجربة محرك إضافي")

                    if len(engines) > 1:
                        backup_result = await self._execute_single_engine(
                            engines[1], task.query, max_results // 2
                        )
                        if backup_result and backup_result.success:
                            results.append(backup_result)
            else:
                # إذا فشل المحرك الأول، جرب البقية
                logger.warning(f"⚠️ فشل {best_engine}, تجربة محركات أخرى")
                for engine_name in engines[1:]:
                    backup_result = await self._execute_single_engine(engine_name, task.query, max_results)
                    if backup_result and backup_result.success:
                        results.append(backup_result)
                        break

            return results

        except Exception as e:
            logger.error(f"❌ فشل في التنفيذ التكيفي: {e}")
            return []

    async def _redundant_execution(self, task: CoordinationTask, engines: List[str], max_results: int) -> List[EngineResult]:
        """تنفيذ مكرر للتأكد من الجودة"""
        try:
            # تنفيذ متوازي لأفضل محركين
            selected_engines = engines[:2] if len(engines) >= 2 else engines

            tasks = []
            for engine_name in selected_engines:
                task_coro = self._execute_single_engine(engine_name, task.query, max_results)
                tasks.append(task_coro)

            results = await asyncio.gather(*tasks, return_exceptions=True)

            # فلترة النتائج الناجحة
            successful_results = []
            for i, result in enumerate(results):
                if isinstance(result, EngineResult) and result.success:
                    successful_results.append(result)

            # إذا كانت النتائج متشابهة، اختر الأفضل
            if len(successful_results) >= 2:
                best_result = max(successful_results, key=lambda x: x.quality_score)
                return [best_result]

            return successful_results

        except Exception as e:
            logger.error(f"❌ فشل في التنفيذ المكرر: {e}")
            return []

    async def _specialized_execution(self, task: CoordinationTask, engines: List[str], max_results: int) -> List[EngineResult]:
        """تنفيذ متخصص حسب نوع المحتوى"""
        try:
            # تحديد التخصص المطلوب
            specialization = self._determine_specialization(task)

            # اختيار المحرك الأنسب للتخصص
            specialized_engine = self._select_specialized_engine(engines, specialization)

            if specialized_engine:
                result = await self._execute_single_engine(specialized_engine, task.query, max_results)
                return [result] if result and result.success else []
            else:
                # العودة للتنفيذ العادي
                return await self._parallel_execution(task, engines, max_results)

        except Exception as e:
            logger.error(f"❌ فشل في التنفيذ المتخصص: {e}")
            return []

    async def _hybrid_execution(self, task: CoordinationTask, engines: List[str], max_results: int) -> List[EngineResult]:
        """تنفيذ مختلط يجمع عدة استراتيجيات"""
        try:
            results = []

            # 60% من النتائج من أفضل محرك
            primary_results = int(max_results * 0.6)
            primary_result = await self._execute_single_engine(engines[0], task.query, primary_results)
            if primary_result and primary_result.success:
                results.append(primary_result)

            # 40% من النتائج من محرك ثانوي
            if len(engines) > 1:
                secondary_results = max_results - primary_results
                secondary_result = await self._execute_single_engine(engines[1], task.query, secondary_results)
                if secondary_result and secondary_result.success:
                    results.append(secondary_result)

            return results

        except Exception as e:
            logger.error(f"❌ فشل في التنفيذ المختلط: {e}")
            return []

    async def _execute_single_engine(self, engine_name: str, query: str, max_results: int) -> Optional[EngineResult]:
        """تنفيذ محرك واحد"""
        start_time = time.time()

        try:
            # تحديث استخدام المحرك
            if engine_name in self.engine_profiles:
                self.engine_profiles[engine_name].current_usage += 1

            # تنفيذ البحث حسب نوع المحرك
            results = []
            success = False
            error_message = None

            if engine_name == 'tavily' and tavily_search:
                # فحص التخزين المؤقت والحدود لـ Tavily
                cached_result = await get_cached_result(query, APIProvider.TAVILY)
                if cached_result:
                    results = cached_result.get('results', [])
                    success = True
                    logger.debug("💾 استخدام نتيجة محفوظة من Tavily")
                elif await can_use_api(APIProvider.TAVILY, UsagePriority.NORMAL):
                    results = await tavily_search.search(query, max_results=max_results)
                    success = bool(results)
                    # تسجيل الاستخدام وحفظ النتيجة
                    await record_usage(APIProvider.TAVILY, success)
                    if success and results:
                        await cache_result(query, {'results': results}, APIProvider.TAVILY, 'news')
                else:
                    results = []
                    success = False
                    error_message = "تم تجاوز حدود استخدام Tavily API"

            elif engine_name == 'serpapi' and serpapi_search:
                # فحص التخزين المؤقت والحدود لـ SerpAPI
                cached_result = await get_cached_result(query, APIProvider.SERPAPI)
                if cached_result:
                    results = cached_result.get('results', [])
                    success = True
                    logger.debug("💾 استخدام نتيجة محفوظة من SerpAPI")
                elif await can_use_api(APIProvider.SERPAPI, UsagePriority.NORMAL):
                    results = await serpapi_search.search(query, num_results=max_results)
                    success = bool(results)
                    # تسجيل الاستخدام وحفظ النتيجة
                    await record_usage(APIProvider.SERPAPI, success)
                    if success and results:
                        await cache_result(query, {'results': results}, APIProvider.SERPAPI, 'general')
                else:
                    results = []
                    success = False
                    error_message = "تم تجاوز حدود استخدام SerpAPI"
            elif engine_name == 'enhanced' and enhanced_search_manager:
                search_result = await enhanced_search_manager.comprehensive_search(query, max_results)
                results = search_result.get('results', [])
                success = bool(results)
            elif engine_name == 'smart' and smart_search_manager:
                from .smart_search_manager import SearchRequest, SearchPriority
                request = SearchRequest(query=query, max_results=max_results, priority=SearchPriority.FREE)
                search_results = await smart_search_manager.search(request)
                results = [asdict(r) for r in search_results]
                success = bool(results)
            else:
                error_message = f"محرك غير مدعوم: {engine_name}"

            execution_time = time.time() - start_time

            # حساب نقاط الجودة والصلة
            quality_score = self._calculate_quality_score(results)
            relevance_score = self._calculate_relevance_score(results, query)

            # إنشاء نتيجة المحرك
            engine_result = EngineResult(
                engine_name=engine_name,
                results=results,
                execution_time=execution_time,
                success=success,
                error_message=error_message,
                quality_score=quality_score,
                relevance_score=relevance_score,
                metadata={
                    'query': query,
                    'max_results': max_results,
                    'timestamp': datetime.now().isoformat()
                }
            )

            # تحديث إحصائيات المحرك
            self.coordination_stats['engine_usage'][engine_name] += 1

            return engine_result

        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"❌ فشل في تنفيذ محرك {engine_name}: {e}")

            return EngineResult(
                engine_name=engine_name,
                results=[],
                execution_time=execution_time,
                success=False,
                error_message=str(e),
                quality_score=0.0,
                relevance_score=0.0,
                metadata={'query': query, 'max_results': max_results}
            )

    def _calculate_quality_score(self, results: List[Dict]) -> float:
        """حساب نقاط الجودة للنتائج"""
        if not results:
            return 0.0

        total_score = 0.0
        for result in results:
            score = 0.5  # نقاط أساسية

            # نقاط للمحتوى
            if result.get('content') and len(result['content']) > 100:
                score += 0.2

            # نقاط للعنوان
            if result.get('title') and len(result['title']) > 10:
                score += 0.1

            # نقاط للمصدر
            if result.get('source'):
                score += 0.1

            # نقاط للتاريخ
            if result.get('published_date'):
                score += 0.1

            total_score += score

        return min(total_score / len(results), 1.0)

    def _calculate_relevance_score(self, results: List[Dict], query: str) -> float:
        """حساب نقاط الصلة للنتائج"""
        if not results:
            return 0.0

        query_words = set(query.lower().split())
        total_relevance = 0.0

        for result in results:
            relevance = 0.0

            # فحص العنوان
            title = result.get('title', '').lower()
            title_words = set(title.split())
            title_overlap = len(query_words & title_words) / max(len(query_words), 1)
            relevance += title_overlap * 0.6

            # فحص المحتوى
            content = result.get('content', '').lower()
            content_words = set(content.split())
            content_overlap = len(query_words & content_words) / max(len(query_words), 1)
            relevance += content_overlap * 0.4

            total_relevance += min(relevance, 1.0)

        return total_relevance / len(results)

    def _determine_specialization(self, task: CoordinationTask) -> str:
        """تحديد التخصص المطلوب"""
        query_lower = task.query.lower()

        if any(word in query_lower for word in ['news', 'breaking', 'announcement']):
            return 'news'
        elif any(word in query_lower for word in ['review', 'rating', 'opinion']):
            return 'review'
        elif any(word in query_lower for word in ['guide', 'tutorial', 'how to']):
            return 'guide'
        elif any(word in query_lower for word in ['analysis', 'deep dive', 'research']):
            return 'analysis'
        else:
            return 'general'

    def _select_specialized_engine(self, engines: List[str], specialization: str) -> Optional[str]:
        """اختيار المحرك المتخصص"""
        specialization_preferences = {
            'news': ['tavily', 'serpapi', 'enhanced'],
            'review': ['serpapi', 'enhanced', 'smart'],
            'guide': ['enhanced', 'smart', 'tavily'],
            'analysis': ['enhanced', 'tavily', 'smart'],
            'general': ['tavily', 'enhanced', 'serpapi']
        }

        preferred_engines = specialization_preferences.get(specialization, engines)

        # العثور على أول محرك متاح من القائمة المفضلة
        for engine in preferred_engines:
            if engine in engines:
                return engine

        return engines[0] if engines else None

    async def _evaluate_and_rank_results(self, results: List[EngineResult], task: CoordinationTask) -> List[EngineResult]:
        """تقييم وترتيب نتائج المحركات"""
        try:
            # حساب نقاط شاملة لكل محرك
            for result in results:
                # النقاط الأساسية
                base_score = (result.quality_score + result.relevance_score) / 2

                # مكافأة السرعة
                speed_bonus = max(0, 1.0 - (result.execution_time / 10.0)) * 0.1

                # مكافأة عدد النتائج
                count_bonus = min(len(result.results) / 10, 0.1)

                # عقوبة الأخطاء
                error_penalty = 0.2 if not result.success else 0.0

                # النقاط النهائية
                final_score = base_score + speed_bonus + count_bonus - error_penalty
                result.metadata['final_score'] = max(0.0, min(1.0, final_score))

            # ترتيب حسب النقاط النهائية
            sorted_results = sorted(results, key=lambda x: x.metadata.get('final_score', 0), reverse=True)

            return sorted_results

        except Exception as e:
            logger.error(f"❌ فشل في تقييم النتائج: {e}")
            return results

    async def _update_coordination_stats(self, task: CoordinationTask, results: List[EngineResult], execution_time: float):
        """تحديث إحصائيات التنسيق"""
        try:
            # تحديث الإحصائيات العامة
            self.coordination_stats['average_response_time'] = (
                (self.coordination_stats['average_response_time'] * (self.coordination_stats['total_coordinations'] - 1) + execution_time) /
                self.coordination_stats['total_coordinations']
            )

            # تحديث أداء الاستراتيجية
            strategy_score = sum(r.metadata.get('final_score', 0) for r in results) / max(len(results), 1)
            self.coordination_stats['strategy_performance'][task.strategy.value].append(strategy_score)

            # الاحتفاظ بآخر 50 نتيجة فقط
            if len(self.coordination_stats['strategy_performance'][task.strategy.value]) > 50:
                self.coordination_stats['strategy_performance'][task.strategy.value] = \
                    self.coordination_stats['strategy_performance'][task.strategy.value][-50:]

        except Exception as e:
            logger.error(f"❌ فشل في تحديث إحصائيات التنسيق: {e}")

    def _update_engine_profiles(self, results: List[EngineResult]):
        """تحديث ملفات المحركات بناءً على الأداء"""
        try:
            for result in results:
                profile = self.engine_profiles.get(result.engine_name)
                if not profile:
                    continue

                # تحديث متوسط وقت الاستجابة
                profile.avg_response_time = (
                    (profile.avg_response_time * 0.8) + (result.execution_time * 0.2)
                )

                # تحديث نقاط الموثوقية
                if result.success:
                    profile.reliability_score = min(1.0, profile.reliability_score * 1.01)
                else:
                    profile.reliability_score = max(0.1, profile.reliability_score * 0.95)

        except Exception as e:
            logger.error(f"❌ فشل في تحديث ملفات المحركات: {e}")

    def get_coordination_insights(self) -> Dict[str, Any]:
        """الحصول على رؤى التنسيق"""
        insights = {
            'total_coordinations': self.coordination_stats['total_coordinations'],
            'success_rate': 0.0,
            'average_response_time': self.coordination_stats['average_response_time'],
            'engine_usage': dict(self.coordination_stats['engine_usage']),
            'strategy_performance': {},
            'engine_profiles': {},
            'recommendations': []
        }

        # حساب معدل النجاح
        if self.coordination_stats['total_coordinations'] > 0:
            insights['success_rate'] = (
                self.coordination_stats['successful_coordinations'] /
                self.coordination_stats['total_coordinations']
            )

        # أداء الاستراتيجيات
        for strategy, scores in self.coordination_stats['strategy_performance'].items():
            if scores:
                insights['strategy_performance'][strategy] = {
                    'average_score': sum(scores) / len(scores),
                    'usage_count': len(scores),
                    'trend': 'improving' if len(scores) > 5 and
                            sum(scores[-5:]) / 5 > sum(scores[:-5]) / max(len(scores) - 5, 1)
                            else 'stable'
                }

        # ملفات المحركات
        for name, profile in self.engine_profiles.items():
            insights['engine_profiles'][name] = {
                'reliability_score': profile.reliability_score,
                'avg_response_time': profile.avg_response_time,
                'usage_ratio': profile.current_usage / max(profile.daily_limit, 1),
                'capabilities': [cap.value for cap in profile.capabilities]
            }

        # توصيات
        if insights['success_rate'] < 0.8:
            insights['recommendations'].append("تحسين اختيار المحركات")

        if insights['average_response_time'] > 5.0:
            insights['recommendations'].append("تحسين أوقات الاستجابة")

        return insights

    async def optimize_coordination(self):
        """تحسين إعدادات التنسيق"""
        try:
            insights = self.get_coordination_insights()

            # تحسين عدد المحركات المتوازية
            if insights['average_response_time'] > 5.0:
                self.coordination_config['max_parallel_engines'] = max(1,
                    self.coordination_config['max_parallel_engines'] - 1)
            elif insights['success_rate'] > 0.9 and insights['average_response_time'] < 3.0:
                self.coordination_config['max_parallel_engines'] = min(5,
                    self.coordination_config['max_parallel_engines'] + 1)

            # تحسين timeout
            if insights['average_response_time'] > 0:
                optimal_timeout = insights['average_response_time'] * 2
                self.coordination_config['default_timeout'] = max(10.0, min(60.0, optimal_timeout))

            # إعادة تعيين استخدام المحركات يومياً
            current_time = datetime.now()
            for profile in self.engine_profiles.values():
                if (current_time - profile.last_reset).days >= 1:
                    profile.current_usage = 0
                    profile.last_reset = current_time

            logger.info("🔧 تم تحسين إعدادات التنسيق")

        except Exception as e:
            logger.error(f"❌ فشل في تحسين التنسيق: {e}")

# إنشاء مثيل عام
engine_coordination_system = EngineCoordinationSystem()
