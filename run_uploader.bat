@echo off
chcp 65001 >nul
title GitHub Uploader Tool

echo ================================================
echo 🚀 أداة رفع المشاريع على GitHub
echo ================================================
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت على النظام
    echo يرجى تثبيت Python من: https://python.org
    pause
    exit /b 1
)

REM التحقق من وجود Git
git --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Git غير مثبت على النظام
    echo يرجى تثبيت Git من: https://git-scm.com
    pause
    exit /b 1
)

REM التحقق من وجود ملف المتطلبات
if not exist requirements.txt (
    echo ❌ ملف requirements.txt غير موجود
    pause
    exit /b 1
)

REM تثبيت المتطلبات
echo 📦 تثبيت المتطلبات...
pip install -r requirements.txt >nul 2>&1
if errorlevel 1 (
    echo ❌ فشل في تثبيت المتطلبات
    echo جاري المحاولة مرة أخرى...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ❌ فشل في تثبيت المتطلبات نهائياً
        pause
        exit /b 1
    )
)

echo ✅ تم تثبيت المتطلبات بنجاح
echo.

REM تشغيل الأداة
echo 🚀 تشغيل أداة GitHub Uploader...
echo.
python github_uploader.py

echo.
echo 👋 شكراً لاستخدام الأداة!
pause
