# مدير مفاتيح API المحسن
import os
import json
import time
import random
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum
from dotenv import load_dotenv
from .logger import logger

# تحميل متغيرات البيئة
load_dotenv()

class ServiceStatus(Enum):
    """حالة الخدمة"""
    ACTIVE = "active"
    DISABLED = "disabled"
    ERROR = "error"
    QUOTA_EXCEEDED = "quota_exceeded"
    RATE_LIMITED = "rate_limited"

@dataclass
class APIKeyInfo:
    """معلومات مفتاح API"""
    key: str
    service_name: str
    status: ServiceStatus
    usage_count: int = 0
    last_used: float = 0
    error_count: int = 0
    quota_reset_time: Optional[float] = None
    daily_limit: Optional[int] = None
    rate_limit_per_minute: Optional[int] = None

class EnhancedAPIManager:
    """مدير مفاتيح API المحسن مع دعم جميع الخدمات"""
    
    def __init__(self):
        self.api_keys: Dict[str, List[APIKeyInfo]] = {}
        self.service_configs: Dict[str, Dict] = {}
        self.usage_stats: Dict[str, Dict] = {}
        
        # تحميل المفاتيح من متغيرات البيئة
        self._load_api_keys()
        
        # تحميل إعدادات الخدمات
        self._load_service_configs()
        
        logger.info(f"🔑 تم تهيئة مدير API المحسن مع {len(self.api_keys)} خدمة")
    
    def _load_api_keys(self):
        """تحميل جميع مفاتيح API من متغيرات البيئة"""

        # المفاتيح الموجودة بالفعل (بدون أرقام)
        self._load_single_key("gemini", "GEMINI_API_KEY")
        self._load_single_key("telegram", "TELEGRAM_BOT_TOKEN")
        self._load_single_key("blogger_client_id", "BLOGGER_CLIENT_ID")
        self._load_single_key("blogger_client_secret", "BLOGGER_CLIENT_SECRET")
        self._load_single_key("freepik", "FREEPIK_API_KEY")
        self._load_single_key("fluxai", "FLUXAI_API_KEY")
        self._load_single_key("rawg", "RAWG_API_KEY")
        self._load_single_key("apify", "APIFY_API_TOKEN")

        # مفاتيح البحث الموجودة
        self._load_keys_group("search1api", "SEARCH1API_KEY_", 3)

        # مفاتيح البحث الجديدة
        self._load_keys_group("google_enhanced", "GOOGLE_ENHANCED_API_KEY_", 14)
        self._load_keys_group("google_search", "GOOGLE_SEARCH_API_KEY_", 10)
        self._load_keys_group("serpapi", "SERPAPI_KEY_", 3)
        self._load_keys_group("tavily", "TAVILY_API_KEY_", 2)
        
        # مفاتيح الذكاء الاصطناعي
        self._load_single_key("gemini", "GEMINI_API_KEY")
        self._load_keys_group("openai", "OPENAI_API_KEY_", 3)
        self._load_keys_group("claude", "CLAUDE_API_KEY_", 2)
        
        # مفاتيح تحويل الصوت
        self._load_keys_group("assemblyai", "ASSEMBLYAI_API_KEY_", 2)
        self._load_keys_group("speechmatics", "SPEECHMATICS_API_KEY_", 2)
        self._load_single_key("ibm_watson", "IBM_WATSON_API_KEY")
        self._load_keys_group("azure_speech", "AZURE_SPEECH_KEY_", 2)
        self._load_keys_group("google_cloud_speech", "GOOGLE_CLOUD_SPEECH_KEY_", 2)
        self._load_keys_group("wit_ai", "WIT_AI_ACCESS_TOKEN_", 2)
        self._load_keys_group("whisper", "WHISPER_API_KEY_", 2)
        
        # مفاتيح الفيديو والصوت
        self._load_single_key("apify", "APIFY_API_TOKEN")
        self._load_keys_group("youtube_data", "YOUTUBE_DATA_API_KEY_", 3)
        self._load_keys_group("rapidapi", "RAPIDAPI_KEY_", 3)
        
        # مفاتيح الصور والوسائط
        self._load_single_key("rawg", "RAWG_API_KEY")
        self._load_single_key("igdb_client_id", "IGDB_CLIENT_ID")
        self._load_single_key("igdb_client_secret", "IGDB_CLIENT_SECRET")
        self._load_keys_group("giant_bomb", "GIANT_BOMB_API_KEY_", 2)
        self._load_keys_group("mobygames", "MOBYGAMES_API_KEY_", 2)
        self._load_keys_group("unsplash", "UNSPLASH_ACCESS_KEY_", 2)
        self._load_keys_group("pexels", "PEXELS_API_KEY_", 3)
        self._load_keys_group("pixabay", "PIXABAY_API_KEY_", 3)
        self._load_keys_group("steam", "STEAM_API_KEY_", 2)
        
        # مفاتيح توليد الصور
        self._load_single_key("openart", "OPENART_API_KEY")
        self._load_single_key("leap_ai", "LEAP_AI_API_KEY")
        self._load_single_key("deepai", "DEEPAI_API_KEY")
        self._load_single_key("replicate", "REPLICATE_API_KEY")
        self._load_keys_group("stability_ai", "STABILITY_AI_API_KEY_", 2)
        self._load_keys_group("midjourney", "MIDJOURNEY_API_KEY_", 2)
        self._load_keys_group("dalle", "DALLE_API_KEY_", 2)
        self._load_single_key("freepik", "FREEPIK_API_KEY")
        self._load_single_key("fluxai", "FLUXAI_API_KEY")
        
        # مفاتيح النشر
        self._load_single_key("blogger_client_id", "BLOGGER_CLIENT_ID")
        self._load_single_key("blogger_client_secret", "BLOGGER_CLIENT_SECRET")
        self._load_keys_group("wordpress", "WORDPRESS_API_KEY_", 2)
        self._load_keys_group("medium", "MEDIUM_ACCESS_TOKEN_", 2)
        self._load_single_key("ghost_admin", "GHOST_ADMIN_API_KEY")
        self._load_single_key("ghost_content", "GHOST_CONTENT_API_KEY")
        
        # مفاتيح وسائل التواصل
        self._load_single_key("telegram", "TELEGRAM_BOT_TOKEN")
        self._load_single_key("twitter_api", "TWITTER_API_KEY_1")
        self._load_single_key("facebook", "FACEBOOK_ACCESS_TOKEN_1")
        self._load_single_key("instagram", "INSTAGRAM_ACCESS_TOKEN_1")
        
        # مفاتيح أخرى
        self._load_keys_group("news_api", "NEWS_API_KEY_", 3)
        self._load_single_key("reddit_client_id", "REDDIT_CLIENT_ID")
        self._load_single_key("aws_access", "AWS_ACCESS_KEY_ID")
        self._load_single_key("google_analytics", "GOOGLE_ANALYTICS_TRACKING_ID")
        
    def _load_keys_group(self, service_name: str, prefix: str, count: int):
        """تحميل مجموعة مفاتيح لخدمة واحدة"""
        keys = []
        for i in range(1, count + 1):
            key_value = os.getenv(f"{prefix}{i}")
            if key_value and key_value != f"your_{service_name.lower()}_key_{i}_here":
                keys.append(APIKeyInfo(
                    key=key_value,
                    service_name=service_name,
                    status=ServiceStatus.ACTIVE
                ))
        
        if keys:
            self.api_keys[service_name] = keys
            logger.info(f"🔑 تم تحميل {len(keys)} مفتاح لخدمة {service_name}")
    
    def _load_single_key(self, service_name: str, env_var: str):
        """تحميل مفتاح واحد لخدمة"""
        key_value = os.getenv(env_var)
        if key_value and not key_value.startswith("your_"):
            self.api_keys[service_name] = [APIKeyInfo(
                key=key_value,
                service_name=service_name,
                status=ServiceStatus.ACTIVE
            )]
            logger.info(f"🔑 تم تحميل مفتاح لخدمة {service_name}")
    
    def _load_service_configs(self):
        """تحميل إعدادات الخدمات"""
        self.service_configs = {
            # خدمات البحث
            "google_enhanced": {
                "daily_limit": 1000,
                "rate_limit_per_minute": 100,
                "priority": 1
            },
            "serpapi": {
                "daily_limit": 100,
                "rate_limit_per_minute": 10,
                "priority": 2
            },
            "tavily": {
                "daily_limit": 1000,
                "rate_limit_per_minute": 20,
                "priority": 3
            },
            
            # خدمات الذكاء الاصطناعي
            "gemini": {
                "daily_limit": 1500,
                "rate_limit_per_minute": 60,
                "priority": 1
            },
            "openai": {
                "daily_limit": 1000,
                "rate_limit_per_minute": 20,
                "priority": 2
            },
            
            # خدمات تحويل الصوت
            "assemblyai": {
                "daily_limit": 100,
                "rate_limit_per_minute": 5,
                "priority": 1
            },
            "azure_speech": {
                "daily_limit": 500,
                "rate_limit_per_minute": 20,
                "priority": 2
            },
            
            # خدمات الصور
            "unsplash": {
                "daily_limit": 5000,
                "rate_limit_per_minute": 50,
                "priority": 1
            },
            "pexels": {
                "daily_limit": 200,
                "rate_limit_per_minute": 20,
                "priority": 2
            },
            
            # خدمات توليد الصور
            "stability_ai": {
                "daily_limit": 100,
                "rate_limit_per_minute": 10,
                "priority": 1
            },
            "dalle": {
                "daily_limit": 50,
                "rate_limit_per_minute": 5,
                "priority": 2
            }
        }
    
    def get_api_key(self, service_name: str) -> Optional[str]:
        """الحصول على مفتاح API لخدمة معينة"""
        if service_name not in self.api_keys:
            logger.warning(f"⚠️ لا توجد مفاتيح متاحة لخدمة {service_name}")
            return None
        
        keys = self.api_keys[service_name]
        active_keys = [k for k in keys if k.status == ServiceStatus.ACTIVE]
        
        if not active_keys:
            logger.warning(f"⚠️ لا توجد مفاتيح نشطة لخدمة {service_name}")
            return None
        
        # اختيار أقل مفتاح استخداماً
        selected_key = min(active_keys, key=lambda k: k.usage_count)
        
        # فحص حدود الاستخدام
        if self._check_rate_limits(selected_key, service_name):
            selected_key.usage_count += 1
            selected_key.last_used = time.time()
            
            logger.debug(f"🔑 استخدام مفتاح {service_name}: {selected_key.key[:8]}...")
            return selected_key.key
        
        logger.warning(f"⚠️ تم تجاوز حدود الاستخدام لخدمة {service_name}")
        return None
    
    def _check_rate_limits(self, key_info: APIKeyInfo, service_name: str) -> bool:
        """فحص حدود معدل الاستخدام"""
        current_time = time.time()
        config = self.service_configs.get(service_name, {})
        
        # فحص الحد اليومي
        daily_limit = config.get("daily_limit")
        if daily_limit and key_info.usage_count >= daily_limit:
            # فحص إذا كان اليوم جديد
            if current_time - key_info.last_used > 86400:  # 24 ساعة
                key_info.usage_count = 0
            else:
                return False
        
        # فحص حد الدقيقة
        rate_limit = config.get("rate_limit_per_minute")
        if rate_limit and current_time - key_info.last_used < 60:
            # حساب عدد الطلبات في الدقيقة الأخيرة
            # هذا تبسيط - يمكن تحسينه بتتبع أكثر دقة
            if key_info.usage_count % rate_limit == 0:
                return False
        
        return True
    
    def mark_key_error(self, service_name: str, api_key: str, error_type: str = "general"):
        """تسجيل خطأ في مفتاح API"""
        if service_name not in self.api_keys:
            return
        
        for key_info in self.api_keys[service_name]:
            if key_info.key == api_key:
                key_info.error_count += 1
                
                if error_type == "quota_exceeded":
                    key_info.status = ServiceStatus.QUOTA_EXCEEDED
                    key_info.quota_reset_time = time.time() + 86400  # 24 ساعة
                elif error_type == "rate_limited":
                    key_info.status = ServiceStatus.RATE_LIMITED
                    key_info.quota_reset_time = time.time() + 3600   # ساعة واحدة
                elif key_info.error_count >= 5:
                    key_info.status = ServiceStatus.ERROR
                
                logger.warning(f"⚠️ خطأ في مفتاح {service_name}: {error_type}")
                break
    
    def get_service_status(self, service_name: str) -> Dict[str, Any]:
        """الحصول على حالة خدمة معينة"""
        if service_name not in self.api_keys:
            return {
                "status": "not_configured",
                "keys_count": 0,
                "active_keys": 0,
                "total_usage": 0,
                "config": {}
            }

        keys = self.api_keys[service_name]
        active_count = len([k for k in keys if k.status == ServiceStatus.ACTIVE])
        total_usage = sum(k.usage_count for k in keys)

        return {
            "status": "active" if active_count > 0 else "inactive",
            "keys_count": len(keys),
            "active_keys": active_count,
            "total_usage": total_usage,
            "config": self.service_configs.get(service_name, {})
        }
    
    def get_all_services_status(self) -> Dict[str, Dict]:
        """الحصول على حالة جميع الخدمات"""
        status = {}
        for service_name in self.api_keys.keys():
            status[service_name] = self.get_service_status(service_name)
        return status
    
    def rotate_keys(self, service_name: str):
        """تدوير مفاتيح خدمة معينة"""
        if service_name not in self.api_keys:
            return
        
        keys = self.api_keys[service_name]
        if len(keys) > 1:
            # نقل المفتاح الأول إلى النهاية
            keys.append(keys.pop(0))
            logger.info(f"🔄 تم تدوير مفاتيح خدمة {service_name}")
    
    def reset_error_keys(self):
        """إعادة تعيين المفاتيح التي بها أخطاء"""
        current_time = time.time()
        
        for service_name, keys in self.api_keys.items():
            for key_info in keys:
                if key_info.status in [ServiceStatus.QUOTA_EXCEEDED, ServiceStatus.RATE_LIMITED]:
                    if key_info.quota_reset_time and current_time > key_info.quota_reset_time:
                        key_info.status = ServiceStatus.ACTIVE
                        key_info.error_count = 0
                        logger.info(f"✅ تم إعادة تفعيل مفتاح {service_name}")
    
    def get_usage_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات الاستخدام"""
        stats = {
            "total_services": len(self.api_keys),
            "total_keys": sum(len(keys) for keys in self.api_keys.values()),
            "active_services": 0,
            "services_detail": {}
        }
        
        for service_name, keys in self.api_keys.items():
            active_keys = [k for k in keys if k.status == ServiceStatus.ACTIVE]
            if active_keys:
                stats["active_services"] += 1
            
            stats["services_detail"][service_name] = {
                "total_keys": len(keys),
                "active_keys": len(active_keys),
                "total_usage": sum(k.usage_count for k in keys),
                "status": "active" if active_keys else "inactive"
            }
        
        return stats

# إنشاء كائن مدير API المحسن
enhanced_api_manager = EnhancedAPIManager()
