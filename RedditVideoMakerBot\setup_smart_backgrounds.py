#!/usr/bin/env python3
"""
إعداد النظام الذكي لمقاطع الخلفية
"""

import json
from pathlib import Path
from smart_background_manager import SmartBackgroundManager

def setup_smart_backgrounds():
    """إعداد النظام الذكي للخلفيات"""
    print("🎬 إعداد النظام الذكي لمقاطع الخلفية")
    print("=" * 45)
    
    manager = SmartBackgroundManager()
    
    # عرض الملفات المتاحة
    manager.list_available_backgrounds()
    
    # تحديث ملف background_videos.json
    print("\n🔄 تحديث قائمة الخلفيات...")
    manager.update_background_videos_json()
    
    # تحديث config.toml لاستخدام ملف محلي إذا كان متاحاً
    update_config_for_local_files(manager)
    
    print("\n✅ تم إعداد النظام الذكي بنجاح!")
    print("💡 البوت سيستخدم الملفات المحلية أولاً قبل التحميل")

def update_config_for_local_files(manager: SmartBackgroundManager):
    """تحديث config.toml لاستخدام الملفات المحلية"""
    try:
        import toml
        
        # قراءة الإعدادات الحالية
        with open("config.toml", "r", encoding="utf-8") as f:
            config = toml.load(f)
        
        current_video = config.get("settings", {}).get("background", {}).get("background_video", "minecraft")
        current_audio = config.get("settings", {}).get("background", {}).get("background_audio", "lofi")
        
        # البحث عن بديل محلي للفيديو
        video_choice, needs_video_download = manager.get_smart_background_choice(current_video)
        
        if not needs_video_download:
            print(f"🎬 تم العثور على فيديو محلي: {video_choice}")
            config["settings"]["background"]["background_video"] = video_choice
        else:
            print(f"⬇️ سيتم تحميل فيديو: {current_video}")
        
        # للصوت، استخدم الصامت إذا لم يكن هناك ملفات صوتية
        audio_files = manager.scan_existing_files()['audio']
        if audio_files and 'silent.mp3' in audio_files:
            print(f"🔇 استخدام صوت صامت محلي")
            config["settings"]["background"]["background_audio"] = "silent"
            config["settings"]["background"]["background_audio_volume"] = 0
        
        # حفظ التحديثات
        with open("config.toml", "w", encoding="utf-8") as f:
            toml.dump(config, f)
        
        print("✅ تم تحديث config.toml")
        
    except Exception as e:
        print(f"⚠️ خطأ في تحديث الإعدادات: {e}")

def create_sample_backgrounds():
    """إنشاء ملفات خلفية تجريبية إذا لم تكن موجودة"""
    print("\n🎨 فحص الملفات التجريبية...")
    
    video_dir = Path("assets/backgrounds/video")
    audio_dir = Path("assets/backgrounds/audio")
    
    # فحص إذا كان هناك ملفات موجودة
    existing_videos = list(video_dir.glob("*.mp4"))
    existing_audios = list(audio_dir.glob("*.mp3"))
    
    if not existing_videos:
        print("📹 لا توجد ملفات فيديو محلية")
        print("💡 يمكنك إضافة ملفات .mp4 إلى مجلد assets/backgrounds/video/")
    else:
        print(f"📹 تم العثور على {len(existing_videos)} ملف فيديو")
    
    if not existing_audios:
        print("🎵 لا توجد ملفات صوت محلية")
        print("💡 يمكنك إضافة ملفات .mp3 إلى مجلد assets/backgrounds/audio/")
    else:
        print(f"🎵 تم العثور على {len(existing_audios)} ملف صوت")

def show_usage_instructions():
    """عرض تعليمات الاستخدام"""
    print("""
📖 تعليمات استخدام النظام الذكي:

1️⃣ إضافة ملفات محلية:
   • ضع ملفات .mp4 في: assets/backgrounds/video/
   • ضع ملفات .mp3 في: assets/backgrounds/audio/

2️⃣ أسماء الملفات المُوصى بها:
   • minecraft-parkour.mp4
   • gta-stunt.mp4
   • rocket-league.mp4
   • fall-guys-gameplay.mp4

3️⃣ تشغيل النظام:
   • python setup_smart_backgrounds.py (لإعداد النظام)
   • python main.py (لتشغيل البوت)

4️⃣ مزايا النظام الذكي:
   ✅ يستخدم الملفات المحلية أولاً
   ✅ يوفر عرض النطاق الترددي
   ✅ أسرع في التشغيل
   ✅ يعمل بدون إنترنت

5️⃣ إذا لم توجد ملفات محلية:
   • سيتم التحميل التلقائي من YouTube
   • يحدث مرة واحدة فقط
   • يتم حفظ الملفات للاستخدام المستقبلي
    """)

if __name__ == "__main__":
    setup_smart_backgrounds()
    create_sample_backgrounds()
    show_usage_instructions()
