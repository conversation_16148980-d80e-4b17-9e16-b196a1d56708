@import url('https://fonts.googleapis.com/css2?family=Exo:wght@400;700&display=swap');


:root {
    --width: 50px;
    --height: 25px;
    --background-color-dark: rgba(0, 0, 0, 0.9);
    --background-color-light: rgba(255, 255, 255, 0.9);
    --text-color-dark: #f2f2f2;
    --text-color-light: #333;
    --nav-color: #f2f2f2;
    --primary-color: #ff0000;
    --secondary-color-dark: #444;
    --secondary-color-light: #ddd;
    --green-color: #28a745;
    --red-color: #dc3545;
}

body {
    /* font-family: monaco, sans-serif; */
    font-family: 'Exo', sans-serif;
    margin: 0;
    padding: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    min-height: 100vh;
    background: url('/static/background.png') no-repeat center center fixed;
    background-size: cover;
    background-color: rgba(0, 0, 0, 0.85);
    background-blend-mode: darken;
    color: var(--text-color-dark);
    transition: background-color 0.3s, color 0.3s;
}

.light-theme {
    --background-color-dark: rgba(255, 255, 255, 0.8);
    /* Light theme background */
    --text-color-dark: #000;
    --secondary-color-dark: #ccc;
}

.dark-theme {
    --background-color-dark: rgba(0, 0, 0, 0.9);
    /* Dark theme background */
    --text-color-dark: #f2f2f2;
    --secondary-color-dark: #444;
}

.navbar {
    width: 100%;
    height: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
    position: fixed;
    top: 0;
    padding: 15px 0;
    z-index: 1000;
    background-color: rgba(0, 0, 0);
}

.navbar-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    max-width: 600px;
    padding: 0 25px;
}

.navbar a,
.navbar span {
    color: var(--nav-color);
    text-decoration: none;
    padding: 10px 15px;
    font-size: 18px;
    transition: color 0.3s, transform 0.3s;
}

.navbar a:hover {
    color: var(--primary-color);
    transform: scale(1.1);
}

/* .navbar .logo {
    font-size: 22px;
    font-weight: bold;
    background: linear-gradient(90deg, white 53%, red 47%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-decoration: none;

} */

.navbar .logo {
    display: flex;
    align-items: center;
    text-decoration: none;
}

.navbar .logo span {
    font-size: 22px;
    font-weight: bold;
    background: linear-gradient(90deg, white 53%, red 47%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.logo-image {
    height: 50px;
    margin-right: 0px; 
}


.theme-toggle-wrapper {
    position: absolute;
    right: 20px;
    top: 24px;
    display: flex;
}

.theme-toggle {
    position: relative;
    width: var(--width);
    height: var(--height);
}

.theme-toggle input[type="checkbox"] {
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0;
    z-index: 1;
    width: var(--width);
    height: var(--height);
}

.theme-toggle .toggle-body {
    position: absolute;
    top: 0;
    left: 0;
    width: var(--width);
    height: var(--height);
    border: 2px solid #080808;
    border-radius: var(--height);
    transition: all 80ms ease-in-out;
}

.theme-toggle input[type="checkbox"]~.toggle-body {
    background: #1d1d1d;
    background-size: cover;
}

.theme-toggle input[type="checkbox"]:checked~.toggle-body {
    background: #fff;
    background-size: cover;
    border-color: #fff;
}

.theme-toggle input[type="checkbox"]~.celestial-body {
    position: absolute;
    width: 20px;
    height: 20px;
    border: 2px solid #fff08e;
    border-radius: 100%;
    background: #fff5c4;
    transition: all 80ms ease-in-out;
}

.theme-toggle input[type="checkbox"]:not(:checked)~.celestial-body {
    top: 3px;
    left: 3px;
}

.theme-toggle input[type="checkbox"]:checked~.celestial-body {
    top: 3px;
    left: calc(100% - 23px);
    background: #ff9900;
    border-color: #ddceb1;
}

.theme-toggle input[type="checkbox"]~.celestial-body::after {
    content: " ";
    opacity: 0;
    position: absolute;
    left: -20px;
    transition: left 0.13s ease-in, opacity 0.15s ease-out;
}

.theme-toggle input[type="checkbox"]:checked~.celestial-body::after {
    content: "";
    position: absolute;
    bottom: -2px;
    left: -10px;
    display: block;
    opacity: 1;
    width: 18px;
    height: 18px;
    background-size: contain;
    background-position: left;
    background-repeat: no-repeat;
}

.container {
    background: var(--background-color-dark);
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    padding: 25px;
    max-width: 600px;
    width: 90%;
    text-align: center;
    margin-top: 100px;
    transition: background-color 0.8s;
}


.container_faq {
    background: var(--background-color-dark);
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    padding: 25px;
    max-width: 800px;
    width: 90%;
    text-align: center;
    margin-top: 100px;
    transition: background-color 0.8s;
}

/* Adjust the style for paragraphs within the container */
.container_faq p {
    font-size: 15px;
    text-align: left;
    margin: 0;
    padding: 0;
    /* line-height: 1.5; */
    max-width: 100%;
    word-wrap: break-word;
}


h1 {
    margin-bottom: 20px;
    font-size: 24px;
    color: var(--primary-color);
}

.description {
    margin-bottom: 20px;
    font-size: 15px;
    color: var(--text-color-dark);
    opacity: 0.9;
}

.input-group {
    /* margin-bottom: 20px; */
    text-align: left;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    margin-bottom: 15px;
}

.input-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

.input-group small {
    display: block;
    margin-bottom: 5px;
    color: var(--text-color-dark);
    opacity: 0.8;
}

.input-group input,
.input-group select {
    width: 100%;
    padding: 10px;
    border: 1px solid #555;
    border-radius: 5px;
    box-sizing: border-box;
    background-color: #222;
    color: var(--text-color-dark);
}

.light-theme .input-group input,
.light-theme .input-group select {
    background-color: #fff;
    color: #000;
}

.input-group input[type="file"] {
    padding: 3px;
}

.switch-case {
    margin-bottom: 20px;
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
}

.switch-case button {
    padding: 10px 20px;
    border: none;
    /* background-color: var(--primary-color); */
    background-color: rgba(255, 0, 0, 0.65);
    color: white;
    border-radius: 5px;
    cursor: pointer;
    margin: 5px;
    transition: background-color 0.3s, transform 0.3s;
}

.switch-case button.active {
    /* background-color: #d00000; */
    background-color: rgba(255, 0, 0);
    transform: scale(1.05);
    transition: background-color 0.3s, transform 0.3s;
}

.switch-case button:hover {
    background-color: #b00000;
    transform: scale(1.05);
}

.actions {
    margin-top: 30px;
    text-align: center;
}

.actions button {
    padding: 10px 30px;
    border: none;
    background-color: var(--green-color);
    color: white;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s, transform 0.3s;
}

.actions button:hover {
    background-color: #218838;
    transform: scale(1.05);
}

.footer {
    margin-top: 40px;
    color: #ccc;
    font-size: 12px;
    text-align: center;
    margin-bottom: 20px;
}

.footer a {
    color: var(--primary-color);
    text-decoration: none;
}

.footer a:hover {
    color: var(--primary-color);
    text-decoration: underline;
}

.footer .heart {
    color: red;
    animation: pulse 1.5s infinite;
}

.alert-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1100;
}

.alert-box {
    background: var(--background-color-dark);
    color: var(--text-color-dark);
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    max-width: 400px;
    width: 90%;
    transition: background-color 0.3s, color 0.3s;
}

.alert-box h2 {
    margin: 0 0 10px;
    font-size: 24px;
    color: var(--primary-color);
}

.alert-box p {
    margin: 0 0 20px;
    font-size: 16px;
}

.alert-box button {
    padding: 10px 20px;
    border: none;
    background-color: var(--primary-color);
    color: white;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s, transform 0.3s;
}

.alert-box button:hover {
    background-color: #d00000;
    transform: scale(1.05);
}

.progress-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1100;
}

.progress-box {
    background: var(--background-color-dark);
    color: var(--text-color-dark);
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    max-width: 500px;
    width: 100%;
    transition: background-color 0.3s, color 0.3s;
    border: 2px solid var(--primary-color);


    /* width: 80%;
    max-width: 600px;
    background: var(--background-color-dark);
    padding: 20px;
    border-radius: 8px; */
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    /* border-radius: 8px; */
    /* position: relative; */

}

.progress-box h2 {
    margin: 0 0 10px;
    font-size: 24px;
    color: var(--primary-color);
}

.progress-box p {
    margin: 0 0 20px;
    font-size: 16px;
}

.progress-animation {
    width: 100%;
    height: 100px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 20px;
    position: relative;
}

.spinner {
    border: 4px solid var(--primary-color);
    border-top: 4px solid var(--text-color-dark);
    border-radius: 50%;
    width: 60px;
    height: 60px;
    animation: spin 4s linear infinite;
    /* Slower animation */
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
}

.progress-percentage {
    position: absolute;
    color: var(--primary-color);
    font-size: 14px;
    font-weight: bold;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

.stats {
    margin-top: 10px;
}

.stats p {
    margin: 5px 0;
    text-align: left;
}

.stats .stat-title {
    font-weight: bold;
    color: var(--primary-color);
}

.hidden {
    display: none;
}


.cancel-button {
    padding: 10px 20px;
    border: none;
    background-color: #ff0000;
    color: white;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s, transform 0.3s;
}

.cancel-button:hover {
    background-color: #d00000;
    transform: scale(1.05);
}


.download-button {
    padding: 10px 20px;
    border: none;
    background-color: var(--green-color);
    color: white;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s, transform 0.3s;
}

.download-button:hover {
    background-color: #218838;
    transform: scale(1.05);
}

.close-button {
    padding: 10px 20px;
    border: none;
    background-color: #ff0000;
    color: white;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s, transform 0.3s;
}

.close-button:hover {
    background-color: #d00000;
    transform: scale(1.05);
}

.preview-container {
    margin-top: 20px;
    padding: 10px;
    background: var(--background-color-light);
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    align-items: center;
    color: var(--text-color-light);

}

.tabs {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
    width: 100%;
}

.tab-button {
    flex: 1;
    background-color: var(--background-color-light);
    border: none;
    cursor: pointer;
    transition: background-color 0.3s;
    border-radius: 4px;
    padding: 10px 20px;
    cursor: pointer;
    border: none;
    margin: 0 5px;
    color: var(--text-color-light);
    flex-grow: 1;
    text-align: center;
}

.tab-button.active {
    background-color: var(--primary-color);
    color: var(--text-color-dark);
}

.preview-content {
    max-height: 250px;
    overflow-y: auto;
    border: 1px solid var(--secondary-color-light);
    padding: 10px;
    background-color: var(--background-color-dark);
    color: var(--text-color-dark);
    border-radius: 8px;
    text-align: left;
}

.preview-content pre {
    white-space: pre-wrap;
    word-wrap: break-word;
}


#previewContainer {
    display: none;
}

#previewContainer.active {
    display: block;
}

.download-emoji {
    bottom: 10px;
    right: 10px;
    font-size: 20px;
    /* background: var(--background-color-light); */
    border: none;
    cursor: pointer;
    color: var(--primary-color);
    /* padding: 0 0; */
}

.download-emoji:hover {
    color: var(--secondary-color-light);
    transform: scale(1.05);
}



.homepage-button {
    padding: 10px 20px;
    border: none;
    background-color: #ff0000;
    color: white;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s, transform 0.3s;
}

.homepage-button:hover {
    background-color: #d00000;
    transform: scale(1.05);
}

.navbar .nav-links {
    display: flex;
    gap: 15px;
    align-items: center;
}

.navbar .menu-toggle {
    display: none;
    font-size: 24px;
    cursor: pointer;
}

.navbar .nav-links .github-button {
    display: inline-block;
    margin-left: 10px;
    vertical-align: middle;
    align-items: center;
}


/* Contact Form Styles */
#contact-form {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.input-group-contact {
    margin: 10px 0;
    width: 90%;
}

input[type="text"],
input[type="email"],
textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 5px;
    background-color: #333;
    color: #ccc;
    box-sizing: border-box;
}

textarea {
    height: 150px;
}

input[type="text"]::placeholder,
input[type="email"]::placeholder,
textarea::placeholder {
    color: #888;
}

input[type="text"]::focus,
input[type="email"]:focus,
textarea:focus {
    border-color: var(--primary-color);
    outline: none;
}

.submit-button {
    background-color: var(--primary-color);
    color: #fff;
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.3s;
}

.submit-button:hover {
    background-color: #b00000;
    transform: scale(1.05);
}


.submit-button i {
    margin-left: 5px;
}

/* Light Theme Adjustments */
body.light-theme input[type="email"],
body.light-theme textarea {
    background-color: #fff;
    color: #333;
    border: 1px solid #ccc;
}


body.light-theme input[type="text"],
body.light-theme textarea {
    background-color: #fff;
    color: #333;
    border: 1px solid #ccc;
}

body.light-theme input[type="email"]:focus,
body.light-theme textarea:focus {
    border-color: var(--primary-color);
    outline: none;
}


body.light-theme input[type="text"]:focus,
body.light-theme textarea:focus {
    border-color: var(--primary-color);
    outline: none;
}

body.light-theme .submit-button {
    background-color: var(--primary-color);
}

body.light-theme .submit-button:hover {
    background-color: #b00000;
    transform: scale(1.05);
}

.table-container {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 20px 0;
}

.model-table {
    width: 80%;
    max-width: 800px;
    border-collapse: collapse;
    margin: 0 auto;
}

.model-table th,
.model-table td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: center;
}

.model-table th {
    background-color: var(--primary-color);
    color: white;
}

.custom-line {
    border: none;
    height: 2px;
    background: linear-gradient(to right, transparent, var(--primary-color), transparent);
    margin: 20px 0;
}


@media (max-width: 768px) {
    .navbar .nav-links {
        display: none;
        flex-direction: column;
        position: absolute;
        top: 60px;
        left: 0;
        width: 100%;
        background-color: var(--background-color-dark);
        z-index: 1000;
    }

    .navbar .menu-toggle {
        display: block;
    }

    .navbar .nav-links.active {
        display: flex;
    }

    .navbar .nav-links a {
        padding: 15px;
        text-align: center;
        border-bottom: 1px solid var(--secondary-color-dark);
    }

    .navbar .nav-links .github-button {
        display: none;

    }
}



@keyframes pulse {
    0% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.1);
    }

    100% {
        transform: scale(1);
    }
}



@media (max-width: 800px) {
    .navbar {
        padding: 10px 0;
    }

    .navbar-content {
        flex-direction: column;
    }

    .navbar a,
    .navbar span {
        padding: 5px;
        font-size: 14px;
    }

    .theme-toggle-wrapper {
        top: 10px;
        right: 10px;
    }

    .container {
        padding: 15px;
        margin-top: 60px;
    }

    .container_faq {
        padding: 15px;
        margin-top: 60px;
    }

    h1 {
        font-size: 20px;
    }

    .description {
        font-size: 12px;
    }

    .actions button {
        padding: 8px 16px;
    }

    .theme-toggle {
        font-size: 18px;
    }
}