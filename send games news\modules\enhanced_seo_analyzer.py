#!/usr/bin/env python3
"""
نظام SEO محسن مع حساب نقاط دقيق
"""

import re
import logging
from typing import Dict, List, Optional
from datetime import datetime

logger = logging.getLogger(__name__)

class EnhancedSEOAnalyzer:
    """محلل SEO محسن مع نقاط أعلى"""
    
    def __init__(self):
        self.min_score_threshold = 60
        self.target_score = 80
    
    def calculate_comprehensive_seo_score(self, article: Dict) -> Dict:
        """حساب نقاط SEO شاملة ومحسنة"""
        try:
            title = article.get('title', '')
            content = article.get('content', '')
            keywords = article.get('keywords', [])
            
            # تحليل مفصل لكل جانب
            title_score = self._analyze_title_seo(title, keywords)
            content_score = self._analyze_content_seo(content, keywords)
            keyword_score = self._analyze_keyword_optimization(title, content, keywords)
            technical_score = self._analyze_technical_seo(article)
            user_experience_score = self._analyze_user_experience(article)
            mobile_score = self._analyze_mobile_optimization(article)
            
            # حساب النقاط الإجمالية مع الأوزان المحسنة
            try:
                from config.settings import SEOConfig
                weights = SEOConfig.SCORE_WEIGHTS
            except:
                # أوزان افتراضية إذا لم توجد الإعدادات
                weights = {
                    'title_optimization': 0.20,
                    'content_quality': 0.25,
                    'keyword_optimization': 0.20,
                    'technical_seo': 0.15,
                    'user_experience': 0.10,
                    'mobile_optimization': 0.10
                }

            total_score = (
                title_score * weights['title_optimization'] +
                content_score * weights['content_quality'] +
                keyword_score * weights['keyword_optimization'] +
                technical_score * weights['technical_seo'] +
                user_experience_score * weights['user_experience'] +
                mobile_score * weights['mobile_optimization']
            )
            
            # تطبيق مكافآت إضافية
            bonus_score = self._calculate_bonus_points(article)
            final_score = min(100, total_score + bonus_score)
            
            return {
                'overall_score': round(final_score, 1),
                'component_scores': {
                    'title_optimization': round(title_score, 1),
                    'content_quality': round(content_score, 1),
                    'keyword_optimization': round(keyword_score, 1),
                    'technical_seo': round(technical_score, 1),
                    'user_experience': round(user_experience_score, 1),
                    'mobile_optimization': round(mobile_score, 1)
                },
                'bonus_points': round(bonus_score, 1),
                'recommendations': self._generate_improvement_recommendations(
                    title_score, content_score, keyword_score, technical_score
                ),
                'analysis_date': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"❌ خطأ في حساب نقاط SEO: {e}")
            return {'overall_score': 50.0, 'error': str(e)}
    
    def _analyze_title_seo(self, title: str, keywords: List[str]) -> float:
        """تحليل SEO العنوان"""
        score = 0.0
        
        if not title:
            return 0.0
        
        # طول العنوان (30-60 حرف مثالي)
        title_length = len(title)
        if 30 <= title_length <= 60:
            score += 30
        elif 20 <= title_length <= 70:
            score += 20
        else:
            score += 10
        
        # وجود كلمات مفتاحية في العنوان
        if keywords:
            keyword_in_title = sum(1 for kw in keywords[:3] if kw.lower() in title.lower())
            score += (keyword_in_title / min(3, len(keywords))) * 25
        
        # وجود أرقام (تزيد الجاذبية)
        if re.search(r'\d+', title):
            score += 10
        
        # وجود كلمات جذابة
        attractive_words = ['أفضل', 'جديد', 'حصري', 'مجاني', 'سري', 'مذهل', 'دليل', 'نصائح']
        if any(word in title for word in attractive_words):
            score += 15
        
        # وجود رموز تعبيرية (مناسبة للألعاب)
        gaming_emojis = ['🎮', '🔥', '⚡', '🚀', '💎', '🏆', '📱', '🎯', '⭐', '🔍']
        if any(emoji in title for emoji in gaming_emojis):
            score += 10
        
        # تجنب الكلمات المحظورة
        spam_words = ['انقر هنا', 'مجاناً 100%', 'احصل على']
        if any(word in title.lower() for word in spam_words):
            score -= 20
        
        return min(100.0, score)
    
    def _analyze_content_seo(self, content: str, keywords: List[str]) -> float:
        """تحليل SEO المحتوى"""
        score = 0.0
        
        if not content:
            return 0.0
        
        word_count = len(content.split())
        
        # طول المحتوى
        if word_count >= 800:
            score += 30
        elif word_count >= 500:
            score += 25
        elif word_count >= 300:
            score += 20
        else:
            score += 10
        
        # كثافة الكلمات المفتاحية
        if keywords and word_count > 0:
            total_keyword_mentions = sum(content.lower().count(kw.lower()) for kw in keywords)
            keyword_density = (total_keyword_mentions / word_count) * 100
            
            if 0.5 <= keyword_density <= 3.0:
                score += 25
            elif keyword_density <= 5.0:
                score += 15
            else:
                score -= 10  # كثافة عالية جداً
        
        # وجود عناوين فرعية
        if re.search(r'(#{1,6}\s|<h[1-6]>)', content):
            score += 15
        
        # وجود قوائم
        if re.search(r'(\*\s|\d+\.\s|<[uo]l>)', content):
            score += 10
        
        # طول الفقرات (تجنب الفقرات الطويلة جداً)
        paragraphs = content.split('\n\n')
        avg_paragraph_length = sum(len(p.split()) for p in paragraphs) / len(paragraphs) if paragraphs else 0
        if 20 <= avg_paragraph_length <= 100:
            score += 10
        
        # وجود دعوة للعمل
        cta_phrases = ['شاركنا رأيك', 'اترك تعليق', 'ما رأيكم', 'جربوا اللعبة']
        if any(phrase in content for phrase in cta_phrases):
            score += 10
        
        return min(100.0, score)
    
    def _analyze_keyword_optimization(self, title: str, content: str, keywords: List[str]) -> float:
        """تحليل تحسين الكلمات المفتاحية"""
        score = 0.0
        
        if not keywords:
            return 20.0  # نقاط أساسية حتى لو لم توجد كلمات مفتاحية
        
        # عدد الكلمات المفتاحية
        keyword_count = len(keywords)
        if 5 <= keyword_count <= 10:
            score += 25
        elif 3 <= keyword_count <= 12:
            score += 20
        else:
            score += 10
        
        # توزيع الكلمات المفتاحية
        title_keywords = sum(1 for kw in keywords if kw.lower() in title.lower())
        content_keywords = sum(1 for kw in keywords if kw.lower() in content.lower())
        
        # نسبة الكلمات المفتاحية الموجودة
        if keyword_count > 0:
            title_ratio = title_keywords / keyword_count
            content_ratio = content_keywords / keyword_count
            
            score += title_ratio * 25  # 25 نقطة للعنوان
            score += content_ratio * 30  # 30 نقطة للمحتوى
        
        # تنوع الكلمات المفتاحية
        unique_keywords = set(kw.lower() for kw in keywords)
        if len(unique_keywords) == len(keywords):
            score += 10  # مكافأة للتنوع
        
        # كلمات مفتاحية طويلة الذيل
        long_tail_keywords = [kw for kw in keywords if len(kw.split()) >= 3]
        if long_tail_keywords:
            score += len(long_tail_keywords) * 2
        
        return min(100.0, score)
    
    def _analyze_technical_seo(self, article: Dict) -> float:
        """تحليل SEO التقني"""
        score = 50.0  # نقاط أساسية
        
        # وجود وصف قصير
        if article.get('summary'):
            summary_length = len(article['summary'])
            if 120 <= summary_length <= 160:
                score += 20
            elif summary_length > 0:
                score += 10
        
        # وجود صور
        if article.get('image_urls') or article.get('images'):
            score += 15
        
        # وجود تصنيف
        if article.get('category'):
            score += 10
        
        # وجود تاريخ النشر
        if article.get('published_date') or article.get('created_at'):
            score += 10
        
        # طول URL (محاكاة)
        title = article.get('title', '')
        if title:
            url_length = len(title.replace(' ', '-').lower())
            if url_length <= 60:
                score += 15
        
        return min(100.0, score)
    
    def _analyze_user_experience(self, article: Dict) -> float:
        """تحليل تجربة المستخدم"""
        score = 60.0  # نقاط أساسية جيدة
        
        content = article.get('content', '')
        
        # سهولة القراءة
        if content:
            sentences = content.split('.')
            avg_sentence_length = sum(len(s.split()) for s in sentences) / len(sentences) if sentences else 0
            
            if 10 <= avg_sentence_length <= 20:
                score += 20
            elif avg_sentence_length <= 25:
                score += 15
        
        # وجود محتوى تفاعلي
        interactive_elements = ['فيديو', 'صورة', 'رابط', 'تحميل']
        if any(element in content for element in interactive_elements):
            score += 10
        
        # جودة التنسيق
        if article.get('formatted_content') or '\n' in content:
            score += 10
        
        return min(100.0, score)
    
    def _analyze_mobile_optimization(self, article: Dict) -> float:
        """تحليل تحسين الموبايل"""
        # افتراض تحسين جيد للموبايل
        return 85.0
    
    def _calculate_bonus_points(self, article: Dict) -> float:
        """حساب نقاط المكافآت"""
        bonus = 0.0
        
        # مكافأة للمحتوى الحديث
        if article.get('published_date'):
            try:
                from datetime import datetime, timedelta
                pub_date = datetime.fromisoformat(article['published_date'].replace('Z', '+00:00'))
                if datetime.now() - pub_date < timedelta(days=7):
                    bonus += 5  # محتوى حديث
            except:
                pass
        
        # مكافأة للمحتوى عالي الجودة
        if article.get('quality_score', 0) > 80:
            bonus += 3
        
        # مكافأة للمحتوى الشامل
        content_length = len(article.get('content', ''))
        if content_length > 1500:
            bonus += 2
        
        return bonus
    
    def _generate_improvement_recommendations(self, title_score: float, content_score: float, 
                                           keyword_score: float, technical_score: float) -> List[str]:
        """توليد توصيات التحسين"""
        recommendations = []
        
        if title_score < 70:
            recommendations.append("حسن العنوان: أضف كلمات مفتاحية وتأكد من الطول المناسب (30-60 حرف)")
        
        if content_score < 70:
            recommendations.append("حسن المحتوى: أضف المزيد من التفاصيل وعناوين فرعية وقوائم")
        
        if keyword_score < 70:
            recommendations.append("حسن الكلمات المفتاحية: أضف 5-10 كلمات مفتاحية ووزعها في المحتوى")
        
        if technical_score < 70:
            recommendations.append("حسن الجوانب التقنية: أضف وصف قصير وصور وتصنيف مناسب")
        
        if not recommendations:
            recommendations.append("المحتوى محسن بشكل جيد! استمر في هذا المستوى")
        
        return recommendations

# إنشاء مثيل عام
enhanced_seo_analyzer = EnhancedSEOAnalyzer()
