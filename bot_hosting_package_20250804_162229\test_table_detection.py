#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام كشف الجداول المحسن
Test improved table detection system
"""

import os
import sys
import logging
from datetime import datetime

# تحميل متغيرات البيئة
from dotenv import load_dotenv
load_dotenv()

# إعداد التسجيل
logging.basicConfig(
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    level=logging.INFO,
    datefmt="%Y-%m-%d %H:%M:%S",
)
logger = logging.getLogger(__name__)

def test_table_detection():
    """اختبار نظام كشف الجداول"""
    logger.info("🔍 اختبار نظام كشف الجداول المحسن...")
    
    try:
        # استيراد الدوال المطلوبة
        from supabase_client import check_required_tables, create_specific_missing_tables
        
        # 1. فحص الجداول الحالية
        logger.info("📋 فحص الجداول الحالية...")
        tables_status = check_required_tables()
        
        # عرض النتائج
        logger.info("📊 نتائج الفحص:")
        logger.info(f"   📈 إجمالي الجداول المطلوبة: {tables_status['total']}")
        logger.info(f"   ✅ الجداول الموجودة: {tables_status['existing_count']}")
        logger.info(f"   ❌ الجداول المفقودة: {tables_status['missing_count']}")
        
        if tables_status['existing']:
            logger.info(f"   📋 الجداول الموجودة: {', '.join(tables_status['existing'])}")
        
        if tables_status['missing']:
            logger.info(f"   📋 الجداول المفقودة: {', '.join(tables_status['missing'])}")
        
        # 2. اختبار إنشاء الجداول المفقودة
        if tables_status['missing']:
            logger.info("🔧 اختبار إنشاء الجداول المفقودة...")
            success = create_specific_missing_tables(tables_status['missing'])
            
            if success:
                logger.info("✅ تم إنشاء الجداول المفقودة بنجاح")
                
                # إعادة فحص الجداول
                logger.info("🔄 إعادة فحص الجداول بعد الإنشاء...")
                new_status = check_required_tables()
                
                if new_status['all_exist']:
                    logger.info("🎉 جميع الجداول موجودة الآن!")
                else:
                    logger.warning(f"⚠️ ما زالت هناك جداول مفقودة: {', '.join(new_status['missing'])}")
            else:
                logger.warning("⚠️ فشل في إنشاء بعض الجداول")
        else:
            logger.info("✅ جميع الجداول موجودة بالفعل")
        
        return tables_status['all_exist']
        
    except ImportError as e:
        logger.error(f"❌ خطأ في الاستيراد: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ خطأ في اختبار كشف الجداول: {e}")
        return False

def test_main_integration():
    """اختبار التكامل مع main.py"""
    logger.info("🔗 اختبار التكامل مع main.py...")
    
    try:
        # محاكاة الكود الموجود في main.py
        from supabase_client import check_required_tables, create_specific_missing_tables
        
        logger.info("🔧 التحقق من الجداول المطلوبة...")
        tables_status = check_required_tables()
        
        if tables_status['all_exist']:
            logger.info("✅ جميع الجداول المطلوبة موجودة")
            return True
        else:
            missing_tables = tables_status['missing']
            logger.warning(f"⚠️ الجداول المفقودة: {', '.join(missing_tables)}")
            
            # محاولة إنشاء الجداول المفقودة فقط
            if create_specific_missing_tables(missing_tables):
                logger.info("✅ تم إنشاء الجداول المفقودة بنجاح")
                return True
            else:
                logger.warning("⚠️ فشل في إنشاء بعض الجداول، راجع ملفات SQL المُنشأة")
                return False
                
    except Exception as e:
        logger.error(f"❌ خطأ في اختبار التكامل: {e}")
        return False

def test_performance():
    """اختبار أداء النظام"""
    logger.info("⚡ اختبار أداء النظام...")
    
    try:
        import time
        from supabase_client import check_required_tables
        
        # قياس وقت فحص الجداول
        start_time = time.time()
        tables_status = check_required_tables()
        end_time = time.time()
        
        duration = end_time - start_time
        logger.info(f"⏱️ وقت فحص الجداول: {duration:.2f} ثانية")
        
        if duration < 5.0:
            logger.info("✅ الأداء ممتاز (أقل من 5 ثوان)")
            return True
        elif duration < 10.0:
            logger.info("⚠️ الأداء مقبول (5-10 ثوان)")
            return True
        else:
            logger.warning("❌ الأداء بطيء (أكثر من 10 ثوان)")
            return False
            
    except Exception as e:
        logger.error(f"❌ خطأ في اختبار الأداء: {e}")
        return False

def generate_status_report():
    """إنشاء تقرير حالة شامل"""
    logger.info("📄 إنشاء تقرير حالة شامل...")
    
    try:
        from supabase_client import check_required_tables
        
        tables_status = check_required_tables()
        
        report = f"""
# 📊 تقرير حالة الجداول
# Tables Status Report

**تاريخ التقرير:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 📈 الإحصائيات العامة

- **إجمالي الجداول المطلوبة:** {tables_status['total']}
- **الجداول الموجودة:** {tables_status['existing_count']}
- **الجداول المفقودة:** {tables_status['missing_count']}
- **نسبة الاكتمال:** {(tables_status['existing_count'] / tables_status['total'] * 100):.1f}%

## ✅ الجداول الموجودة

"""
        
        if tables_status['existing']:
            for table in tables_status['existing']:
                report += f"- ✅ `{table}`\n"
        else:
            report += "- لا توجد جداول موجودة\n"
        
        report += "\n## ❌ الجداول المفقودة\n\n"
        
        if tables_status['missing']:
            for table in tables_status['missing']:
                report += f"- ❌ `{table}`\n"
        else:
            report += "- لا توجد جداول مفقودة\n"
        
        report += f"""
## 🎯 التوصيات

"""
        
        if tables_status['all_exist']:
            report += "✅ **جميع الجداول موجودة** - النظام جاهز للعمل!\n"
        else:
            report += f"""❌ **يوجد {tables_status['missing_count']} جدول مفقود**

### خطوات الحل:

1. شغل الأمر التالي:
   ```bash
   python test_table_detection.py
   ```

2. أو قم بتنفيذ ملف SQL في Supabase:
   - افتح Supabase Dashboard
   - اذهب إلى SQL Editor
   - نفذ أحد ملفات SQL الموجودة

3. أعد تشغيل البوت:
   ```bash
   python main.py
   ```
"""
        
        # حفظ التقرير
        with open('TABLES_STATUS_REPORT.md', 'w', encoding='utf-8') as f:
            f.write(report)
        
        logger.info("✅ تم إنشاء تقرير الحالة: TABLES_STATUS_REPORT.md")
        return True
        
    except Exception as e:
        logger.error(f"❌ خطأ في إنشاء تقرير الحالة: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔍 اختبار نظام كشف الجداول المحسن")
    print("=" * 60)
    
    tests = [
        ("كشف الجداول", test_table_detection),
        ("التكامل مع main.py", test_main_integration),
        ("الأداء", test_performance),
        ("تقرير الحالة", generate_status_report)
    ]
    
    success_count = 0
    for test_name, test_func in tests:
        logger.info(f"\n🧪 اختبار: {test_name}")
        try:
            if test_func():
                logger.info(f"✅ {test_name}: نجح")
                success_count += 1
            else:
                logger.warning(f"⚠️ {test_name}: فشل")
        except Exception as e:
            logger.error(f"❌ {test_name}: خطأ - {e}")
    
    # النتائج النهائية
    print("\n" + "=" * 60)
    print(f"📊 النتائج: {success_count}/{len(tests)} اختبارات نجحت")
    
    if success_count == len(tests):
        print("🎉 جميع الاختبارات نجحت!")
        print("📋 النظام المحسن يعمل بشكل مثالي")
    elif success_count > 0:
        print("⚠️ بعض الاختبارات نجحت")
        print("📋 راجع تقرير الحالة للتفاصيل")
    else:
        print("❌ فشلت جميع الاختبارات")
        print("🔧 تحقق من إعدادات Supabase")
    
    return success_count > 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
