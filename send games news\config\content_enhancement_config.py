# إعدادات تحسين المحتوى
import os

# إعدادات الروابط الداخلية
INTERNAL_LINKS_CONFIG = {
    'enabled': True,
    'max_links_per_article': 10,
    'link_density_threshold': 0.05,  # 5% من الكلمات كحد أقصى
    'avoid_duplicate_links': True,
    'link_first_occurrence_only': True,
    'exclude_patterns': [
        r'<a\s+[^>]*href',  # تجنب الروابط الموجودة
        r'<img\s+[^>]*src',  # تجنب الصور
        r'<code[^>]*>.*?</code>',  # تجنب الكود
        r'<pre[^>]*>.*?</pre>'  # تجنب النصوص المنسقة مسبقاً
    ]
}

# إعدادات الكلمات المفتاحية
KEYWORDS_CONFIG = {
    'enabled': True,
    'max_keywords': 15,
    'min_keyword_length': 3,
    'include_base_gaming_keywords': True,
    'auto_detect_from_content': True,
    'prioritize_english_keywords': True,
    'keywords_section_style': {
        'background': 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        'border_radius': '10px',
        'padding': '20px',
        'margin_top': '30px',
        'text_color': 'white',
        'icon': '🔍'
    }
}

# إعدادات الكتابة الطبيعية
NATURAL_WRITING_CONFIG = {
    'enabled': True,
    'error_rate': 0.25,  # 25% من الكلمات قد تحتوي على تغييرات
    'dialect_preferences': {
        'egyptian': {
            'weight': 0.4,
            'common_expressions': True,
            'spelling_variations': True
        },
        'saudi': {
            'weight': 0.3,
            'common_expressions': True,
            'spelling_variations': True
        },
        'standard': {
            'weight': 0.3,
            'common_expressions': False,
            'spelling_variations': True
        }
    },
    'natural_transitions': {
        'enabled': True,
        'frequency': 0.15,  # 15% من الجمل
        'expressions': [
            'وبكده', 'وكده', 'يعني', 'بصراحة', 'الحقيقة',
            'في الواقع', 'بالمناسبة', 'عموماً', 'على فكرة',
            'مش كده وبس', 'وده مش كل حاجة', 'وفي الآخر',
            'في النهاية', 'خلاصة الكلام', 'المهم', 'على العموم'
        ]
    },
    'punctuation_variation': {
        'enabled': True,
        'frequency': 0.1,  # 10% من علامات الترقيم
        'mix_arabic_english': True
    }
}

# إعدادات اللهجات المدعومة
SUPPORTED_DIALECTS = {
    'egyptian': {
        'name': 'المصرية',
        'description': 'اللهجة المصرية العامية',
        'sample_text': 'ازيك؟ ايه اخبار الألعاب النهاردة؟',
        'common_words': {
            'كيف': 'ازاي',
            'ماذا': 'ايه',
            'أين': 'فين',
            'متى': 'امتى',
            'نحن': 'احنا',
            'أنتم': 'انتو'
        }
    },
    'saudi': {
        'name': 'السعودية',
        'description': 'اللهجة السعودية العامية',
        'sample_text': 'وش اخبارك؟ وش جديد الألعاب اليوم؟',
        'common_words': {
            'ماذا': 'وش',
            'أين': 'وين',
            'لماذا': 'ليش',
            'كثيراً': 'وايد',
            'جداً': 'مره',
            'أريد': 'ابغى'
        }
    },
    'standard': {
        'name': 'الفصحى المبسطة',
        'description': 'العربية الفصحى مع أخطاء إملائية بسيطة',
        'sample_text': 'مرحبا، ما اخبار الألعاب اليوم؟',
        'common_words': {
            'هذا': 'هاذا',
            'التي': 'الي',
            'لأن': 'عشان',
            'جداً': 'جدا'
        }
    }
}

# إعدادات تحليل المحتوى
CONTENT_ANALYSIS_CONFIG = {
    'detect_content_type': True,
    'content_types': {
        'news': {
            'keywords': ['أخبار', 'إعلان', 'تطوير', 'إصدار', 'تحديث'],
            'style': 'إخباري',
            'tone': 'رسمي مع لمسة عامية'
        },
        'review': {
            'keywords': ['مراجعة', 'تقييم', 'رأي', 'تجربة', 'نقد'],
            'style': 'تحليلي',
            'tone': 'شخصي ومفصل'
        },
        'guide': {
            'keywords': ['دليل', 'شرح', 'كيفية', 'طريقة', 'نصائح'],
            'style': 'تعليمي',
            'tone': 'ودود ومساعد'
        },
        'update': {
            'keywords': ['تحديث', 'باتش', 'إصلاح', 'تطوير', 'جديد'],
            'style': 'إعلامي',
            'tone': 'مباشر ومفيد'
        }
    },
    'auto_adjust_style': True
}

# إعدادات الجودة والتحقق
QUALITY_CONTROL_CONFIG = {
    'min_content_length': 500,  # الحد الأدنى لطول المحتوى
    'max_content_length': 3000,  # الحد الأقصى لطول المحتوى
    'min_internal_links': 2,  # الحد الأدنى للروابط الداخلية
    'max_internal_links': 8,  # الحد الأقصى للروابط الداخلية
    'min_keywords': 5,  # الحد الأدنى للكلمات المفتاحية
    'max_keywords': 15,  # الحد الأقصى للكلمات المفتاحية
    'check_readability': True,
    'check_seo_compliance': True,
    'validate_html': True
}

# إعدادات التخصيص حسب نوع المحتوى
CONTENT_TYPE_CUSTOMIZATION = {
    'gaming_news': {
        'required_elements': ['title', 'summary', 'details', 'impact'],
        'preferred_keywords': [
            'ألعاب الفيديو', 'gaming news', 'أخبار الألعاب',
            'إصدارات جديدة', 'تحديثات الألعاب'
        ],
        'internal_links_focus': ['companies', 'games', 'platforms'],
        'tone': 'إخباري مع حماس'
    },
    'game_reviews': {
        'required_elements': ['gameplay', 'graphics', 'story', 'rating'],
        'preferred_keywords': [
            'مراجعة لعبة', 'game review', 'تقييم الألعاب',
            'gameplay', 'graphics', 'story'
        ],
        'internal_links_focus': ['games', 'platforms', 'companies'],
        'tone': 'تحليلي وشخصي'
    },
    'gaming_guides': {
        'required_elements': ['introduction', 'steps', 'tips', 'conclusion'],
        'preferred_keywords': [
            'دليل الألعاب', 'gaming guide', 'نصائح الألعاب',
            'gaming tips', 'walkthrough'
        ],
        'internal_links_focus': ['games', 'platforms'],
        'tone': 'تعليمي ومساعد'
    }
}

# إعدادات الأداء والتحسين
PERFORMANCE_CONFIG = {
    'cache_keywords': True,
    'cache_internal_links': True,
    'cache_duration_hours': 24,
    'batch_processing': True,
    'async_processing': True,
    'max_concurrent_requests': 5,
    'timeout_seconds': 30
}

# إعدادات التسجيل والمراقبة
LOGGING_CONFIG = {
    'log_enhancements': True,
    'log_level': 'INFO',
    'track_enhancement_stats': True,
    'save_enhancement_history': True,
    'generate_enhancement_reports': True
}

# إعدادات التجريب والاختبار
EXPERIMENTAL_CONFIG = {
    'enable_ai_style_detection': True,
    'enable_advanced_nlp': False,
    'enable_sentiment_analysis': False,
    'enable_topic_modeling': False,
    'a_b_testing': {
        'enabled': False,
        'test_groups': ['control', 'enhanced'],
        'metrics': ['engagement', 'readability', 'seo_score']
    }
}

# دالة للحصول على إعدادات مخصصة
def get_enhancement_config(content_type: str = 'gaming_news', 
                          dialect: str = 'standard') -> dict:
    """الحصول على إعدادات التحسين المخصصة"""
    
    config = {
        'internal_links': INTERNAL_LINKS_CONFIG,
        'keywords': KEYWORDS_CONFIG,
        'natural_writing': NATURAL_WRITING_CONFIG,
        'quality_control': QUALITY_CONTROL_CONFIG,
        'performance': PERFORMANCE_CONFIG
    }
    
    # تخصيص حسب نوع المحتوى
    if content_type in CONTENT_TYPE_CUSTOMIZATION:
        config['content_customization'] = CONTENT_TYPE_CUSTOMIZATION[content_type]
    
    # تخصيص حسب اللهجة
    if dialect in SUPPORTED_DIALECTS:
        config['dialect_config'] = SUPPORTED_DIALECTS[dialect]
    
    return config

# دالة للتحقق من صحة الإعدادات
def validate_config() -> bool:
    """التحقق من صحة إعدادات التحسين"""
    try:
        # فحص الإعدادات الأساسية
        assert KEYWORDS_CONFIG['max_keywords'] > 0
        assert NATURAL_WRITING_CONFIG['error_rate'] <= 1.0
        assert INTERNAL_LINKS_CONFIG['max_links_per_article'] > 0
        
        # فحص اللهجات المدعومة
        assert len(SUPPORTED_DIALECTS) > 0
        
        # فحص إعدادات الجودة
        assert QUALITY_CONTROL_CONFIG['min_content_length'] > 0
        assert QUALITY_CONTROL_CONFIG['max_content_length'] > QUALITY_CONTROL_CONFIG['min_content_length']
        
        return True
        
    except AssertionError:
        return False

# تحميل الإعدادات من متغيرات البيئة إذا كانت متوفرة
def load_env_config():
    """تحميل الإعدادات من متغيرات البيئة"""
    
    # تحديث إعدادات الكلمات المفتاحية
    if os.getenv('MAX_KEYWORDS'):
        KEYWORDS_CONFIG['max_keywords'] = int(os.getenv('MAX_KEYWORDS'))
    
    # تحديث إعدادات الكتابة الطبيعية
    if os.getenv('NATURAL_WRITING_ERROR_RATE'):
        NATURAL_WRITING_CONFIG['error_rate'] = float(os.getenv('NATURAL_WRITING_ERROR_RATE'))
    
    # تحديث إعدادات الروابط الداخلية
    if os.getenv('MAX_INTERNAL_LINKS'):
        INTERNAL_LINKS_CONFIG['max_links_per_article'] = int(os.getenv('MAX_INTERNAL_LINKS'))

# تحميل الإعدادات عند استيراد الملف
load_env_config()

# التحقق من صحة الإعدادات
if not validate_config():
    print("⚠️ تحذير: توجد مشاكل في إعدادات التحسين")
