# مدير البحث المحسن مع النماذج الاحتياطية
import asyncio
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from enum import Enum
import json

from .logger import logger
from .fallback_ai_manager import fallback_ai_manager, SearchRequest, AIModelType
from .advanced_search_manager import advanced_search_manager
from .tavily_search import tavily_search
from .serpapi_search import serpapi_search
from .smart_search_manager import smart_search_manager


class SearchStrategy(Enum):
    """استراتيجيات البحث المختلفة"""
    TRADITIONAL_WEB = "traditional_web"      # البحث التقليدي على الويب
    AI_ENHANCED = "ai_enhanced"              # البحث المعزز بالذكاء الاصطناعي
    FALLBACK_AI = "fallback_ai"              # النماذج الاحتياطية
    HYBRID = "hybrid"                        # مزيج من جميع الطرق

class SearchPriority(Enum):
    """أولويات البحث"""
    HIGH_QUALITY = 1    # جودة عالية (بطيء لكن دقيق)
    BALANCED = 2        # متوازن (جودة ومعقولة وسرعة)
    FAST = 3           # سريع (أولوية للسرعة)
    EMERGENCY = 4      # طوارئ (أي نتيجة متاحة)

class EnhancedSearchManager:
    """مدير البحث المحسن مع النماذج الاحتياطية"""
    
    def __init__(self):
        self.search_stats = {
            'total_searches': 0,
            'successful_searches': 0,
            'failed_searches': 0,
            'strategy_usage': {},
            'average_response_time': 0,
            'last_reset': datetime.now().date()
        }
        
        # إعدادات استراتيجيات البحث
        self.strategy_config = {
            SearchStrategy.TRADITIONAL_WEB: {
                'timeout': 30,
                'max_retries': 2,
                'quality_threshold': 0.6
            },
            SearchStrategy.AI_ENHANCED: {
                'timeout': 45,
                'max_retries': 3,
                'quality_threshold': 0.8
            },
            SearchStrategy.FALLBACK_AI: {
                'timeout': 60,
                'max_retries': 1,
                'quality_threshold': 0.5
            },
            SearchStrategy.HYBRID: {
                'timeout': 90,
                'max_retries': 2,
                'quality_threshold': 0.7
            }
        }
        
        # ترتيب الأولوية للاستراتيجيات
        self.strategy_priority = {
            SearchPriority.HIGH_QUALITY: [
                SearchStrategy.AI_ENHANCED,
                SearchStrategy.HYBRID,
                SearchStrategy.TRADITIONAL_WEB,
                SearchStrategy.FALLBACK_AI
            ],
            SearchPriority.BALANCED: [
                SearchStrategy.HYBRID,
                SearchStrategy.AI_ENHANCED,
                SearchStrategy.TRADITIONAL_WEB,
                SearchStrategy.FALLBACK_AI
            ],
            SearchPriority.FAST: [
                SearchStrategy.TRADITIONAL_WEB,
                SearchStrategy.FALLBACK_AI,
                SearchStrategy.AI_ENHANCED,
                SearchStrategy.HYBRID
            ],
            SearchPriority.EMERGENCY: [
                SearchStrategy.FALLBACK_AI,
                SearchStrategy.TRADITIONAL_WEB,
                SearchStrategy.AI_ENHANCED,
                SearchStrategy.HYBRID
            ]
        }
    
    async def comprehensive_search(self, 
                                 query: str, 
                                 max_results: int = 10,
                                 priority: SearchPriority = SearchPriority.BALANCED,
                                 include_ai_analysis: bool = True) -> Dict:
        """البحث الشامل مع النماذج الاحتياطية"""
        
        search_start_time = time.time()
        self.search_stats['total_searches'] += 1
        
        logger.info(f"🔍 بدء البحث الشامل المحسن: {query}")
        logger.info(f"📊 الأولوية: {priority.name}, النتائج المطلوبة: {max_results}")
        
        # الحصول على استراتيجيات البحث حسب الأولوية
        strategies = self.strategy_priority[priority]
        
        best_result = None
        all_results = []
        strategy_results = {}
        
        for strategy in strategies:
            try:
                logger.info(f"🎯 تجربة استراتيجية: {strategy.value}")
                
                result = await self._execute_search_strategy(
                    strategy, query, max_results, include_ai_analysis
                )
                
                if result and result.get('success'):
                    strategy_results[strategy.value] = result
                    all_results.extend(result.get('results', []))
                    
                    # تقييم جودة النتيجة
                    quality_score = self._evaluate_result_quality(result)
                    result['quality_score'] = quality_score
                    
                    logger.info(f"✅ نجحت استراتيجية {strategy.value} - جودة: {quality_score:.2f}")
                    
                    # إذا كانت الجودة عالية بما فيه الكفاية، استخدم هذه النتيجة
                    quality_threshold = self.strategy_config[strategy]['quality_threshold']
                    if quality_score >= quality_threshold:
                        best_result = result
                        break
                    
                    # احتفظ بأفضل نتيجة حتى الآن
                    if not best_result or quality_score > best_result.get('quality_score', 0):
                        best_result = result
                
                else:
                    logger.warning(f"⚠️ فشلت استراتيجية {strategy.value}")
                    
            except Exception as e:
                logger.error(f"❌ خطأ في استراتيجية {strategy.value}: {e}")
                continue
        
        # إنشاء النتيجة النهائية
        search_end_time = time.time()
        total_time = search_end_time - search_start_time
        
        if best_result:
            self.search_stats['successful_searches'] += 1
            self._update_strategy_usage(best_result.get('strategy', 'unknown'))
            
            final_result = {
                'success': True,
                'query': query,
                'best_strategy': best_result.get('strategy'),
                'total_results': len(all_results),
                'unique_results': len(self._deduplicate_results(all_results)),
                'search_time': total_time,
                'quality_score': best_result.get('quality_score', 0),
                'results': self._deduplicate_results(all_results)[:max_results],
                'strategy_breakdown': strategy_results,
                'timestamp': datetime.now()
            }
            
            # إضافة تحليل AI إذا طُلب
            if include_ai_analysis and best_result.get('ai_analysis'):
                final_result['ai_analysis'] = best_result['ai_analysis']
            
            logger.info(f"🎉 البحث الشامل نجح في {total_time:.2f} ثانية")
            logger.info(f"📈 الجودة: {final_result['quality_score']:.2f}, النتائج: {final_result['unique_results']}")
            
            return final_result
        
        else:
            self.search_stats['failed_searches'] += 1
            logger.error(f"❌ فشل البحث الشامل بعد تجربة جميع الاستراتيجيات")
            
            return {
                'success': False,
                'query': query,
                'error': 'جميع استراتيجيات البحث فشلت',
                'search_time': total_time,
                'strategies_tried': [s.value for s in strategies],
                'timestamp': datetime.now()
            }
    
    async def _execute_search_strategy(self, 
                                     strategy: SearchStrategy, 
                                     query: str, 
                                     max_results: int,
                                     include_ai_analysis: bool) -> Optional[Dict]:
        """تنفيذ استراتيجية بحث محددة"""
        
        config = self.strategy_config[strategy]
        
        try:
            if strategy == SearchStrategy.TRADITIONAL_WEB:
                return await self._traditional_web_search(query, max_results, config)
            
            elif strategy == SearchStrategy.AI_ENHANCED:
                return await self._ai_enhanced_search(query, max_results, config, include_ai_analysis)
            
            elif strategy == SearchStrategy.FALLBACK_AI:
                return await self._fallback_ai_search(query, max_results, config)
            
            elif strategy == SearchStrategy.HYBRID:
                return await self._hybrid_search(query, max_results, config, include_ai_analysis)
            
        except asyncio.TimeoutError:
            logger.warning(f"⏰ انتهت مهلة استراتيجية {strategy.value}")
            return None
        except Exception as e:
            logger.error(f"❌ خطأ في تنفيذ استراتيجية {strategy.value}: {e}")
            return None
    
    async def _traditional_web_search(self, query: str, max_results: int, config: Dict) -> Optional[Dict]:
        """البحث التقليدي على الويب مع Gemini 2.5 Pro كبديل لـ Tavily"""
        results = []

        # محاولة Tavily أولاً
        if tavily_search.enabled:
            try:
                tavily_results = await tavily_search.search(
                    query=f"{query} gaming news",
                    search_depth="standard",
                    max_results=max_results // 2
                )
                if tavily_results:
                    results.extend(tavily_results)
                    logger.info(f"✅ Tavily: {len(tavily_results)} نتيجة")
            except Exception as e:
                logger.warning(f"⚠️ فشل Tavily: {e}")

        # إذا فشل Tavily أو لم يعطِ نتائج كافية، استخدم النماذج الاحتياطية
        if len(results) < max_results // 2:
            try:
                # جرب النماذج الاحتياطية
                logger.info("🔄 محاولة استخدام النماذج الاحتياطية...")
                from modules.fallback_ai_manager import fallback_ai_manager
                fallback_result = await fallback_ai_manager.search_with_fallback(query)
                if fallback_result and fallback_result.get('success'):
                    # تحويل نتيجة النموذج الاحتياطي إلى تنسيق النتائج
                    fallback_content = fallback_result.get('content', '')
                    if fallback_content:
                        fallback_converted = {
                            'title': f"تحليل AI: {query}",
                            'content': fallback_content,
                            'url': f"ai://fallback-{fallback_result.get('model_type', 'unknown')}",
                            'source': fallback_result.get('source', 'AI Fallback'),
                            'relevance_score': 0.7,
                            'search_engine': 'AI Fallback Models',
                            'has_web_search': fallback_result.get('supports_web_search', False),
                            'timestamp': fallback_result.get('timestamp'),
                            'metadata': {'fallback_mode': True}
                        }
                        results.append(fallback_converted)
                        logger.info(f"✅ النماذج الاحتياطية: 1 نتيجة من {fallback_result.get('source')}")
            except Exception as fallback_error:
                logger.warning(f"⚠️ فشلت النماذج الاحتياطية أيضاً: {fallback_error}")

        # محاولة SerpAPI كاحتياط
        if len(results) < max_results // 2 and serpapi_search.enabled:
            try:
                serpapi_results = await serpapi_search.search_gaming_news(
                    query, max_results=max_results // 2
                )
                if serpapi_results:
                    results.extend(serpapi_results)
                    logger.info(f"✅ SerpAPI: {len(serpapi_results)} نتيجة")
            except Exception as e:
                logger.warning(f"⚠️ فشل SerpAPI: {e}")

        # محاولة البحث المتقدم كاحتياط أخير
        if len(results) < 3:
            try:
                advanced_results = await advanced_search_manager.search_with_fallback(
                    query, max_results=max_results
                )
                if advanced_results:
                    results.extend(advanced_results)
                    logger.info(f"✅ البحث المتقدم: {len(advanced_results)} نتيجة")
            except Exception as e:
                logger.warning(f"⚠️ فشل البحث المتقدم: {e}")

        if results:
            # تحديد الطريقة المستخدمة
            methods_used = []
            if any('tavily' in str(result.get('source', '')).lower() for result in results):
                methods_used.append('Tavily')
            if any('gemini' in str(result.get('source', '')).lower() for result in results):
                methods_used.append('Gemini 2.5 Pro')
            if any('serpapi' in str(result.get('source', '')).lower() for result in results):
                methods_used.append('SerpAPI')
            if any('advanced' in str(result.get('source', '')).lower() for result in results):
                methods_used.append('Advanced Search')

            return {
                'success': True,
                'strategy': 'traditional_web',
                'results': results,
                'source': 'Enhanced Web Search with Gemini 2.5 Pro',
                'method': ' + '.join(methods_used) if methods_used else 'Mixed Sources'
            }

        return None

    async def _ai_enhanced_search(self, query: str, max_results: int, config: Dict, include_ai_analysis: bool) -> Optional[Dict]:
        """البحث المعزز بالذكاء الاصطناعي"""
        results = []
        ai_analysis = None

        # أولاً: البحث التقليدي للحصول على البيانات الأساسية
        web_results = await self._traditional_web_search(query, max_results // 2, config)
        if web_results and web_results.get('results'):
            results.extend(web_results['results'])

        # ثانياً: تحليل AI للنتائج إذا طُلب
        if include_ai_analysis:
            try:
                search_request = SearchRequest(
                    query=f"تحليل وتلخيص أخبار الألعاب: {query}",
                    max_results=3,
                    search_depth="deep",
                    include_web_search=True
                )

                ai_result = await fallback_ai_manager.search_with_fallback(search_request)
                if ai_result and ai_result.get('success'):
                    ai_analysis = ai_result.get('content', '')

                    # إضافة تحليل AI كنتيجة منفصلة
                    ai_summary = {
                        'title': f"تحليل ذكي: {query}",
                        'content': ai_analysis,
                        'url': 'ai://enhanced-analysis',
                        'source': ai_result.get('source', 'AI Analysis'),
                        'content_type': 'ai_analysis',
                        'relevance_score': 9.5,
                        'search_engine': 'AI Enhanced',
                        'timestamp': datetime.now(),
                        'is_ai_generated': True
                    }
                    results.insert(0, ai_summary)

            except Exception as e:
                logger.warning(f"⚠️ فشل التحليل بالذكاء الاصطناعي: {e}")

        if results:
            return {
                'success': True,
                'strategy': 'ai_enhanced',
                'results': results,
                'ai_analysis': ai_analysis,
                'source': 'AI Enhanced Search',
                'method': 'Traditional Web + AI Analysis'
            }

        return None

    async def _fallback_ai_search(self, query: str, max_results: int, config: Dict) -> Optional[Dict]:
        """البحث باستخدام النماذج الاحتياطية فقط"""
        try:
            search_request = SearchRequest(
                query=query,
                max_results=max_results,
                search_depth="comprehensive",
                include_web_search=True
            )

            ai_result = await fallback_ai_manager.search_with_fallback(search_request)

            if ai_result and ai_result.get('success'):
                # تحويل نتيجة AI إلى تنسيق النتائج المعياري
                ai_content = ai_result.get('content', '')

                # تقسيم المحتوى إلى نقاط منفصلة إذا أمكن
                content_parts = ai_content.split('\n\n')
                results = []

                for i, part in enumerate(content_parts[:max_results]):
                    if len(part.strip()) > 50:  # تجاهل الأجزاء القصيرة جداً
                        result = {
                            'title': f"نتيجة AI {i+1}: {query}",
                            'content': part.strip(),
                            'url': f"ai://fallback-{ai_result.get('model_type', 'unknown')}-{i+1}",
                            'source': ai_result.get('source', 'AI Fallback'),
                            'content_type': 'ai_generated',
                            'relevance_score': 8.0 - (i * 0.5),  # تقليل النقاط تدريجياً
                            'search_engine': ai_result.get('model_type', 'AI Fallback'),
                            'timestamp': datetime.now(),
                            'is_ai_generated': True,
                            'supports_web_search': ai_result.get('supports_web_search', False)
                        }
                        results.append(result)

                if results:
                    return {
                        'success': True,
                        'strategy': 'fallback_ai',
                        'results': results,
                        'ai_model': ai_result.get('model_type', 'unknown'),
                        'source': ai_result.get('source', 'AI Fallback'),
                        'method': 'Pure AI Fallback Search'
                    }

        except Exception as e:
            logger.error(f"❌ خطأ في البحث الاحتياطي بالذكاء الاصطناعي: {e}")

        return None

    async def _hybrid_search(self, query: str, max_results: int, config: Dict, include_ai_analysis: bool) -> Optional[Dict]:
        """البحث المختلط (تقليدي + AI)"""
        all_results = []
        ai_analysis = None

        # تشغيل البحث التقليدي والذكي بالتوازي
        tasks = [
            self._traditional_web_search(query, max_results // 2, config),
            self._fallback_ai_search(query, max_results // 2, config)
        ]

        try:
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # معالجة نتائج البحث التقليدي
            if results[0] and not isinstance(results[0], Exception):
                web_results = results[0]
                if web_results.get('success') and web_results.get('results'):
                    all_results.extend(web_results['results'])

            # معالجة نتائج البحث بالذكاء الاصطناعي
            if results[1] and not isinstance(results[1], Exception):
                ai_results = results[1]
                if ai_results.get('success') and ai_results.get('results'):
                    all_results.extend(ai_results['results'])

            # إضافة تحليل شامل إذا طُلب
            if include_ai_analysis and len(all_results) > 0:
                try:
                    # تجميع المحتوى للتحليل
                    combined_content = "\n".join([
                        f"- {result.get('title', '')}: {result.get('content', '')[:200]}..."
                        for result in all_results[:5]
                    ])

                    analysis_request = SearchRequest(
                        query=f"حلل وقدم ملخص شامل لهذه المعلومات حول {query}:\n{combined_content}",
                        max_results=1,
                        search_depth="deep"
                    )

                    analysis_result = await fallback_ai_manager.search_with_fallback(analysis_request)
                    if analysis_result and analysis_result.get('success'):
                        ai_analysis = analysis_result.get('content', '')

                except Exception as e:
                    logger.warning(f"⚠️ فشل التحليل الشامل: {e}")

            if all_results:
                return {
                    'success': True,
                    'strategy': 'hybrid',
                    'results': all_results,
                    'ai_analysis': ai_analysis,
                    'source': 'Hybrid Search',
                    'method': 'Traditional Web + AI Fallback + Analysis'
                }

        except Exception as e:
            logger.error(f"❌ خطأ في البحث المختلط: {e}")

        return None

    def _evaluate_result_quality(self, result: Dict) -> float:
        """تقييم جودة النتيجة"""
        quality_score = 0.0

        results = result.get('results', [])
        if not results:
            return 0.0

        # عدد النتائج (25%)
        result_count_score = min(len(results) / 10, 1.0) * 0.25
        quality_score += result_count_score

        # جودة المحتوى (35%)
        content_scores = []
        for res in results:
            content_length = len(res.get('content', ''))
            title_length = len(res.get('title', ''))

            # نقاط طول المحتوى
            content_score = min(content_length / 500, 1.0) * 0.6
            # نقاط طول العنوان
            title_score = min(title_length / 50, 1.0) * 0.4

            content_scores.append(content_score + title_score)

        avg_content_score = sum(content_scores) / len(content_scores) if content_scores else 0
        quality_score += avg_content_score * 0.35

        # تنوع المصادر (20%)
        sources = set(res.get('source', 'unknown') for res in results)
        source_diversity = min(len(sources) / 5, 1.0) * 0.20
        quality_score += source_diversity

        # وجود تحليل AI (20%)
        if result.get('ai_analysis'):
            quality_score += 0.20

        return min(quality_score, 1.0)

    def _deduplicate_results(self, results: List[Dict]) -> List[Dict]:
        """إزالة النتائج المكررة"""
        seen_titles = set()
        seen_content_hashes = set()
        unique_results = []

        for result in results:
            title = result.get('title', '').lower().strip()
            content = result.get('content', '')

            # تنظيف العنوان
            clean_title = ''.join(c for c in title if c.isalnum() or c.isspace()).strip()

            # حساب hash للمحتوى
            content_hash = hash(content[:200]) if content else 0

            # فحص التكرار
            if clean_title not in seen_titles and content_hash not in seen_content_hashes:
                seen_titles.add(clean_title)
                seen_content_hashes.add(content_hash)
                unique_results.append(result)

        return unique_results

    def _update_strategy_usage(self, strategy: str):
        """تحديث إحصائيات استخدام الاستراتيجيات"""
        if strategy not in self.search_stats['strategy_usage']:
            self.search_stats['strategy_usage'][strategy] = 0
        self.search_stats['strategy_usage'][strategy] += 1

    def get_search_statistics(self) -> Dict:
        """الحصول على إحصائيات البحث"""
        total_searches = self.search_stats['total_searches']
        success_rate = (self.search_stats['successful_searches'] / total_searches * 100) if total_searches > 0 else 0

        return {
            'total_searches': total_searches,
            'successful_searches': self.search_stats['successful_searches'],
            'failed_searches': self.search_stats['failed_searches'],
            'success_rate': round(success_rate, 2),
            'strategy_usage': self.search_stats['strategy_usage'],
            'fallback_ai_stats': fallback_ai_manager.get_usage_stats(),
            'last_reset': self.search_stats['last_reset']
        }

    async def quick_search(self, query: str, max_results: int = 5) -> Dict:
        """بحث سريع للحالات العاجلة"""
        return await self.comprehensive_search(
            query=query,
            max_results=max_results,
            priority=SearchPriority.FAST,
            include_ai_analysis=False
        )

    async def deep_search(self, query: str, max_results: int = 15) -> Dict:
        """بحث عميق عالي الجودة"""
        return await self.comprehensive_search(
            query=query,
            max_results=max_results,
            priority=SearchPriority.HIGH_QUALITY,
            include_ai_analysis=True
        )

    async def emergency_search(self, query: str, max_results: int = 3) -> Dict:
        """بحث طوارئ - أي نتيجة متاحة"""
        return await self.comprehensive_search(
            query=query,
            max_results=max_results,
            priority=SearchPriority.EMERGENCY,
            include_ai_analysis=False
        )

# إنشاء مثيل عام
enhanced_search_manager = EnhancedSearchManager()
