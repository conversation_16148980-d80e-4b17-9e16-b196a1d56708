#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام الاتصال بنماذج الذكاء الاصطناعي المجانية عبر الإنترنت
"""

import asyncio
import aiohttp
import json
from datetime import datetime
from typing import List, Dict, Optional
import time
import random
from .logger import logger

class FreeAIModels:
    """نظام الاتصال بنماذج AI المجانية عبر الإنترنت"""
    
    def __init__(self):
        # نماذج AI مجانية متاحة عبر الإنترنت
        self.free_models = {
            'deepseek': {
                'name': 'DeepSeek',
                'endpoints': [
                    'https://chat.deepseek.com/api/v0/chat/completions',
                    'https://api.deepseek.com/v1/chat/completions'
                ],
                'model': 'deepseek-chat',
                'free_limit': 'unlimited',  # مجاني مع حدود معقولة
                'strength': 'برمجة وتحليل متقدم',
                'status': 'available'
            },
            'qwen': {
                'name': '<PERSON><PERSON> (Alibaba)',
                'endpoints': [
                    'https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation',
                    'https://qwen.alibaba.com/api/chat'
                ],
                'model': 'qwen-turbo',
                'free_limit': '1M tokens/month',
                'strength': 'فهم اللغة العربية والصينية',
                'status': 'available'
            },
            'chatgpt_free': {
                'name': 'ChatGPT Free',
                'endpoints': [
                    'https://chatgpt.com/api/conversation',
                    'https://chat.openai.com/backend-api/conversation'
                ],
                'model': 'gpt-3.5-turbo',
                'free_limit': '20 messages/3 hours',
                'strength': 'محادثة طبيعية وإبداع',
                'status': 'limited'
            },
            'claude_free': {
                'name': 'Claude Free',
                'endpoints': [
                    'https://claude.ai/api/organizations/*/chat_conversations',
                    'https://claude.anthropic.com/api/chat'
                ],
                'model': 'claude-3-haiku',
                'free_limit': '5 conversations/day',
                'strength': 'تحليل وكتابة متقدمة',
                'status': 'limited'
            },
            'gemini_free': {
                'name': 'Gemini Free',
                'endpoints': [
                    'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent',
                    'https://ai.google.dev/api/generate'
                ],
                'model': 'gemini-pro',
                'free_limit': '60 requests/minute',
                'strength': 'بحث ومعلومات حديثة',
                'status': 'available'
            },
            'huggingface_free': {
                'name': 'Hugging Face Free',
                'endpoints': [
                    'https://api-inference.huggingface.co/models/microsoft/DialoGPT-large',
                    'https://api-inference.huggingface.co/models/facebook/blenderbot-400M-distill'
                ],
                'model': 'various',
                'free_limit': 'unlimited',
                'strength': 'نماذج متنوعة ومفتوحة',
                'status': 'available'
            },
            'cohere_free': {
                'name': 'Cohere Free',
                'endpoints': [
                    'https://api.cohere.ai/v1/generate',
                    'https://api.cohere.ai/v1/chat'
                ],
                'model': 'command',
                'free_limit': '100 requests/month',
                'strength': 'توليد نصوص وتلخيص',
                'status': 'available'
            }
        }
        
        # إحصائيات الاستخدام
        self.usage_stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'models_used': {},
            'response_times': []
        }
        
        # إعدادات الطلبات
        self.request_config = {
            'timeout': 30,
            'max_retries': 3,
            'retry_delay': 2,
            'user_agents': [
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
                'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36'
            ]
        }
    
    async def analyze_gaming_content(self, content: str, analysis_type: str = 'quality') -> Dict:
        """تحليل محتوى الألعاب باستخدام نماذج AI مجانية"""
        try:
            logger.info(f"🤖 بدء تحليل المحتوى باستخدام AI مجاني...")
            
            # تحضير البرومبت حسب نوع التحليل
            prompt = self._prepare_analysis_prompt(content, analysis_type)
            
            # محاولة النماذج بالترتيب حسب الأولوية
            priority_models = ['deepseek', 'qwen', 'gemini_free', 'huggingface_free']
            
            for model_name in priority_models:
                try:
                    result = await self._query_model(model_name, prompt)
                    if result:
                        logger.info(f"✅ تم التحليل بنجاح باستخدام {model_name}")
                        return self._parse_analysis_result(result, model_name)
                except Exception as e:
                    logger.warning(f"⚠️ فشل {model_name}: {e}")
                    continue
            
            # إذا فشلت جميع النماذج، استخدم تحليل افتراضي
            return self._fallback_analysis(content)
            
        except Exception as e:
            logger.error(f"❌ خطأ في تحليل المحتوى: {e}")
            return self._fallback_analysis(content)
    
    async def enhance_search_query(self, original_query: str) -> List[str]:
        """تحسين استعلام البحث باستخدام AI"""
        try:
            logger.info(f"🔍 تحسين استعلام البحث: {original_query}")
            
            prompt = f"""
            أنت خبير في أخبار الألعاب. حسن هذا الاستعلام للحصول على أفضل النتائج:
            
            الاستعلام الأصلي: "{original_query}"
            
            اقترح 5 استعلامات محسنة باللغة الإنجليزية للبحث عن أخبار الألعاب:
            1. استعلام عام
            2. استعلام متخصص
            3. استعلام بكلمات مفتاحية
            4. استعلام بأسماء شركات
            5. استعلام بتواريخ حديثة
            
            أجب فقط بالاستعلامات، كل واحد في سطر منفصل.
            """
            
            # محاولة النماذج المتاحة
            for model_name in ['deepseek', 'qwen', 'gemini_free']:
                try:
                    result = await self._query_model(model_name, prompt)
                    if result:
                        enhanced_queries = self._parse_enhanced_queries(result)
                        if enhanced_queries:
                            logger.info(f"✅ تم تحسين الاستعلام باستخدام {model_name}")
                            return enhanced_queries
                except Exception as e:
                    logger.warning(f"⚠️ فشل تحسين الاستعلام مع {model_name}: {e}")
                    continue
            
            # استعلامات افتراضية محسنة
            return self._generate_fallback_queries(original_query)
            
        except Exception as e:
            logger.error(f"❌ خطأ في تحسين الاستعلام: {e}")
            return [original_query]
    
    async def _query_model(self, model_name: str, prompt: str) -> Optional[str]:
        """استعلام نموذج AI محدد"""
        if model_name not in self.free_models:
            return None
        
        model_config = self.free_models[model_name]
        
        # جرب كل endpoint متاح
        for endpoint in model_config['endpoints']:
            try:
                start_time = time.time()
                
                # تحضير الطلب حسب النموذج
                if model_name == 'deepseek':
                    result = await self._query_deepseek(endpoint, prompt)
                elif model_name == 'qwen':
                    result = await self._query_qwen(endpoint, prompt)
                elif model_name == 'gemini_free':
                    result = await self._query_gemini(endpoint, prompt)
                elif model_name == 'huggingface_free':
                    result = await self._query_huggingface(endpoint, prompt)
                else:
                    result = await self._query_generic(endpoint, prompt, model_config)
                
                # تسجيل الإحصائيات
                response_time = time.time() - start_time
                self._update_stats(model_name, True, response_time)
                
                return result
                
            except Exception as e:
                logger.warning(f"⚠️ فشل endpoint {endpoint}: {e}")
                continue
        
        # تسجيل الفشل
        self._update_stats(model_name, False, 0)
        return None
    
    async def _query_deepseek(self, endpoint: str, prompt: str) -> Optional[str]:
        """استعلام DeepSeek"""
        headers = {
            'Content-Type': 'application/json',
            'User-Agent': random.choice(self.request_config['user_agents']),
            'Accept': 'application/json'
        }
        
        payload = {
            'model': 'deepseek-chat',
            'messages': [
                {
                    'role': 'user',
                    'content': prompt
                }
            ],
            'max_tokens': 1000,
            'temperature': 0.3,
            'stream': False
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(
                endpoint,
                headers=headers,
                json=payload,
                timeout=self.request_config['timeout']
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    if 'choices' in data and data['choices']:
                        return data['choices'][0]['message']['content']
                else:
                    logger.warning(f"DeepSeek API خطأ: {response.status}")
                    return None
    
    async def _query_qwen(self, endpoint: str, prompt: str) -> Optional[str]:
        """استعلام Qwen"""
        headers = {
            'Content-Type': 'application/json',
            'User-Agent': random.choice(self.request_config['user_agents'])
        }
        
        payload = {
            'model': 'qwen-turbo',
            'input': {
                'messages': [
                    {
                        'role': 'user',
                        'content': prompt
                    }
                ]
            },
            'parameters': {
                'max_tokens': 1000,
                'temperature': 0.3
            }
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(
                endpoint,
                headers=headers,
                json=payload,
                timeout=self.request_config['timeout']
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    if 'output' in data and 'text' in data['output']:
                        return data['output']['text']
                else:
                    logger.warning(f"Qwen API خطأ: {response.status}")
                    return None
    
    async def _query_gemini(self, endpoint: str, prompt: str) -> Optional[str]:
        """استعلام Gemini"""
        headers = {
            'Content-Type': 'application/json',
            'User-Agent': random.choice(self.request_config['user_agents'])
        }
        
        payload = {
            'contents': [
                {
                    'parts': [
                        {
                            'text': prompt
                        }
                    ]
                }
            ],
            'generationConfig': {
                'maxOutputTokens': 1000,
                'temperature': 0.3
            }
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(
                endpoint,
                headers=headers,
                json=payload,
                timeout=self.request_config['timeout']
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    if 'candidates' in data and data['candidates']:
                        return data['candidates'][0]['content']['parts'][0]['text']
                else:
                    logger.warning(f"Gemini API خطأ: {response.status}")
                    return None
    
    async def _query_huggingface(self, endpoint: str, prompt: str) -> Optional[str]:
        """استعلام Hugging Face"""
        headers = {
            'Content-Type': 'application/json',
            'User-Agent': random.choice(self.request_config['user_agents'])
        }
        
        payload = {
            'inputs': prompt,
            'parameters': {
                'max_length': 500,
                'temperature': 0.3,
                'do_sample': True
            }
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(
                endpoint,
                headers=headers,
                json=payload,
                timeout=self.request_config['timeout']
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    if isinstance(data, list) and data:
                        return data[0].get('generated_text', '')
                    elif isinstance(data, dict):
                        return data.get('generated_text', '')
                else:
                    logger.warning(f"Hugging Face API خطأ: {response.status}")
                    return None
    
    def _prepare_analysis_prompt(self, content: str, analysis_type: str) -> str:
        """تحضير برومبت التحليل"""
        if analysis_type == 'quality':
            return f"""
            حلل جودة هذا المحتوى الخاص بأخبار الألعاب وقيمه من 1-10:
            
            المحتوى: {content[:500]}
            
            قيم:
            1. جودة المحتوى (1-10)
            2. الصلة بالألعاب (1-10)
            3. حداثة المعلومات (1-10)
            4. وضوح الكتابة (1-10)
            
            أجب بتنسيق JSON:
            {{"quality": X, "relevance": X, "freshness": X, "clarity": X, "summary": "ملخص قصير"}}
            """
        elif analysis_type == 'keywords':
            return f"""
            استخرج الكلمات المفتاحية المهمة من هذا المحتوى عن الألعاب:
            
            {content[:500]}
            
            أجب بقائمة من 5-10 كلمات مفتاحية مفصولة بفواصل.
            """
        else:
            return f"""
            حلل هذا المحتوى عن الألعاب وقدم تقييماً شاملاً:
            
            {content[:500]}
            
            قدم تحليلاً مختصراً في 3 جمل.
            """
    
    def _parse_analysis_result(self, result: str, model_name: str) -> Dict:
        """تحليل نتيجة AI"""
        try:
            # محاولة تحليل JSON
            import re
            json_match = re.search(r'\{.*\}', result, re.DOTALL)
            if json_match:
                json_data = json.loads(json_match.group())
                return {
                    'ai_quality_score': json_data.get('quality', 7),
                    'ai_relevance_score': json_data.get('relevance', 8),
                    'ai_freshness_score': json_data.get('freshness', 7),
                    'ai_clarity_score': json_data.get('clarity', 8),
                    'ai_summary': json_data.get('summary', 'تحليل AI'),
                    'ai_model_used': model_name,
                    'ai_analysis_time': datetime.now().isoformat()
                }
        except:
            pass
        
        # تحليل نصي بسيط
        numbers = re.findall(r'\d+', result)
        scores = [min(int(n), 10) for n in numbers if int(n) <= 10]
        
        return {
            'ai_quality_score': scores[0] if len(scores) > 0 else 7,
            'ai_relevance_score': scores[1] if len(scores) > 1 else 8,
            'ai_freshness_score': scores[2] if len(scores) > 2 else 7,
            'ai_clarity_score': scores[3] if len(scores) > 3 else 8,
            'ai_summary': result[:100] + '...' if len(result) > 100 else result,
            'ai_model_used': model_name,
            'ai_analysis_time': datetime.now().isoformat()
        }
    
    def _parse_enhanced_queries(self, result: str) -> List[str]:
        """تحليل الاستعلامات المحسنة"""
        lines = result.strip().split('\n')
        queries = []
        
        for line in lines:
            # إزالة الأرقام والرموز من بداية السطر
            clean_line = re.sub(r'^\d+\.?\s*', '', line.strip())
            if clean_line and len(clean_line) > 5:
                queries.append(clean_line)
        
        return queries[:5]  # أول 5 استعلامات
    
    def _generate_fallback_queries(self, original_query: str) -> List[str]:
        """توليد استعلامات احتياطية"""
        base_terms = ['gaming news', 'video game', 'game update', 'gaming industry', 'esports']
        
        queries = []
        for term in base_terms:
            queries.append(f"{original_query} {term}")
        
        return queries
    
    def _fallback_analysis(self, content: str) -> Dict:
        """تحليل احتياطي بدون AI"""
        # تحليل بسيط بناءً على طول المحتوى والكلمات المفتاحية
        gaming_keywords = ['game', 'gaming', 'player', 'console', 'pc', 'mobile', 'esports']
        content_lower = content.lower()
        
        keyword_count = sum(1 for keyword in gaming_keywords if keyword in content_lower)
        length_score = min(len(content) // 100, 10)
        
        return {
            'ai_quality_score': min(length_score + keyword_count, 10),
            'ai_relevance_score': min(keyword_count * 2, 10),
            'ai_freshness_score': 7,
            'ai_clarity_score': 8,
            'ai_summary': 'تحليل تلقائي بدون AI',
            'ai_model_used': 'fallback',
            'ai_analysis_time': datetime.now().isoformat()
        }
    
    def _update_stats(self, model_name: str, success: bool, response_time: float):
        """تحديث الإحصائيات"""
        self.usage_stats['total_requests'] += 1
        
        if success:
            self.usage_stats['successful_requests'] += 1
            self.usage_stats['response_times'].append(response_time)
        else:
            self.usage_stats['failed_requests'] += 1
        
        if model_name not in self.usage_stats['models_used']:
            self.usage_stats['models_used'][model_name] = {'success': 0, 'failed': 0}
        
        if success:
            self.usage_stats['models_used'][model_name]['success'] += 1
        else:
            self.usage_stats['models_used'][model_name]['failed'] += 1
    
    async def get_models_status(self) -> Dict:
        """الحصول على حالة النماذج"""
        return {
            'available_models': len(self.free_models),
            'models': self.free_models,
            'usage_stats': self.usage_stats,
            'average_response_time': sum(self.usage_stats['response_times']) / max(len(self.usage_stats['response_times']), 1),
            'success_rate': (self.usage_stats['successful_requests'] / max(self.usage_stats['total_requests'], 1)) * 100
        }

# إنشاء مثيل عام
free_ai_models = FreeAIModels()
