[settings]
allow_nsfw = false
theme = "dark"
times_to_run = 1
opacity = 0.9
storymode = false
storymodemethod = 0
storymode_max_length = 1000
resolution_w = 1080
resolution_h = 1920
zoom = 1.0
channel_name = "Reddit Tales"

[tts]
choice = "gtts"
python_voice = "1"
py_voice_num = "2"
silence_duration = 0.3
no_emojis = false

[background]
background_choice = "minecraft"
background_audio = true
background_audio_volume = 0.3
background_thumbnail = false
background_thumbnail_font_family = "arial"
background_thumbnail_font_size = 96
background_thumbnail_font_color = [ 255, 255, 255,]

[imagemagick]
path = "/usr/bin/"

[automation]
enabled = true

[render]
enabled = true
video_quality = "medium"
max_video_duration = 45
auto_cleanup_hours = 1
storage_limit_mb = 1000

[reddit.creds]
client_id = ""
client_secret = ""
username = ""
password = ""
user_agent = "RedditVideoMakerBot by u/LukaHietala"

[reddit.thread]
subreddit = "AskReddit"
post_id = ""
max_comment_length = 500
theme = "dark"
times_to_run = 1
ai_similarity_enabled = true
ai_similarity_level = 1

[tts.gtts]
temp_audio_folder = "assets/temp/mp3"
tld = "com"

[tts.elevenlabs]
api_key = ""
voice = "Josh"
model = "eleven_monolingual_v1"

[automation.telegram]
enabled = true
bot_token = "**********************************************"
chat_id = ""

[automation.gemini]
enabled = false
api_key = ""

[automation.youtube]
enabled = false
client_id = ""
client_secret = ""

[automation.schedule]
enabled = true
interval_hours = 6
max_videos_per_day = 4
