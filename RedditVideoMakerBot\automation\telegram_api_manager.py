#!/usr/bin/env python3
"""
مدير API Keys عبر بوت Telegram
يسمح بإدارة وتخصيص مفاتيح الـ APIs عن بُعد
"""

import logging
import json
import asyncio
import subprocess
import sys
import os
from typing import Dict, List, Optional
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import Application, CommandHandler, CallbackQueryHandler, MessageHandler, filters, ContextTypes
from pathlib import Path
import toml

from utils import settings
from automation.telegram_bot import TelegramBot

logger = logging.getLogger(__name__)

class TelegramAPIManager:
    """مدير API Keys عبر التيليجرام"""
    
    def __init__(self, bot_token: str, authorized_users: List[int] = None):
        self.bot_token = bot_token
        self.authorized_users = authorized_users or []
        self.application = None
        self.pending_operations = {}
        self.bot_process = None
        self.bot_running = False
        
    async def start_bot(self):
        """بدء تشغيل البوت"""
        self.application = Application.builder().token(self.bot_token).build()
        
        # إضافة معالجات الأوامر
        self.application.add_handler(CommandHandler("start", self.start_command))
        self.application.add_handler(CommandHandler("status", self.status_command))
        self.application.add_handler(CommandHandler("apis", self.apis_command))
        self.application.add_handler(CommandHandler("add_key", self.add_key_command))
        self.application.add_handler(CommandHandler("test_key", self.test_key_command))
        self.application.add_handler(CommandHandler("remove_key", self.remove_key_command))
        self.application.add_handler(CommandHandler("run_bot", self.run_bot_command))
        self.application.add_handler(CommandHandler("stop_bot", self.stop_bot_command))
        self.application.add_handler(CommandHandler("bot_status", self.bot_status_command))
        self.application.add_handler(CallbackQueryHandler(self.button_callback))
        self.application.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, self.handle_message))
        
        # بدء البوت
        await self.application.initialize()
        await self.application.start()
        await self.application.updater.start_polling()
        
    def _is_authorized(self, user_id: int) -> bool:
        """فحص صلاحية المستخدم"""
        return len(self.authorized_users) == 0 or user_id in self.authorized_users
    
    async def start_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """أمر البداية"""
        if not self._is_authorized(update.effective_user.id):
            await update.message.reply_text("❌ غير مصرح لك باستخدام هذا البوت")
            return
            
        # تحديد حالة البوت
        bot_status_text = "🟢 يعمل" if self.bot_running else "🔴 متوقف"
        bot_action_text = "⏹️ إيقاف البوت" if self.bot_running else "▶️ تشغيل البوت"
        bot_action_data = "stop_bot" if self.bot_running else "start_bot"

        keyboard = [
            [InlineKeyboardButton("📊 حالة النظام", callback_data="status")],
            [InlineKeyboardButton("🔑 إدارة APIs", callback_data="manage_apis")],
            [InlineKeyboardButton("🧪 اختبار APIs", callback_data="test_apis")],
            [InlineKeyboardButton(f"🤖 البوت: {bot_status_text}", callback_data="bot_status")],
            [InlineKeyboardButton(bot_action_text, callback_data=bot_action_data)],
            [InlineKeyboardButton("🎤 إعداد ElevenLabs مجاني", callback_data="setup_free_elevenlabs")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await update.message.reply_text(
            "🤖 مرحباً بك في مدير Reddit Video Bot\n\n"
            "يمكنك إدارة النظام والـ APIs من هنا:",
            reply_markup=reply_markup
        )
    
    async def status_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """عرض حالة النظام"""
        if not self._is_authorized(update.effective_user.id):
            return
            
        # استيراد مدير TTS
        try:
            from TTS.smart_tts_manager import SmartTTSManager
            tts_manager = SmartTTSManager()
            status_report = tts_manager.get_status_report()
        except Exception as e:
            status_report = f"❌ خطأ في الحصول على التقرير: {str(e)}"
        
        await update.message.reply_text(status_report)
    
    async def apis_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """عرض APIs المتاحة"""
        if not self._is_authorized(update.effective_user.id):
            return
            
        apis_info = self._get_apis_info()
        await update.message.reply_text(apis_info)
    
    def _get_apis_info(self) -> str:
        """الحصول على معلومات APIs"""
        info = "🔑 معلومات APIs المتاحة:\n\n"
        
        # ElevenLabs
        elevenlabs_keys = settings.config["settings"]["tts"]["api_keys"]["elevenlabs_keys"]
        info += f"🎤 ElevenLabs: {len(elevenlabs_keys)} مفاتيح\n"
        for i, key in enumerate(elevenlabs_keys):
            if key.strip():
                info += f"  - المفتاح {i+1}: {key[:10]}...{key[-4:]}\n"
        
        # AWS
        aws_profiles = settings.config["settings"]["tts"]["api_keys"]["aws_profiles"]
        info += f"\n☁️ AWS Polly: {len(aws_profiles)} ملفات تعريف\n"
        for profile in aws_profiles:
            info += f"  - {profile}\n"
        
        # TikTok
        tiktok_session = settings.config["settings"]["tts"]["tiktok_sessionid"]
        tiktok_status = "✅ مُعيّن" if tiktok_session.strip() else "❌ غير مُعيّن"
        info += f"\n🎵 TikTok: {tiktok_status}\n"
        
        return info
    
    async def add_key_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """إضافة مفتاح API جديد"""
        if not self._is_authorized(update.effective_user.id):
            return
            
        keyboard = [
            [InlineKeyboardButton("🎤 ElevenLabs", callback_data="add_elevenlabs")],
            [InlineKeyboardButton("☁️ AWS Profile", callback_data="add_aws")],
            [InlineKeyboardButton("🎵 TikTok Session", callback_data="add_tiktok")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await update.message.reply_text(
            "🔑 اختر نوع API لإضافة مفتاح جديد:",
            reply_markup=reply_markup
        )
    
    async def test_key_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """اختبار مفاتيح API"""
        if not self._is_authorized(update.effective_user.id):
            return
            
        await update.message.reply_text("🧪 بدء اختبار جميع مفاتيح API...")
        
        # اختبار ElevenLabs
        elevenlabs_results = await self._test_elevenlabs_keys()
        
        # اختبار AWS
        aws_results = await self._test_aws_profiles()
        
        # اختبار TikTok
        tiktok_result = await self._test_tiktok_session()
        
        results = f"🧪 نتائج اختبار APIs:\n\n{elevenlabs_results}\n{aws_results}\n{tiktok_result}"
        await update.message.reply_text(results)

    async def run_bot_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """تشغيل البوت"""
        if not self._is_authorized(update.effective_user.id):
            return

        if self.bot_running:
            await update.message.reply_text("🟢 البوت يعمل بالفعل!")
            return

        try:
            await update.message.reply_text("🚀 بدء تشغيل البوت...")

            # تشغيل البوت في عملية منفصلة مع إعادة توجيه المخرجات
            self.bot_process = subprocess.Popen([
                sys.executable, "main.py"
            ], cwd=os.getcwd(),
               stdout=subprocess.PIPE,
               stderr=subprocess.PIPE,
               text=True)

            self.bot_running = True

            # إرسال رسالة نجاح مع معلومات إضافية
            success_msg = (
                "✅ تم تشغيل البوت بنجاح!\n\n"
                f"🆔 معرف العملية: {self.bot_process.pid}\n"
                "📊 سيتم إرسال تحديثات الحالة تلقائياً\n"
                "⚠️ ستصلك إشعارات عند وجود مشاكل في الإعدادات\n\n"
                "استخدم /bot_status لمراقبة الحالة"
            )
            await update.message.reply_text(success_msg)

            # بدء مراقبة العملية
            asyncio.create_task(self._monitor_bot_process(update.effective_chat.id))

        except Exception as e:
            await update.message.reply_text(f"❌ فشل في تشغيل البوت: {str(e)}")

    async def stop_bot_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """إيقاف البوت"""
        if not self._is_authorized(update.effective_user.id):
            return

        if not self.bot_running:
            await update.message.reply_text("🔴 البوت متوقف بالفعل!")
            return

        try:
            await update.message.reply_text("⏹️ إيقاف البوت...")

            if self.bot_process:
                self.bot_process.terminate()
                self.bot_process.wait(timeout=10)
                self.bot_process = None

            self.bot_running = False
            await update.message.reply_text("✅ تم إيقاف البوت بنجاح!")

        except Exception as e:
            await update.message.reply_text(f"❌ فشل في إيقاف البوت: {str(e)}")

    async def bot_status_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """حالة البوت"""
        if not self._is_authorized(update.effective_user.id):
            return

        status_text = "🟢 يعمل" if self.bot_running else "🔴 متوقف"
        process_info = ""

        if self.bot_process:
            try:
                # فحص إذا كانت العملية ما زالت تعمل
                if self.bot_process.poll() is None:
                    process_info = f"\n📊 معرف العملية: {self.bot_process.pid}"
                else:
                    self.bot_running = False
                    process_info = "\n⚠️ العملية انتهت"
            except:
                process_info = "\n❓ حالة العملية غير معروفة"

        await update.message.reply_text(f"🤖 حالة البوت: {status_text}{process_info}")
    
    async def _test_elevenlabs_keys(self) -> str:
        """اختبار مفاتيح ElevenLabs"""
        results = "🎤 ElevenLabs:\n"
        keys = settings.config["settings"]["tts"]["api_keys"]["elevenlabs_keys"]
        
        for i, key in enumerate(keys):
            if not key.strip():
                continue
                
            try:
                from TTS.elevenlabs import elevenlabs
                # تحديث المفتاح مؤقتاً
                original_key = settings.config["settings"]["tts"]["elevenlabs_api_key"]
                settings.config["settings"]["tts"]["elevenlabs_api_key"] = key
                
                engine = elevenlabs()
                engine.initialize()
                
                # استعادة المفتاح الأصلي
                settings.config["settings"]["tts"]["elevenlabs_api_key"] = original_key
                
                results += f"  ✅ المفتاح {i+1}: يعمل\n"
            except Exception as e:
                results += f"  ❌ المفتاح {i+1}: {str(e)[:50]}...\n"
        
        return results
    
    async def _test_aws_profiles(self) -> str:
        """اختبار ملفات AWS"""
        results = "☁️ AWS Polly:\n"
        profiles = settings.config["settings"]["tts"]["api_keys"]["aws_profiles"]
        
        for profile in profiles:
            try:
                from boto3 import Session
                session = Session(profile_name=profile)
                client = session.client("polly")
                client.describe_voices()
                results += f"  ✅ {profile}: يعمل\n"
            except Exception as e:
                results += f"  ❌ {profile}: {str(e)[:50]}...\n"
        
        return results
    
    async def _test_tiktok_session(self) -> str:
        """اختبار TikTok session"""
        session_id = settings.config["settings"]["tts"]["tiktok_sessionid"]
        
        if not session_id.strip():
            return "🎵 TikTok: ❌ لا يوجد session ID"
        
        try:
            from TTS.TikTok import TikTok
            engine = TikTok()
            # محاولة بسيطة للاختبار
            return "🎵 TikTok: ✅ Session ID مُعيّن"
        except Exception as e:
            return f"🎵 TikTok: ❌ {str(e)[:50]}..."
    
    async def button_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """معالج أزرار الكيبورد"""
        query = update.callback_query
        await query.answer()
        
        if not self._is_authorized(query.from_user.id):
            return
        
        data = query.data
        
        if data == "status":
            await self.status_command(update, context)
        elif data == "manage_apis":
            await self.apis_command(update, context)
        elif data.startswith("add_"):
            await self._handle_add_key(query, data)
        elif data == "test_apis":
            await self.test_key_command(update, context)
        elif data == "start_bot":
            await self._handle_start_bot(query)
        elif data == "stop_bot":
            await self._handle_stop_bot(query)
        elif data == "bot_status":
            await self.bot_status_command(update, context)
        elif data == "setup_free_elevenlabs":
            await self._handle_free_elevenlabs_setup(query)
    
    async def _handle_add_key(self, query, data):
        """معالجة إضافة مفتاح جديد"""
        user_id = query.from_user.id

        if data == "add_elevenlabs":
            self.pending_operations[user_id] = "elevenlabs_key"
            await query.edit_message_text("🎤 أرسل مفتاح ElevenLabs API:")
        elif data == "add_aws":
            self.pending_operations[user_id] = "aws_profile"
            await query.edit_message_text("☁️ أرسل اسم AWS Profile:")
        elif data == "add_tiktok":
            self.pending_operations[user_id] = "tiktok_session"
            await query.edit_message_text("🎵 أرسل TikTok Session ID:")

    async def _handle_start_bot(self, query):
        """معالجة تشغيل البوت"""
        if self.bot_running:
            await query.edit_message_text("🟢 البوت يعمل بالفعل!")
            return

        try:
            await query.edit_message_text("🚀 بدء تشغيل البوت...")

            # تشغيل البوت في عملية منفصلة
            self.bot_process = subprocess.Popen([
                sys.executable, "main.py"
            ], cwd=os.getcwd())

            self.bot_running = True
            await query.edit_message_text("✅ تم تشغيل البوت بنجاح!")

        except Exception as e:
            await query.edit_message_text(f"❌ فشل في تشغيل البوت: {str(e)}")

    async def _handle_stop_bot(self, query):
        """معالجة إيقاف البوت"""
        if not self.bot_running:
            await query.edit_message_text("🔴 البوت متوقف بالفعل!")
            return

        try:
            await query.edit_message_text("⏹️ إيقاف البوت...")

            if self.bot_process:
                self.bot_process.terminate()
                self.bot_process.wait(timeout=10)
                self.bot_process = None

            self.bot_running = False
            await query.edit_message_text("✅ تم إيقاف البوت بنجاح!")

        except Exception as e:
            await query.edit_message_text(f"❌ فشل في إيقاف البوت: {str(e)}")

    async def _handle_free_elevenlabs_setup(self, query):
        """معالجة إعداد ElevenLabs المجاني"""
        user_id = query.from_user.id
        self.pending_operations[user_id] = "elevenlabs_free_voice_id"

        message = (
            "🎤 إعداد ElevenLabs المجاني\n\n"
            "يمكنك استخدام الأصوات المجانية من ElevenLabs بدون API key!\n\n"
            "الأصوات المجانية المتاحة:\n"
            "• Rachel (21m00Tcm4TlvDq8ikWAM)\n"
            "• Drew (29vD33N1CtxCmqQRPOHJ)\n"
            "• Clyde (2EiwWnXFnvU5JabPnv8n)\n"
            "• Paul (5Q0t7uMcjvnagumLfvZi)\n"
            "• Domi (AZnzlk1XvdvUeBnXmlld)\n"
            "• Dave (CYw3kZ02Hs0563khs1Fj)\n"
            "• Fin (D38z5RcWu1voky8WS1ja)\n"
            "• Sarah (EXAVITQu4vr4xnSDxMaL)\n"
            "• Antoni (ErXwobaYiN019PkySvjV)\n"
            "• Thomas (GBv7mTt0atIp3Br8iCZE)\n"
            "• Charlie (IKne3meq5aSn9XLyUdCD)\n"
            "• Emily (LcfcDJNUP1GQjkzn1xUU)\n"
            "• Elli (MF3mGyEYCl7XYWbV9V6O)\n"
            "• Callum (N2lVS1w4EtoT3dr4eOWO)\n"
            "• Patrick (ODq5zmih8GrVes37Dizd)\n"
            "• Harry (SOYHLrjzK2X1ezoPC6cr)\n"
            "• Liam (TX3LPaxmHKxFdv7VOQHJ)\n"
            "• Dorothy (ThT5KcBeYPX3keUQqHPh)\n"
            "• Josh (TxGEqnHWrfWFTfGW9XjX)\n"
            "• Arnold (VR6AewLTigWG4xSOukaG)\n"
            "• Adam (pNInz6obpgDQGcFmaJgB)\n"
            "• Sam (yoZ06aMxZJJ28mfd3POQ)\n\n"
            "أرسل Voice ID الذي تريد استخدامه:"
        )

        await query.edit_message_text(message)
    
    async def handle_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """معالج الرسائل النصية"""
        user_id = update.effective_user.id
        
        if not self._is_authorized(user_id):
            return
        
        if user_id not in self.pending_operations:
            return
        
        operation = self.pending_operations[user_id]
        message_text = update.message.text.strip()
        
        try:
            if operation == "elevenlabs_key":
                await self._add_elevenlabs_key(message_text, update)
            elif operation == "aws_profile":
                await self._add_aws_profile(message_text, update)
            elif operation == "tiktok_session":
                await self._add_tiktok_session(message_text, update)
            elif operation == "elevenlabs_free_voice_id":
                await self._set_free_elevenlabs_voice(message_text, update)
        finally:
            del self.pending_operations[user_id]
    
    async def _add_elevenlabs_key(self, api_key: str, update: Update):
        """إضافة مفتاح ElevenLabs"""
        try:
            keys = settings.config["settings"]["tts"]["api_keys"]["elevenlabs_keys"]
            if api_key not in keys:
                keys.append(api_key)
                self._save_config()
                await update.message.reply_text("✅ تم إضافة مفتاح ElevenLabs بنجاح!")
            else:
                await update.message.reply_text("⚠️ هذا المفتاح موجود بالفعل")
        except Exception as e:
            await update.message.reply_text(f"❌ خطأ في إضافة المفتاح: {str(e)}")
    
    async def _add_aws_profile(self, profile_name: str, update: Update):
        """إضافة AWS profile"""
        try:
            profiles = settings.config["settings"]["tts"]["api_keys"]["aws_profiles"]
            if profile_name not in profiles:
                profiles.append(profile_name)
                self._save_config()
                await update.message.reply_text("✅ تم إضافة AWS Profile بنجاح!")
            else:
                await update.message.reply_text("⚠️ هذا Profile موجود بالفعل")
        except Exception as e:
            await update.message.reply_text(f"❌ خطأ في إضافة Profile: {str(e)}")
    
    async def _add_tiktok_session(self, session_id: str, update: Update):
        """إضافة TikTok session"""
        try:
            settings.config["settings"]["tts"]["tiktok_sessionid"] = session_id
            self._save_config()
            await update.message.reply_text("✅ تم تحديث TikTok Session ID بنجاح!")
        except Exception as e:
            await update.message.reply_text(f"❌ خطأ في تحديث Session ID: {str(e)}")

    async def _set_free_elevenlabs_voice(self, voice_id: str, update: Update):
        """إعداد صوت ElevenLabs المجاني"""
        try:
            # قائمة الأصوات المجانية المتاحة
            free_voices = {
                "21m00Tcm4TlvDq8ikWAM": "Rachel",
                "29vD33N1CtxCmqQRPOHJ": "Drew",
                "2EiwWnXFnvU5JabPnv8n": "Clyde",
                "5Q0t7uMcjvnagumLfvZi": "Paul",
                "AZnzlk1XvdvUeBnXmlld": "Domi",
                "CYw3kZ02Hs0563khs1Fj": "Dave",
                "D38z5RcWu1voky8WS1ja": "Fin",
                "EXAVITQu4vr4xnSDxMaL": "Sarah",
                "ErXwobaYiN019PkySvjV": "Antoni",
                "GBv7mTt0atIp3Br8iCZE": "Thomas",
                "IKne3meq5aSn9XLyUdCD": "Charlie",
                "LcfcDJNUP1GQjkzn1xUU": "Emily",
                "MF3mGyEYCl7XYWbV9V6O": "Elli",
                "N2lVS1w4EtoT3dr4eOWO": "Callum",
                "ODq5zmih8GrVes37Dizd": "Patrick",
                "SOYHLrjzK2X1ezoPC6cr": "Harry",
                "TX3LPaxmHKxFdv7VOQHJ": "Liam",
                "ThT5KcBeYPX3keUQqHPh": "Dorothy",
                "TxGEqnHWrfWFTfGW9XjX": "Josh",
                "VR6AewLTigWG4xSOukaG": "Arnold",
                "pNInz6obpgDQGcFmaJgB": "Adam",
                "yoZ06aMxZJJ28mfd3POQ": "Sam"
            }

            voice_id = voice_id.strip()

            # التحقق من صحة Voice ID
            if voice_id in free_voices:
                voice_name = free_voices[voice_id]

                # إعداد ElevenLabs للاستخدام المجاني
                settings.config["settings"]["tts"]["voice_choice"] = "ElevenLabs"
                settings.config["settings"]["tts"]["elevenlabs_voice_id"] = voice_id
                settings.config["settings"]["tts"]["elevenlabs_voice_name"] = voice_name
                settings.config["settings"]["tts"]["elevenlabs_use_free"] = True

                # إزالة API key للاستخدام المجاني
                settings.config["settings"]["tts"]["elevenlabs_api_key"] = ""

                self._save_config()

                await update.message.reply_text(
                    f"✅ تم إعداد ElevenLabs المجاني بنجاح!\n\n"
                    f"🎤 الصوت المختار: {voice_name}\n"
                    f"🆔 Voice ID: {voice_id}\n\n"
                    f"يمكنك الآن استخدام ElevenLabs مجاناً بدون API key!"
                )

            else:
                await update.message.reply_text(
                    "❌ Voice ID غير صحيح أو غير مجاني!\n\n"
                    "يرجى اختيار أحد الأصوات المجانية المتاحة."
                )

        except Exception as e:
            await update.message.reply_text(f"❌ خطأ في إعداد الصوت المجاني: {str(e)}")
    
    def _save_config(self):
        """حفظ التكوين"""
        with open("config.toml", "w", encoding="utf-8") as f:
            toml.dump(settings.config, f)

    async def _monitor_bot_process(self, chat_id: int):
        """مراقبة عملية البوت وإرسال التحديثات"""
        if not self.bot_process:
            return

        try:
            # انتظار لمدة قصيرة للسماح للبوت بالبدء
            await asyncio.sleep(5)

            # فحص إذا كانت العملية ما زالت تعمل
            if self.bot_process.poll() is not None:
                # العملية انتهت
                self.bot_running = False

                # قراءة مخرجات الخطأ
                _, stderr = self.bot_process.communicate()

                error_msg = (
                    "🚨 **البوت توقف بشكل غير متوقع!**\n\n"
                    f"📊 كود الخروج: {self.bot_process.returncode}\n"
                )

                if stderr:
                    error_msg += f"❌ الخطأ: {stderr[:500]}...\n\n"

                error_msg += (
                    "💡 **الحلول المقترحة:**\n"
                    "1. تحقق من إعدادات TTS\n"
                    "2. تأكد من صحة بيانات Reddit\n"
                    "3. جرب إعادة التشغيل\n\n"
                    "استخدم /start لإعادة المحاولة"
                )

                # إرسال الإشعار
                await self._send_message_to_chat(chat_id, error_msg)

            else:
                # البوت يعمل بشكل طبيعي
                success_msg = (
                    "✅ **البوت يعمل بشكل طبيعي**\n\n"
                    "🎬 بدء عملية إنشاء الفيديو...\n"
                    "📱 ستصلك إشعارات عند اكتمال العملية"
                )
                await self._send_message_to_chat(chat_id, success_msg)

                # مراقبة مستمرة
                while self.bot_running and self.bot_process and self.bot_process.poll() is None:
                    await asyncio.sleep(30)  # فحص كل 30 ثانية

                # إذا انتهت العملية بنجاح
                if self.bot_process and self.bot_process.poll() == 0:
                    self.bot_running = False
                    completion_msg = (
                        "🎉 **تم إنشاء الفيديو بنجاح!**\n\n"
                        "✅ العملية اكتملت بدون أخطاء\n"
                        "📁 تحقق من مجلد المخرجات\n\n"
                        "🔄 يمكنك تشغيل البوت مرة أخرى لإنشاء فيديو جديد"
                    )
                    await self._send_message_to_chat(chat_id, completion_msg)

        except Exception as e:
            logger.error(f"خطأ في مراقبة العملية: {str(e)}")

    async def _send_message_to_chat(self, chat_id: int, message: str):
        """إرسال رسالة إلى محادثة محددة"""
        try:
            from telegram import Bot
            bot = Bot(token=self.bot_token)
            await bot.send_message(chat_id=chat_id, text=message, parse_mode='Markdown')
        except Exception as e:
            logger.error(f"فشل في إرسال الرسالة: {str(e)}")

# دالة لبدء مدير API
async def start_api_manager():
    """بدء مدير API"""
    bot_token = settings.config["telegram"]["bot_token"]
    manager = TelegramAPIManager(bot_token)
    await manager.start_bot()
