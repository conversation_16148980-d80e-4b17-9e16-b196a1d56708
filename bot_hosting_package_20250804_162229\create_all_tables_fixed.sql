-- ===================================================================
-- كود SQL محدث لإنشاء جميع الجداول المطلوبة في Supabase
-- Updated SQL Code to Create All Required Tables in Supabase
-- ===================================================================

-- ===================================================================
-- 1. جدول المودات الرئيسي (Main Mods Table)
-- متطابق مع الكود الموجود في supabase_client.py
-- ===================================================================

CREATE TABLE IF NOT EXISTS public.mods (
    id SERIAL PRIMARY KEY,
    name TEXT NOT NULL,                    -- يتطابق مع 'title' في الكود
    description TEXT,                      -- الوصف الإنجليزي
    description_ar TEXT,                   -- الوصف العربي
    telegram_description_ar TEXT,          -- وصف تليجرام العربي
    telegram_description_en TEXT,          -- وصف تليجرام الإنجليزي
    category TEXT NOT NULL CHECK (category IN ('addons', 'shaders', 'texture_packs', 'seeds', 'maps')),
    version TEXT NOT NULL,                 -- إصدار Minecraft (كان mc_version)
    download_url TEXT NOT NULL,            -- رابط التحميل
    image_urls TEXT[],                     -- مصفوفة روابط الصور
    file_size TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT true,
    download_count INTEGER DEFAULT 0,
    rating DECIMAL(3,2) DEFAULT 0.00,
    tags TEXT[],
    author TEXT,
    mod_type TEXT DEFAULT 'mod',           -- نوع المود
    compatibility TEXT[],
    requirements TEXT,
    installation_guide TEXT
);

-- فهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_mods_category ON public.mods(category);
CREATE INDEX IF NOT EXISTS idx_mods_version ON public.mods(version);
CREATE INDEX IF NOT EXISTS idx_mods_active ON public.mods(is_active);
CREATE INDEX IF NOT EXISTS idx_mods_created_at ON public.mods(created_at);
CREATE INDEX IF NOT EXISTS idx_mods_name ON public.mods(name);

-- ===================================================================
-- 2. جداول نظام الإشعارات (Notifications System Tables)
-- ===================================================================

-- جدول الإشعارات المحفوظة
CREATE TABLE IF NOT EXISTS public.saved_notifications (
    id SERIAL PRIMARY KEY,
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    message_type TEXT DEFAULT 'text' CHECK (message_type IN ('text', 'photo', 'video', 'document')),
    media_url TEXT,
    button_text TEXT,
    button_url TEXT,
    created_by TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT true
);

-- جدول سجل الإرسال
CREATE TABLE IF NOT EXISTS public.notification_broadcasts (
    id SERIAL PRIMARY KEY,
    notification_id INTEGER REFERENCES public.saved_notifications(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    message_type TEXT DEFAULT 'text',
    media_url TEXT,
    button_text TEXT,
    button_url TEXT,
    sent_by TEXT NOT NULL,
    total_users INTEGER DEFAULT 0,
    successful_sends INTEGER DEFAULT 0,
    failed_sends INTEGER DEFAULT 0,
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'sending', 'completed', 'failed')),
    error_message TEXT
);

-- جدول تفاصيل الإرسال لكل مستخدم
CREATE TABLE IF NOT EXISTS public.notification_user_logs (
    id SERIAL PRIMARY KEY,
    broadcast_id INTEGER REFERENCES public.notification_broadcasts(id) ON DELETE CASCADE,
    user_id TEXT NOT NULL,
    sent_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'sent', 'failed', 'blocked')),
    error_message TEXT
);

-- فهارس نظام الإشعارات
CREATE INDEX IF NOT EXISTS idx_saved_notifications_created_by ON public.saved_notifications(created_by);
CREATE INDEX IF NOT EXISTS idx_saved_notifications_active ON public.saved_notifications(is_active);
CREATE INDEX IF NOT EXISTS idx_notification_broadcasts_sent_by ON public.notification_broadcasts(sent_by);
CREATE INDEX IF NOT EXISTS idx_notification_broadcasts_status ON public.notification_broadcasts(status);
CREATE INDEX IF NOT EXISTS idx_notification_user_logs_broadcast_id ON public.notification_user_logs(broadcast_id);
CREATE INDEX IF NOT EXISTS idx_notification_user_logs_user_id ON public.notification_user_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_notification_user_logs_status ON public.notification_user_logs(status);

-- ===================================================================
-- 3. جداول نظام المهام (Tasks System Tables)
-- ===================================================================

CREATE TABLE IF NOT EXISTS public.tasks (
    id SERIAL PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    task_type TEXT NOT NULL CHECK (task_type IN ('join_channel', 'visit_link', 'share_content', 'custom')),
    target_url TEXT,
    channel_username TEXT,
    reward_points INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_by TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE,
    max_completions INTEGER,
    current_completions INTEGER DEFAULT 0
);

CREATE TABLE IF NOT EXISTS public.user_task_completions (
    id SERIAL PRIMARY KEY,
    user_id TEXT NOT NULL,
    task_id INTEGER REFERENCES public.tasks(id) ON DELETE CASCADE,
    completed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    verified BOOLEAN DEFAULT false,
    points_awarded INTEGER DEFAULT 0,
    UNIQUE(user_id, task_id)
);

-- فهارس نظام المهام
CREATE INDEX IF NOT EXISTS idx_tasks_active ON public.tasks(is_active);
CREATE INDEX IF NOT EXISTS idx_tasks_type ON public.tasks(task_type);
CREATE INDEX IF NOT EXISTS idx_user_task_completions_user_id ON public.user_task_completions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_task_completions_task_id ON public.user_task_completions(task_id);

-- ===================================================================
-- 4. جداول نظام الإعلانات (Ads System Tables)
-- ===================================================================

CREATE TABLE IF NOT EXISTS public.user_ads_settings (
    id SERIAL PRIMARY KEY,
    user_id TEXT UNIQUE NOT NULL,
    ads_enabled BOOLEAN DEFAULT true,
    ads_frequency INTEGER DEFAULT 3,
    last_ad_shown TIMESTAMP WITH TIME ZONE,
    total_ads_shown INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS public.ads_stats (
    id SERIAL PRIMARY KEY,
    user_id TEXT NOT NULL,
    ad_type TEXT NOT NULL,
    shown_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    clicked BOOLEAN DEFAULT false,
    click_time TIMESTAMP WITH TIME ZONE
);

-- فهارس نظام الإعلانات
CREATE INDEX IF NOT EXISTS idx_user_ads_settings_user_id ON public.user_ads_settings(user_id);
CREATE INDEX IF NOT EXISTS idx_ads_stats_user_id ON public.ads_stats(user_id);
CREATE INDEX IF NOT EXISTS idx_ads_stats_shown_at ON public.ads_stats(shown_at);

-- ===================================================================
-- 5. جداول نظام تقصير الروابط (URL Shortener Tables)
-- ===================================================================

CREATE TABLE IF NOT EXISTS public.user_url_shortener_settings (
    id SERIAL PRIMARY KEY,
    user_id TEXT UNIQUE NOT NULL,
    service_enabled BOOLEAN DEFAULT true,
    api_key TEXT,
    service_type TEXT DEFAULT 'default' CHECK (service_type IN ('default', 'bitly', 'tinyurl', 'custom')),
    custom_domain TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS public.url_shortener_stats (
    id SERIAL PRIMARY KEY,
    user_id TEXT NOT NULL,
    original_url TEXT NOT NULL,
    shortened_url TEXT NOT NULL,
    clicks INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_clicked TIMESTAMP WITH TIME ZONE
);

-- فهارس نظام تقصير الروابط
CREATE INDEX IF NOT EXISTS idx_user_url_shortener_user_id ON public.user_url_shortener_settings(user_id);
CREATE INDEX IF NOT EXISTS idx_url_shortener_stats_user_id ON public.url_shortener_stats(user_id);
CREATE INDEX IF NOT EXISTS idx_url_shortener_stats_created_at ON public.url_shortener_stats(created_at);

-- ===================================================================
-- 6. جداول تخصيص الصفحات (Page Customization Tables)
-- ===================================================================

CREATE TABLE IF NOT EXISTS public.user_page_customization (
    id SERIAL PRIMARY KEY,
    user_id TEXT UNIQUE NOT NULL,
    theme_color TEXT DEFAULT '#007bff',
    background_color TEXT DEFAULT '#ffffff',
    text_color TEXT DEFAULT '#333333',
    logo_url TEXT,
    custom_css TEXT,
    custom_js TEXT,
    page_title TEXT,
    meta_description TEXT,
    favicon_url TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- فهارس تخصيص الصفحات
CREATE INDEX IF NOT EXISTS idx_user_page_customization_user_id ON public.user_page_customization(user_id);
CREATE INDEX IF NOT EXISTS idx_user_page_customization_active ON public.user_page_customization(is_active);

-- ===================================================================
-- 7. جداول الروابط المخصصة (Custom Download Links Tables)
-- ===================================================================

CREATE TABLE IF NOT EXISTS public.custom_download_links (
    id SERIAL PRIMARY KEY,
    user_id TEXT NOT NULL,
    mod_id INTEGER REFERENCES public.mods(id) ON DELETE CASCADE,
    custom_url TEXT NOT NULL,
    original_url TEXT NOT NULL,
    click_count INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE,
    UNIQUE(user_id, mod_id)
);

-- فهارس الروابط المخصصة
CREATE INDEX IF NOT EXISTS idx_custom_download_links_user_id ON public.custom_download_links(user_id);
CREATE INDEX IF NOT EXISTS idx_custom_download_links_mod_id ON public.custom_download_links(mod_id);
CREATE INDEX IF NOT EXISTS idx_custom_download_links_active ON public.custom_download_links(is_active);

-- ===================================================================
-- 8. جداول إحصائيات النظام (System Statistics Tables)
-- ===================================================================

CREATE TABLE IF NOT EXISTS public.system_stats (
    id SERIAL PRIMARY KEY,
    stat_name TEXT UNIQUE NOT NULL,
    stat_value BIGINT DEFAULT 0,
    stat_type TEXT DEFAULT 'counter' CHECK (stat_type IN ('counter', 'gauge', 'histogram')),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    metadata JSONB
);

-- إدراج إحصائيات أساسية
INSERT INTO public.system_stats (stat_name, stat_value, stat_type) VALUES
('total_users', 0, 'counter'),
('total_mods', 0, 'counter'),
('total_downloads', 0, 'counter'),
('total_notifications_sent', 0, 'counter'),
('total_tasks_completed', 0, 'counter')
ON CONFLICT (stat_name) DO NOTHING;

-- ===================================================================
-- 9. إعداد Row Level Security (RLS) للأمان
-- ===================================================================

-- تفعيل RLS على الجداول الحساسة
ALTER TABLE public.saved_notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notification_broadcasts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notification_user_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_ads_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_url_shortener_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_page_customization ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.custom_download_links ENABLE ROW LEVEL SECURITY;

-- ===================================================================
-- 10. إنشاء دوال مساعدة (Helper Functions)
-- ===================================================================

-- دالة لتحديث updated_at تلقائياً
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- إضافة triggers لتحديث updated_at
CREATE TRIGGER update_mods_updated_at BEFORE UPDATE ON public.mods
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_saved_notifications_updated_at BEFORE UPDATE ON public.saved_notifications
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_tasks_updated_at BEFORE UPDATE ON public.tasks
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_ads_settings_updated_at BEFORE UPDATE ON public.user_ads_settings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_url_shortener_settings_updated_at BEFORE UPDATE ON public.user_url_shortener_settings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_page_customization_updated_at BEFORE UPDATE ON public.user_page_customization
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_custom_download_links_updated_at BEFORE UPDATE ON public.custom_download_links
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ===================================================================
-- تم إنشاء جميع الجداول بنجاح!
-- All tables created successfully!
-- ===================================================================
