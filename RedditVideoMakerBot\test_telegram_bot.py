#!/usr/bin/env python3
"""
اختبار بوت Telegram التفاعلي
يتحقق من جميع الوظائف والأزرار
"""

import asyncio
import logging
import sys
import os
from pathlib import Path

# إعداد السجلات
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_telegram_config():
    """اختبار إعدادات Telegram"""
    print("🔍 اختبار إعدادات Telegram...")
    
    try:
        from utils import settings
        config = settings.config
        
        # التحقق من رمز البوت
        bot_token = config.get("telegram", {}).get("bot_token")
        if not bot_token or bot_token == "YOUR_BOT_TOKEN_HERE":
            print("❌ رمز البوت غير مُعرَّف في config.toml")
            return False
        
        print(f"✅ رمز البوت موجود: {bot_token[:10]}...")
        
        # التحقق من معرف المحادثة
        chat_id = config.get("telegram", {}).get("chat_id")
        if chat_id and chat_id != "YOUR_CHAT_ID_HERE":
            print(f"✅ معرف المحادثة موجود: {chat_id}")
        else:
            print("⚠️ معرف المحادثة غير مُعرَّف (سيتم اكتشافه تلقائياً)")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في قراءة الإعدادات: {e}")
        return False

def test_bot_import():
    """اختبار استيراد البوت"""
    print("\n🤖 اختبار استيراد البوت...")
    
    try:
        from automation.telegram_bot import EnhancedTelegramBot
        print("✅ تم استيراد EnhancedTelegramBot بنجاح")
        
        # إنشاء مثيل البوت
        bot = EnhancedTelegramBot()
        print("✅ تم إنشاء مثيل البوت بنجاح")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في استيراد البوت: {e}")
        return False

async def test_bot_connection():
    """اختبار اتصال البوت"""
    print("\n🌐 اختبار اتصال البوت...")
    
    try:
        from automation.telegram_bot import EnhancedTelegramBot
        bot = EnhancedTelegramBot()
        
        # اختبار الاتصال بـ Telegram API
        import requests
        response = requests.get(f"https://api.telegram.org/bot{bot.bot_token}/getMe", timeout=10)
        
        if response.status_code == 200:
            bot_info = response.json()
            if bot_info['ok']:
                print(f"✅ البوت متصل: @{bot_info['result']['username']}")
                return True
            else:
                print(f"❌ رمز البوت غير صحيح: {bot_info}")
                return False
        else:
            print(f"❌ خطأ HTTP: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الاتصال: {e}")
        return False

def test_keyboard_buttons():
    """اختبار أزرار الكيبورد"""
    print("\n⌨️ اختبار أزرار الكيبورد...")
    
    try:
        from telegram import InlineKeyboardButton, InlineKeyboardMarkup
        
        # إنشاء أزرار تجريبية
        keyboard = [
            [
                InlineKeyboardButton("▶️ تشغيل النظام", callback_data="start_system"),
                InlineKeyboardButton("⏹️ إيقاف النظام", callback_data="stop_system")
            ],
            [
                InlineKeyboardButton("📊 حالة النظام", callback_data="system_status"),
                InlineKeyboardButton("📋 السجلات", callback_data="view_logs")
            ]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        print("✅ تم إنشاء أزرار الكيبورد بنجاح")
        print(f"   عدد الصفوف: {len(keyboard)}")
        print(f"   عدد الأزرار: {sum(len(row) for row in keyboard)}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء أزرار الكيبورد: {e}")
        return False

async def test_start_command():
    """اختبار أمر البداية"""
    print("\n🚀 اختبار أمر البداية...")
    
    try:
        from automation.telegram_bot import EnhancedTelegramBot
        from unittest.mock import Mock, AsyncMock
        
        # إنشاء البوت
        bot = EnhancedTelegramBot()
        
        # إنشاء mock objects
        update = Mock()
        update.effective_chat.id = 123456789
        update.message.reply_text = AsyncMock()
        
        context = Mock()
        
        # اختبار أمر البداية
        await bot.start_command(update, context)
        
        # التحقق من استدعاء reply_text
        if update.message.reply_text.called:
            print("✅ تم استدعاء أمر البداية بنجاح")
            
            # فحص المعاملات
            args, kwargs = update.message.reply_text.call_args
            if 'reply_markup' in kwargs:
                print("✅ تم إرسال أزرار الكيبورد")
            else:
                print("⚠️ لم يتم إرسال أزرار الكيبورد")
            
            return True
        else:
            print("❌ لم يتم استدعاء reply_text")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار أمر البداية: {e}")
        return False

def show_bot_instructions():
    """عرض تعليمات تشغيل البوت"""
    print("\n" + "=" * 60)
    print("📋 تعليمات تشغيل بوت Telegram")
    print("=" * 60)
    
    print("""
🔧 للإعداد:
1. تأكد من إعداد bot_token في config.toml
2. شغل: python fix_telegram_issue.py (لإصلاح المشاكل)

🚀 للتشغيل:
1. البوت التفاعلي: python start_telegram_bot.py
2. البوت المستمر: python continuous_bot.py

📱 للاستخدام:
1. ابحث عن البوت في Telegram
2. أرسل /start
3. استخدم الأزرار للتحكم

🔍 للاختبار:
1. اختبار الإعدادات: python test_telegram_bot.py
2. اختبار الإصلاح: python fix_telegram_issue.py
    """)

async def run_comprehensive_test():
    """تشغيل اختبار شامل"""
    print("🧪 اختبار شامل لبوت Telegram")
    print("=" * 60)
    
    tests = [
        ("إعدادات Telegram", test_telegram_config),
        ("استيراد البوت", test_bot_import),
        ("اتصال البوت", test_bot_connection),
        ("أزرار الكيبورد", test_keyboard_buttons),
        ("أمر البداية", test_start_command),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {e}")
            results[test_name] = False
    
    # تلخيص النتائج
    print("\n" + "=" * 60)
    print("📊 ملخص نتائج الاختبار")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nالنتيجة النهائية: {passed}/{total} اختبارات نجحت")
    
    if passed == total:
        print("🎉 ممتاز! البوت جاهز للاستخدام")
        print("\nللتشغيل:")
        print("python start_telegram_bot.py")
    elif passed >= total * 0.7:
        print("👍 جيد! معظم الاختبارات نجحت")
        print("⚠️ قد تحتاج إصلاحات بسيطة")
    else:
        print("❌ يحتاج إصلاحات")
        print("🔧 شغل: python fix_telegram_issue.py")
    
    return passed == total

async def main():
    """الدالة الرئيسية"""
    try:
        # التأكد من وجود مجلد logs
        Path("logs").mkdir(exist_ok=True)
        
        # تشغيل الاختبار الشامل
        success = await run_comprehensive_test()
        
        # عرض التعليمات
        show_bot_instructions()
        
        print(f"\n{'='*60}")
        print("🏁 انتهى اختبار البوت")
        print(f"{'='*60}")
        
        return success
        
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الاختبار بواسطة المستخدم")
        return False
    except Exception as e:
        print(f"\n❌ خطأ حرج في الاختبار: {e}")
        import traceback
        print(f"تفاصيل الخطأ:\n{traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
