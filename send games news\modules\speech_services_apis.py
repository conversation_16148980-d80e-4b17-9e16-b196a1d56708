# واجهات API متخصصة لخدمات تحويل النص إلى صوت
import asyncio
import aiohttp
import json
import base64
import time
from typing import Dict, Optional, Any
from abc import ABC, abstractmethod

from modules.logger import logger

class BaseSpeechAPI(ABC):
    """واجهة أساسية لجميع خدمات تحويل النص إلى صوت"""
    
    def __init__(self, api_key: str, api_url: str, timeout: int = 300):
        self.api_key = api_key
        self.api_url = api_url
        self.timeout = timeout
        self.session = None
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
            
    @abstractmethod
    async def transcribe(self, audio_data: bytes, language: str = "auto") -> Dict[str, Any]:
        """تحويل الصوت إلى نص"""
        pass
        
    @abstractmethod
    def get_supported_languages(self) -> list:
        """الحصول على اللغات المدعومة"""
        pass

class AssemblyAIAPI(BaseSpeechAPI):
    """واجهة AssemblyAI API"""
    
    def __init__(self, api_key: str):
        super().__init__(
            api_key=api_key,
            api_url="https://api.assemblyai.com/v2/transcript",
            timeout=600
        )
        self.upload_url = "https://api.assemblyai.com/v2/upload"
        
    async def transcribe(self, audio_data: bytes, language: str = "auto") -> Dict[str, Any]:
        """تحويل الصوت إلى نص باستخدام AssemblyAI"""
        try:
            # 1. رفع الملف الصوتي
            upload_result = await self._upload_audio(audio_data)
            if not upload_result["success"]:
                return upload_result
                
            audio_url = upload_result["upload_url"]
            
            # 2. إرسال طلب التحويل
            transcript_result = await self._submit_transcript(audio_url, language)
            if not transcript_result["success"]:
                return transcript_result
                
            transcript_id = transcript_result["transcript_id"]
            
            # 3. انتظار اكتمال التحويل
            return await self._wait_for_completion(transcript_id)
            
        except Exception as e:
            logger.error(f"❌ خطأ في AssemblyAI: {e}")
            return {
                "success": False,
                "error": f"خطأ في AssemblyAI: {str(e)}",
                "text": "",
                "confidence": 0.0
            }
            
    async def _upload_audio(self, audio_data: bytes) -> Dict[str, Any]:
        """رفع الملف الصوتي"""
        headers = {"authorization": self.api_key}
        
        async with self.session.post(
            self.upload_url,
            headers=headers,
            data=audio_data,
            timeout=aiohttp.ClientTimeout(total=self.timeout)
        ) as response:
            
            if response.status == 200:
                result = await response.json()
                return {
                    "success": True,
                    "upload_url": result["upload_url"]
                }
            else:
                error_text = await response.text()
                return {
                    "success": False,
                    "error": f"فشل رفع الملف: {error_text}"
                }
                
    async def _submit_transcript(self, audio_url: str, language: str) -> Dict[str, Any]:
        """إرسال طلب التحويل"""
        headers = {"authorization": self.api_key, "content-type": "application/json"}
        
        data = {
            "audio_url": audio_url,
            "language_detection": True if language == "auto" else False,
            "punctuate": True,
            "format_text": True,
            "speaker_labels": False,
            "auto_chapters": False
        }
        
        if language != "auto":
            data["language_code"] = "ar" if language == "arabic" else "en"
            
        async with self.session.post(
            self.api_url,
            headers=headers,
            json=data,
            timeout=aiohttp.ClientTimeout(total=60)
        ) as response:
            
            if response.status == 200:
                result = await response.json()
                return {
                    "success": True,
                    "transcript_id": result["id"]
                }
            else:
                error_text = await response.text()
                return {
                    "success": False,
                    "error": f"فشل إرسال طلب التحويل: {error_text}"
                }
                
    async def _wait_for_completion(self, transcript_id: str) -> Dict[str, Any]:
        """انتظار اكتمال التحويل"""
        headers = {"authorization": self.api_key}
        max_wait_time = 600  # 10 دقائق
        start_time = time.time()
        
        while time.time() - start_time < max_wait_time:
            async with self.session.get(
                f"{self.api_url}/{transcript_id}",
                headers=headers,
                timeout=aiohttp.ClientTimeout(total=30)
            ) as response:
                
                if response.status == 200:
                    result = await response.json()
                    
                    if result["status"] == "completed":
                        return {
                            "success": True,
                            "text": result.get("text", ""),
                            "confidence": result.get("confidence", 0.0),
                            "language": result.get("language_code", "unknown"),
                            "words": result.get("words", [])
                        }
                    elif result["status"] == "error":
                        return {
                            "success": False,
                            "error": f"خطأ في التحويل: {result.get('error', 'خطأ غير محدد')}",
                            "text": "",
                            "confidence": 0.0
                        }
                        
            await asyncio.sleep(5)  # انتظار 5 ثوان
            
        return {
            "success": False,
            "error": "انتهت مهلة انتظار التحويل",
            "text": "",
            "confidence": 0.0
        }
        
    def get_supported_languages(self) -> list:
        """اللغات المدعومة في AssemblyAI"""
        return [
            "en", "es", "fr", "de", "it", "pt", "nl", "hi", "ja", "zh", "ko", "ar"
        ]

class SpeechmaticsAPI(BaseSpeechAPI):
    """واجهة Speechmatics API"""
    
    def __init__(self, api_key: str):
        super().__init__(
            api_key=api_key,
            api_url="https://asr.api.speechmatics.com/v2/jobs",
            timeout=900
        )
        
    async def transcribe(self, audio_data: bytes, language: str = "auto") -> Dict[str, Any]:
        """تحويل الصوت إلى نص باستخدام Speechmatics"""
        try:
            # تحديد اللغة
            lang_code = "ar" if language == "arabic" else "en"
            if language == "auto":
                lang_code = "en"  # افتراضي
                
            # إعداد التكوين
            job_config = {
                "type": "transcription",
                "transcription_config": {
                    "language": lang_code,
                    "operating_point": "enhanced",
                    "enable_partials": False,
                    "max_delay": 10,
                    "enable_entities": True,
                    "diarization": "none"
                }
            }
            
            # إنشاء FormData
            data = aiohttp.FormData()
            data.add_field('config', json.dumps(job_config), content_type='application/json')
            data.add_field('data_file', audio_data, filename='audio.mp3', content_type='audio/mpeg')
            
            headers = {"Authorization": f"Bearer {self.api_key}"}
            
            # إرسال الطلب
            async with self.session.post(
                self.api_url,
                headers=headers,
                data=data,
                timeout=aiohttp.ClientTimeout(total=self.timeout)
            ) as response:
                
                if response.status != 201:
                    error_text = await response.text()
                    return {
                        "success": False,
                        "error": f"فشل إرسال الطلب: {error_text}",
                        "text": "",
                        "confidence": 0.0
                    }
                
                result = await response.json()
                job_id = result["id"]
                
            # انتظار اكتمال المعالجة
            return await self._wait_for_job_completion(job_id)
            
        except Exception as e:
            logger.error(f"❌ خطأ في Speechmatics: {e}")
            return {
                "success": False,
                "error": f"خطأ في Speechmatics: {str(e)}",
                "text": "",
                "confidence": 0.0
            }
            
    async def _wait_for_job_completion(self, job_id: str) -> Dict[str, Any]:
        """انتظار اكتمال المهمة"""
        headers = {"Authorization": f"Bearer {self.api_key}"}
        max_wait_time = 600  # 10 دقائق
        start_time = time.time()
        
        while time.time() - start_time < max_wait_time:
            # فحص حالة المهمة
            async with self.session.get(
                f"{self.api_url}/{job_id}",
                headers=headers,
                timeout=aiohttp.ClientTimeout(total=30)
            ) as response:
                
                if response.status == 200:
                    status_result = await response.json()
                    job_status = status_result["job"]["status"]
                    
                    if job_status == "done":
                        # الحصول على النتيجة
                        return await self._get_transcript_result(job_id)
                    elif job_status == "rejected":
                        errors = status_result["job"].get("errors", ["خطأ غير محدد"])
                        return {
                            "success": False,
                            "error": f"تم رفض المهمة: {errors[0]}",
                            "text": "",
                            "confidence": 0.0
                        }
                        
            await asyncio.sleep(5)
            
        return {
            "success": False,
            "error": "انتهت مهلة انتظار التحويل",
            "text": "",
            "confidence": 0.0
        }
        
    async def _get_transcript_result(self, job_id: str) -> Dict[str, Any]:
        """الحصول على نتيجة التحويل"""
        headers = {"Authorization": f"Bearer {self.api_key}"}
        
        async with self.session.get(
            f"{self.api_url}/{job_id}/transcript",
            headers=headers,
            timeout=aiohttp.ClientTimeout(total=30)
        ) as response:
            
            if response.status == 200:
                transcript_data = await response.json()
                
                # استخراج النص والثقة
                text_parts = []
                confidence_scores = []
                
                for result in transcript_data.get("results", []):
                    for alternative in result.get("alternatives", []):
                        text_parts.append(alternative.get("content", ""))
                        confidence_scores.append(alternative.get("confidence", 0.0))
                
                full_text = " ".join(text_parts)
                avg_confidence = sum(confidence_scores) / len(confidence_scores) if confidence_scores else 0.0
                
                return {
                    "success": True,
                    "text": full_text,
                    "confidence": avg_confidence,
                    "language": "detected",
                    "results": transcript_data.get("results", [])
                }
            else:
                error_text = await response.text()
                return {
                    "success": False,
                    "error": f"فشل الحصول على النتيجة: {error_text}",
                    "text": "",
                    "confidence": 0.0
                }
                
    def get_supported_languages(self) -> list:
        """اللغات المدعومة في Speechmatics"""
        return [
            "en", "es", "fr", "de", "it", "pt", "ru", "ja", "zh", "ko", "ar", "hi"
        ]

class IBMWatsonAPI(BaseSpeechAPI):
    """واجهة IBM Watson Speech-to-Text API"""

    def __init__(self, api_key: str, service_url: str):
        super().__init__(
            api_key=api_key,
            api_url=service_url,
            timeout=600
        )

    async def transcribe(self, audio_data: bytes, language: str = "auto") -> Dict[str, Any]:
        """تحويل الصوت إلى نص باستخدام IBM Watson"""
        try:
            # تحديد نموذج اللغة
            lang_model = "ar-MS_BroadbandModel" if language == "arabic" else "en-US_BroadbandModel"
            if language == "auto":
                lang_model = "en-US_BroadbandModel"

            # ترميز الصوت
            audio_base64 = base64.b64encode(audio_data).decode('utf-8')

            request_data = {
                "audio": audio_base64,
                "content_type": "audio/mp3",
                "model": lang_model,
                "timestamps": True,
                "word_confidence": True,
                "smart_formatting": True,
                "speaker_labels": False
            }

            # إعداد المصادقة
            auth = aiohttp.BasicAuth("apikey", self.api_key)
            headers = {"Content-Type": "application/json"}

            async with self.session.post(
                f"{self.api_url}/v1/recognize",
                headers=headers,
                auth=auth,
                json=request_data,
                timeout=aiohttp.ClientTimeout(total=self.timeout)
            ) as response:

                if response.status == 200:
                    result = await response.json()

                    # استخراج النص والثقة
                    text_parts = []
                    confidence_scores = []

                    for result_item in result.get("results", []):
                        for alternative in result_item.get("alternatives", []):
                            text_parts.append(alternative.get("transcript", ""))
                            confidence_scores.append(alternative.get("confidence", 0.0))

                    full_text = " ".join(text_parts).strip()
                    avg_confidence = sum(confidence_scores) / len(confidence_scores) if confidence_scores else 0.0

                    return {
                        "success": True,
                        "text": full_text,
                        "confidence": avg_confidence,
                        "language": lang_model.split("_")[0],
                        "results": result.get("results", [])
                    }
                else:
                    error_text = await response.text()
                    return {
                        "success": False,
                        "error": f"فشل الطلب: {error_text}",
                        "text": "",
                        "confidence": 0.0
                    }

        except Exception as e:
            logger.error(f"❌ خطأ في IBM Watson: {e}")
            return {
                "success": False,
                "error": f"خطأ في IBM Watson: {str(e)}",
                "text": "",
                "confidence": 0.0
            }

    def get_supported_languages(self) -> list:
        """اللغات المدعومة في IBM Watson"""
        return [
            "ar-MS", "en-US", "en-GB", "es-ES", "fr-FR", "de-DE", "it-IT",
            "ja-JP", "ko-KR", "pt-BR", "zh-CN"
        ]

class AzureSpeechAPI(BaseSpeechAPI):
    """واجهة Microsoft Azure Speech-to-Text API"""

    def __init__(self, api_key: str, region: str):
        endpoint = f"https://{region}.stt.speech.microsoft.com/speech/recognition/conversation/cognitiveservices/v1"
        super().__init__(
            api_key=api_key,
            api_url=endpoint,
            timeout=300
        )
        self.region = region

    async def transcribe(self, audio_data: bytes, language: str = "auto") -> Dict[str, Any]:
        """تحويل الصوت إلى نص باستخدام Azure Speech"""
        try:
            # تحديد اللغة
            lang_code = "ar-SA" if language == "arabic" else "en-US"
            if language == "auto":
                lang_code = "en-US"

            headers = {
                "Ocp-Apim-Subscription-Key": self.api_key,
                "Content-Type": "audio/wav; codecs=audio/pcm; samplerate=16000",
                "Accept": "application/json"
            }

            params = {
                "language": lang_code,
                "format": "detailed",
                "profanity": "masked"
            }

            async with self.session.post(
                self.api_url,
                headers=headers,
                params=params,
                data=audio_data,
                timeout=aiohttp.ClientTimeout(total=self.timeout)
            ) as response:

                if response.status == 200:
                    result = await response.json()

                    if result.get("RecognitionStatus") == "Success":
                        text = result.get("DisplayText", "")
                        confidence = result.get("Confidence", 0.0)

                        return {
                            "success": True,
                            "text": text,
                            "confidence": confidence,
                            "language": lang_code,
                            "duration": result.get("Duration", 0)
                        }
                    else:
                        return {
                            "success": False,
                            "error": f"فشل التعرف: {result.get('RecognitionStatus', 'خطأ غير محدد')}",
                            "text": "",
                            "confidence": 0.0
                        }
                else:
                    error_text = await response.text()
                    return {
                        "success": False,
                        "error": f"فشل الطلب: {error_text}",
                        "text": "",
                        "confidence": 0.0
                    }

        except Exception as e:
            logger.error(f"❌ خطأ في Azure Speech: {e}")
            return {
                "success": False,
                "error": f"خطأ في Azure Speech: {str(e)}",
                "text": "",
                "confidence": 0.0
            }

    def get_supported_languages(self) -> list:
        """اللغات المدعومة في Azure Speech"""
        return [
            "ar-SA", "ar-EG", "en-US", "en-GB", "es-ES", "fr-FR", "de-DE",
            "it-IT", "ja-JP", "ko-KR", "pt-BR", "zh-CN", "hi-IN"
        ]

class GoogleCloudSpeechAPI(BaseSpeechAPI):
    """واجهة Google Cloud Speech-to-Text API"""

    def __init__(self, api_key: str, project_id: str = ""):
        super().__init__(
            api_key=api_key,
            api_url="https://speech.googleapis.com/v1/speech:recognize",
            timeout=300
        )
        self.project_id = project_id

    async def transcribe(self, audio_data: bytes, language: str = "auto") -> Dict[str, Any]:
        """تحويل الصوت إلى نص باستخدام Google Cloud Speech"""
        try:
            # تحديد اللغة
            lang_code = "ar-SA" if language == "arabic" else "en-US"
            if language == "auto":
                lang_code = "en-US"

            # ترميز الصوت
            audio_base64 = base64.b64encode(audio_data).decode('utf-8')

            request_data = {
                "config": {
                    "encoding": "MP3",
                    "sampleRateHertz": 16000,
                    "languageCode": lang_code,
                    "enableAutomaticPunctuation": True,
                    "enableWordTimeOffsets": True,
                    "enableWordConfidence": True,
                    "model": "latest_long"
                },
                "audio": {
                    "content": audio_base64
                }
            }

            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }

            async with self.session.post(
                self.api_url,
                headers=headers,
                json=request_data,
                timeout=aiohttp.ClientTimeout(total=self.timeout)
            ) as response:

                if response.status == 200:
                    result = await response.json()

                    # استخراج النص والثقة
                    text_parts = []
                    confidence_scores = []

                    for result_item in result.get("results", []):
                        for alternative in result_item.get("alternatives", []):
                            text_parts.append(alternative.get("transcript", ""))
                            confidence_scores.append(alternative.get("confidence", 0.0))

                    full_text = " ".join(text_parts).strip()
                    avg_confidence = sum(confidence_scores) / len(confidence_scores) if confidence_scores else 0.0

                    return {
                        "success": True,
                        "text": full_text,
                        "confidence": avg_confidence,
                        "language": lang_code,
                        "results": result.get("results", [])
                    }
                else:
                    error_text = await response.text()
                    return {
                        "success": False,
                        "error": f"فشل الطلب: {error_text}",
                        "text": "",
                        "confidence": 0.0
                    }

        except Exception as e:
            logger.error(f"❌ خطأ في Google Cloud Speech: {e}")
            return {
                "success": False,
                "error": f"خطأ في Google Cloud Speech: {str(e)}",
                "text": "",
                "confidence": 0.0
            }

    def get_supported_languages(self) -> list:
        """اللغات المدعومة في Google Cloud Speech"""
        return [
            "ar-SA", "ar-EG", "en-US", "en-GB", "es-ES", "fr-FR", "de-DE",
            "it-IT", "ja-JP", "ko-KR", "pt-BR", "zh-CN", "hi-IN", "ru-RU"
        ]

class WitAIAPI(BaseSpeechAPI):
    """واجهة Wit.ai Speech-to-Text API"""

    def __init__(self, access_token: str):
        super().__init__(
            api_key=access_token,
            api_url="https://api.wit.ai/speech",
            timeout=120
        )

    async def transcribe(self, audio_data: bytes, language: str = "auto") -> Dict[str, Any]:
        """تحويل الصوت إلى نص باستخدام Wit.ai"""
        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "audio/mpeg3"
            }

            # إضافة معاملات اللغة
            params = {}
            if language == "arabic":
                params["v"] = "20220622"  # إصدار يدعم العربية

            async with self.session.post(
                self.api_url,
                headers=headers,
                params=params,
                data=audio_data,
                timeout=aiohttp.ClientTimeout(total=self.timeout)
            ) as response:

                if response.status == 200:
                    result = await response.json()

                    text = result.get("text", "")
                    confidence = result.get("confidence", 0.0)

                    if text:
                        return {
                            "success": True,
                            "text": text,
                            "confidence": confidence,
                            "language": language,
                            "entities": result.get("entities", {}),
                            "intents": result.get("intents", [])
                        }
                    else:
                        return {
                            "success": False,
                            "error": "لم يتم التعرف على أي نص",
                            "text": "",
                            "confidence": 0.0
                        }
                else:
                    error_text = await response.text()
                    return {
                        "success": False,
                        "error": f"فشل الطلب: {error_text}",
                        "text": "",
                        "confidence": 0.0
                    }

        except Exception as e:
            logger.error(f"❌ خطأ في Wit.ai: {e}")
            return {
                "success": False,
                "error": f"خطأ في Wit.ai: {str(e)}",
                "text": "",
                "confidence": 0.0
            }

    def get_supported_languages(self) -> list:
        """اللغات المدعومة في Wit.ai"""
        return [
            "en", "es", "fr", "de", "it", "pt", "ru", "ja", "zh", "ko", "ar", "hi"
        ]
