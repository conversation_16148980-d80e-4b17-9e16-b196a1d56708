#!/usr/bin/env python3
"""
فحص شامل وعميق للنظام لاكتشاف سبب إنشاء ملفات صوتية كثيرة
"""

import os
import sys
import re
import logging
from pathlib import Path
from collections import defaultdict

# إعداد السجلات
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def analyze_audio_files_pattern():
    """تحليل نمط الملفات الصوتية المنشأة"""
    print("🔍 تحليل نمط الملفات الصوتية...")
    
    # إعادة إنشاء مجلد temp للفحص
    temp_dir = "assets/temp"
    if not os.path.exists(temp_dir):
        print("❌ مجلد temp غير موجود - لا يمكن التحليل")
        return
    
    # فحص آخر فيديو تم إنشاؤه
    results_dir = "results"
    latest_video = None
    latest_time = 0
    
    if os.path.exists(results_dir):
        for root, dirs, files in os.walk(results_dir):
            for file in files:
                if file.endswith('.mp4'):
                    file_path = os.path.join(root, file)
                    mtime = os.path.getmtime(file_path)
                    if mtime > latest_time:
                        latest_time = mtime
                        latest_video = file_path
    
    if latest_video:
        print(f"📹 آخر فيديو: {latest_video}")
        print(f"📅 تاريخ الإنشاء: {time.ctime(latest_time)}")
    
    # تحليل الملفات المؤقتة (إذا كانت موجودة)
    audio_files = []
    for root, dirs, files in os.walk(temp_dir):
        for file in files:
            if file.endswith('.mp3'):
                file_path = os.path.join(root, file)
                audio_files.append(file_path)
    
    print(f"🎵 عدد الملفات الصوتية الموجودة: {len(audio_files)}")
    
    if audio_files:
        # تحليل أسماء الملفات
        file_names = [os.path.basename(f) for f in audio_files]
        print("📋 أسماء الملفات:")
        for name in sorted(file_names)[:20]:  # أول 20 ملف
            print(f"   {name}")
        
        if len(file_names) > 20:
            print(f"   ... و {len(file_names) - 20} ملف آخر")

def analyze_code_for_audio_generation():
    """تحليل الكود لاكتشاف مواضع إنشاء الصوت"""
    print("\n🔍 تحليل الكود لاكتشاف مواضع إنشاء الصوت...")
    
    files_to_check = [
        "main.py",
        "TTS/engine_wrapper.py", 
        "video_creation/final_video.py",
        "reddit/subreddit.py"
    ]
    
    audio_generation_points = []
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            print(f"\n📄 فحص {file_path}...")
            
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')
            
            # البحث عن استدعاءات إنشاء الصوت
            patterns = [
                r'\.run\(',
                r'call_tts\(',
                r'generate_speech\(',
                r'\.mp3',
                r'for.*comment',
                r'for.*idx',
                r'range\(',
                r'times_to_run'
            ]
            
            for i, line in enumerate(lines):
                for pattern in patterns:
                    if re.search(pattern, line, re.IGNORECASE):
                        audio_generation_points.append({
                            'file': file_path,
                            'line': i + 1,
                            'code': line.strip(),
                            'pattern': pattern
                        })
    
    # تجميع النتائج
    print(f"\n📊 تم العثور على {len(audio_generation_points)} نقطة محتملة لإنشاء الصوت:")
    
    by_file = defaultdict(list)
    for point in audio_generation_points:
        by_file[point['file']].append(point)
    
    for file_path, points in by_file.items():
        print(f"\n📄 {file_path} ({len(points)} نقطة):")
        for point in points[:10]:  # أول 10 نقاط
            print(f"   السطر {point['line']}: {point['code']}")
        if len(points) > 10:
            print(f"   ... و {len(points) - 10} نقطة أخرى")

def analyze_loops_and_iterations():
    """تحليل الحلقات والتكرارات في الكود"""
    print("\n🔄 تحليل الحلقات والتكرارات...")
    
    critical_files = [
        "main.py",
        "TTS/engine_wrapper.py",
        "video_creation/final_video.py"
    ]
    
    for file_path in critical_files:
        if os.path.exists(file_path):
            print(f"\n📄 فحص الحلقات في {file_path}...")
            
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')
            
            # البحث عن حلقات for و while
            loop_patterns = [
                r'for\s+.*\s+in\s+',
                r'while\s+.*:',
                r'range\(',
                r'enumerate\(',
                r'times_to_run'
            ]
            
            loops_found = []
            for i, line in enumerate(lines):
                for pattern in loop_patterns:
                    if re.search(pattern, line, re.IGNORECASE):
                        # البحث عن السياق (5 أسطر قبل وبعد)
                        start = max(0, i - 2)
                        end = min(len(lines), i + 3)
                        context = lines[start:end]
                        
                        loops_found.append({
                            'line': i + 1,
                            'code': line.strip(),
                            'context': context,
                            'pattern': pattern
                        })
            
            if loops_found:
                print(f"   🔄 تم العثور على {len(loops_found)} حلقة:")
                for loop in loops_found:
                    print(f"      السطر {loop['line']}: {loop['code']}")
                    
                    # فحص إذا كانت الحلقة تحتوي على إنشاء صوت
                    context_text = '\n'.join(loop['context'])
                    if any(keyword in context_text.lower() for keyword in ['tts', 'mp3', 'audio', 'sound']):
                        print(f"      ⚠️ هذه الحلقة قد تُنشئ ملفات صوتية!")
            else:
                print("   ✅ لا توجد حلقات مشبوهة")

def check_config_settings():
    """فحص إعدادات التكوين المؤثرة على إنشاء الصوت"""
    print("\n⚙️ فحص إعدادات التكوين...")
    
    try:
        import toml
        with open("config.toml", 'r', encoding='utf-8') as f:
            config = toml.load(f)
        
        # الإعدادات المهمة
        critical_settings = {
            'times_to_run': config.get('settings', {}).get('times_to_run', 'غير محدد'),
            'storymode': config.get('settings', {}).get('storymode', 'غير محدد'),
            'storymode_max_length': config.get('settings', {}).get('storymode_max_length', 'غير محدد'),
            'max_comment_length': config.get('settings', {}).get('max_comment_length', 'غير محدد'),
            'voice_choice': config.get('settings', {}).get('tts', {}).get('voice_choice', 'غير محدد'),
            'max_retries': config.get('settings', {}).get('tts', {}).get('priority_order', {}).get('max_retries_per_engine', 'غير محدد')
        }
        
        print("📋 الإعدادات الحرجة:")
        for setting, value in critical_settings.items():
            status = "⚠️" if value == 'غير محدد' else "✅"
            print(f"   {status} {setting}: {value}")
            
            # تحذيرات خاصة
            if setting == 'times_to_run' and isinstance(value, int) and value > 1:
                print(f"      🚨 خطر! سيتم إنشاء {value} فيديوهات!")
            elif setting == 'storymode' and value:
                print(f"      ⚠️ storymode مفعل - قد يُنشئ ملفات كثيرة")
        
    except Exception as e:
        print(f"❌ خطأ في قراءة الإعدادات: {e}")

def analyze_reddit_data_processing():
    """تحليل معالجة بيانات Reddit"""
    print("\n📝 تحليل معالجة بيانات Reddit...")
    
    try:
        # فحص ملف subreddit.py
        subreddit_file = "reddit/subreddit.py"
        if os.path.exists(subreddit_file):
            with open(subreddit_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # البحث عن حلقات معالجة التعليقات
            comment_processing = []
            lines = content.split('\n')
            
            for i, line in enumerate(lines):
                if any(keyword in line.lower() for keyword in ['comment', 'reply', 'submission']):
                    if any(loop_word in line for loop_word in ['for', 'while', 'range']):
                        comment_processing.append({
                            'line': i + 1,
                            'code': line.strip()
                        })
            
            if comment_processing:
                print(f"🔄 تم العثور على {len(comment_processing)} حلقة معالجة تعليقات:")
                for proc in comment_processing:
                    print(f"   السطر {proc['line']}: {proc['code']}")
            else:
                print("✅ لا توجد حلقات معالجة مشبوهة")
        
    except Exception as e:
        print(f"❌ خطأ في تحليل Reddit: {e}")

def check_video_creation_process():
    """فحص عملية إنشاء الفيديو"""
    print("\n🎬 فحص عملية إنشاء الفيديو...")
    
    try:
        final_video_file = "video_creation/final_video.py"
        if os.path.exists(final_video_file):
            with open(final_video_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # البحث عن حلقات إنشاء الصوت
            audio_loops = []
            lines = content.split('\n')
            
            for i, line in enumerate(lines):
                if 'for' in line and any(keyword in line.lower() for keyword in ['comment', 'audio', 'tts', 'mp3']):
                    # الحصول على السياق
                    start = max(0, i - 3)
                    end = min(len(lines), i + 5)
                    context = lines[start:end]
                    
                    audio_loops.append({
                        'line': i + 1,
                        'code': line.strip(),
                        'context': context
                    })
            
            if audio_loops:
                print(f"🔄 تم العثور على {len(audio_loops)} حلقة إنشاء صوت:")
                for loop in audio_loops:
                    print(f"   السطر {loop['line']}: {loop['code']}")
                    print("   السياق:")
                    for ctx_line in loop['context']:
                        if ctx_line.strip():
                            print(f"      {ctx_line}")
                    print()
            else:
                print("✅ لا توجد حلقات إنشاء صوت مشبوهة")
        
    except Exception as e:
        print(f"❌ خطأ في فحص إنشاء الفيديو: {e}")

def run_deep_analysis():
    """تشغيل التحليل العميق"""
    print("🕵️ بدء التحليل العميق للنظام")
    print("=" * 60)
    
    analyses = [
        ("تحليل نمط الملفات الصوتية", analyze_audio_files_pattern),
        ("تحليل الكود لإنشاء الصوت", analyze_code_for_audio_generation),
        ("تحليل الحلقات والتكرارات", analyze_loops_and_iterations),
        ("فحص إعدادات التكوين", check_config_settings),
        ("تحليل معالجة Reddit", analyze_reddit_data_processing),
        ("فحص عملية إنشاء الفيديو", check_video_creation_process),
    ]
    
    for analysis_name, analysis_func in analyses:
        try:
            analysis_func()
        except Exception as e:
            print(f"❌ خطأ في {analysis_name}: {e}")
    
    print("\n" + "=" * 60)
    print("🏁 انتهى التحليل العميق")
    print("=" * 60)

if __name__ == "__main__":
    import time
    
    try:
        run_deep_analysis()
        
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف التحليل بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ حرج في التحليل: {e}")
        import traceback
        print(f"تفاصيل الخطأ:\n{traceback.format_exc()}")
