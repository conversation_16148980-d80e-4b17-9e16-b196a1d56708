# متطلبات Cloud Functions محسنة لـ Free Tier
# تم تقليل المكتبات للحد الأدنى لتوفير الذاكرة

# Google Cloud Libraries (أساسية)
functions-framework==3.4.0
google-cloud-storage==2.10.0
google-auth==2.23.4

# Reddit API (خفيف)
praw==7.7.1
prawcore==2.3.0

# معالجة الفيديو (مبسطة)
moviepy==1.0.3
Pillow==10.0.1

# Gemini AI (خفيف)
google-generativeai==0.3.2

# YouTube API (أساسي)
google-api-python-client==2.108.0
google-auth-oauthlib==1.1.0

# أدوات مساعدة خفيفة
requests==2.32.3
python-telegram-bot==20.7

# معالجة النصوص (مبسط)
unidecode==1.3.8

# تجنب المكتبات الثقيلة:
# - لا ffmpeg-python (ثقيل جداً)
# - لا playwright (يحتاج Chrome)
# - لا rich (غير ضروري للـ Functions)
# - لا Flask (غير مطلوب)
# - لا psutil (اختياري)
# - لا schedule (نستخدم Cloud Scheduler)
# - لا watchdog (غير مطلوب)

# ملاحظة: إجمالي حجم المكتبات يجب أن يكون أقل من 100 MB
