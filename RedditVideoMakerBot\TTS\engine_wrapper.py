import os
import re
import logging
from pathlib import Path
from typing import Tuple

import numpy as np
import translators
from moviepy.audio.AudioClip import AudioClip
from moviepy.audio.fx.volumex import volumex
from moviepy.editor import AudioFileClip
from rich.progress import track

from utils import settings
from utils.console import print_step, print_substep
from utils.voice import sanitize_text
from TTS.smart_tts_manager import SmartTTSManager
from automation.telegram_bot import send_error, send_notification

# إعداد logger
logger = logging.getLogger(__name__)

DEFAULT_MAX_LENGTH: int = (
    50  # Video length variable, edit this on your own risk. It should work, but it's not supported
)


class TTSEngine:
    """Calls the given TTS engine to reduce code duplication and allow multiple TTS engines.

    Args:
        tts_module            : The TTS module. Your module should handle the TTS itself and saving to the given path under the run method.
        reddit_object         : The reddit object that contains the posts to read.
        path (Optional)       : The unix style path to save the mp3 files to. This must not have leading or trailing slashes.
        max_length (Optional) : The maximum length of the mp3 files in total.

    Notes:
        tts_module must take the arguments text and filepath.
    """

    def __init__(
        self,
        tts_module,
        reddit_object: dict,
        path: str = "assets/temp/",
        max_length: int = DEFAULT_MAX_LENGTH,
        last_clip_length: int = 0,
    ):
        # استخدام النظام الذكي مع ترتيب الأولوية من الإعدادات
        self.smart_tts = SmartTTSManager()

        # إنشاء tts_module للتوافق مع الكود القديم
        try:
            from TTS.GTTS import GTTS
            self.tts_module = GTTS()
        except:
            # في حالة فشل GoogleTranslate، استخدم pyttsx كبديل
            from TTS.pyttsx import pyttsx
            self.tts_module = pyttsx()

        # إرسال إشعار بدء TTS
        send_notification(f"🎤 بدء تحويل النص إلى صوت باستخدام النظام الذكي")

        self.reddit_object = reddit_object

        self.redditid = re.sub(r"[^\w\s-]", "", reddit_object["thread_id"])
        self.path = path + self.redditid + "/mp3"
        self.max_length = max_length
        self.length = 0
        self.last_clip_length = last_clip_length

    def add_periods(
        self,
    ):  # adds periods to the end of paragraphs (where people often forget to put them) so tts doesn't blend sentences
        for comment in self.reddit_object["comments"]:
            # remove links
            regex_urls = r"((http|https)\:\/\/)?[a-zA-Z0-9\.\/\?\:@\-_=#]+\.([a-zA-Z]){2,6}([a-zA-Z0-9\.\&\/\?\:@\-_=#])*"
            comment["comment_body"] = re.sub(regex_urls, " ", comment["comment_body"])
            comment["comment_body"] = comment["comment_body"].replace("\n", ". ")
            comment["comment_body"] = re.sub(r"\bAI\b", "A.I", comment["comment_body"])
            comment["comment_body"] = re.sub(r"\bAGI\b", "A.G.I", comment["comment_body"])
            if comment["comment_body"][-1] != ".":
                comment["comment_body"] += "."
            comment["comment_body"] = comment["comment_body"].replace(". . .", ".")
            comment["comment_body"] = comment["comment_body"].replace(".. . ", ".")
            comment["comment_body"] = comment["comment_body"].replace(". . ", ".")
            comment["comment_body"] = re.sub(r'\."\.', '".', comment["comment_body"])

    def run(self) -> Tuple[int, int]:
        Path(self.path).mkdir(parents=True, exist_ok=True)
        print_step("Saving Text to MP3 files...")

        self.add_periods()
        self.call_tts("title", process_text(self.reddit_object["thread_title"]))
        # processed_text = ##self.reddit_object["thread_post"] != ""
        idx = 0

        if settings.config["settings"]["storymode"]:
            if settings.config["settings"]["storymodemethod"] == 0:
                if len(self.reddit_object["thread_post"]) > self.tts_module.max_chars:
                    self.split_post(self.reddit_object["thread_post"], "postaudio")
                else:
                    self.call_tts("postaudio", process_text(self.reddit_object["thread_post"]))
            elif settings.config["settings"]["storymodemethod"] == 1:
                for idx, text in track(enumerate(self.reddit_object["thread_post"])):
                    self.call_tts(f"postaudio-{idx}", process_text(text))

        else:
            # حد أقصى للتعليقات لتجنب إنشاء ملفات كثيرة
            MAX_COMMENTS = 10
            comments_to_process = self.reddit_object["comments"][:MAX_COMMENTS]

            logger.info(f"🎤 معالجة {len(comments_to_process)} تعليق من أصل {len(self.reddit_object['comments'])}")

            for idx, comment in track(enumerate(comments_to_process), "Saving..."):
                # ! Stop creating mp3 files if the length is greater than max length.
                if self.length > self.max_length and idx > 1:
                    self.length -= self.last_clip_length
                    idx -= 1
                    break

                comment_length = len(comment["comment_body"])

                # تخطي التعليقات الطويلة جداً لتجنب إنشاء ملفات كثيرة
                if comment_length > self.tts_module.max_chars * 3:  # أكثر من 15000 حرف
                    logger.warning(f"⚠️ تخطي تعليق طويل جداً ({comment_length} حرف)")
                    continue

                if comment_length > self.tts_module.max_chars:
                    # تقسيم التعليق فقط إذا كان ضرورياً
                    self.split_post(comment["comment_body"], idx)
                else:
                    # التعليق قصير، إنشاء ملف واحد فقط
                    self.call_tts(f"{idx}", process_text(comment["comment_body"]))

        print_substep("Saved Text to MP3 files successfully.", style="bold green")
        return self.length, idx

    def split_post(self, text: str, idx):
        split_files = []
        split_text = [
            x.group().strip()
            for x in re.finditer(
                r" *(((.|\n){0," + str(self.tts_module.max_chars) + r"})(\.|.$))", text
            )
        ]

        # حد أقصى 5 أجزاء لكل تعليق لتجنب إنشاء ملفات كثيرة
        MAX_PARTS = 5
        if len(split_text) > MAX_PARTS:
            logger.warning(f"⚠️ تقليل عدد أجزاء التعليق من {len(split_text)} إلى {MAX_PARTS}")
            split_text = split_text[:MAX_PARTS]

        self.create_silence_mp3()

        idy = None
        for idy, text_cut in enumerate(split_text):
            newtext = process_text(text_cut)
            # print(f"{idx}-{idy}: {newtext}\n")

            if not newtext or newtext.isspace():
                print("newtext was blank because sanitized split text resulted in none")
                continue
            else:
                self.call_tts(f"{idx}-{idy}.part", newtext)
                with open(f"{self.path}/list.txt", "w") as f:
                    for idz in range(0, len(split_text)):
                        f.write("file " + f"'{idx}-{idz}.part.mp3'" + "\n")
                    split_files.append(str(f"{self.path}/{idx}-{idy}.part.mp3"))
                    f.write("file " + f"'silence.mp3'" + "\n")

                os.system(
                    "ffmpeg -f concat -y -hide_banner -loglevel panic -safe 0 "
                    + "-i "
                    + f"{self.path}/list.txt "
                    + "-c copy "
                    + f"{self.path}/{idx}.mp3"
                )
        try:
            for i in range(0, len(split_files)):
                os.unlink(split_files[i])
        except FileNotFoundError as e:
            print("File not found: " + e.filename)
        except OSError:
            print("OSError")

    def call_tts(self, filename: str, text: str):
        filepath = f"{self.path}/{filename}.mp3"

        # استخدام النظام الذكي أو التقليدي
        if self.smart_tts:
            try:
                success = self.smart_tts.generate_speech(text, filepath)
                if success:
                    return
                else:
                    logger.warning("⚠️ فشل النظام الذكي، التبديل للمحرك التقليدي")
            except Exception as e:
                logger.warning(f"⚠️ خطأ في النظام الذكي: {e}")

        # استخدام المحرك التقليدي كبديل
        if self.tts_module:
            try:
                self.tts_module.run(
                    text,
                    filepath=filepath,
                    random_voice=settings.config["settings"]["tts"]["random_voice"],
                )
            except Exception as e:
                error_msg = f"❌ فشل في إنشاء الصوت للملف: {filename} - {str(e)}"
                logger.error(error_msg)
                send_error(error_msg)
                raise Exception(f"فشل جميع محركات الصوت: {e}")
        else:
            error_msg = "❌ لا يوجد محرك TTS متاح"
            logger.error(error_msg)
            send_error(error_msg)
            raise Exception(error_msg)
        # try:
        #     self.length += MP3(f"{self.path}/{filename}.mp3").info.length
        # except (MutagenError, HeaderNotFoundError):
        #     self.length += sox.file_info.duration(f"{self.path}/{filename}.mp3")
        try:
            clip = AudioFileClip(f"{self.path}/{filename}.mp3")
            self.last_clip_length = clip.duration
            self.length += clip.duration
            clip.close()
        except Exception as e:
            logger.warning(f"⚠️ فشل في قراءة مدة الملف الصوتي {filename}.mp3: {e}")
            # لا نقوم بإعادة تعيين self.length إلى 0، بل نحتفظ بالقيمة الحالية

    def create_silence_mp3(self):
        silence_duration = settings.config["settings"]["tts"]["silence_duration"]
        silence = AudioClip(
            make_frame=lambda t: np.sin(440 * 2 * np.pi * t),
            duration=silence_duration,
            fps=44100,
        )
        silence = volumex(silence, 0)
        silence.write_audiofile(f"{self.path}/silence.mp3", fps=44100, verbose=False, logger=None)


def process_text(text: str, clean: bool = True):
    lang = settings.config["reddit"]["thread"]["post_lang"]
    new_text = sanitize_text(text) if clean else text
    if lang:
        print_substep("Translating Text...")
        translated_text = translators.translate_text(text, translator="google", to_language=lang)
        new_text = sanitize_text(translated_text)
    return new_text
