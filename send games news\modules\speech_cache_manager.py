# نظام التخزين المؤقت الذكي لخدمات تحويل النص إلى صوت
import os
import json
import hashlib
import sqlite3
import time
import gzip
from datetime import datetime, timedelta
from typing import Dict, Optional, Any, List, Tuple
from dataclasses import dataclass, asdict
import threading

from modules.logger import logger

@dataclass
class CacheEntry:
    """مدخل في التخزين المؤقت"""
    audio_hash: str
    text: str
    confidence: float
    language: str
    service_used: str
    created_at: datetime
    last_accessed: datetime
    access_count: int
    file_size: int
    duration_seconds: float
    quality_score: float

class SpeechCacheManager:
    """مدير التخزين المؤقت الذكي لخدمات تحويل النص إلى صوت"""
    
    def __init__(self, cache_dir: str = "cache/speech_cache", max_size_mb: int = 500):
        self.cache_dir = cache_dir
        self.max_size_mb = max_size_mb
        self.max_size_bytes = max_size_mb * 1024 * 1024
        self.db_path = os.path.join(cache_dir, "cache_index.db")
        self.audio_dir = os.path.join(cache_dir, "audio")
        self.text_dir = os.path.join(cache_dir, "text")
        
        # إعدادات التخزين المؤقت
        self.max_age_days = 30  # الحد الأقصى لعمر المدخلات
        self.cleanup_threshold = 0.8  # نسبة امتلاء التخزين للبدء في التنظيف
        self.min_quality_score = 0.5  # الحد الأدنى لجودة النص للحفظ
        
        # قفل للأمان في البيئة متعددة الخيوط
        self._lock = threading.RLock()
        
        self._initialize_cache()
        
    def _initialize_cache(self):
        """تهيئة نظام التخزين المؤقت"""
        try:
            # إنشاء المجلدات
            os.makedirs(self.cache_dir, exist_ok=True)
            os.makedirs(self.audio_dir, exist_ok=True)
            os.makedirs(self.text_dir, exist_ok=True)
            
            # إنشاء قاعدة البيانات
            self._create_database()
            
            # تنظيف أولي
            self._cleanup_expired_entries()
            
            logger.info(f"✅ تم تهيئة نظام التخزين المؤقت: {self.cache_dir}")
            
        except Exception as e:
            logger.error(f"❌ خطأ في تهيئة التخزين المؤقت: {e}")
            
    def _create_database(self):
        """إنشاء قاعدة بيانات التخزين المؤقت"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS cache_entries (
                    audio_hash TEXT PRIMARY KEY,
                    text TEXT NOT NULL,
                    confidence REAL NOT NULL,
                    language TEXT NOT NULL,
                    service_used TEXT NOT NULL,
                    created_at TEXT NOT NULL,
                    last_accessed TEXT NOT NULL,
                    access_count INTEGER DEFAULT 1,
                    file_size INTEGER NOT NULL,
                    duration_seconds REAL NOT NULL,
                    quality_score REAL NOT NULL
                )
            """)
            
            # إنشاء فهارس للبحث السريع
            conn.execute("CREATE INDEX IF NOT EXISTS idx_created_at ON cache_entries(created_at)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_last_accessed ON cache_entries(last_accessed)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_quality_score ON cache_entries(quality_score)")
            
            conn.commit()
            
    def _generate_audio_hash(self, audio_data: bytes, language: str = "auto") -> str:
        """إنشاء hash فريد للصوت"""
        # دمج بيانات الصوت مع اللغة لضمان التفرد
        combined_data = audio_data + language.encode('utf-8')
        return hashlib.sha256(combined_data).hexdigest()
        
    def _calculate_quality_score(self, text: str, confidence: float, service_used: str) -> float:
        """حساب نقاط جودة النص"""
        score = 0.0
        
        # طول النص (40%)
        text_length = len(text.strip())
        if text_length > 100:
            length_score = min(1.0, text_length / 1000) * 0.4
        else:
            length_score = (text_length / 100) * 0.4
        score += length_score
        
        # مستوى الثقة (35%)
        confidence_score = confidence * 0.35
        score += confidence_score
        
        # جودة الخدمة (15%)
        service_quality = {
            "assemblyai": 0.95,
            "speechmatics": 0.90,
            "ibm_watson": 0.85,
            "azure_speech": 0.80,
            "google_cloud": 0.75,
            "witai": 0.65,
            "whisper": 0.70
        }
        service_score = service_quality.get(service_used.lower(), 0.5) * 0.15
        score += service_score
        
        # فحص جودة النص (10%)
        text_quality = self._assess_text_quality(text) * 0.1
        score += text_quality
        
        return min(1.0, score)
        
    def _assess_text_quality(self, text: str) -> float:
        """تقييم جودة النص"""
        if not text or len(text.strip()) < 10:
            return 0.0
            
        # فحص وجود كلمات مفيدة
        words = text.split()
        if len(words) < 5:
            return 0.3
            
        # فحص التكرار المفرط
        unique_words = set(words)
        repetition_ratio = len(unique_words) / len(words)
        if repetition_ratio < 0.3:
            return 0.4
            
        # فحص وجود رموز غريبة
        special_chars_ratio = sum(1 for c in text if not c.isalnum() and c not in ' .,!?-') / len(text)
        if special_chars_ratio > 0.3:
            return 0.5
            
        return 0.8
        
    def store_transcription(self, audio_data: bytes, text: str, confidence: float,
                          language: str, service_used: str, duration_seconds: float = 0.0) -> bool:
        """حفظ نتيجة التحويل في التخزين المؤقت"""
        try:
            with self._lock:
                # حساب hash الصوت
                audio_hash = self._generate_audio_hash(audio_data, language)
                
                # حساب نقاط الجودة
                quality_score = self._calculate_quality_score(text, confidence, service_used)
                
                # فحص الحد الأدنى للجودة
                if quality_score < self.min_quality_score:
                    logger.debug(f"⏭️ تخطي حفظ نص منخفض الجودة: {quality_score:.2f}")
                    return False
                    
                # فحص ما إذا كان موجود بالفعل
                if self._entry_exists(audio_hash):
                    logger.debug(f"📋 النص موجود بالفعل في التخزين المؤقت: {audio_hash[:8]}")
                    self._update_access_time(audio_hash)
                    return True
                    
                # فحص مساحة التخزين
                if self._get_cache_size() > self.max_size_bytes * self.cleanup_threshold:
                    self._cleanup_old_entries()
                    
                # حفظ الملفات
                audio_file = os.path.join(self.audio_dir, f"{audio_hash}.gz")
                text_file = os.path.join(self.text_dir, f"{audio_hash}.txt")
                
                # ضغط وحفظ الصوت
                with gzip.open(audio_file, 'wb') as f:
                    f.write(audio_data)
                    
                # حفظ النص
                with open(text_file, 'w', encoding='utf-8') as f:
                    f.write(text)
                    
                # حفظ المعلومات في قاعدة البيانات
                entry = CacheEntry(
                    audio_hash=audio_hash,
                    text=text,
                    confidence=confidence,
                    language=language,
                    service_used=service_used,
                    created_at=datetime.now(),
                    last_accessed=datetime.now(),
                    access_count=1,
                    file_size=len(audio_data),
                    duration_seconds=duration_seconds,
                    quality_score=quality_score
                )
                
                self._save_entry_to_db(entry)
                
                logger.info(f"💾 تم حفظ النص في التخزين المؤقت: {audio_hash[:8]} (جودة: {quality_score:.2f})")
                return True
                
        except Exception as e:
            logger.error(f"❌ خطأ في حفظ التخزين المؤقت: {e}")
            return False
            
    def get_transcription(self, audio_data: bytes, language: str = "auto") -> Optional[Dict[str, Any]]:
        """البحث عن نتيجة محفوظة في التخزين المؤقت"""
        try:
            with self._lock:
                audio_hash = self._generate_audio_hash(audio_data, language)
                
                # البحث في قاعدة البيانات
                entry = self._get_entry_from_db(audio_hash)
                if not entry:
                    return None
                    
                # فحص وجود الملفات
                text_file = os.path.join(self.text_dir, f"{audio_hash}.txt")
                if not os.path.exists(text_file):
                    logger.warning(f"⚠️ ملف النص مفقود: {audio_hash[:8]}")
                    self._remove_entry(audio_hash)
                    return None
                    
                # قراءة النص
                with open(text_file, 'r', encoding='utf-8') as f:
                    text = f.read()
                    
                # تحديث وقت الوصول
                self._update_access_time(audio_hash)
                
                logger.info(f"📋 تم العثور على نص محفوظ: {audio_hash[:8]}")
                
                return {
                    'success': True,
                    'text': text,
                    'confidence': entry.confidence,
                    'language': entry.language,
                    'service_used': f"{entry.service_used} (Cached)",
                    'processing_time': 0.0,
                    'word_count': len(text.split()),
                    'duration_seconds': entry.duration_seconds,
                    'quality_score': entry.quality_score,
                    'cached': True,
                    'cache_age_hours': (datetime.now() - entry.created_at).total_seconds() / 3600
                }
                
        except Exception as e:
            logger.error(f"❌ خطأ في البحث في التخزين المؤقت: {e}")
            return None
            
    def _entry_exists(self, audio_hash: str) -> bool:
        """فحص وجود مدخل في التخزين المؤقت"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("SELECT 1 FROM cache_entries WHERE audio_hash = ?", (audio_hash,))
            return cursor.fetchone() is not None
            
    def _get_entry_from_db(self, audio_hash: str) -> Optional[CacheEntry]:
        """الحصول على مدخل من قاعدة البيانات"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("""
                SELECT * FROM cache_entries WHERE audio_hash = ?
            """, (audio_hash,))
            
            row = cursor.fetchone()
            if not row:
                return None
                
            return CacheEntry(
                audio_hash=row[0],
                text=row[1],
                confidence=row[2],
                language=row[3],
                service_used=row[4],
                created_at=datetime.fromisoformat(row[5]),
                last_accessed=datetime.fromisoformat(row[6]),
                access_count=row[7],
                file_size=row[8],
                duration_seconds=row[9],
                quality_score=row[10]
            )
            
    def _save_entry_to_db(self, entry: CacheEntry):
        """حفظ مدخل في قاعدة البيانات"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                INSERT OR REPLACE INTO cache_entries VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                entry.audio_hash,
                entry.text,
                entry.confidence,
                entry.language,
                entry.service_used,
                entry.created_at.isoformat(),
                entry.last_accessed.isoformat(),
                entry.access_count,
                entry.file_size,
                entry.duration_seconds,
                entry.quality_score
            ))
            conn.commit()
            
    def _update_access_time(self, audio_hash: str):
        """تحديث وقت الوصول الأخير"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                UPDATE cache_entries 
                SET last_accessed = ?, access_count = access_count + 1
                WHERE audio_hash = ?
            """, (datetime.now().isoformat(), audio_hash))
            conn.commit()
            
    def _get_cache_size(self) -> int:
        """الحصول على حجم التخزين المؤقت بالبايت"""
        total_size = 0
        
        for root, dirs, files in os.walk(self.cache_dir):
            for file in files:
                file_path = os.path.join(root, file)
                if os.path.exists(file_path):
                    total_size += os.path.getsize(file_path)
                    
        return total_size
        
    def _cleanup_expired_entries(self):
        """تنظيف المدخلات المنتهية الصلاحية"""
        try:
            cutoff_date = datetime.now() - timedelta(days=self.max_age_days)
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("""
                    SELECT audio_hash FROM cache_entries 
                    WHERE created_at < ?
                """, (cutoff_date.isoformat(),))
                
                expired_hashes = [row[0] for row in cursor.fetchall()]
                
            for audio_hash in expired_hashes:
                self._remove_entry(audio_hash)
                
            if expired_hashes:
                logger.info(f"🗑️ تم حذف {len(expired_hashes)} مدخل منتهي الصلاحية")
                
        except Exception as e:
            logger.error(f"❌ خطأ في تنظيف المدخلات المنتهية: {e}")
            
    def _cleanup_old_entries(self):
        """تنظيف المدخلات القديمة لتوفير مساحة"""
        try:
            # حذف المدخلات الأقل استخداماً والأقل جودة
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("""
                    SELECT audio_hash FROM cache_entries 
                    ORDER BY access_count ASC, quality_score ASC, last_accessed ASC
                    LIMIT 50
                """)
                
                old_hashes = [row[0] for row in cursor.fetchall()]
                
            for audio_hash in old_hashes:
                self._remove_entry(audio_hash)
                
            logger.info(f"🗑️ تم حذف {len(old_hashes)} مدخل قديم لتوفير مساحة")
            
        except Exception as e:
            logger.error(f"❌ خطأ في تنظيف المدخلات القديمة: {e}")
            
    def _remove_entry(self, audio_hash: str):
        """حذف مدخل من التخزين المؤقت"""
        try:
            # حذف الملفات
            audio_file = os.path.join(self.audio_dir, f"{audio_hash}.gz")
            text_file = os.path.join(self.text_dir, f"{audio_hash}.txt")
            
            for file_path in [audio_file, text_file]:
                if os.path.exists(file_path):
                    os.remove(file_path)
                    
            # حذف من قاعدة البيانات
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("DELETE FROM cache_entries WHERE audio_hash = ?", (audio_hash,))
                conn.commit()
                
        except Exception as e:
            logger.error(f"❌ خطأ في حذف المدخل {audio_hash[:8]}: {e}")


# إنشاء مثيل عام
speech_cache_manager = SpeechCacheManager()
