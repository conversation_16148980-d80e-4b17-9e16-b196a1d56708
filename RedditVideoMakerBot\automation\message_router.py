#!/usr/bin/env python3
"""
موجه الرسائل - نظام موحد لإرسال جميع الرسائل والأخطاء عبر التلغرام
يستبدل جميع رسائل الكونسول ويرسلها عبر البوت
"""

import logging
import sys
import traceback
from datetime import datetime
from typing import Optional, Any
from pathlib import Path

logger = logging.getLogger(__name__)

class TelegramMessageRouter:
    """موجه الرسائل عبر التلغرام"""
    
    def __init__(self):
        self.bot_instance = None
        self._initialize_bot()
    
    def _initialize_bot(self):
        """تهيئة البوت"""
        try:
            from automation.telegram_bot import EnhancedTelegramBot
            self.bot_instance = EnhancedTelegramBot()
            logger.info("✅ تم تهيئة موجه الرسائل")
        except Exception as e:
            logger.error(f"❌ فشل في تهيئة موجه الرسائل: {e}")
    
    def send_console_message(self, message: str, level: str = "INFO", emoji: str = "ℹ️"):
        """إرسال رسالة كونسول عبر التلغرام"""
        try:
            if not self.bot_instance:
                print(f"[{level}] {message}")  # fallback للكونسول
                return
            
            # تنسيق الرسالة
            formatted_message = f"""
{emoji} **{level}**

🕐 {datetime.now().strftime('%H:%M:%S')}

📝 {message}
            """.strip()
            
            self.bot_instance.send_message(formatted_message)
            
        except Exception as e:
            # في حالة فشل الإرسال، استخدم الكونسول
            print(f"[{level}] {message}")
            logger.error(f"فشل في إرسال رسالة عبر التلغرام: {e}")
    
    def send_error(self, error_message: str, error_details: str = None, context: dict = None):
        """إرسال رسالة خطأ مفصلة"""
        try:
            if not self.bot_instance:
                print(f"[ERROR] {error_message}")
                if error_details:
                    print(f"Details: {error_details}")
                return
            
            message = f"""
❌ **خطأ في النظام**

🕐 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

⚠️ **الخطأ:** {error_message}
            """.strip()
            
            if error_details:
                message += f"\n\n📋 **التفاصيل:**\n```\n{error_details[:1000]}\n```"
            
            if context:
                message += f"\n\n🔍 **السياق:**\n"
                for key, value in context.items():
                    message += f"• {key}: {value}\n"
            
            self.bot_instance.send_message(message)
            
        except Exception as e:
            print(f"[ERROR] {error_message}")
            logger.error(f"فشل في إرسال خطأ عبر التلغرام: {e}")
    
    def send_success(self, success_message: str, details: str = None):
        """إرسال رسالة نجاح"""
        try:
            if not self.bot_instance:
                print(f"[SUCCESS] {success_message}")
                return
            
            message = f"""
✅ **عملية ناجحة**

🕐 {datetime.now().strftime('%H:%M:%S')}

🎉 {success_message}
            """.strip()
            
            if details:
                message += f"\n\n📋 **التفاصيل:**\n{details}"
            
            self.bot_instance.send_message(message)
            
        except Exception as e:
            print(f"[SUCCESS] {success_message}")
            logger.error(f"فشل في إرسال رسالة نجاح عبر التلغرام: {e}")
    
    def send_warning(self, warning_message: str, suggestion: str = None):
        """إرسال رسالة تحذير"""
        try:
            if not self.bot_instance:
                print(f"[WARNING] {warning_message}")
                return
            
            message = f"""
⚠️ **تحذير**

🕐 {datetime.now().strftime('%H:%M:%S')}

🔔 {warning_message}
            """.strip()
            
            if suggestion:
                message += f"\n\n💡 **اقتراح:**\n{suggestion}"
            
            self.bot_instance.send_message(message)
            
        except Exception as e:
            print(f"[WARNING] {warning_message}")
            logger.error(f"فشل في إرسال تحذير عبر التلغرام: {e}")
    
    def send_progress(self, step: str, progress: str = None, total_steps: int = None, current_step: int = None):
        """إرسال تحديث تقدم العملية"""
        try:
            if not self.bot_instance:
                print(f"[PROGRESS] {step}")
                return
            
            message = f"""
🔄 **تقدم العملية**

🕐 {datetime.now().strftime('%H:%M:%S')}

📋 **الخطوة الحالية:** {step}
            """.strip()
            
            if progress:
                message += f"\n📊 **التقدم:** {progress}"
            
            if total_steps and current_step:
                percentage = (current_step / total_steps) * 100
                message += f"\n📈 **النسبة:** {current_step}/{total_steps} ({percentage:.1f}%)"
            
            self.bot_instance.send_message(message)
            
        except Exception as e:
            print(f"[PROGRESS] {step}")
            logger.error(f"فشل في إرسال تحديث التقدم عبر التلغرام: {e}")

# إنشاء مثيل عام
message_router = TelegramMessageRouter()

# دوال مساعدة للاستخدام السهل
def send_telegram_message(message: str, level: str = "INFO"):
    """إرسال رسالة عامة"""
    emoji_map = {
        "INFO": "ℹ️",
        "SUCCESS": "✅", 
        "WARNING": "⚠️",
        "ERROR": "❌",
        "DEBUG": "🔍"
    }
    message_router.send_console_message(message, level, emoji_map.get(level, "ℹ️"))

def send_telegram_error(error_message: str, error_details: str = None, context: dict = None):
    """إرسال خطأ"""
    message_router.send_error(error_message, error_details, context)

def send_telegram_success(success_message: str, details: str = None):
    """إرسال نجاح"""
    message_router.send_success(success_message, details)

def send_telegram_warning(warning_message: str, suggestion: str = None):
    """إرسال تحذير"""
    message_router.send_warning(warning_message, suggestion)

def send_telegram_progress(step: str, progress: str = None, total_steps: int = None, current_step: int = None):
    """إرسال تقدم"""
    message_router.send_progress(step, progress, total_steps, current_step)

# استبدال print للرسائل المهمة
def enhanced_print(message: str, level: str = "INFO"):
    """print محسن يرسل عبر التلغرام أيضاً"""
    print(f"[{level}] {message}")  # للكونسول المحلي
    send_telegram_message(message, level)  # للتلغرام

if __name__ == "__main__":
    # اختبار النظام
    send_telegram_message("🧪 اختبار نظام توجيه الرسائل")
    send_telegram_success("تم تشغيل النظام بنجاح")
    send_telegram_warning("هذا تحذير تجريبي")
    send_telegram_error("هذا خطأ تجريبي", "تفاصيل الخطأ هنا")
    send_telegram_progress("معالجة البيانات", "50%", 10, 5)
