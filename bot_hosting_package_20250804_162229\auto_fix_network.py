#!/usr/bin/env python3
"""
أداة إصلاح مشاكل الشبكة التلقائية
Automatic Network Issues Fixer
"""

import os
import sys
import subprocess
import socket
import time
import logging
from typing import List, Dict, Any

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('network_fix.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class NetworkFixer:
    """مصلح مشاكل الشبكة"""
    
    def __init__(self):
        self.is_windows = os.name == 'nt'
        self.fixes_applied = []
        self.test_servers = [
            ('*******', 53),
            ('*******', 53),
            ('api.telegram.org', 443)
        ]
    
    def run_all_fixes(self):
        """تشغيل جميع الإصلاحات"""
        logger.info("🔧 بدء إصلاح مشاكل الشبكة...")
        
        # فحص الاتصال الأولي
        if self.test_connectivity():
            logger.info("✅ الشبكة تعمل بشكل طبيعي")
            return True
        
        logger.warning("⚠️ تم اكتشاف مشاكل في الشبكة، بدء الإصلاح...")
        
        # تطبيق الإصلاحات
        fixes = [
            self.fix_dns_cache,
            self.fix_winsock,
            self.fix_tcp_ip,
            self.fix_firewall,
            self.fix_proxy_settings,
            self.fix_hosts_file,
            self.optimize_network_settings
        ]
        
        for fix in fixes:
            try:
                if fix():
                    logger.info(f"✅ تم تطبيق: {fix.__name__}")
                    self.fixes_applied.append(fix.__name__)
                else:
                    logger.warning(f"⚠️ فشل في: {fix.__name__}")
            except Exception as e:
                logger.error(f"❌ خطأ في {fix.__name__}: {e}")
        
        # فحص الاتصال النهائي
        time.sleep(5)  # انتظار قصير
        if self.test_connectivity():
            logger.info("🎉 تم إصلاح مشاكل الشبكة بنجاح!")
            self.show_summary()
            return True
        else:
            logger.error("❌ لم يتم حل جميع مشاكل الشبكة")
            self.show_manual_steps()
            return False
    
    def test_connectivity(self) -> bool:
        """اختبار الاتصال"""
        logger.info("🔍 اختبار الاتصال...")
        
        for server, port in self.test_servers:
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(10)
                result = sock.connect_ex((server, port))
                sock.close()
                
                if result == 0:
                    logger.info(f"✅ الاتصال مع {server}:{port} يعمل")
                    return True
                else:
                    logger.warning(f"⚠️ فشل الاتصال مع {server}:{port}")
            except Exception as e:
                logger.warning(f"⚠️ خطأ في اختبار {server}:{port}: {e}")
        
        return False
    
    def fix_dns_cache(self) -> bool:
        """إصلاح DNS cache"""
        if not self.is_windows:
            return True
        
        try:
            logger.info("🔧 تنظيف DNS cache...")
            result = subprocess.run(
                ['ipconfig', '/flushdns'],
                capture_output=True,
                text=True,
                timeout=30
            )
            return result.returncode == 0
        except Exception as e:
            logger.error(f"فشل في تنظيف DNS cache: {e}")
            return False
    
    def fix_winsock(self) -> bool:
        """إصلاح Winsock"""
        if not self.is_windows:
            return True
        
        try:
            logger.info("🔧 إعادة تعيين Winsock...")
            result = subprocess.run(
                ['netsh', 'winsock', 'reset'],
                capture_output=True,
                text=True,
                timeout=30
            )
            return result.returncode == 0
        except Exception as e:
            logger.error(f"فشل في إعادة تعيين Winsock: {e}")
            return False
    
    def fix_tcp_ip(self) -> bool:
        """إصلاح TCP/IP stack"""
        if not self.is_windows:
            return True
        
        try:
            logger.info("🔧 إعادة تعيين TCP/IP stack...")
            commands = [
                ['netsh', 'int', 'ip', 'reset'],
                ['netsh', 'int', 'ipv4', 'reset'],
                ['netsh', 'int', 'ipv6', 'reset']
            ]
            
            for cmd in commands:
                result = subprocess.run(
                    cmd,
                    capture_output=True,
                    text=True,
                    timeout=30
                )
                if result.returncode != 0:
                    return False
            
            return True
        except Exception as e:
            logger.error(f"فشل في إعادة تعيين TCP/IP: {e}")
            return False
    
    def fix_firewall(self) -> bool:
        """فحص وإصلاح إعدادات Firewall"""
        if not self.is_windows:
            return True
        
        try:
            logger.info("🔧 فحص إعدادات Firewall...")
            # فحص حالة الـ firewall
            result = subprocess.run(
                ['netsh', 'advfirewall', 'show', 'allprofiles', 'state'],
                capture_output=True,
                text=True,
                timeout=30
            )
            
            if result.returncode == 0:
                logger.info("✅ Firewall يعمل بشكل طبيعي")
                return True
            
            return False
        except Exception as e:
            logger.warning(f"تحذير في فحص Firewall: {e}")
            return True  # لا نريد أن يفشل الإصلاح بسبب هذا
    
    def fix_proxy_settings(self) -> bool:
        """إصلاح إعدادات البروكسي"""
        try:
            logger.info("🔧 فحص إعدادات البروكسي...")
            
            # إزالة متغيرات البروكسي المؤقتة
            proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy']
            for var in proxy_vars:
                if var in os.environ:
                    del os.environ[var]
                    logger.info(f"تم إزالة متغير البروكسي: {var}")
            
            return True
        except Exception as e:
            logger.error(f"فشل في إصلاح إعدادات البروكسي: {e}")
            return False
    
    def fix_hosts_file(self) -> bool:
        """فحص وإصلاح ملف hosts"""
        try:
            if self.is_windows:
                hosts_path = r'C:\Windows\System32\drivers\etc\hosts'
            else:
                hosts_path = '/etc/hosts'
            
            if not os.path.exists(hosts_path):
                logger.warning("ملف hosts غير موجود")
                return False
            
            logger.info("🔧 فحص ملف hosts...")
            
            with open(hosts_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # فحص وجود إدخالات مشبوهة
            suspicious_entries = [
                'api.telegram.org',
                'telegram.org',
                '0.0.0.0 api.telegram.org'
            ]
            
            for entry in suspicious_entries:
                if entry in content:
                    logger.warning(f"تم العثور على إدخال مشبوه في hosts: {entry}")
                    # يمكن إضافة كود لإزالة الإدخالات المشبوهة هنا
            
            return True
        except Exception as e:
            logger.error(f"فشل في فحص ملف hosts: {e}")
            return False
    
    def optimize_network_settings(self) -> bool:
        """تحسين إعدادات الشبكة"""
        try:
            logger.info("🔧 تحسين إعدادات الشبكة...")
            
            # تعيين DNS servers
            if self.is_windows:
                dns_commands = [
                    ['netsh', 'interface', 'ip', 'set', 'dns', 'name="Wi-Fi"', 'static', '*******'],
                    ['netsh', 'interface', 'ip', 'add', 'dns', 'name="Wi-Fi"', '*******', 'index=2'],
                    ['netsh', 'interface', 'ip', 'set', 'dns', 'name="Ethernet"', 'static', '*******'],
                    ['netsh', 'interface', 'ip', 'add', 'dns', 'name="Ethernet"', '*******', 'index=2']
                ]
                
                for cmd in dns_commands:
                    try:
                        subprocess.run(cmd, capture_output=True, timeout=15)
                    except:
                        pass  # تجاهل الأخطاء في تعيين DNS
            
            return True
        except Exception as e:
            logger.error(f"فشل في تحسين إعدادات الشبكة: {e}")
            return False
    
    def show_summary(self):
        """عرض ملخص الإصلاحات"""
        logger.info("📋 ملخص الإصلاحات المطبقة:")
        for fix in self.fixes_applied:
            logger.info(f"  ✅ {fix}")
        
        logger.info("\n🔄 يُنصح بإعادة تشغيل النظام لضمان تطبيق جميع التغييرات")
    
    def show_manual_steps(self):
        """عرض خطوات الإصلاح اليدوي"""
        logger.info("📋 خطوات الإصلاح اليدوي:")
        logger.info("1. تحقق من اتصال الإنترنت")
        logger.info("2. تحقق من إعدادات Firewall")
        logger.info("3. تحقق من إعدادات البروكسي")
        logger.info("4. تحقق من إعدادات DNS")
        logger.info("5. أعد تشغيل جهاز الراوتر")
        logger.info("6. اتصل بمزود خدمة الإنترنت")

def main():
    """الدالة الرئيسية"""
    print("🔧 أداة إصلاح مشاكل الشبكة التلقائية")
    print("=" * 50)
    
    # فحص الصلاحيات
    if os.name == 'nt':
        try:
            import ctypes
            if not ctypes.windll.shell32.IsUserAnAdmin():
                print("⚠️ تحذير: يُنصح بتشغيل الأداة كمدير للحصول على أفضل النتائج")
        except:
            pass
    
    fixer = NetworkFixer()
    success = fixer.run_all_fixes()
    
    if success:
        print("\n🎉 تم إصلاح مشاكل الشبكة بنجاح!")
        sys.exit(0)
    else:
        print("\n❌ لم يتم حل جميع المشاكل. راجع الخطوات اليدوية أعلاه.")
        sys.exit(1)

if __name__ == "__main__":
    main()
