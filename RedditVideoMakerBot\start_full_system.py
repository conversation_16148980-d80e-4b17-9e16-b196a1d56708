#!/usr/bin/env python3
"""
تشغيل النظام الكامل المحدث
يشغل البوت المستمر + بوت Telegram التفاعلي معاً
"""

import asyncio
import threading
import logging
import sys
import time
from pathlib import Path

# إعداد السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/full_system.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class FullSystemManager:
    def __init__(self):
        """إعداد مدير النظام الكامل"""
        self.continuous_bot = None
        self.telegram_bot = None
        self.running = False
        
    def start_continuous_bot(self):
        """تشغيل البوت المستمر في thread منفصل"""
        try:
            logger.info("🤖 بدء تشغيل البوت المستمر...")
            
            from continuous_bot import ContinuousVideoBot
            self.continuous_bot = ContinuousVideoBot()
            self.continuous_bot.start()
            
        except Exception as e:
            logger.error(f"❌ خطأ في تشغيل البوت المستمر: {e}")

    async def start_telegram_bot(self):
        """تشغيل بوت Telegram التفاعلي"""
        try:
            logger.info("📱 بدء تشغيل بوت Telegram التفاعلي...")
            
            from automation.telegram_bot import EnhancedTelegramBot
            self.telegram_bot = EnhancedTelegramBot()
            await self.telegram_bot.start_interactive_bot()
            
        except Exception as e:
            logger.error(f"❌ خطأ في تشغيل بوت Telegram: {e}")

    async def start_full_system(self):
        """تشغيل النظام الكامل"""
        try:
            logger.info("🚀 بدء تشغيل النظام الكامل...")
            self.running = True
            
            # إرسال إشعار البدء
            try:
                from automation.telegram_bot import send_notification
                send_notification("""
🚀 **تم بدء النظام الكامل المحدث**

🤖 البوت المستمر: تشغيل (كل 10 ساعات)
📱 بوت Telegram: تشغيل (أزرار تفاعلية)
🎬 YouTube: رفع تلقائي
🌍 اللغة: إنجليزية للعناوين والأوصاف
⚙️ جميع الأنظمة: جاهزة

استخدم /start في Telegram للتحكم الكامل
                """)
            except:
                pass
            
            # تشغيل البوت المستمر في thread منفصل
            continuous_thread = threading.Thread(
                target=self.start_continuous_bot, 
                daemon=True
            )
            continuous_thread.start()
            
            # انتظار قليل للتأكد من بدء البوت المستمر
            await asyncio.sleep(3)
            
            # تشغيل بوت Telegram (blocking)
            await self.start_telegram_bot()
            
        except KeyboardInterrupt:
            logger.info("⏹️ تم إيقاف النظام بواسطة المستخدم")
            await self.stop_system()
        except Exception as e:
            logger.error(f"❌ خطأ في النظام الكامل: {e}")
            await self.stop_system()

    async def stop_system(self):
        """إيقاف النظام الكامل"""
        logger.info("🛑 إيقاف النظام الكامل...")
        self.running = False
        
        # إيقاف البوت المستمر
        if self.continuous_bot:
            try:
                self.continuous_bot.stop()
            except:
                pass
        
        # إرسال إشعار الإيقاف
        try:
            from automation.telegram_bot import send_notification
            send_notification("🛑 تم إيقاف النظام الكامل")
        except:
            pass

def check_system_requirements():
    """فحص متطلبات النظام"""
    print("🔍 فحص متطلبات النظام...")
    
    # فحص Python version
    if sys.version_info < (3, 7):
        print("❌ يتطلب Python 3.7 أو أحدث")
        return False
    
    # فحص الملفات المطلوبة
    required_files = [
        "config.toml",
        "main.py",
        "continuous_bot.py",
        "automation/telegram_bot.py"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print(f"❌ ملفات مطلوبة غير موجودة: {', '.join(missing_files)}")
        return False
    
    print("✅ جميع المتطلبات متوفرة")
    return True

def show_startup_info():
    """عرض معلومات البدء"""
    print("""
╔══════════════════════════════════════════════════════════════╗
║                    🚀 النظام الكامل المحدث                   ║
║              Reddit Video Maker Bot v3.0                  ║
║                                                            ║
║  🤖 البوت المستمر: إنشاء فيديوهات كل 10 ساعات              ║
║  📱 بوت Telegram: تحكم تفاعلي كامل مع أزرار                ║
║  🎬 YouTube: رفع تلقائي                                    ║
║  🤖 Gemini AI: محتوى ذكي بالإنجليزية                      ║
║  🔧 إصلاحات: جميع المشاكل تم حلها                         ║
╚══════════════════════════════════════════════════════════════╝

🎯 المميزات الجديدة:
• ✅ إنشاء فيديوهات مستمر (لا يتوقف)
• ✅ عناوين وأوصاف بالإنجليزية
• ✅ أزرار Telegram تعمل بشكل صحيح
• ✅ معالجة أخطاء محسنة
• ✅ مراقبة مستمرة للنظام

📱 للتحكم:
1. ابحث عن البوت في Telegram
2. أرسل /start
3. استخدم الأزرار للتحكم

⏹️ للإيقاف: اضغط Ctrl+C
    """)

def show_next_steps():
    """عرض الخطوات التالية"""
    print("""
🎯 الخطوات التالية:

1. 📱 تحكم عبر Telegram:
   - أرسل /start للبوت
   - استخدم الأزرار للتحكم

2. 📊 مراقبة النظام:
   - تحقق من الإشعارات في Telegram
   - راقب ملفات السجلات في مجلد logs/

3. 🎬 مراقبة الفيديوهات:
   - تحقق من مجلد results/ للفيديوهات
   - راقب قناة YouTube للفيديوهات المرفوعة

4. 🔧 في حالة المشاكل:
   - python fix_telegram_issue.py
   - python test_system_after_fix.py
    """)

async def main():
    """الدالة الرئيسية"""
    try:
        # التأكد من وجود مجلد logs
        Path("logs").mkdir(exist_ok=True)
        
        # عرض معلومات البدء
        show_startup_info()
        
        # فحص المتطلبات
        if not check_system_requirements():
            print("\n❌ فشل في فحص المتطلبات")
            print("🔧 شغل: python fix_video_creation_issue.py")
            sys.exit(1)
        
        print("\n🚀 بدء تشغيل النظام...")
        
        # بدء النظام الكامل
        system_manager = FullSystemManager()
        await system_manager.start_full_system()
        
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف النظام بواسطة المستخدم")
        show_next_steps()
    except Exception as e:
        logger.error(f"❌ خطأ حرج: {e}")
        print(f"❌ خطأ: {e}")
        print("\n🔧 للإصلاح:")
        print("python fix_video_creation_issue.py")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
