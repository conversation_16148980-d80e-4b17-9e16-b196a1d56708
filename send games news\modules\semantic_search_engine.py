# محرك البحث الدلالي المتقدم
import asyncio
import json
import time
import hashlib
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any, Union
from dataclasses import dataclass, asdict
from enum import Enum
import re
import math
from collections import defaultdict, Counter

from .logger import logger
from .fallback_ai_manager import fallback_ai_manager, AIModelType
from .database import db

class SemanticSearchType(Enum):
    """أنواع البحث الدلالي"""
    CONCEPTUAL = "conceptual"       # بحث مفاهيمي
    CONTEXTUAL = "contextual"       # بحث سياقي
    INTENT_BASED = "intent_based"   # بحث قائم على النية
    SIMILARITY = "similarity"       # بحث بالتشابه
    ASSOCIATIVE = "associative"     # بحث ترابطي
    HYBRID = "hybrid"              # بحث مختلط

class SemanticRelation(Enum):
    """أنواع العلاقات الدلالية"""
    SYNONYM = "synonym"             # مرادف
    ANTONYM = "antonym"            # متضاد
    HYPERNYM = "hypernym"          # أعم
    HYPONYM = "hyponym"            # أخص
    MERONYM = "meronym"            # جزء من
    HOLONYM = "holonym"            # كل يحتوي على
    RELATED = "related"            # مرتبط
    SIMILAR = "similar"            # مشابه

@dataclass
class SemanticConcept:
    """مفهوم دلالي"""
    term: str
    concept_type: str
    weight: float
    relations: Dict[str, List[str]]
    context_vector: Optional[List[float]] = None
    frequency: int = 1
    last_updated: datetime = None

@dataclass
class SemanticQuery:
    """استعلام دلالي"""
    original_query: str
    concepts: List[SemanticConcept]
    intent_vector: Optional[List[float]]
    context_embeddings: Optional[List[float]]
    semantic_expansions: List[str]
    confidence_score: float
    search_type: SemanticSearchType

@dataclass
class SemanticResult:
    """نتيجة بحث دلالي"""
    content: Dict[str, Any]
    semantic_score: float
    concept_matches: List[str]
    intent_alignment: float
    context_relevance: float
    novelty_score: float
    final_semantic_score: float
    explanation: str

class SemanticSearchEngine:
    """محرك البحث الدلالي المتقدم"""
    
    def __init__(self):
        # قاموس المفاهيم الدلالية
        self.concept_database = {}
        self.concept_relations = defaultdict(dict)
        
        # نماذج التشابه الدلالي
        self.semantic_models = {
            'gaming_concepts': self._load_gaming_concepts(),
            'intent_patterns': self._load_intent_patterns(),
            'context_vectors': {}
        }
        
        # إعدادات البحث الدلالي
        self.semantic_config = {
            'similarity_threshold': 0.6,
            'concept_expansion_limit': 10,
            'context_window_size': 5,
            'intent_weight': 0.3,
            'concept_weight': 0.4,
            'context_weight': 0.3,
            'novelty_bonus': 0.1
        }
        
        # إحصائيات البحث الدلالي
        self.semantic_stats = {
            'total_semantic_searches': 0,
            'successful_expansions': 0,
            'concept_discoveries': 0,
            'average_semantic_score': 0.0,
            'search_type_usage': defaultdict(int)
        }
        
        # تهيئة النظام
        self._initialize_semantic_database()
        
        logger.info("🧠 تم تهيئة محرك البحث الدلالي")
    
    def _load_gaming_concepts(self) -> Dict[str, SemanticConcept]:
        """تحميل المفاهيم المتعلقة بالألعاب"""
        gaming_concepts = {}
        
        # مفاهيم أساسية
        base_concepts = {
            'game': {
                'type': 'core_concept',
                'relations': {
                    'synonym': ['gaming', 'videogame', 'video game'],
                    'hyponym': ['rpg', 'fps', 'strategy', 'puzzle', 'action'],
                    'related': ['player', 'gameplay', 'developer', 'publisher']
                }
            },
            'player': {
                'type': 'actor',
                'relations': {
                    'synonym': ['gamer', 'user'],
                    'related': ['gaming', 'gameplay', 'experience', 'skill']
                }
            },
            'developer': {
                'type': 'actor',
                'relations': {
                    'synonym': ['studio', 'dev team'],
                    'related': ['game', 'development', 'programming', 'design']
                }
            },
            'release': {
                'type': 'event',
                'relations': {
                    'synonym': ['launch', 'debut', 'premiere'],
                    'related': ['game', 'date', 'announcement', 'trailer']
                }
            },
            'review': {
                'type': 'content',
                'relations': {
                    'synonym': ['critique', 'evaluation', 'assessment'],
                    'related': ['score', 'rating', 'opinion', 'analysis']
                }
            }
        }
        
        # تحويل إلى كائنات SemanticConcept
        for term, data in base_concepts.items():
            concept = SemanticConcept(
                term=term,
                concept_type=data['type'],
                weight=1.0,
                relations=data['relations'],
                last_updated=datetime.now()
            )
            gaming_concepts[term] = concept
        
        return gaming_concepts
    
    def _load_intent_patterns(self) -> Dict[str, List[str]]:
        """تحميل أنماط النوايا"""
        return {
            'find_news': [
                'latest news', 'breaking news', 'recent announcement',
                'what happened', 'new information', 'updates'
            ],
            'find_reviews': [
                'review', 'opinion', 'rating', 'score', 'critique',
                'what do people think', 'is it good', 'worth playing'
            ],
            'find_guides': [
                'how to', 'guide', 'tutorial', 'walkthrough',
                'tips', 'help', 'instructions', 'strategy'
            ],
            'find_comparisons': [
                'vs', 'versus', 'compare', 'difference', 'better',
                'which one', 'pros and cons', 'similarities'
            ],
            'find_releases': [
                'release date', 'when', 'coming out', 'launch',
                'available', 'upcoming', 'scheduled'
            ]
        }
    
    def _initialize_semantic_database(self):
        """تهيئة قاعدة البيانات الدلالية"""
        try:
            # بناء شبكة العلاقات
            for concept_name, concept in self.semantic_models['gaming_concepts'].items():
                for relation_type, related_terms in concept.relations.items():
                    for term in related_terms:
                        if relation_type not in self.concept_relations[concept_name]:
                            self.concept_relations[concept_name][relation_type] = []
                        self.concept_relations[concept_name][relation_type].append(term)
            
            logger.info(f"✅ تم تهيئة {len(self.semantic_models['gaming_concepts'])} مفهوم دلالي")
            
        except Exception as e:
            logger.error(f"❌ فشل في تهيئة قاعدة البيانات الدلالية: {e}")
    
    async def semantic_search(self, 
                            query: str,
                            search_type: SemanticSearchType = SemanticSearchType.HYBRID,
                            max_expansions: int = 10) -> SemanticQuery:
        """البحث الدلالي الرئيسي"""
        
        start_time = time.time()
        self.semantic_stats['total_semantic_searches'] += 1
        self.semantic_stats['search_type_usage'][search_type.value] += 1
        
        try:
            # 1. تحليل الاستعلام وتحديد المفاهيم
            concepts = await self._extract_semantic_concepts(query)
            
            # 2. تحديد النية الدلالية
            intent_vector = await self._analyze_semantic_intent(query)
            
            # 3. إنشاء تمثيل سياقي
            context_embeddings = await self._generate_context_embeddings(query, concepts)
            
            # 4. توسيع الاستعلام دلالياً
            semantic_expansions = await self._expand_query_semantically(
                query, concepts, search_type, max_expansions
            )
            
            # 5. حساب نقاط الثقة
            confidence_score = self._calculate_semantic_confidence(concepts, intent_vector)
            
            # إنشاء الاستعلام الدلالي
            semantic_query = SemanticQuery(
                original_query=query,
                concepts=concepts,
                intent_vector=intent_vector,
                context_embeddings=context_embeddings,
                semantic_expansions=semantic_expansions,
                confidence_score=confidence_score,
                search_type=search_type
            )
            
            execution_time = time.time() - start_time
            logger.info(f"🧠 البحث الدلالي مكتمل: {len(concepts)} مفهوم، {len(semantic_expansions)} توسيع في {execution_time:.2f}ث")
            
            return semantic_query
            
        except Exception as e:
            logger.error(f"❌ فشل في البحث الدلالي: {e}")
            # إرجاع استعلام أساسي
            return self._create_basic_semantic_query(query, search_type)
    
    async def _extract_semantic_concepts(self, query: str) -> List[SemanticConcept]:
        """استخراج المفاهيم الدلالية من الاستعلام"""
        try:
            concepts = []
            query_lower = query.lower()
            
            # البحث عن المفاهيم المباشرة
            for concept_name, concept in self.semantic_models['gaming_concepts'].items():
                if concept_name in query_lower:
                    concepts.append(concept)
                    continue
                
                # البحث في المرادفات
                for relation_type, related_terms in concept.relations.items():
                    if relation_type == 'synonym':
                        for synonym in related_terms:
                            if synonym.lower() in query_lower:
                                # إنشاء مفهوم مشتق
                                derived_concept = SemanticConcept(
                                    term=synonym,
                                    concept_type=concept.concept_type,
                                    weight=concept.weight * 0.9,  # وزن أقل للمرادفات
                                    relations=concept.relations,
                                    frequency=1,
                                    last_updated=datetime.now()
                                )
                                concepts.append(derived_concept)
                                break
            
            # استخراج مفاهيم جديدة باستخدام AI
            if len(concepts) < 3:
                ai_concepts = await self._extract_concepts_with_ai(query)
                concepts.extend(ai_concepts)
            
            # ترتيب حسب الوزن
            concepts.sort(key=lambda x: x.weight, reverse=True)
            
            return concepts[:10]  # أفضل 10 مفاهيم
            
        except Exception as e:
            logger.error(f"❌ فشل في استخراج المفاهيم الدلالية: {e}")
            return []
    
    async def _extract_concepts_with_ai(self, query: str) -> List[SemanticConcept]:
        """استخراج المفاهيم باستخدام الذكاء الاصطناعي"""
        try:
            # طلب تحليل من النماذج الاحتياطية
            analysis_request = {
                'query': query,
                'task': 'extract_gaming_concepts',
                'context': 'semantic_search'
            }
            
            ai_result = await fallback_ai_manager.analyze_semantic_concepts(analysis_request)
            
            if ai_result and 'concepts' in ai_result:
                ai_concepts = []
                for concept_data in ai_result['concepts']:
                    concept = SemanticConcept(
                        term=concept_data.get('term', ''),
                        concept_type=concept_data.get('type', 'unknown'),
                        weight=concept_data.get('weight', 0.5),
                        relations=concept_data.get('relations', {}),
                        frequency=1,
                        last_updated=datetime.now()
                    )
                    ai_concepts.append(concept)
                
                self.semantic_stats['concept_discoveries'] += len(ai_concepts)
                return ai_concepts
            
        except Exception as e:
            logger.error(f"❌ فشل في استخراج المفاهيم بالذكاء الاصطناعي: {e}")
        
        return []
    
    async def _analyze_semantic_intent(self, query: str) -> Optional[List[float]]:
        """تحليل النية الدلالية"""
        try:
            intent_scores = {}
            query_lower = query.lower()
            
            # حساب نقاط لكل نية
            for intent, patterns in self.semantic_models['intent_patterns'].items():
                score = 0.0
                for pattern in patterns:
                    if pattern.lower() in query_lower:
                        score += 1.0
                    # فحص تشابه جزئي
                    pattern_words = set(pattern.lower().split())
                    query_words = set(query_lower.split())
                    overlap = len(pattern_words & query_words)
                    if overlap > 0:
                        score += overlap / len(pattern_words) * 0.5
                
                intent_scores[intent] = score
            
            # تحويل إلى vector
            intent_vector = []
            for intent in self.semantic_models['intent_patterns'].keys():
                intent_vector.append(intent_scores.get(intent, 0.0))
            
            # تطبيع الـ vector
            total_score = sum(intent_vector)
            if total_score > 0:
                intent_vector = [score / total_score for score in intent_vector]
            
            return intent_vector
            
        except Exception as e:
            logger.error(f"❌ فشل في تحليل النية الدلالية: {e}")
            return None
    
    async def _generate_context_embeddings(self, query: str, concepts: List[SemanticConcept]) -> Optional[List[float]]:
        """إنشاء تمثيل سياقي للاستعلام"""
        try:
            # تمثيل مبسط للسياق
            context_features = []
            
            # ميزات الطول
            context_features.append(len(query.split()) / 20.0)  # تطبيع لـ 20 كلمة
            
            # ميزات المفاهيم
            context_features.append(len(concepts) / 10.0)  # تطبيع لـ 10 مفاهيم
            
            # ميزات التعقيد
            complexity = len(set(query.lower().split())) / len(query.split()) if query.split() else 0
            context_features.append(complexity)
            
            # ميزات الزمن (وجود كلمات زمنية)
            temporal_words = ['today', 'yesterday', 'recent', 'latest', 'new', '2025']
            temporal_score = sum(1 for word in temporal_words if word in query.lower()) / len(temporal_words)
            context_features.append(temporal_score)
            
            # ميزات النوع (نوع المحتوى المطلوب)
            content_types = ['news', 'review', 'guide', 'tutorial', 'analysis']
            content_score = sum(1 for ctype in content_types if ctype in query.lower()) / len(content_types)
            context_features.append(content_score)
            
            return context_features
            
        except Exception as e:
            logger.error(f"❌ فشل في إنشاء التمثيل السياقي: {e}")
            return None

    async def _expand_query_semantically(self,
                                       query: str,
                                       concepts: List[SemanticConcept],
                                       search_type: SemanticSearchType,
                                       max_expansions: int) -> List[str]:
        """توسيع الاستعلام دلالياً"""
        try:
            expansions = []

            if search_type == SemanticSearchType.CONCEPTUAL:
                expansions = await self._conceptual_expansion(query, concepts)
            elif search_type == SemanticSearchType.CONTEXTUAL:
                expansions = await self._contextual_expansion(query, concepts)
            elif search_type == SemanticSearchType.INTENT_BASED:
                expansions = await self._intent_based_expansion(query, concepts)
            elif search_type == SemanticSearchType.SIMILARITY:
                expansions = await self._similarity_expansion(query, concepts)
            elif search_type == SemanticSearchType.ASSOCIATIVE:
                expansions = await self._associative_expansion(query, concepts)
            else:  # HYBRID
                expansions = await self._hybrid_expansion(query, concepts)

            # إزالة التكرار والحد من العدد
            unique_expansions = []
            for expansion in expansions:
                if expansion != query and expansion not in unique_expansions:
                    unique_expansions.append(expansion)

            if unique_expansions:
                self.semantic_stats['successful_expansions'] += 1

            return unique_expansions[:max_expansions]

        except Exception as e:
            logger.error(f"❌ فشل في التوسيع الدلالي: {e}")
            return []

    async def _conceptual_expansion(self, query: str, concepts: List[SemanticConcept]) -> List[str]:
        """توسيع مفاهيمي"""
        expansions = []

        for concept in concepts[:3]:  # أفضل 3 مفاهيم
            # إضافة المرادفات
            if 'synonym' in concept.relations:
                for synonym in concept.relations['synonym']:
                    expanded_query = query.replace(concept.term, synonym)
                    if expanded_query != query:
                        expansions.append(expanded_query)

            # إضافة المفاهيم الأعم
            if 'hypernym' in concept.relations:
                for hypernym in concept.relations['hypernym']:
                    expansions.append(f"{query} {hypernym}")

            # إضافة المفاهيم الأخص
            if 'hyponym' in concept.relations:
                for hyponym in concept.relations['hyponym'][:2]:  # أفضل 2
                    expansions.append(f"{hyponym} {query}")

        return expansions

    async def _contextual_expansion(self, query: str, concepts: List[SemanticConcept]) -> List[str]:
        """توسيع سياقي"""
        expansions = []

        # إضافة سياق زمني
        temporal_contexts = ['latest', 'recent', '2025', 'new']
        for context in temporal_contexts:
            if context not in query.lower():
                expansions.append(f"{context} {query}")

        # إضافة سياق نوعي
        for concept in concepts[:2]:
            if concept.concept_type == 'core_concept':
                expansions.append(f"{query} {concept.concept_type}")

            # إضافة المفاهيم المرتبطة
            if 'related' in concept.relations:
                for related in concept.relations['related'][:2]:
                    expansions.append(f"{query} {related}")

        return expansions

    async def _intent_based_expansion(self, query: str, concepts: List[SemanticConcept]) -> List[str]:
        """توسيع قائم على النية"""
        expansions = []

        # تحديد النية المحتملة
        query_lower = query.lower()

        if any(word in query_lower for word in ['news', 'announcement', 'revealed']):
            expansions.extend([
                f"{query} breaking news",
                f"{query} latest announcement",
                f"{query} official news"
            ])
        elif any(word in query_lower for word in ['review', 'opinion', 'rating']):
            expansions.extend([
                f"{query} game review",
                f"{query} player opinion",
                f"{query} critic score"
            ])
        elif any(word in query_lower for word in ['guide', 'how to', 'tutorial']):
            expansions.extend([
                f"{query} complete guide",
                f"{query} step by step",
                f"{query} beginner tutorial"
            ])
        else:
            # نية عامة
            expansions.extend([
                f"{query} gaming",
                f"{query} video game",
                f"{query} game news"
            ])

        return expansions

    def _calculate_semantic_confidence(self, concepts: List[SemanticConcept], intent_vector: Optional[List[float]]) -> float:
        """حساب نقاط الثقة الدلالية"""
        confidence = 0.5  # نقاط أساسية

        # نقاط للمفاهيم المكتشفة
        if concepts:
            concept_score = min(len(concepts) / 5, 0.3)  # حتى 0.3 لـ 5 مفاهيم
            confidence += concept_score

        # نقاط للنية الواضحة
        if intent_vector:
            max_intent_score = max(intent_vector) if intent_vector else 0
            confidence += max_intent_score * 0.2

        # نقاط لجودة المفاهيم
        if concepts:
            avg_weight = sum(c.weight for c in concepts) / len(concepts)
            confidence += avg_weight * 0.1

        return min(confidence, 1.0)

# إنشاء مثيل عام
semantic_search_engine = SemanticSearchEngine()
