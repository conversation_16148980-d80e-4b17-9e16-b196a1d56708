#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام فحص الفهرسة باستخدام SEOwl API
SEOwl API Indexing Checker System
"""

import asyncio
import aiohttp
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import hashlib

from modules.logger import logger
from modules.database import db


class SEOwlIndexingChecker:
    """فاحص الفهرسة باستخدام SEOwl API"""
    
    def __init__(self, api_key: str = "V0P8IMfg8yDLL2buW3ZNijYnVXQIJICFlWCdBRsmbm1KZ1nA47PlgDhS2zje"):
        self.api_key = api_key
        self.base_url = "https://api.seowl.co/v1"
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
            "User-Agent": "Gaming-News-Agent/1.0"
        }
        
        # إعدادات النظام
        self.check_delay_hours = 3  # 3 ساعات انتظار بعد النشر
        self.max_retries = 3
        self.timeout = 30
        
        # قائمة المقالات المنتظرة للفحص
        self.pending_checks = {}
        
        # إحصائيات النظام
        self.stats = {
            'total_checks': 0,
            'successful_checks': 0,
            'failed_checks': 0,
            'issues_found': 0,
            'issues_fixed': 0,
            'last_check': None
        }
    
    async def schedule_article_check(self, article_data: Dict, published_url: str):
        """جدولة فحص مقال بعد 3 ساعات من النشر"""
        try:
            article_id = article_data.get('id', str(hash(published_url)))
            
            # حساب وقت الفحص (3 ساعات من الآن)
            check_time = datetime.now() + timedelta(hours=self.check_delay_hours)
            
            # إضافة المقال لقائمة الانتظار
            self.pending_checks[article_id] = {
                'article_data': article_data,
                'published_url': published_url,
                'scheduled_time': check_time,
                'status': 'pending',
                'created_at': datetime.now().isoformat()
            }
            
            # حفظ في قاعدة البيانات
            await self._save_pending_check(article_id, self.pending_checks[article_id])
            
            logger.info(f"📅 تم جدولة فحص المقال: {published_url} في {check_time.strftime('%Y-%m-%d %H:%M:%S')}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في جدولة فحص المقال: {e}")
            return False
    
    async def run_scheduled_checks(self):
        """تشغيل الفحوصات المجدولة"""
        try:
            current_time = datetime.now()
            
            # البحث عن المقالات الجاهزة للفحص
            ready_checks = []
            
            for article_id, check_data in self.pending_checks.items():
                if (check_data['status'] == 'pending' and 
                    current_time >= check_data['scheduled_time']):
                    ready_checks.append((article_id, check_data))
            
            if not ready_checks:
                return
            
            logger.info(f"🔍 بدء فحص {len(ready_checks)} مقال جاهز للفحص...")
            
            for article_id, check_data in ready_checks:
                try:
                    # تحديث حالة الفحص
                    self.pending_checks[article_id]['status'] = 'checking'
                    
                    # تشغيل الفحص
                    result = await self._check_article_indexing(
                        check_data['published_url'],
                        check_data['article_data']
                    )
                    
                    if result['success']:
                        self.pending_checks[article_id]['status'] = 'completed'
                        self.pending_checks[article_id]['result'] = result
                        
                        logger.info(f"✅ تم فحص المقال بنجاح: {check_data['published_url']}")
                        
                        # إصلاح المشاكل إذا وجدت
                        if result.get('issues'):
                            await self._fix_article_issues(check_data['published_url'], result['issues'])
                    
                    else:
                        self.pending_checks[article_id]['status'] = 'failed'
                        self.pending_checks[article_id]['error'] = result.get('error')
                        
                        logger.error(f"❌ فشل فحص المقال: {check_data['published_url']}")
                    
                    # تحديث الإحصائيات
                    await self._update_stats(result)
                    
                    # انتظار قصير بين الفحوصات
                    await asyncio.sleep(5)
                    
                except Exception as e:
                    logger.error(f"❌ خطأ في فحص المقال {article_id}: {e}")
                    self.pending_checks[article_id]['status'] = 'error'
                    self.pending_checks[article_id]['error'] = str(e)
            
            # تنظيف المقالات المكتملة القديمة
            await self._cleanup_completed_checks()
            
        except Exception as e:
            logger.error(f"❌ خطأ في تشغيل الفحوصات المجدولة: {e}")
    
    async def _check_article_indexing(self, url: str, article_data: Dict) -> Dict:
        """فحص فهرسة مقال واحد باستخدام SEOwl API"""
        try:
            logger.info(f"🔍 بدء فحص فهرسة المقال: {url}")
            
            # 1. فحص حالة الفهرسة
            indexing_status = await self._check_indexing_status(url)
            
            # 2. فحص مشاكل SEO
            seo_issues = await self._check_seo_issues(url)
            
            # 3. فحص سرعة الصفحة
            page_speed = await self._check_page_speed(url)
            
            # 4. فحص الروابط المكسورة
            broken_links = await self._check_broken_links(url)
            
            # تجميع النتائج
            result = {
                'success': True,
                'url': url,
                'checked_at': datetime.now().isoformat(),
                'indexing_status': indexing_status,
                'seo_issues': seo_issues,
                'page_speed': page_speed,
                'broken_links': broken_links,
                'issues': []
            }
            
            # تحليل المشاكل
            issues = []
            
            if not indexing_status.get('indexed', False):
                issues.append({
                    'type': 'not_indexed',
                    'severity': 'high',
                    'description': 'الصفحة غير مفهرسة في Google',
                    'fix_action': 'submit_to_google'
                })
            
            if seo_issues.get('issues'):
                for issue in seo_issues['issues']:
                    issues.append({
                        'type': 'seo_issue',
                        'severity': issue.get('severity', 'medium'),
                        'description': issue.get('description'),
                        'fix_action': issue.get('fix_action')
                    })
            
            if page_speed.get('score', 100) < 70:
                issues.append({
                    'type': 'page_speed',
                    'severity': 'medium',
                    'description': f'سرعة الصفحة منخفضة: {page_speed.get("score", 0)}',
                    'fix_action': 'optimize_performance'
                })
            
            if broken_links.get('broken_count', 0) > 0:
                issues.append({
                    'type': 'broken_links',
                    'severity': 'medium',
                    'description': f'روابط مكسورة: {broken_links.get("broken_count", 0)}',
                    'fix_action': 'fix_broken_links'
                })
            
            result['issues'] = issues
            result['total_issues'] = len(issues)
            
            # تحديث الإحصائيات
            self.stats['total_checks'] += 1
            self.stats['successful_checks'] += 1
            self.stats['issues_found'] += len(issues)
            self.stats['last_check'] = datetime.now().isoformat()
            
            return result
            
        except Exception as e:
            logger.error(f"❌ خطأ في فحص فهرسة المقال: {e}")
            
            self.stats['total_checks'] += 1
            self.stats['failed_checks'] += 1
            
            return {
                'success': False,
                'url': url,
                'error': str(e),
                'checked_at': datetime.now().isoformat()
            }
    
    async def _check_indexing_status(self, url: str) -> Dict:
        """فحص حالة الفهرسة باستخدام SEOwl API"""
        try:
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
                
                # استعلام فحص الفهرسة
                payload = {
                    "url": url,
                    "check_type": "indexing_status"
                }
                
                async with session.post(
                    f"{self.base_url}/seo/check",
                    headers=self.headers,
                    json=payload
                ) as response:
                    
                    if response.status == 200:
                        data = await response.json()
                        
                        return {
                            'indexed': data.get('indexed', False),
                            'last_crawled': data.get('last_crawled'),
                            'crawl_status': data.get('crawl_status'),
                            'indexing_issues': data.get('issues', [])
                        }
                    
                    else:
                        logger.warning(f"⚠️ استجابة غير متوقعة من SEOwl API: {response.status}")
                        return {'indexed': None, 'error': f'HTTP {response.status}'}
        
        except Exception as e:
            logger.error(f"❌ خطأ في فحص حالة الفهرسة: {e}")
            return {'indexed': None, 'error': str(e)}
    
    async def _check_seo_issues(self, url: str) -> Dict:
        """فحص مشاكل SEO باستخدام SEOwl API"""
        try:
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
                
                payload = {
                    "url": url,
                    "check_type": "seo_audit"
                }
                
                async with session.post(
                    f"{self.base_url}/seo/audit",
                    headers=self.headers,
                    json=payload
                ) as response:
                    
                    if response.status == 200:
                        data = await response.json()
                        
                        return {
                            'score': data.get('seo_score', 0),
                            'issues': data.get('issues', []),
                            'recommendations': data.get('recommendations', [])
                        }
                    
                    else:
                        return {'score': None, 'issues': [], 'error': f'HTTP {response.status}'}
        
        except Exception as e:
            logger.error(f"❌ خطأ في فحص مشاكل SEO: {e}")
            return {'score': None, 'issues': [], 'error': str(e)}
    
    async def _check_page_speed(self, url: str) -> Dict:
        """فحص سرعة الصفحة باستخدام SEOwl API"""
        try:
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
                
                payload = {
                    "url": url,
                    "device": "mobile"  # فحص الموبايل أولاً
                }
                
                async with session.post(
                    f"{self.base_url}/performance/speed",
                    headers=self.headers,
                    json=payload
                ) as response:
                    
                    if response.status == 200:
                        data = await response.json()
                        
                        return {
                            'score': data.get('performance_score', 0),
                            'loading_time': data.get('loading_time', 0),
                            'recommendations': data.get('recommendations', [])
                        }
                    
                    else:
                        return {'score': None, 'error': f'HTTP {response.status}'}
        
        except Exception as e:
            logger.error(f"❌ خطأ في فحص سرعة الصفحة: {e}")
            return {'score': None, 'error': str(e)}
    
    async def _check_broken_links(self, url: str) -> Dict:
        """فحص الروابط المكسورة باستخدام SEOwl API"""
        try:
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
                
                payload = {
                    "url": url,
                    "check_internal": True,
                    "check_external": True
                }
                
                async with session.post(
                    f"{self.base_url}/links/check",
                    headers=self.headers,
                    json=payload
                ) as response:
                    
                    if response.status == 200:
                        data = await response.json()
                        
                        return {
                            'total_links': data.get('total_links', 0),
                            'broken_count': data.get('broken_count', 0),
                            'broken_links': data.get('broken_links', [])
                        }
                    
                    else:
                        return {'broken_count': None, 'error': f'HTTP {response.status}'}
        
        except Exception as e:
            logger.error(f"❌ خطأ في فحص الروابط المكسورة: {e}")
            return {'broken_count': None, 'error': str(e)}

    async def _fix_article_issues(self, url: str, issues: List[Dict]):
        """إصلاح مشاكل المقال تلقائياً"""
        try:
            logger.info(f"🔧 بدء إصلاح {len(issues)} مشكلة للمقال: {url}")

            fixed_count = 0

            for issue in issues:
                try:
                    issue_type = issue.get('type')
                    fix_action = issue.get('fix_action')

                    if issue_type == 'not_indexed':
                        success = await self._submit_to_google_indexing(url)
                        if success:
                            fixed_count += 1
                            logger.info(f"✅ تم إرسال الصفحة لفهرسة Google: {url}")

                    elif issue_type == 'seo_issue':
                        success = await self._fix_seo_issue(url, issue)
                        if success:
                            fixed_count += 1
                            logger.info(f"✅ تم إصلاح مشكلة SEO: {issue.get('description')}")

                    elif issue_type == 'page_speed':
                        success = await self._optimize_page_performance(url)
                        if success:
                            fixed_count += 1
                            logger.info(f"✅ تم تحسين أداء الصفحة: {url}")

                    elif issue_type == 'broken_links':
                        success = await self._fix_broken_links(url, issue)
                        if success:
                            fixed_count += 1
                            logger.info(f"✅ تم إصلاح الروابط المكسورة: {url}")

                    # انتظار قصير بين الإصلاحات
                    await asyncio.sleep(2)

                except Exception as e:
                    logger.error(f"❌ خطأ في إصلاح المشكلة {issue_type}: {e}")
                    continue

            # تحديث الإحصائيات
            self.stats['issues_fixed'] += fixed_count

            logger.info(f"🎉 تم إصلاح {fixed_count} من {len(issues)} مشكلة للمقال: {url}")

            return fixed_count

        except Exception as e:
            logger.error(f"❌ خطأ في إصلاح مشاكل المقال: {e}")
            return 0

    async def _submit_to_google_indexing(self, url: str) -> bool:
        """إرسال الصفحة لفهرسة Google"""
        try:
            # استخدام Google Search Console API أو IndexNow

            # 1. محاولة IndexNow أولاً (أسرع)
            indexnow_success = await self._submit_to_indexnow(url)

            if indexnow_success:
                logger.info(f"✅ تم إرسال الصفحة لـ IndexNow: {url}")
                return True

            # 2. محاولة Google Search Console API
            gsc_success = await self._submit_to_google_search_console(url)

            if gsc_success:
                logger.info(f"✅ تم إرسال الصفحة لـ Google Search Console: {url}")
                return True

            # 3. إنشاء sitemap محدث كحل بديل
            await self._update_sitemap_with_url(url)
            logger.info(f"✅ تم إضافة الصفحة لخريطة الموقع: {url}")

            return True

        except Exception as e:
            logger.error(f"❌ خطأ في إرسال الصفحة للفهرسة: {e}")
            return False

    async def _submit_to_indexnow(self, url: str) -> bool:
        """إرسال الصفحة لـ IndexNow"""
        try:
            # IndexNow API endpoints
            indexnow_endpoints = [
                "https://api.indexnow.org/indexnow",
                "https://www.bing.com/indexnow",
                "https://search.seznam.cz/indexnow"
            ]

            # إنشاء key فريد للموقع
            site_key = hashlib.md5(url.encode()).hexdigest()

            payload = {
                "host": url.split('/')[2],  # استخراج الدومين
                "key": site_key,
                "urlList": [url]
            }

            async with aiohttp.ClientSession() as session:
                for endpoint in indexnow_endpoints:
                    try:
                        async with session.post(
                            endpoint,
                            json=payload,
                            headers={"Content-Type": "application/json"}
                        ) as response:

                            if response.status in [200, 202]:
                                return True

                    except Exception:
                        continue

            return False

        except Exception as e:
            logger.error(f"❌ خطأ في IndexNow: {e}")
            return False

    async def _submit_to_google_search_console(self, url: str) -> bool:
        """إرسال الصفحة لـ Google Search Console"""
        try:
            # هذا يتطلب إعداد Google Search Console API
            # يمكن تطويره لاحقاً مع المفاتيح المناسبة

            logger.info(f"📝 تم تسجيل طلب إرسال لـ Google Search Console: {url}")

            # حفظ الطلب في قاعدة البيانات للمعالجة اللاحقة
            await self._save_indexing_request(url, 'google_search_console')

            return True

        except Exception as e:
            logger.error(f"❌ خطأ في Google Search Console: {e}")
            return False

    async def _update_sitemap_with_url(self, url: str):
        """تحديث خريطة الموقع بإضافة الرابط الجديد"""
        try:
            sitemap_path = "sitemap.xml"

            # قراءة خريطة الموقع الحالية
            sitemap_content = ""
            if os.path.exists(sitemap_path):
                with open(sitemap_path, 'r', encoding='utf-8') as f:
                    sitemap_content = f.read()

            # إضافة الرابط الجديد
            new_url_entry = f"""  <url>
    <loc>{url}</loc>
    <lastmod>{datetime.now().strftime('%Y-%m-%d')}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.8</priority>
  </url>"""

            # إدراج الرابط الجديد قبل إغلاق urlset
            if "</urlset>" in sitemap_content:
                sitemap_content = sitemap_content.replace("</urlset>", new_url_entry + "\n</urlset>")
            else:
                # إنشاء خريطة موقع جديدة
                sitemap_content = f"""<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
{new_url_entry}
</urlset>"""

            # حفظ خريطة الموقع المحدثة
            with open(sitemap_path, 'w', encoding='utf-8') as f:
                f.write(sitemap_content)

            logger.info(f"✅ تم تحديث خريطة الموقع بالرابط: {url}")

        except Exception as e:
            logger.error(f"❌ خطأ في تحديث خريطة الموقع: {e}")

    async def _fix_seo_issue(self, url: str, issue: Dict) -> bool:
        """إصلاح مشكلة SEO محددة"""
        try:
            description = issue.get('description', '')

            # تحليل نوع المشكلة وإصلاحها
            if 'title' in description.lower():
                return await self._fix_title_issue(url, issue)

            elif 'meta description' in description.lower():
                return await self._fix_meta_description_issue(url, issue)

            elif 'canonical' in description.lower():
                return await self._fix_canonical_issue(url, issue)

            elif 'heading' in description.lower() or 'h1' in description.lower():
                return await self._fix_heading_issue(url, issue)

            else:
                # مشكلة عامة - تسجيل للمراجعة اليدوية
                await self._log_manual_review_needed(url, issue)
                return True

        except Exception as e:
            logger.error(f"❌ خطأ في إصلاح مشكلة SEO: {e}")
            return False

    async def _optimize_page_performance(self, url: str) -> bool:
        """تحسين أداء الصفحة"""
        try:
            # تحسينات أساسية للأداء
            optimizations = [
                await self._enable_compression(url),
                await self._optimize_images(url),
                await self._minify_resources(url),
                await self._enable_caching(url)
            ]

            success_count = sum(optimizations)

            if success_count > 0:
                logger.info(f"✅ تم تطبيق {success_count} تحسين للأداء: {url}")
                return True

            return False

        except Exception as e:
            logger.error(f"❌ خطأ في تحسين الأداء: {e}")
            return False

    async def _fix_broken_links(self, url: str, issue: Dict) -> bool:
        """إصلاح الروابط المكسورة"""
        try:
            broken_links = issue.get('broken_links', [])

            fixed_count = 0

            for broken_link in broken_links:
                # محاولة إيجاد رابط بديل
                alternative_url = await self._find_alternative_url(broken_link)

                if alternative_url:
                    # تسجيل الحاجة لاستبدال الرابط
                    await self._log_link_replacement(url, broken_link, alternative_url)
                    fixed_count += 1

                else:
                    # تسجيل الحاجة لحذف الرابط
                    await self._log_link_removal(url, broken_link)
                    fixed_count += 1

            if fixed_count > 0:
                logger.info(f"✅ تم معالجة {fixed_count} رابط مكسور: {url}")
                return True

            return False

        except Exception as e:
            logger.error(f"❌ خطأ في إصلاح الروابط المكسورة: {e}")
            return False

    async def _save_pending_check(self, article_id: str, check_data: Dict):
        """حفظ فحص معلق في قاعدة البيانات"""
        try:
            # حفظ في قاعدة البيانات
            db.save_pending_indexing_check({
                'article_id': article_id,
                'check_data': json.dumps(check_data, ensure_ascii=False),
                'scheduled_time': check_data['scheduled_time'].isoformat(),
                'status': check_data['status']
            })

        except Exception as e:
            logger.error(f"❌ خطأ في حفظ الفحص المعلق: {e}")

    async def _update_stats(self, result: Dict):
        """تحديث إحصائيات النظام"""
        try:
            # حفظ الإحصائيات في قاعدة البيانات
            db.save_seowl_stats(self.stats)

        except Exception as e:
            logger.error(f"❌ خطأ في تحديث الإحصائيات: {e}")

    async def _cleanup_completed_checks(self):
        """تنظيف الفحوصات المكتملة القديمة"""
        try:
            cutoff_time = datetime.now() - timedelta(days=7)  # حذف بعد أسبوع

            to_remove = []
            for article_id, check_data in self.pending_checks.items():
                if (check_data['status'] in ['completed', 'failed', 'error'] and
                    datetime.fromisoformat(check_data['created_at']) < cutoff_time):
                    to_remove.append(article_id)

            for article_id in to_remove:
                del self.pending_checks[article_id]

            if to_remove:
                logger.info(f"🧹 تم تنظيف {len(to_remove)} فحص مكتمل قديم")

        except Exception as e:
            logger.error(f"❌ خطأ في تنظيف الفحوصات: {e}")

    async def _save_indexing_request(self, url: str, service: str):
        """حفظ طلب فهرسة للمعالجة اللاحقة"""
        try:
            db.save_indexing_request({
                'url': url,
                'service': service,
                'status': 'pending',
                'created_at': datetime.now().isoformat()
            })

        except Exception as e:
            logger.error(f"❌ خطأ في حفظ طلب الفهرسة: {e}")

    async def _fix_title_issue(self, url: str, issue: Dict) -> bool:
        """إصلاح مشكلة العنوان"""
        try:
            # تسجيل الحاجة لتحسين العنوان
            await self._log_seo_improvement(url, 'title', issue.get('description'))
            return True

        except Exception as e:
            logger.error(f"❌ خطأ في إصلاح مشكلة العنوان: {e}")
            return False

    async def _fix_meta_description_issue(self, url: str, issue: Dict) -> bool:
        """إصلاح مشكلة الوصف التعريفي"""
        try:
            await self._log_seo_improvement(url, 'meta_description', issue.get('description'))
            return True

        except Exception as e:
            logger.error(f"❌ خطأ في إصلاح الوصف التعريفي: {e}")
            return False

    async def _fix_canonical_issue(self, url: str, issue: Dict) -> bool:
        """إصلاح مشكلة العلامة الأساسية"""
        try:
            await self._log_seo_improvement(url, 'canonical', issue.get('description'))
            return True

        except Exception as e:
            logger.error(f"❌ خطأ في إصلاح العلامة الأساسية: {e}")
            return False

    async def _fix_heading_issue(self, url: str, issue: Dict) -> bool:
        """إصلاح مشكلة العناوين"""
        try:
            await self._log_seo_improvement(url, 'headings', issue.get('description'))
            return True

        except Exception as e:
            logger.error(f"❌ خطأ في إصلاح العناوين: {e}")
            return False

    async def _log_manual_review_needed(self, url: str, issue: Dict):
        """تسجيل الحاجة لمراجعة يدوية"""
        try:
            db.save_manual_review_request({
                'url': url,
                'issue_type': 'seo_issue',
                'description': issue.get('description'),
                'severity': issue.get('severity'),
                'created_at': datetime.now().isoformat()
            })

        except Exception as e:
            logger.error(f"❌ خطأ في تسجيل المراجعة اليدوية: {e}")

    async def _log_seo_improvement(self, url: str, improvement_type: str, description: str):
        """تسجيل تحسين SEO مطلوب"""
        try:
            db.save_seo_improvement({
                'url': url,
                'improvement_type': improvement_type,
                'description': description,
                'status': 'pending',
                'created_at': datetime.now().isoformat()
            })

        except Exception as e:
            logger.error(f"❌ خطأ في تسجيل تحسين SEO: {e}")

    async def _enable_compression(self, url: str) -> bool:
        """تفعيل ضغط الملفات"""
        try:
            # تسجيل الحاجة لتفعيل الضغط
            await self._log_performance_improvement(url, 'compression', 'تفعيل ضغط gzip/brotli')
            return True

        except Exception as e:
            logger.error(f"❌ خطأ في تفعيل الضغط: {e}")
            return False

    async def _optimize_images(self, url: str) -> bool:
        """تحسين الصور"""
        try:
            await self._log_performance_improvement(url, 'images', 'تحسين وضغط الصور')
            return True

        except Exception as e:
            logger.error(f"❌ خطأ في تحسين الصور: {e}")
            return False

    async def _minify_resources(self, url: str) -> bool:
        """تصغير الموارد"""
        try:
            await self._log_performance_improvement(url, 'minification', 'تصغير CSS/JS')
            return True

        except Exception as e:
            logger.error(f"❌ خطأ في تصغير الموارد: {e}")
            return False

    async def _enable_caching(self, url: str) -> bool:
        """تفعيل التخزين المؤقت"""
        try:
            await self._log_performance_improvement(url, 'caching', 'تفعيل التخزين المؤقت')
            return True

        except Exception as e:
            logger.error(f"❌ خطأ في تفعيل التخزين المؤقت: {e}")
            return False

    async def _log_performance_improvement(self, url: str, improvement_type: str, description: str):
        """تسجيل تحسين أداء مطلوب"""
        try:
            db.save_performance_improvement({
                'url': url,
                'improvement_type': improvement_type,
                'description': description,
                'status': 'pending',
                'created_at': datetime.now().isoformat()
            })

        except Exception as e:
            logger.error(f"❌ خطأ في تسجيل تحسين الأداء: {e}")

    async def _find_alternative_url(self, broken_url: str) -> Optional[str]:
        """البحث عن رابط بديل للرابط المكسور"""
        try:
            # محاولة إيجاد رابط مشابه في قاعدة البيانات
            # أو استخدام خدمة البحث

            # للآن، نعيد None (يمكن تطوير هذا لاحقاً)
            return None

        except Exception as e:
            logger.error(f"❌ خطأ في البحث عن رابط بديل: {e}")
            return None

    async def _log_link_replacement(self, url: str, broken_link: str, alternative_url: str):
        """تسجيل الحاجة لاستبدال رابط"""
        try:
            db.save_link_fix_request({
                'page_url': url,
                'broken_link': broken_link,
                'alternative_url': alternative_url,
                'action': 'replace',
                'status': 'pending',
                'created_at': datetime.now().isoformat()
            })

        except Exception as e:
            logger.error(f"❌ خطأ في تسجيل استبدال الرابط: {e}")

    async def _log_link_removal(self, url: str, broken_link: str):
        """تسجيل الحاجة لحذف رابط"""
        try:
            db.save_link_fix_request({
                'page_url': url,
                'broken_link': broken_link,
                'alternative_url': None,
                'action': 'remove',
                'status': 'pending',
                'created_at': datetime.now().isoformat()
            })

        except Exception as e:
            logger.error(f"❌ خطأ في تسجيل حذف الرابط: {e}")

    def get_stats(self) -> Dict:
        """الحصول على إحصائيات النظام"""
        return {
            **self.stats,
            'pending_checks': len([c for c in self.pending_checks.values() if c['status'] == 'pending']),
            'checking_now': len([c for c in self.pending_checks.values() if c['status'] == 'checking']),
            'completed_today': len([c for c in self.pending_checks.values()
                                  if c['status'] == 'completed' and
                                  datetime.fromisoformat(c['created_at']).date() == datetime.now().date()])
        }

    def get_pending_checks(self) -> List[Dict]:
        """الحصول على قائمة الفحوصات المعلقة"""
        return [
            {
                'article_id': article_id,
                'url': check_data['published_url'],
                'scheduled_time': check_data['scheduled_time'].isoformat() if isinstance(check_data['scheduled_time'], datetime) else check_data['scheduled_time'],
                'status': check_data['status'],
                'created_at': check_data['created_at']
            }
            for article_id, check_data in self.pending_checks.items()
        ]


# إنشاء مثيل عام للاستخدام
seowl_checker = SEOwlIndexingChecker()
