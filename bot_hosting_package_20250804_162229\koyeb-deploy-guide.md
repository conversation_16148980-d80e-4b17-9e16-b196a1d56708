# 🚀 دليل رفع البوت على Koyeb

## الخطوة 1: إنشاء حساب
1. اذهب إلى [koyeb.com](https://www.koyeb.com)
2. اضغط "Sign up for free"
3. سجل دخول بـ GitHub أو Google أو Email

## الخطوة 2: إنشاء تطبيق جديد
1. من لوحة التحكم اضغط "Create App"
2. اختر "Deploy from Git repository"
3. اضغط "Upload files" أو "Connect GitHub"

## الخطوة 3: رفع الملفات
### الطريقة الأولى: رفع مباشر
1. اضغط "Upload files"
2. اسحب جميع ملفات البوت أو اختر "Browse"
3. تأكد من رفع:
   - main.py
   - requirements.txt
   - Procfile
   - جميع ملفات JSON

### الطريقة الثانية: GitHub
1. ارفع الملفات على GitHub أولاً
2. اختر "Connect GitHub repository"
3. اختر المستودع والفرع

## الخطوة 4: إعداد التطبيق
1. **App name**: اختر اسم (مثل: minecraft-mods-bot)
2. **Region**: اختر أقرب منطقة
3. **Build command**: `pip install -r requirements.txt`
4. **Run command**: `python main.py`

## الخطوة 5: إعداد متغيرات البيئة
في قسم "Environment variables" أضف:
```
BOT_TOKEN=your_bot_token_here
ADMIN_CHAT_ID=your_admin_id_here
SUPABASE_URL=your_supabase_url
SUPABASE_KEY=your_supabase_key
ENVIRONMENT=production
DEBUG=false
LOG_LEVEL=INFO
```

## الخطوة 6: النشر
1. اضغط "Deploy"
2. انتظر حتى ينتهي البناء
3. راقب اللوجز في تبويب "Logs"

## الخطوة 7: مراقبة الأداء
1. اذهب إلى تبويب "Metrics" لمراقبة الأداء
2. استخدم "Logs" لمراقبة أخطاء البوت
3. "Settings" لتعديل الإعدادات

## نصائح مهمة:
- ✅ مجاني تماماً مع حدود سخية
- ✅ لا ينام أبداً (Always-on)
- ✅ سرعة عالية ووقت استجابة ممتاز
- ✅ دعم فني ممتاز
- ✅ SSL تلقائي
- 💡 مثالي للبوتات التي تحتاج عمل مستمر
