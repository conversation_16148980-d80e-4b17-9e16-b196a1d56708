#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير اتصال قاعدة البيانات المحسن
Enhanced database connection manager for hosting environments
"""

import os
import time
import logging
import requests
from typing import Optional, Dict, Any
from functools import wraps
import json

logger = logging.getLogger(__name__)

class DatabaseConnectionManager:
    """مدير اتصال قاعدة البيانات مع إعادة المحاولة والتعافي التلقائي"""
    
    def __init__(self):
        self.supabase_url = os.getenv('SUPABASE_URL', '').rstrip('/')
        self.supabase_key = os.getenv('SUPABASE_KEY', '')
        self.connection_healthy = False
        self.last_check_time = 0
        self.check_interval = 300  # 5 دقائق
        self.retry_delays = [1, 2, 5, 10, 20]  # تأخيرات إعادة المحاولة
        
        # إعداد headers
        self.headers = {
            'apikey': self.supabase_key,
            'Authorization': f'Bearer {self.supabase_key}',
            'Content-Type': 'application/json',
            'Prefer': 'return=representation'
        }
        
        # فحص الإعدادات الأولي
        self._validate_config()
    
    def _validate_config(self):
        """التحقق من صحة إعدادات قاعدة البيانات"""
        if not self.supabase_url:
            logger.error("❌ SUPABASE_URL مفقود")
            return False
        
        if not self.supabase_key:
            logger.error("❌ SUPABASE_KEY مفقود")
            return False
        
        if not self.supabase_url.startswith(('http://', 'https://')):
            logger.error(f"❌ رابط Supabase غير صحيح: {self.supabase_url}")
            return False
        
        logger.info("✅ إعدادات قاعدة البيانات صحيحة")
        return True
    
    def check_connection(self, force_check=False):
        """فحص حالة الاتصال مع قاعدة البيانات"""
        current_time = time.time()
        
        # فحص إذا كان الوقت مناسب للفحص
        if not force_check and (current_time - self.last_check_time) < self.check_interval:
            return self.connection_healthy
        
        self.last_check_time = current_time
        
        if not self.supabase_url or not self.supabase_key:
            logger.warning("⚠️ إعدادات Supabase مفقودة")
            self.connection_healthy = False
            return False
        
        try:
            # اختبار بسيط للاتصال
            url = f"{self.supabase_url}/rest/v1/"
            response = requests.get(url, headers=self.headers, timeout=10)
            
            if response.status_code == 200:
                self.connection_healthy = True
                logger.debug("✅ اتصال قاعدة البيانات صحي")
                return True
            elif response.status_code == 401:
                logger.error("❌ مفتاح Supabase غير صحيح")
                self.connection_healthy = False
                return False
            else:
                logger.warning(f"⚠️ استجابة غير متوقعة: {response.status_code}")
                self.connection_healthy = False
                return False
                
        except requests.exceptions.Timeout:
            logger.warning("⚠️ انتهت مهلة الاتصال مع قاعدة البيانات")
            self.connection_healthy = False
            return False
        except requests.exceptions.ConnectionError:
            logger.warning("⚠️ خطأ في الاتصال مع قاعدة البيانات")
            self.connection_healthy = False
            return False
        except Exception as e:
            logger.error(f"❌ خطأ غير متوقع في فحص الاتصال: {e}")
            self.connection_healthy = False
            return False
    
    def execute_with_retry(self, operation_func, *args, **kwargs):
        """تنفيذ عملية مع إعادة المحاولة التلقائية"""
        last_exception = None
        
        for attempt, delay in enumerate(self.retry_delays):
            try:
                # فحص الاتصال قبل المحاولة
                if not self.check_connection():
                    logger.warning(f"⚠️ اتصال قاعدة البيانات غير صحي - المحاولة {attempt + 1}")
                
                # تنفيذ العملية
                result = operation_func(*args, **kwargs)
                
                # إذا نجحت العملية، تحديث حالة الاتصال
                self.connection_healthy = True
                return result
                
            except requests.exceptions.Timeout as e:
                last_exception = e
                logger.warning(f"⚠️ انتهت مهلة العملية - المحاولة {attempt + 1}/{len(self.retry_delays)}")
                
            except requests.exceptions.ConnectionError as e:
                last_exception = e
                logger.warning(f"⚠️ خطأ اتصال - المحاولة {attempt + 1}/{len(self.retry_delays)}")
                
            except requests.exceptions.HTTPError as e:
                last_exception = e
                if e.response and e.response.status_code in [500, 502, 503, 504]:
                    logger.warning(f"⚠️ خطأ خادم مؤقت - المحاولة {attempt + 1}/{len(self.retry_delays)}")
                else:
                    # خطأ غير قابل للإعادة
                    logger.error(f"❌ خطأ HTTP غير قابل للإعادة: {e}")
                    break
                    
            except Exception as e:
                last_exception = e
                logger.error(f"❌ خطأ غير متوقع: {e}")
                break
            
            # انتظار قبل إعادة المحاولة
            if attempt < len(self.retry_delays) - 1:
                logger.info(f"⏳ انتظار {delay} ثانية قبل إعادة المحاولة...")
                time.sleep(delay)
        
        # إذا فشلت جميع المحاولات
        self.connection_healthy = False
        logger.error(f"❌ فشلت جميع محاولات تنفيذ العملية. آخر خطأ: {last_exception}")
        return None
    
    def safe_request(self, method, url, **kwargs):
        """طلب HTTP آمن مع إعادة المحاولة"""
        def _make_request():
            kwargs.setdefault('headers', self.headers)
            kwargs.setdefault('timeout', 15)
            
            response = requests.request(method, url, **kwargs)
            response.raise_for_status()
            return response
        
        return self.execute_with_retry(_make_request)

# إنشاء instance عام
db_manager = DatabaseConnectionManager()

def with_db_retry(func):
    """decorator لإضافة إعادة المحاولة التلقائية للدوال"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        return db_manager.execute_with_retry(func, *args, **kwargs)
    return wrapper

def check_database_health():
    """فحص صحة قاعدة البيانات"""
    return db_manager.check_connection(force_check=True)

def get_database_status():
    """الحصول على حالة قاعدة البيانات"""
    return {
        'healthy': db_manager.connection_healthy,
        'url_configured': bool(db_manager.supabase_url),
        'key_configured': bool(db_manager.supabase_key),
        'last_check': db_manager.last_check_time
    }

# دوال مساعدة للاستخدام في الكود الحالي
def safe_supabase_request(method, endpoint, **kwargs):
    """طلب آمن لـ Supabase"""
    if not db_manager.supabase_url:
        logger.error("❌ رابط Supabase غير مكون")
        return None
    
    url = f"{db_manager.supabase_url}/rest/v1/{endpoint.lstrip('/')}"
    return db_manager.safe_request(method, url, **kwargs)

def test_connection_with_details():
    """اختبار مفصل للاتصال"""
    logger.info("🔍 اختبار اتصال قاعدة البيانات...")
    
    status = get_database_status()
    logger.info(f"📊 حالة الإعدادات:")
    logger.info(f"   URL مكون: {'✅' if status['url_configured'] else '❌'}")
    logger.info(f"   KEY مكون: {'✅' if status['key_configured'] else '❌'}")
    
    if not status['url_configured'] or not status['key_configured']:
        logger.error("❌ إعدادات قاعدة البيانات مفقودة")
        return False
    
    # اختبار الاتصال
    healthy = check_database_health()
    logger.info(f"🔗 حالة الاتصال: {'✅ صحي' if healthy else '❌ غير صحي'}")
    
    return healthy

# تصدير الدوال المهمة
__all__ = [
    'DatabaseConnectionManager',
    'db_manager',
    'with_db_retry',
    'check_database_health',
    'get_database_status',
    'safe_supabase_request',
    'test_connection_with_details'
]
