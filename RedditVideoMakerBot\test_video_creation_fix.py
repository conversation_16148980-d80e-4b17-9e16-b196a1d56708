#!/usr/bin/env python3
"""
اختبار إصلاح مشكلة إنشاء الفيديو
يتحقق من جميع المكونات ويحاول إنشاء فيديو تجريبي
"""

import os
import sys
import logging
import traceback
from pathlib import Path

# إعداد السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/test_fix.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def test_imports():
    """اختبار استيراد جميع الوحدات المطلوبة"""
    print("🔍 اختبار استيراد الوحدات...")
    
    try:
        import ffmpeg
        print("✅ ffmpeg-python")
    except ImportError as e:
        print(f"❌ ffmpeg-python: {e}")
        return False
    
    try:
        from utils import settings
        print("✅ utils.settings")
    except ImportError as e:
        print(f"❌ utils.settings: {e}")
        return False
    
    try:
        from video_creation.final_video import make_final_video
        print("✅ video_creation.final_video")
    except ImportError as e:
        print(f"❌ video_creation.final_video: {e}")
        return False
    
    try:
        from reddit.subreddit import get_subreddit_threads
        print("✅ reddit.subreddit")
    except ImportError as e:
        print(f"❌ reddit.subreddit: {e}")
        return False
    
    return True

def test_ffmpeg_installation():
    """اختبار تثبيت FFmpeg"""
    print("\n🔍 اختبار FFmpeg...")
    
    try:
        import subprocess
        result = subprocess.run(['ffmpeg', '-version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✅ FFmpeg مثبت ويعمل بشكل صحيح")
            return True
        else:
            print(f"❌ FFmpeg لا يعمل: {result.stderr}")
            return False
    except FileNotFoundError:
        print("❌ FFmpeg غير مثبت")
        return False
    except Exception as e:
        print(f"❌ خطأ في اختبار FFmpeg: {e}")
        return False

def test_directories():
    """اختبار وجود المجلدات المطلوبة"""
    print("\n🔍 اختبار المجلدات...")
    
    required_dirs = [
        "results",
        "assets/temp",
        "logs",
        "TTS",
        "video_creation",
        "utils"
    ]
    
    all_exist = True
    for dir_path in required_dirs:
        if os.path.exists(dir_path):
            print(f"✅ {dir_path}")
        else:
            print(f"❌ {dir_path} غير موجود")
            all_exist = False
            
            # إنشاء المجلد إذا لم يكن موجوداً
            try:
                os.makedirs(dir_path, exist_ok=True)
                print(f"🔧 تم إنشاء {dir_path}")
            except Exception as e:
                print(f"❌ فشل في إنشاء {dir_path}: {e}")
    
    return all_exist

def test_config_file():
    """اختبار ملف الإعدادات"""
    print("\n🔍 اختبار ملف الإعدادات...")
    
    if not os.path.exists("config.toml"):
        print("❌ ملف config.toml غير موجود")
        return False
    
    try:
        from utils import settings
        config = settings.config
        
        # التحقق من الأقسام المهمة
        required_sections = [
            "reddit.creds",
            "settings",
            "settings.tts",
            "settings.background"
        ]
        
        for section in required_sections:
            keys = section.split('.')
            current = config
            try:
                for key in keys:
                    current = current[key]
                print(f"✅ {section}")
            except KeyError:
                print(f"❌ {section} غير موجود في الإعدادات")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في قراءة الإعدادات: {e}")
        return False

def test_reddit_connection():
    """اختبار الاتصال بـ Reddit (اختياري)"""
    print("\n🔍 اختبار الاتصال بـ Reddit...")
    
    try:
        from utils import settings
        creds = settings.config["reddit"]["creds"]
        
        if not creds["client_id"] or not creds["client_secret"]:
            print("⚠️ بيانات Reddit API غير مكتملة")
            return False
        
        print("✅ بيانات Reddit API موجودة")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فحص بيانات Reddit: {e}")
        return False

def run_comprehensive_test():
    """تشغيل اختبار شامل"""
    print("=" * 60)
    print("🧪 اختبار شامل لإصلاح مشكلة إنشاء الفيديو")
    print("=" * 60)
    
    tests = [
        ("استيراد الوحدات", test_imports),
        ("تثبيت FFmpeg", test_ffmpeg_installation),
        ("المجلدات المطلوبة", test_directories),
        ("ملف الإعدادات", test_config_file),
        ("اتصال Reddit", test_reddit_connection),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {e}")
            results[test_name] = False
    
    # تلخيص النتائج
    print("\n" + "=" * 60)
    print("📊 ملخص نتائج الاختبار")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nالنتيجة النهائية: {passed}/{total} اختبارات نجحت")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام")
        return True
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى إصلاح المشاكل قبل المتابعة")
        return False

def suggest_fixes():
    """اقتراح حلول للمشاكل الشائعة"""
    print("\n" + "=" * 60)
    print("🔧 اقتراحات لإصلاح المشاكل الشائعة")
    print("=" * 60)
    
    print("""
1. إذا كان FFmpeg غير مثبت:
   - قم بتشغيل: python utils/ffmpeg_install.py
   - أو قم بتثبيته يدوياً من: https://ffmpeg.org/

2. إذا كانت المكتبات غير مثبتة:
   - قم بتشغيل: pip install -r requirements.txt

3. إذا كانت بيانات Reddit API غير صحيحة:
   - تأكد من إعداد Reddit API في config.toml
   - راجع الدليل في README_ARABIC.md

4. إذا كانت المجلدات غير موجودة:
   - سيتم إنشاؤها تلقائياً عند تشغيل هذا الاختبار

5. لاختبار إنشاء فيديو فعلي:
   - قم بتشغيل: python main.py
   - أو: python quick_test.py
    """)

if __name__ == "__main__":
    try:
        success = run_comprehensive_test()
        
        if not success:
            suggest_fixes()
        
        print(f"\n{'='*60}")
        print("🏁 انتهى الاختبار")
        print(f"{'='*60}")
        
    except Exception as e:
        print(f"❌ خطأ حرج في الاختبار: {e}")
        print(f"تفاصيل الخطأ:\n{traceback.format_exc()}")
        sys.exit(1)
