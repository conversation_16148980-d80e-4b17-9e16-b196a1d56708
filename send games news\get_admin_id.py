#!/usr/bin/env python3
"""
سكريبت محسن للحصول على معرف المدير الرقمي من تيليجرام
"""

import asyncio
from telegram import Bo<PERSON>
from config.settings import BotConfig

async def get_admin_id():
    """الحصول على معرف المدير مع تحسينات"""
    try:
        # إنشاء البوت
        bot = Bot(token=BotConfig.TELEGRAM_BOT_TOKEN)

        print("🤖 بوت تيليجرام جاهز!")
        print("📱 للحصول على معرف المدير:")
        print("1. اذهب إلى البوت في تيليجرام")
        print("2. أرسل أي رسالة للبوت (مثل: /start أو مرحبا)")
        print("3. سيظهر معرف المدير هنا")
        print("=" * 50)

        # الحصول على التحديثات
        updates = await bot.get_updates(limit=20)

        if updates:
            print(f"📨 تم العثور على {len(updates)} رسالة")
            print("\n📋 قائمة المستخدمين الذين أرسلوا رسائل:")

            valid_admins = []

            for i, update in enumerate(updates[-10:], 1):  # آخر 10 رسائل
                if update.message and update.message.from_user:
                    chat_id = update.message.chat_id
                    username = update.message.from_user.username or "غير محدد"
                    first_name = update.message.from_user.first_name or "غير محدد"
                    message_text = update.message.text or "رسالة غير نصية"

                    print(f"\n{i}. 💬 رسالة من:")
                    print(f"   📱 معرف المحادثة: {chat_id}")
                    print(f"   👤 اسم المستخدم: @{username}")
                    print(f"   📝 الاسم الأول: {first_name}")
                    print(f"   📄 النص: {message_text[:50]}...")

                    # التحقق من صحة معرف المحادثة
                    try:
                        await bot.send_chat_action(chat_id=chat_id, action="typing")
                        print(f"   ✅ معرف صالح للإرسال")
                        valid_admins.append({
                            'chat_id': chat_id,
                            'username': username,
                            'first_name': first_name
                        })
                    except Exception as test_error:
                        print(f"   ❌ معرف غير صالح: {test_error}")

                    print("-" * 30)

            if valid_admins:
                print(f"\n✅ تم العثور على {len(valid_admins)} معرف صالح:")
                for admin in valid_admins:
                    print(f"   🆔 {admin['chat_id']} - {admin['first_name']} (@{admin['username']})")

                print(f"\n💡 لاستخدام أول معرف صالح، أضف هذا السطر في config/settings.py:")
                print(f"TELEGRAM_ADMIN_ID = \"{valid_admins[0]['chat_id']}\"")
            else:
                print("\n⚠️ لم يتم العثور على معرفات صالحة")

        else:
            print("⚠️ لا توجد رسائل حديثة")
            print("💡 أرسل رسالة للبوت أولاً ثم شغل هذا السكريبت مرة أخرى")
            print(f"🔗 رابط البوت: https://t.me/{BotConfig.TELEGRAM_BOT_USERNAME.replace('@', '')}")

    except Exception as e:
        print(f"❌ خطأ: {e}")
        if "Unauthorized" in str(e):
            print("💡 تحقق من صحة TELEGRAM_BOT_TOKEN في config/settings.py")

if __name__ == "__main__":
    asyncio.run(get_admin_id())
