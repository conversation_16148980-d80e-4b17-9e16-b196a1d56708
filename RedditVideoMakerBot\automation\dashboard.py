#!/usr/bin/env python3
"""
واجهة مراقبة ويب لنظام إنشاء الفيديوهات
تعرض حالة النظام والإحصائيات والسجلات
"""

from flask import Flask, render_template, jsonify, request, redirect, url_for
import json
import os
import logging
from datetime import datetime, timedelta
from pathlib import Path
import psutil
import threading
import time
from typing import Dict, List, Any

# إعداد السجلات
logger = logging.getLogger(__name__)

class SystemMonitor:
    def __init__(self):
        self.stats_file = "logs/system_stats.json"
        self.logs_dir = Path("logs")
        self.logs_dir.mkdir(exist_ok=True)
        
        # إحصائيات النظام
        self.stats = {
            'start_time': datetime.now().isoformat(),
            'videos_created': 0,
            'videos_published': 0,
            'errors_count': 0,
            'last_video': None,
            'last_error': None,
            'status': 'running'
        }
        
        # تحميل الإحصائيات المحفوظة
        self.load_stats()
        
    def load_stats(self):
        """تحميل الإحصائيات من الملف"""
        try:
            if os.path.exists(self.stats_file):
                with open(self.stats_file, 'r', encoding='utf-8') as f:
                    saved_stats = json.load(f)
                    self.stats.update(saved_stats)
        except Exception as e:
            logger.error(f"خطأ في تحميل الإحصائيات: {e}")
            
    def save_stats(self):
        """حفظ الإحصائيات في الملف"""
        try:
            with open(self.stats_file, 'w', encoding='utf-8') as f:
                json.dump(self.stats, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"خطأ في حفظ الإحصائيات: {e}")
            
    def update_video_created(self, video_info: Dict[str, Any]):
        """تحديث إحصائيات إنشاء فيديو"""
        self.stats['videos_created'] += 1
        self.stats['last_video'] = {
            'timestamp': datetime.now().isoformat(),
            'info': video_info
        }
        self.save_stats()
        
    def update_video_published(self, video_url: str):
        """تحديث إحصائيات نشر فيديو"""
        self.stats['videos_published'] += 1
        if self.stats['last_video']:
            self.stats['last_video']['published_url'] = video_url
        self.save_stats()
        
    def update_error(self, error_info: Dict[str, Any]):
        """تحديث إحصائيات الأخطاء"""
        self.stats['errors_count'] += 1
        self.stats['last_error'] = {
            'timestamp': datetime.now().isoformat(),
            'info': error_info
        }
        self.save_stats()
        
    def get_system_info(self) -> Dict[str, Any]:
        """الحصول على معلومات النظام"""
        try:
            # معلومات الموارد
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # وقت التشغيل
            start_time = datetime.fromisoformat(self.stats['start_time'])
            uptime = datetime.now() - start_time
            
            return {
                'cpu_usage': cpu_percent,
                'memory_usage': memory.percent,
                'memory_total': memory.total // (1024**3),  # GB
                'memory_used': memory.used // (1024**3),   # GB
                'disk_usage': (disk.used / disk.total) * 100,
                'disk_total': disk.total // (1024**3),     # GB
                'disk_free': disk.free // (1024**3),       # GB
                'uptime': str(uptime).split('.')[0],  # بدون الميكروثواني
                'status': self.stats['status']
            }
        except Exception as e:
            logger.error(f"خطأ في الحصول على معلومات النظام: {e}")
            return {}
            
    def get_recent_logs(self, log_type: str = "all", limit: int = 100) -> List[Dict[str, Any]]:
        """الحصول على السجلات الحديثة"""
        logs = []
        try:
            log_files = {
                'scheduler': 'logs/scheduler.log',
                'main': 'logs/main.log',
                'errors': 'logs/errors.log'
            }
            
            files_to_read = [log_files[log_type]] if log_type in log_files else log_files.values()
            
            for log_file in files_to_read:
                if os.path.exists(log_file):
                    with open(log_file, 'r', encoding='utf-8') as f:
                        lines = f.readlines()
                        for line in reversed(lines[-limit:]):
                            if line.strip():
                                logs.append({
                                    'timestamp': self._extract_timestamp(line),
                                    'level': self._extract_level(line),
                                    'message': line.strip(),
                                    'source': os.path.basename(log_file)
                                })
                                
            # ترتيب حسب الوقت
            logs.sort(key=lambda x: x['timestamp'], reverse=True)
            return logs[:limit]
            
        except Exception as e:
            logger.error(f"خطأ في قراءة السجلات: {e}")
            return []
            
    def _extract_timestamp(self, log_line: str) -> str:
        """استخراج الوقت من سطر السجل"""
        try:
            # تنسيق: 2024-01-01 12:00:00,123 - LEVEL - MESSAGE
            parts = log_line.split(' - ')
            if len(parts) >= 2:
                return parts[0]
        except:
            pass
        return datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
    def _extract_level(self, log_line: str) -> str:
        """استخراج مستوى السجل"""
        try:
            parts = log_line.split(' - ')
            if len(parts) >= 2:
                return parts[1]
        except:
            pass
        return "INFO"

# إنشاء مثيل المراقب
monitor = SystemMonitor()

# إنشاء تطبيق Flask
app = Flask(__name__, template_folder='../templates', static_folder='../static')
app.secret_key = 'reddit_video_maker_dashboard_2024'

@app.route('/')
def dashboard():
    """الصفحة الرئيسية للوحة التحكم"""
    return render_template('dashboard.html')

@app.route('/api/stats')
def api_stats():
    """API للحصول على الإحصائيات"""
    try:
        system_info = monitor.get_system_info()
        stats = monitor.stats.copy()
        stats.update(system_info)
        return jsonify(stats)
    except Exception as e:
        logger.error(f"خطأ في API الإحصائيات: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/logs')
def api_logs():
    """API للحصول على السجلات"""
    try:
        log_type = request.args.get('type', 'all')
        limit = int(request.args.get('limit', 100))
        logs = monitor.get_recent_logs(log_type, limit)
        return jsonify(logs)
    except Exception as e:
        logger.error(f"خطأ في API السجلات: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/control/<action>')
def api_control(action):
    """API للتحكم في النظام"""
    try:
        if action == 'start':
            # بدء النظام
            monitor.stats['status'] = 'running'
            monitor.save_stats()
            return jsonify({'success': True, 'message': 'تم بدء النظام'})
            
        elif action == 'stop':
            # إيقاف النظام
            monitor.stats['status'] = 'stopped'
            monitor.save_stats()
            return jsonify({'success': True, 'message': 'تم إيقاف النظام'})
            
        elif action == 'restart':
            # إعادة تشغيل النظام
            monitor.stats['status'] = 'restarting'
            monitor.save_stats()
            return jsonify({'success': True, 'message': 'جاري إعادة التشغيل'})
            
        else:
            return jsonify({'error': 'إجراء غير معروف'}), 400
            
    except Exception as e:
        logger.error(f"خطأ في API التحكم: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/logs')
def logs_page():
    """صفحة السجلات"""
    return render_template('logs.html')

@app.route('/settings')
def settings_page():
    """صفحة الإعدادات"""
    return render_template('settings.html')

def run_dashboard(host='0.0.0.0', port=5000, debug=False):
    """تشغيل واجهة المراقبة"""
    try:
        logger.info(f"🌐 بدء تشغيل واجهة المراقبة على http://{host}:{port}")
        app.run(host=host, port=port, debug=debug, threaded=True)
    except Exception as e:
        logger.error(f"خطأ في تشغيل واجهة المراقبة: {e}")

def start_dashboard_thread():
    """بدء واجهة المراقبة في خيط منفصل"""
    dashboard_thread = threading.Thread(target=run_dashboard, daemon=True)
    dashboard_thread.start()
    return dashboard_thread

# دوال مساعدة للاستخدام من الوحدات الأخرى
def log_video_created(video_info: Dict[str, Any]):
    """تسجيل إنشاء فيديو جديد"""
    monitor.update_video_created(video_info)

def log_video_published(video_url: str):
    """تسجيل نشر فيديو"""
    monitor.update_video_published(video_url)

def log_error(error_info: Dict[str, Any]):
    """تسجيل خطأ"""
    monitor.update_error(error_info)

if __name__ == "__main__":
    # تشغيل واجهة المراقبة للاختبار
    run_dashboard(debug=True)
