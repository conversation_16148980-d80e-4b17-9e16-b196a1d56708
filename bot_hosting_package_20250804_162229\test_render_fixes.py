#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاحات الشبكة لبيئة Render
"""

import os
import sys
import logging
import asyncio
from pathlib import Path

# إعداد نظام السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger(__name__)

def test_environment_detection():
    """اختبار كشف البيئة"""
    logger.info("🔍 اختبار كشف البيئة...")
    
    # محاكاة بيئة Render
    os.environ['RENDER'] = 'true'
    
    try:
        from render_network_fix import RenderNetworkFixer
        fixer = RenderNetworkFixer()
        
        if fixer.is_cloud_environment:
            logger.info("✅ تم كشف البيئة السحابية بنجاح")
            return True
        else:
            logger.error("❌ فشل في كشف البيئة السحابية")
            return False
            
    except Exception as e:
        logger.error(f"❌ خطأ في اختبار كشف البيئة: {e}")
        return False

def test_network_fixes():
    """اختبار إصلاحات الشبكة"""
    logger.info("🔍 اختبار إصلاحات الشبكة...")
    
    try:
        from render_network_fix import RenderNetworkFixer
        fixer = RenderNetworkFixer()
        
        # تطبيق الإصلاحات
        result = fixer.apply_network_fixes()
        
        if result:
            logger.info("✅ تم تطبيق إصلاحات الشبكة بنجاح")
            return True
        else:
            logger.error("❌ فشل في تطبيق إصلاحات الشبكة")
            return False
            
    except Exception as e:
        logger.error(f"❌ خطأ في اختبار إصلاحات الشبكة: {e}")
        return False

def test_connectivity_check():
    """اختبار فحص الاتصال"""
    logger.info("🔍 اختبار فحص الاتصال...")
    
    try:
        from render_network_fix import RenderNetworkFixer
        fixer = RenderNetworkFixer()
        
        # فحص الاتصال الأساسي
        basic_ok = fixer.check_basic_connectivity()
        
        # فحص اتصال Telegram
        telegram_ok = fixer.check_telegram_connectivity()
        
        if basic_ok:
            logger.info("✅ فحص الاتصال الأساسي نجح")
        else:
            logger.warning("⚠️ فحص الاتصال الأساسي فشل")
        
        if telegram_ok:
            logger.info("✅ فحص اتصال Telegram نجح")
        else:
            logger.warning("⚠️ فحص اتصال Telegram فشل")
        
        # نعتبر الاختبار ناجحاً إذا نجح أحد الفحوصات على الأقل
        return basic_ok or telegram_ok
        
    except Exception as e:
        logger.error(f"❌ خطأ في اختبار فحص الاتصال: {e}")
        return False

def test_network_config_import():
    """اختبار استيراد network_config المحدث"""
    logger.info("🔍 اختبار استيراد network_config...")
    
    try:
        from network_config import (
            check_network_connectivity,
            check_telegram_connectivity,
            check_cloud_connectivity,
            check_telegram_cloud_connectivity
        )
        
        logger.info("✅ تم استيراد جميع الدوال المحدثة")
        
        # اختبار الدوال الجديدة
        try:
            # محاكاة بيئة سحابية
            os.environ['RENDER'] = 'true'
            
            # اختبار الدوال
            result1 = check_cloud_connectivity()
            result2 = check_telegram_cloud_connectivity()
            
            logger.info(f"✅ دالة check_cloud_connectivity: {'نجحت' if result1 else 'فشلت'}")
            logger.info(f"✅ دالة check_telegram_cloud_connectivity: {'نجحت' if result2 else 'فشلت'}")
            
            return True
            
        except Exception as e:
            logger.warning(f"⚠️ خطأ في اختبار الدوال الجديدة: {e}")
            return True  # الاستيراد نجح حتى لو فشلت الدوال
            
    except ImportError as e:
        logger.error(f"❌ فشل في استيراد network_config: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ خطأ في اختبار network_config: {e}")
        return False

def test_main_py_fixes():
    """اختبار إصلاحات main.py"""
    logger.info("🔍 اختبار إصلاحات main.py...")
    
    try:
        # محاولة استيراد الدوال المحدثة من main.py
        sys.path.insert(0, '.')
        
        # اختبار وجود الدوال المحدثة
        import main
        
        # التحقق من وجود دالة check_internet_connection
        if hasattr(main, 'check_internet_connection'):
            logger.info("✅ دالة check_internet_connection موجودة")
        else:
            logger.warning("⚠️ دالة check_internet_connection غير موجودة")
        
        # التحقق من وجود متغير NETWORK_CONFIG_ENABLED
        if hasattr(main, 'NETWORK_CONFIG_ENABLED'):
            logger.info("✅ متغير NETWORK_CONFIG_ENABLED موجود")
        else:
            logger.warning("⚠️ متغير NETWORK_CONFIG_ENABLED غير موجود")
        
        logger.info("✅ تم اختبار main.py بنجاح")
        return True
        
    except Exception as e:
        logger.error(f"❌ خطأ في اختبار main.py: {e}")
        return False

def test_file_structure():
    """اختبار بنية الملفات"""
    logger.info("🔍 اختبار بنية الملفات...")
    
    required_files = [
        'render_network_fix.py',
        'start_render.py',
        'network_config.py',
        'main.py',
        'Procfile',
        'render.yaml',
        'requirements.txt'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        logger.error(f"❌ ملفات مفقودة: {', '.join(missing_files)}")
        return False
    else:
        logger.info("✅ جميع الملفات المطلوبة موجودة")
        return True

async def run_all_tests():
    """تشغيل جميع الاختبارات"""
    logger.info("🚀 بدء تشغيل جميع الاختبارات")
    logger.info("=" * 50)
    
    tests = [
        ("بنية الملفات", test_file_structure),
        ("كشف البيئة", test_environment_detection),
        ("إصلاحات الشبكة", test_network_fixes),
        ("فحص الاتصال", test_connectivity_check),
        ("استيراد network_config", test_network_config_import),
        ("إصلاحات main.py", test_main_py_fixes),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n🔍 تشغيل اختبار: {test_name}")
        try:
            result = test_func()
            results[test_name] = result
            status = "✅ نجح" if result else "❌ فشل"
            logger.info(f"   النتيجة: {status}")
        except Exception as e:
            results[test_name] = False
            logger.error(f"   ❌ خطأ: {e}")
    
    # عرض النتائج النهائية
    logger.info("\n" + "=" * 50)
    logger.info("📊 ملخص النتائج:")
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅" if result else "❌"
        logger.info(f"   {status} {test_name}")
        if result:
            passed += 1
    
    logger.info(f"\n🎯 النتيجة النهائية: {passed}/{total} اختبارات نجحت")
    
    if passed == total:
        logger.info("🎉 جميع الاختبارات نجحت! الإصلاحات جاهزة للاستخدام")
        return True
    elif passed >= total * 0.7:  # 70% نجاح
        logger.info("✅ معظم الاختبارات نجحت، الإصلاحات قابلة للاستخدام")
        return True
    else:
        logger.warning("⚠️ بعض الاختبارات فشلت، راجع الأخطاء أعلاه")
        return False

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار إصلاحات الشبكة لبيئة Render")
    print("=" * 50)
    
    try:
        result = asyncio.run(run_all_tests())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        logger.info("⏹️ تم إيقاف الاختبارات")
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ خطأ في تشغيل الاختبارات: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
