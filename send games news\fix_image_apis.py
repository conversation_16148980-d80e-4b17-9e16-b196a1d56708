#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة إصلاح وتحسين APIs الصور
"""

import asyncio
import sys
import os
import aiohttp
import json
from datetime import datetime

# إضافة المسار الحالي لـ Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.logger import logger
from config.settings import BotConfig

class ImageAPIFixer:
    """أداة إصلاح وتحسين APIs الصور"""
    
    def __init__(self):
        self.config = BotConfig()
        
    async def test_freepik_connection(self):
        """اختبار اتصال Freepik API"""
        print("🔍 اختبار اتصال Freepik API...")
        
        try:
            headers = {
                'x-freepik-api-key': self.config.FREEPIK_API_KEY,
                'Content-Type': 'application/json'
            }
            
            # اختبار endpoint بسيط
            test_url = "https://api.freepik.com/v1/ai/text-to-image"
            
            async with aiohttp.ClientSession() as session:
                # اختبار POST request بسيط
                test_data = {
                    "prompt": "test image",
                    "num_images": 1
                }
                
                async with session.post(test_url, headers=headers, json=test_data, timeout=30) as response:
                    print(f"   📊 كود الاستجابة: {response.status}")
                    
                    if response.status == 200:
                        print("   ✅ Freepik API يعمل بشكل صحيح")
                        return True
                    elif response.status == 401:
                        print("   ❌ مشكلة في مفتاح API")
                        return False
                    elif response.status == 429:
                        print("   ⚠️ تم تجاوز حد الطلبات")
                        return False
                    else:
                        response_text = await response.text()
                        print(f"   ⚠️ استجابة غير متوقعة: {response_text[:200]}...")
                        return False
                        
        except aiohttp.ClientConnectorError as e:
            print(f"   ❌ خطأ في الاتصال: {e}")
            return False
        except asyncio.TimeoutError:
            print("   ⏰ انتهت مهلة الاتصال")
            return False
        except Exception as e:
            print(f"   ❌ خطأ غير متوقع: {e}")
            return False
    
    async def test_fluxai_connection(self):
        """اختبار اتصال FluxAI API"""
        print("\n🔍 اختبار اتصال FluxAI API...")
        
        try:
            # اختبار DNS resolution أولاً
            import socket
            try:
                socket.gethostbyname('api.fluxai.art')
                print("   ✅ DNS resolution يعمل")
            except socket.gaierror:
                print("   ❌ فشل DNS resolution - المضيف غير موجود")
                return False
            
            headers = {
                'Authorization': f'Bearer {self.config.FLUXAI_API_KEY}',
                'Content-Type': 'application/json'
            }
            
            test_url = "https://api.fluxai.art/v1/images/generations"
            
            async with aiohttp.ClientSession() as session:
                test_data = {
                    "prompt": "test image",
                    "model": "flux-pro",
                    "width": 512,
                    "height": 512
                }
                
                async with session.post(test_url, headers=headers, json=test_data, timeout=30) as response:
                    print(f"   📊 كود الاستجابة: {response.status}")
                    
                    if response.status == 200:
                        print("   ✅ FluxAI API يعمل بشكل صحيح")
                        return True
                    elif response.status == 401:
                        print("   ❌ مشكلة في مفتاح API")
                        return False
                    elif response.status == 429:
                        print("   ⚠️ تم تجاوز حد الطلبات")
                        return False
                    else:
                        response_text = await response.text()
                        print(f"   ⚠️ استجابة غير متوقعة: {response_text[:200]}...")
                        return False
                        
        except aiohttp.ClientConnectorError as e:
            print(f"   ❌ خطأ في الاتصال: {e}")
            print("   💡 تحقق من الاتصال بالإنترنت أو حالة الخدمة")
            return False
        except asyncio.TimeoutError:
            print("   ⏰ انتهت مهلة الاتصال")
            return False
        except Exception as e:
            print(f"   ❌ خطأ غير متوقع: {e}")
            return False
    
    def check_api_keys(self):
        """فحص مفاتيح APIs"""
        print("🔑 فحص مفاتيح APIs...")
        
        issues = []
        
        # فحص Freepik API key
        if not self.config.FREEPIK_API_KEY:
            issues.append("❌ مفتاح Freepik API غير موجود")
        elif len(self.config.FREEPIK_API_KEY) < 20:
            issues.append("⚠️ مفتاح Freepik API قصير جداً")
        else:
            print(f"   ✅ Freepik API Key: {self.config.FREEPIK_API_KEY[:10]}...")
        
        # فحص FluxAI API key
        if not self.config.FLUXAI_API_KEY:
            issues.append("❌ مفتاح FluxAI API غير موجود")
        elif len(self.config.FLUXAI_API_KEY) < 20:
            issues.append("⚠️ مفتاح FluxAI API قصير جداً")
        else:
            print(f"   ✅ FluxAI API Key: {self.config.FLUXAI_API_KEY[:10]}...")
        
        if issues:
            print("\n⚠️ مشاكل في المفاتيح:")
            for issue in issues:
                print(f"   {issue}")
            return False
        
        return True
    
    async def suggest_freepik_improvements(self):
        """اقتراح تحسينات لـ Freepik"""
        print("\n💡 تحسينات مقترحة لـ Freepik:")
        print("-" * 35)
        
        suggestions = [
            "🔧 زيادة timeout إلى 45 ثانية",
            "🔄 إضافة retry mechanism مع backoff",
            "📊 تحسين polling للحصول على النتائج",
            "⏰ إضافة webhook للحصول على نتائج فورية",
            "🗄️ تخزين مؤقت للنتائج الناجحة"
        ]
        
        for suggestion in suggestions:
            print(f"   {suggestion}")
    
    async def suggest_fluxai_improvements(self):
        """اقتراح تحسينات لـ FluxAI"""
        print("\n💡 تحسينات مقترحة لـ FluxAI:")
        print("-" * 35)
        
        suggestions = [
            "🌐 فحص DNS وحالة الخدمة",
            "🔄 إضافة fallback endpoints",
            "⏰ تحسين timeout settings",
            "🛡️ إضافة error handling محسن",
            "📊 مراقبة حالة الخدمة"
        ]
        
        for suggestion in suggestions:
            print(f"   {suggestion}")
    
    def create_improved_config(self):
        """إنشاء تكوين محسن"""
        print("\n⚙️ إنشاء تكوين محسن...")
        
        improved_config = {
            "image_generation": {
                "freepik": {
                    "timeout": 45,
                    "max_retries": 3,
                    "retry_delay": 5,
                    "polling_interval": 3,
                    "max_polling_time": 60
                },
                "fluxai": {
                    "timeout": 30,
                    "max_retries": 2,
                    "retry_delay": 3,
                    "fallback_endpoints": [
                        "https://api.fluxai.art/v1",
                        "https://backup.fluxai.art/v1"
                    ]
                },
                "fallback": {
                    "enabled": True,
                    "high_quality_sources": [
                        "unsplash",
                        "pixabay",
                        "pexels"
                    ],
                    "cache_duration": 24
                }
            },
            "monitoring": {
                "enabled": True,
                "log_api_calls": True,
                "track_success_rates": True,
                "alert_on_failures": True
            }
        }
        
        # حفظ التكوين المحسن
        with open('improved_image_config.json', 'w', encoding='utf-8') as f:
            json.dump(improved_config, f, indent=2, ensure_ascii=False)
        
        print("✅ تم إنشاء التكوين المحسن: improved_image_config.json")
    
    async def run_comprehensive_test(self):
        """تشغيل اختبار شامل"""
        print("🧪 تشغيل اختبار شامل لـ APIs الصور...")
        print("=" * 45)
        
        # فحص المفاتيح
        keys_ok = self.check_api_keys()
        
        if not keys_ok:
            print("\n❌ لا يمكن المتابعة بدون مفاتيح صحيحة")
            return False
        
        # اختبار الاتصالات
        freepik_ok = await self.test_freepik_connection()
        fluxai_ok = await self.test_fluxai_connection()
        
        # عرض النتائج
        print("\n" + "=" * 45)
        print("📊 ملخص نتائج الاختبار:")
        print("-" * 25)
        
        print(f"🔑 مفاتيح APIs: {'✅ صحيحة' if keys_ok else '❌ مشاكل'}")
        print(f"🎨 Freepik API: {'✅ يعمل' if freepik_ok else '❌ مشاكل'}")
        print(f"🖼️ FluxAI API: {'✅ يعمل' if fluxai_ok else '❌ مشاكل'}")
        
        # اقتراح التحسينات
        if not freepik_ok:
            await self.suggest_freepik_improvements()
        
        if not fluxai_ok:
            await self.suggest_fluxai_improvements()
        
        # إنشاء تكوين محسن
        self.create_improved_config()
        
        # تقييم عام
        if freepik_ok or fluxai_ok:
            print(f"\n✅ على الأقل API واحد يعمل - النظام الاحتياطي سيعمل")
            return True
        else:
            print(f"\n⚠️ جميع APIs لا تعمل - سيتم الاعتماد على الصور الاحتياطية")
            return False

async def main():
    """الدالة الرئيسية"""
    print("🛠️ أداة إصلاح وتحسين APIs الصور")
    print("=" * 40)
    
    fixer = ImageAPIFixer()
    
    try:
        # تشغيل الاختبار الشامل
        success = await fixer.run_comprehensive_test()
        
        print("\n" + "=" * 40)
        if success:
            print("🎉 تم إنجاز التشخيص والإصلاح بنجاح!")
            print("\n📋 الخطوات التالية:")
            print("   1. مراجعة improved_image_config.json")
            print("   2. تطبيق التحسينات المقترحة")
            print("   3. مراقبة الأداء باستخدام monitor_image_apis_live.py")
        else:
            print("⚠️ تم العثور على مشاكل تحتاج إصلاح")
            print("\n📋 الخطوات المطلوبة:")
            print("   1. إصلاح مفاتيح APIs")
            print("   2. فحص الاتصال بالإنترنت")
            print("   3. التواصل مع مقدمي الخدمة")
        
        return success
        
    except Exception as e:
        print(f"\n❌ خطأ في الإصلاح: {e}")
        return False

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الإصلاح بواسطة المستخدم")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        sys.exit(1)
