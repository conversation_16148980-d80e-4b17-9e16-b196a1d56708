# وحدة إدارة مفاتيح API الديناميكية المحسنة
import random
import time
from datetime import datetime, timedelta
from typing import List, Dict, Optional
from .logger import logger

class ApiKeyManager:
    """
    يدير مجموعة من مفاتيح API لخدمة معينة، ويدعم التبديل التلقائي عند الفشل
    مع ميزات متقدمة لتوزيع الحمولة وإعادة التفعيل التلقائي والتوفير الذكي.
    """
    def __init__(self, api_keys: List[str], service_name: str,
                 auto_recovery_minutes: int = 60,
                 load_balancing: bool = True,
                 daily_limit_per_key: int = None,
                 rate_limit_per_minute: int = None):
        """
        تهيئة مدير المفاتيح المحسن.
        :param api_keys: قائمة بمفاتيح API.
        :param service_name: اسم الخدمة (للتسجيل).
        :param auto_recovery_minutes: دقائق إعادة تفعيل المفاتيح المعطلة.
        :param load_balancing: تفعيل توزيع الحمولة.
        :param daily_limit_per_key: الحد الأقصى اليومي لكل مفتاح.
        :param rate_limit_per_minute: الحد الأقصى للطلبات في الدقيقة.
        """
        if not isinstance(api_keys, list) or not api_keys:
            raise ValueError("api_keys must be a non-empty list.")

        self.service_name = service_name
        self.keys = api_keys
        self.current_key_index = 0
        self.blacklisted_keys = {}  # {key: blacklist_time}
        self.auto_recovery_minutes = auto_recovery_minutes
        self.load_balancing = load_balancing
        self.daily_limit_per_key = daily_limit_per_key
        self.rate_limit_per_minute = rate_limit_per_minute

        # إحصائيات محسنة
        self.usage_stats = {
            key: {
                'calls': 0,
                'failures': 0,
                'last_used': None,
                'daily_calls': 0,
                'daily_reset_date': datetime.now().date(),
                'minute_calls': [],  # قائمة بأوقات الاستدعاءات في الدقيقة الأخيرة
                'success_rate': 100.0
            } for key in api_keys
        }
        self.total_calls = 0

        # إعدادات التوفير الذكي
        self.smart_conservation = {
            'enabled': True,
            'priority_threshold': 0.8,  # عندما تصل نسبة الاستخدام لـ 80%
            'emergency_threshold': 0.95,  # عندما تصل لـ 95%
            'conservation_mode': False
        }

        logger.info(f"🔑 تم تهيئة مدير المفاتيح المحسن لخدمة '{self.service_name}' مع {len(self.keys)} مفتاح.")

    def get_key(self) -> str:
        """
        الحصول على مفتاح API الصالح الحالي مع التحقق من الحدود والتوفير الذكي.
        :return: مفتاح API كسلسلة نصية.
        """
        # إعادة تفعيل المفاتيح المعطلة إذا انتهت مدة العقوبة
        self._auto_recover_keys()

        # تحديث إحصائيات اليوم
        self._update_daily_stats()

        # فحص وضع التوفير الذكي
        self._check_conservation_mode()

        # إذا كان توزيع الحمولة مفعل، اختر أفضل مفتاح
        if self.load_balancing:
            best_key = self._get_best_key_for_load_balancing()
            if best_key:
                self.current_key_index = self.keys.index(best_key)

        if self.is_all_blacklisted():
            logger.critical(f"🚨 جميع مفاتيح API لخدمة '{self.service_name}' تم إدراجها في القائمة السوداء!")
            raise Exception(f"No valid API keys available for {self.service_name}")

        current_key = self.keys[self.current_key_index]

        # فحص الحدود قبل الاستخدام
        if not self._can_use_key(current_key):
            # البحث عن مفتاح بديل
            alternative_key = self._find_alternative_key()
            if alternative_key:
                current_key = alternative_key
                self.current_key_index = self.keys.index(current_key)
            else:
                if self.smart_conservation['conservation_mode']:
                    logger.warning(f"⚠️ وضع التوفير الطارئ: تأجيل الطلب لخدمة '{self.service_name}'")
                    raise Exception(f"Conservation mode active for {self.service_name} - request delayed")
                else:
                    logger.warning(f"⚠️ تم الوصول للحدود اليومية لجميع مفاتيح '{self.service_name}'")

        # تحديث إحصائيات الاستخدام
        self._update_key_usage(current_key)

        return current_key

    def get_api_key(self) -> str:
        """
        طريقة بديلة للتوافق مع الكود الموجود.
        :return: مفتاح API كسلسلة نصية.
        """
        return self.get_key()

    def rotate_key(self) -> str:
        """
        تبديل إلى المفتاح التالي في القائمة ووضع المفتاح الحالي في القائمة السوداء.
        :return: مفتاح API الجديد.
        """
        # الحصول على المفتاح الحالي قبل التبديل
        current_key = self.keys[self.current_key_index]

        # إضافة المفتاح الحالي إلى القائمة السوداء مع الوقت
        self.blacklisted_keys[current_key] = datetime.now()
        self.usage_stats[current_key]['failures'] += 1

        logger.warning(f"🚫 تم وضع المفتاح '{current_key[:4]}...{current_key[-4:]}' في القائمة السوداء لخدمة '{self.service_name}'.")

        # إعادة تفعيل المفاتيح المعطلة إذا انتهت مدة العقوبة
        self._auto_recover_keys()

        # التحقق مما إذا كانت جميع المفاتيح قد استنفدت
        if self.is_all_blacklisted():
            logger.error(f"❌ استنفدت جميع مفاتيح API لخدمة '{self.service_name}'.")
            self._send_exhaustion_alert()
            raise Exception(f"All API keys for {self.service_name} are blacklisted.")

        # البحث عن المفتاح الصالح التالي
        self.current_key_index = (self.current_key_index + 1) % len(self.keys)
        while self.keys[self.current_key_index] in self.blacklisted_keys:
            self.current_key_index = (self.current_key_index + 1) % len(self.keys)

        new_key = self.keys[self.current_key_index]
        logger.info(f"🔄 التبديل إلى مفتاح API جديد لخدمة '{self.service_name}': '{new_key[:4]}...{new_key[-4:]}'")
        return new_key

    def is_all_blacklisted(self) -> bool:
        """التحقق مما إذا كانت جميع المفاتيح في القائمة السوداء."""
        return len(self.blacklisted_keys) == len(self.keys)

    def _auto_recover_keys(self):
        """إعادة تفعيل المفاتيح المعطلة تلقائياً بعد انتهاء مدة العقوبة"""
        current_time = datetime.now()
        keys_to_recover = []

        for key, blacklist_time in self.blacklisted_keys.items():
            if current_time - blacklist_time >= timedelta(minutes=self.auto_recovery_minutes):
                keys_to_recover.append(key)

        for key in keys_to_recover:
            del self.blacklisted_keys[key]
            logger.info(f"🔓 تم إعادة تفعيل المفتاح '{key[:4]}...{key[-4:]}' لخدمة '{self.service_name}' تلقائياً")

    def _get_best_key_for_load_balancing(self) -> Optional[str]:
        """اختيار أفضل مفتاح لتوزيع الحمولة مع مراعاة الحدود"""
        available_keys = [key for key in self.keys if key not in self.blacklisted_keys and self._can_use_key(key)]

        if not available_keys:
            return None

        # اختيار المفتاح الأقل استخداماً مع أفضل معدل نجاح
        best_key = min(available_keys, key=lambda k: (
            self.usage_stats[k]['daily_calls'],
            -self.usage_stats[k]['success_rate']  # سالب لترتيب تنازلي
        ))
        return best_key

    def _can_use_key(self, key: str) -> bool:
        """فحص ما إذا كان يمكن استخدام المفتاح"""
        if key in self.blacklisted_keys:
            return False

        stats = self.usage_stats[key]

        # فحص الحد اليومي
        if self.daily_limit_per_key and stats['daily_calls'] >= self.daily_limit_per_key:
            return False

        # فحص حد الدقيقة
        if self.rate_limit_per_minute:
            current_time = datetime.now()
            minute_calls = [call_time for call_time in stats['minute_calls']
                          if current_time - call_time < timedelta(minutes=1)]
            if len(minute_calls) >= self.rate_limit_per_minute:
                return False

        return True

    def _find_alternative_key(self) -> Optional[str]:
        """البحث عن مفتاح بديل متاح"""
        for key in self.keys:
            if self._can_use_key(key):
                return key
        return None

    def _update_daily_stats(self):
        """تحديث إحصائيات اليوم"""
        current_date = datetime.now().date()

        for key in self.keys:
            stats = self.usage_stats[key]
            if stats['daily_reset_date'] != current_date:
                stats['daily_calls'] = 0
                stats['daily_reset_date'] = current_date

    def _update_key_usage(self, key: str):
        """تحديث إحصائيات استخدام المفتاح"""
        current_time = datetime.now()
        stats = self.usage_stats[key]

        # تحديث الإحصائيات العامة
        stats['calls'] += 1
        stats['daily_calls'] += 1
        stats['last_used'] = current_time
        self.total_calls += 1

        # تحديث قائمة استدعاءات الدقيقة
        stats['minute_calls'].append(current_time)
        # إزالة الاستدعاءات القديمة (أكثر من دقيقة)
        stats['minute_calls'] = [call_time for call_time in stats['minute_calls']
                               if current_time - call_time < timedelta(minutes=1)]

    def _check_conservation_mode(self):
        """فحص وتفعيل وضع التوفير الذكي"""
        if not self.smart_conservation['enabled']:
            return

        total_daily_usage = sum(stats['daily_calls'] for stats in self.usage_stats.values())
        total_daily_limit = (self.daily_limit_per_key or 1000) * len(self.keys)

        usage_ratio = total_daily_usage / total_daily_limit if total_daily_limit > 0 else 0

        if usage_ratio >= self.smart_conservation['emergency_threshold']:
            if not self.smart_conservation['conservation_mode']:
                self.smart_conservation['conservation_mode'] = True
                logger.warning(f"🚨 تم تفعيل وضع التوفير الطارئ لخدمة '{self.service_name}' - استخدام: {usage_ratio:.1%}")
        elif usage_ratio >= self.smart_conservation['priority_threshold']:
            if not self.smart_conservation['conservation_mode']:
                logger.warning(f"⚠️ تحذير: اقتراب من حد التوفير لخدمة '{self.service_name}' - استخدام: {usage_ratio:.1%}")
        else:
            if self.smart_conservation['conservation_mode']:
                self.smart_conservation['conservation_mode'] = False
                logger.info(f"✅ تم إلغاء وضع التوفير لخدمة '{self.service_name}' - استخدام: {usage_ratio:.1%}")

    def _send_exhaustion_alert(self):
        """إرسال تنبيه عند استنفاد جميع المفاتيح"""
        logger.critical(f"🚨 تنبيه: تم استنفاد جميع مفاتيح API لخدمة '{self.service_name}'!")
        logger.critical(f"📊 إحصائيات الاستخدام:")

        for key, stats in self.usage_stats.items():
            masked_key = f"{key[:4]}...{key[-4:]}"
            logger.critical(f"   {masked_key}: {stats['calls']} استدعاء، {stats['failures']} فشل")

    def get_usage_stats(self) -> Dict:
        """الحصول على إحصائيات الاستخدام المحسنة"""
        total_daily_usage = sum(stats['daily_calls'] for stats in self.usage_stats.values())
        total_daily_limit = (self.daily_limit_per_key or 1000) * len(self.keys)
        usage_ratio = total_daily_usage / total_daily_limit if total_daily_limit > 0 else 0

        stats = {
            'service_name': self.service_name,
            'total_keys': len(self.keys),
            'blacklisted_keys': len(self.blacklisted_keys),
            'available_keys': len(self.keys) - len(self.blacklisted_keys),
            'total_calls': self.total_calls,
            'daily_usage': {
                'total_calls': total_daily_usage,
                'total_limit': total_daily_limit,
                'usage_ratio': usage_ratio,
                'conservation_mode': self.smart_conservation['conservation_mode']
            },
            'limits': {
                'daily_limit_per_key': self.daily_limit_per_key,
                'rate_limit_per_minute': self.rate_limit_per_minute
            },
            'key_stats': {}
        }

        for key, key_stats in self.usage_stats.items():
            masked_key = f"{key[:4]}...{key[-4:]}"

            # حساب معدل النجاح
            total_attempts = key_stats['calls'] + key_stats['failures']
            success_rate = (key_stats['calls'] / total_attempts * 100) if total_attempts > 0 else 100

            stats['key_stats'][masked_key] = {
                'calls': key_stats['calls'],
                'daily_calls': key_stats['daily_calls'],
                'failures': key_stats['failures'],
                'success_rate': round(success_rate, 1),
                'last_used': key_stats['last_used'].isoformat() if key_stats['last_used'] else None,
                'is_blacklisted': key in self.blacklisted_keys,
                'can_use': self._can_use_key(key),
                'minute_calls_count': len(key_stats['minute_calls'])
            }

        return stats

    def mark_key_failed(self, key: str):
        """وضع مفتاح معين في القائمة السوداء"""
        if key in self.keys:
            self.blacklisted_keys[key] = datetime.now()
            self.usage_stats[key]['failures'] += 1
            logger.warning(f"🚫 تم وضع المفتاح '{key[:4]}...{key[-4:]}' في القائمة السوداء لخدمة '{self.service_name}'")
        else:
            logger.warning(f"⚠️ محاولة وضع مفتاح غير موجود في القائمة السوداء: {key[:4]}...")

    def get_available_keys_count(self) -> int:
        """الحصول على عدد المفاتيح المتاحة (غير المعطلة)"""
        return len(self.keys) - len(self.blacklisted_keys)

    def reset_key_failures(self, key: str = None):
        """إعادة تعيين فشل مفتاح معين أو جميع المفاتيح"""
        if key:
            if key in self.blacklisted_keys:
                del self.blacklisted_keys[key]
                logger.info(f"🔓 تم إعادة تفعيل المفتاح '{key[:4]}...{key[-4:]}' يدوياً")
        else:
            self.blacklisted_keys.clear()
            logger.info(f"🔓 تم إعادة تفعيل جميع مفاتيح '{self.service_name}' يدوياً")

# يمكن إنشاء مثيلات مركزية هنا إذا لزم الأمر
# google_api_manager = ApiKeyManager(...)
