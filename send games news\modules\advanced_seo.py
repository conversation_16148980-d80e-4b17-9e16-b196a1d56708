# نظام تحسين محركات البحث المتقدم
import re
import json
import requests
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import sqlite3
from urllib.parse import quote_plus
import hashlib
from .logger import logger
from .database import db

class AdvancedSEO:
    """نظام تحسين محركات البحث المتقدم والذكي"""
    
    def __init__(self):
        self.serp_api_key = None  # مفتاح SERP API
        self.semrush_api_key = None  # مفتاح SEMrush
        self.ahrefs_api_key = None  # مفتاح Ahrefs
        self.schema_templates = self._load_schema_templates()
        self.ranking_factors = self._load_ranking_factors()
        self.competitor_domains = self._load_competitor_domains()
        
    def _load_schema_templates(self) -> Dict:
        """تحميل قوالب Schema Markup"""
        return {
            'article': {
                "@context": "https://schema.org",
                "@type": "Article",
                "headline": "",
                "description": "",
                "image": "",
                "author": {
                    "@type": "Person",
                    "name": "فريق التحرير"
                },
                "publisher": {
                    "@type": "Organization",
                    "name": "موقع أخبار الألعاب",
                    "logo": {
                        "@type": "ImageObject",
                        "url": ""
                    }
                },
                "datePublished": "",
                "dateModified": ""
            },
            'game_review': {
                "@context": "https://schema.org",
                "@type": "Review",
                "itemReviewed": {
                    "@type": "VideoGame",
                    "name": "",
                    "genre": "",
                    "platform": ""
                },
                "reviewRating": {
                    "@type": "Rating",
                    "ratingValue": "",
                    "bestRating": "10"
                },
                "author": {
                    "@type": "Person",
                    "name": "مراجع الألعاب"
                }
            },
            'faq': {
                "@context": "https://schema.org",
                "@type": "FAQPage",
                "mainEntity": []
            }
        }
    
    def _load_ranking_factors(self) -> Dict:
        """تحميل عوامل الترتيب في محركات البحث - محدث 2025"""
        return {
            'content_quality': {
                'weight': 0.30,  # زيادة الوزن بناءً على تحديثات Google 2024-2025
                'factors': [
                    'helpful_content_score',  # تركيز Google الجديد على المحتوى المفيد
                    'expertise_authority_trust',  # E-A-T المحدث
                    'word_count_depth',
                    'readability_score',
                    'content_freshness',
                    'semantic_richness',  # التنوع الدلالي
                    'user_intent_match',
                    'original_research_data'  # المحتوى الأصلي والبحثي
                ]
            },
            'technical_seo': {
                'weight': 0.25,  # زيادة الأهمية للجوانب التقنية
                'factors': [
                    'core_web_vitals',  # LCP, FID, CLS
                    'page_speed_mobile',
                    'mobile_first_indexing',
                    'schema_markup_advanced',
                    'internal_linking_strategy',
                    'url_structure_seo',
                    'https_security',
                    'crawlability_indexability',
                    'structured_data_quality'
                ]
            },
            'user_experience': {
                'weight': 0.20,
                'factors': [
                    'bounce_rate_optimized',
                    'dwell_time_engagement',
                    'click_through_rate_serp',
                    'page_experience_signals',
                    'mobile_usability',
                    'accessibility_compliance',
                    'interactive_elements',
                    'visual_stability'
                ]
            },
            'authority_trust': {
                'weight': 0.15,
                'factors': [
                    'domain_authority_score',
                    'backlink_quality_profile',
                    'social_signals_engagement',
                    'brand_mentions_citations',
                    'author_expertise',
                    'site_reputation',
                    'trust_signals'
                ]
            },
            'search_intent_relevance': {
                'weight': 0.10,
                'factors': [
                    'keyword_semantic_match',
                    'topic_cluster_relevance',
                    'search_intent_alignment',
                    'featured_snippet_optimization',
                    'voice_search_compatibility',
                    'local_search_relevance'
                ]
            }
        }
    
    def _load_competitor_domains(self) -> List[str]:
        """تحميل نطاقات المنافسين"""
        return [
            'gamespot.com',
            'ign.com',
            'polygon.com',
            'kotaku.com',
            'eurogamer.net',
            'pcgamer.com',
            'gamesradar.com'
        ]
    
    async def analyze_keyword_opportunities(self, base_keywords: List[str]) -> Dict:
        """تحليل فرص الكلمات المفتاحية"""
        try:
            opportunities = {
                'high_potential_keywords': [],
                'long_tail_opportunities': [],
                'competitor_gaps': [],
                'trending_keywords': [],
                'semantic_keywords': []
            }
            
            for keyword in base_keywords:
                # تحليل الكلمة المفتاحية
                keyword_analysis = await self._analyze_single_keyword(keyword)
                
                # تصنيف الفرص
                if keyword_analysis['difficulty'] < 30 and keyword_analysis['volume'] > 1000:
                    opportunities['high_potential_keywords'].append(keyword_analysis)
                
                # البحث عن كلمات مفتاحية طويلة الذيل
                long_tail = await self._find_long_tail_keywords(keyword)
                opportunities['long_tail_opportunities'].extend(long_tail)
                
                # البحث عن كلمات مفتاحية دلالية
                semantic = await self._find_semantic_keywords(keyword)
                opportunities['semantic_keywords'].extend(semantic)
            
            # تحليل فجوات المنافسين
            competitor_gaps = await self._analyze_competitor_gaps(base_keywords)
            opportunities['competitor_gaps'] = competitor_gaps
            
            # الكلمات المفتاحية الرائجة
            trending = await self._find_trending_keywords()
            opportunities['trending_keywords'] = trending
            
            logger.info(f"🔍 تم تحليل {len(base_keywords)} كلمة مفتاحية")
            
            return opportunities
            
        except Exception as e:
            logger.error("❌ فشل في تحليل فرص الكلمات المفتاحية", e)
            return {}
    
    async def _analyze_single_keyword(self, keyword: str) -> Dict:
        """تحليل كلمة مفتاحية واحدة"""
        try:
            # محاكاة تحليل الكلمة المفتاحية
            # في التطبيق الحقيقي، سيتم استخدام APIs مثل SEMrush أو Ahrefs
            
            analysis = {
                'keyword': keyword,
                'volume': self._estimate_search_volume(keyword),
                'difficulty': self._estimate_keyword_difficulty(keyword),
                'cpc': self._estimate_cpc(keyword),
                'competition': self._analyze_competition(keyword),
                'intent': self._analyze_search_intent(keyword),
                'seasonality': self._analyze_seasonality(keyword)
            }
            
            return analysis
            
        except Exception as e:
            logger.error(f"❌ فشل في تحليل الكلمة المفتاحية: {keyword}", e)
            return {}
    
    def _estimate_search_volume(self, keyword: str) -> int:
        """تقدير حجم البحث"""
        # محاكاة بناءً على طول الكلمة ونوعها
        base_volume = 1000
        
        if 'game' in keyword.lower():
            base_volume *= 2
        if 'review' in keyword.lower():
            base_volume *= 1.5
        if 'guide' in keyword.lower():
            base_volume *= 1.3
        
        # تقليل الحجم للكلمات الطويلة
        word_count = len(keyword.split())
        if word_count > 3:
            base_volume = int(base_volume / word_count)
        
        return max(100, base_volume)
    
    def _estimate_keyword_difficulty(self, keyword: str) -> int:
        """تقدير صعوبة الكلمة المفتاحية"""
        # محاكاة بناءً على عوامل مختلفة
        difficulty = 50  # متوسط
        
        if len(keyword.split()) == 1:
            difficulty += 20  # الكلمات المفردة أصعب
        
        if 'best' in keyword.lower() or 'top' in keyword.lower():
            difficulty += 15  # كلمات تنافسية
        
        if len(keyword) > 20:
            difficulty -= 10  # الكلمات الطويلة أسهل
        
        return max(10, min(90, difficulty))
    
    def _estimate_cpc(self, keyword: str) -> float:
        """تقدير تكلفة النقرة"""
        base_cpc = 0.5
        
        if 'gaming' in keyword.lower():
            base_cpc *= 1.5
        if 'review' in keyword.lower():
            base_cpc *= 1.2
        
        return round(base_cpc, 2)
    
    def _analyze_competition(self, keyword: str) -> Dict:
        """تحليل المنافسة"""
        return {
            'level': 'medium',
            'top_competitors': ['gamespot.com', 'ign.com'],
            'content_gaps': ['detailed guides', 'video content']
        }
    
    def _analyze_search_intent(self, keyword: str) -> str:
        """تحليل نية البحث"""
        keyword_lower = keyword.lower()
        
        if any(word in keyword_lower for word in ['how', 'guide', 'tutorial', 'كيف']):
            return 'informational'
        elif any(word in keyword_lower for word in ['best', 'top', 'review', 'أفضل']):
            return 'commercial'
        elif any(word in keyword_lower for word in ['buy', 'download', 'شراء']):
            return 'transactional'
        else:
            return 'navigational'
    
    def _analyze_seasonality(self, keyword: str) -> Dict:
        """تحليل الموسمية"""
        return {
            'is_seasonal': False,
            'peak_months': [],
            'trend_direction': 'stable'
        }
    
    async def _find_long_tail_keywords(self, base_keyword: str) -> List[Dict]:
        """البحث عن كلمات مفتاحية طويلة الذيل"""
        long_tail_variations = [
            f"{base_keyword} guide",
            f"{base_keyword} tips",
            f"{base_keyword} review",
            f"{base_keyword} gameplay",
            f"best {base_keyword}",
            f"how to {base_keyword}",
            f"{base_keyword} 2025"
        ]
        
        long_tail_keywords = []
        for variation in long_tail_variations:
            analysis = await self._analyze_single_keyword(variation)
            if analysis and analysis.get('difficulty', 100) < 40:
                long_tail_keywords.append(analysis)
        
        return long_tail_keywords[:5]  # أفضل 5
    
    async def _find_semantic_keywords(self, base_keyword: str) -> List[str]:
        """البحث عن كلمات مفتاحية دلالية"""
        # محاكاة الكلمات الدلالية
        semantic_map = {
            'gaming': ['video games', 'esports', 'gameplay', 'gamer'],
            'review': ['rating', 'evaluation', 'analysis', 'critique'],
            'guide': ['tutorial', 'walkthrough', 'tips', 'strategy']
        }
        
        semantic_keywords = []
        for word in base_keyword.split():
            if word.lower() in semantic_map:
                semantic_keywords.extend(semantic_map[word.lower()])
        
        return semantic_keywords[:10]
    
    async def _analyze_competitor_gaps(self, keywords: List[str]) -> List[Dict]:
        """تحليل فجوات المنافسين"""
        gaps = []
        
        # محاكاة تحليل فجوات المنافسين
        for keyword in keywords[:5]:  # تحليل أول 5 كلمات
            gap = {
                'keyword': keyword,
                'competitors_missing': ['competitor1.com', 'competitor2.com'],
                'opportunity_score': 75,
                'recommended_action': 'create_comprehensive_content'
            }
            gaps.append(gap)
        
        return gaps
    
    async def _find_trending_keywords(self) -> List[Dict]:
        """البحث عن الكلمات المفتاحية الرائجة"""
        # محاكاة الكلمات الرائجة
        trending = [
            {'keyword': 'VR gaming 2025', 'trend_score': 95, 'growth_rate': 150},
            {'keyword': 'mobile gaming trends', 'trend_score': 88, 'growth_rate': 120},
            {'keyword': 'cloud gaming services', 'trend_score': 82, 'growth_rate': 200},
            {'keyword': 'indie game reviews', 'trend_score': 76, 'growth_rate': 80},
            {'keyword': 'gaming hardware 2025', 'trend_score': 71, 'growth_rate': 90}
        ]
        
        return trending
    
    def generate_schema_markup(self, article: Dict, schema_type: str = 'article') -> str:
        """توليد Schema Markup"""
        try:
            template = self.schema_templates.get(schema_type, self.schema_templates['article'])
            schema = template.copy()
            
            if schema_type == 'article':
                schema['headline'] = article.get('title', '')
                schema['description'] = article.get('meta_description', '')
                schema['image'] = article.get('image_urls', [''])[0] if article.get('image_urls') else ''
                schema['datePublished'] = article.get('generated_at', datetime.now()).isoformat() if isinstance(article.get('generated_at'), datetime) else str(article.get('generated_at', ''))
                schema['dateModified'] = datetime.now().isoformat()
            
            elif schema_type == 'game_review':
                schema['itemReviewed']['name'] = self._extract_game_name(article.get('title', ''))
                schema['reviewRating']['ratingValue'] = str(self._extract_rating(article.get('content', '')))
            
            return json.dumps(schema, ensure_ascii=False, indent=2)
            
        except Exception as e:
            logger.error("❌ فشل في توليد Schema Markup", e)
            return ""
    
    def _extract_game_name(self, title: str) -> str:
        """استخراج اسم اللعبة من العنوان"""
        # البحث عن أسماء الألعاب الشائعة
        game_patterns = [
            r'\b([A-Z][a-zA-Z\s]+(?:of|the|and|&)\s+[A-Z][a-zA-Z\s]*)\b',
            r'\b([A-Z][a-zA-Z]+\s+\d+)\b',
            r'\b([A-Z][a-zA-Z]{3,})\b'
        ]
        
        for pattern in game_patterns:
            match = re.search(pattern, title)
            if match:
                return match.group(1).strip()
        
        return "لعبة فيديو"
    
    def _extract_rating(self, content: str) -> int:
        """استخراج التقييم من المحتوى"""
        # البحث عن أرقام التقييم
        rating_patterns = [
            r'(\d+)/10',
            r'(\d+)\s*من\s*10',
            r'تقييم\s*(\d+)',
            r'نقاط\s*(\d+)'
        ]
        
        for pattern in rating_patterns:
            match = re.search(pattern, content)
            if match:
                return int(match.group(1))
        
        return 8  # تقييم افتراضي
    
    def optimize_content_for_featured_snippets(self, content: str, target_keyword: str) -> str:
        """تحسين المحتوى للمقتطفات المميزة - محدث 2025"""
        try:
            # إضافة هيكل للإجابات المباشرة
            optimized_content = content

            # إضافة إجابة مباشرة في البداية
            direct_answer = self._generate_direct_answer(target_keyword, content)
            if direct_answer:
                optimized_content = f"{direct_answer}\n\n{optimized_content}"

            # إضافة قسم الأسئلة الشائعة المحسن
            faq_section = self._generate_enhanced_faq_section(target_keyword)
            optimized_content += f"\n\n{faq_section}"

            # إضافة قوائم منظمة للمقتطفات
            if any(word in target_keyword.lower() for word in ['أفضل', 'best', 'top', 'أسوأ']):
                list_section = self._generate_snippet_optimized_list(target_keyword)
                optimized_content += f"\n\n{list_section}"

            # إضافة جدول مقارنة محسن للمقتطفات
            if any(word in target_keyword.lower() for word in ['مقارنة', 'vs', 'ضد', 'comparison']):
                comparison_table = self._generate_snippet_optimized_table(target_keyword)
                optimized_content += f"\n\n{comparison_table}"

            # تحسين للبحث الصوتي
            voice_optimized = self._optimize_for_voice_search(optimized_content, target_keyword)

            # إضافة People Also Ask optimization
            paa_section = self._generate_people_also_ask_content(target_keyword)
            voice_optimized += f"\n\n{paa_section}"

            return voice_optimized

        except Exception as e:
            logger.error("❌ فشل في تحسين المحتوى للمقتطفات المميزة", e)
            return content

    def _generate_direct_answer(self, keyword: str, content: str) -> str:
        """توليد إجابة مباشرة للمقتطف المميز"""
        try:
            # قوالب الإجابات المباشرة
            answer_templates = {
                'what': f"{keyword} هو/هي",
                'how': f"لـ {keyword}، يجب",
                'why': f"السبب في {keyword} هو",
                'when': f"{keyword} يحدث عندما",
                'where': f"يمكن العثور على {keyword} في"
            }

            # تحديد نوع السؤال
            question_type = 'what'  # افتراضي
            if any(word in keyword.lower() for word in ['كيف', 'how']):
                question_type = 'how'
            elif any(word in keyword.lower() for word in ['لماذا', 'why']):
                question_type = 'why'
            elif any(word in keyword.lower() for word in ['متى', 'when']):
                question_type = 'when'
            elif any(word in keyword.lower() for word in ['أين', 'where']):
                question_type = 'where'

            # استخراج الجملة الأولى من المحتوى كإجابة
            first_sentence = content.split('.')[0] if '.' in content else content[:150]

            return f"**{keyword}:** {first_sentence}."

        except Exception:
            return ""

    def _generate_enhanced_faq_section(self, keyword: str) -> str:
        """توليد قسم أسئلة شائعة محسن للمقتطفات"""
        enhanced_faqs = [
            f"## الأسئلة الشائعة حول {keyword}\n",
            f"### ما هو {keyword}؟\n{keyword} هو عنصر مهم في عالم الألعاب يوفر تجربة فريدة للاعبين.\n",
            f"### كيف يعمل {keyword}؟\n{keyword} يعمل من خلال آليات متقدمة تضمن أفضل أداء وتجربة ممكنة.\n",
            f"### لماذا {keyword} مهم؟\nأهمية {keyword} تكمن في قدرته على تحسين تجربة اللعب وإضافة عمق للمحتوى.\n",
            f"### متى يُستخدم {keyword}؟\nيُستخدم {keyword} في المواقف التي تتطلب استراتيجية متقدمة أو مهارات خاصة.\n",
            f"### هل {keyword} مناسب للمبتدئين؟\nنعم، {keyword} مصمم ليكون سهل الاستخدام للمبتدئين مع إمكانيات متقدمة للخبراء.\n"
        ]

        return '\n'.join(enhanced_faqs)

    def _generate_snippet_optimized_list(self, keyword: str) -> str:
        """توليد قائمة محسنة للمقتطفات المميزة"""
        return f"""
## أفضل خيارات {keyword}

1. **الخيار الأول المتقدم**: يوفر أداءً استثنائياً وميزات فريدة تجعله الخيار الأمثل للاعبين المحترفين.

2. **الخيار الثاني المتوازن**: يجمع بين السهولة والفعالية، مما يجعله مناسباً لجميع مستويات اللاعبين.

3. **الخيار الثالث الاقتصادي**: يقدم قيمة ممتازة مقابل السعر مع أداء موثوق وميزات أساسية قوية.

4. **الخيار الرابع المبتكر**: يتميز بتقنيات حديثة وتصميم مبتكر يضعه في المقدمة.

5. **الخيار الخامس الشامل**: يوفر تجربة متكاملة مع دعم شامل وتحديثات مستمرة.
"""

    def _generate_snippet_optimized_table(self, keyword: str) -> str:
        """توليد جدول محسن للمقتطفات المميزة"""
        return f"""
## جدول مقارنة شامل لـ {keyword}

| المعيار | الخيار الأول | الخيار الثاني | الخيار الثالث | الخيار الرابع |
|---------|-------------|-------------|-------------|-------------|
| **الأداء** | ممتاز (95%) | جيد جداً (88%) | جيد (82%) | متوسط (75%) |
| **سهولة الاستخدام** | متوسط | سهل جداً | سهل | صعب |
| **السعر** | مرتفع | متوسط | منخفض | مرتفع |
| **الميزات** | شاملة | أساسية+ | أساسية | متقدمة |
| **الدعم** | 24/7 | أوقات العمل | محدود | مجتمعي |
| **التقييم العام** | 9.2/10 | 8.5/10 | 7.8/10 | 8.0/10 |
| **الأنسب لـ** | المحترفين | المبتدئين | الميزانية المحدودة | المتقدمين |
"""

    def _optimize_for_voice_search(self, content: str, keyword: str) -> str:
        """تحسين المحتوى للبحث الصوتي"""
        try:
            # إضافة أسئلة وإجابات بأسلوب محادثة
            voice_qa = f"""
## أسئلة شائعة بالبحث الصوتي

**س: كيف أستخدم {keyword}؟**
ج: لاستخدام {keyword} بفعالية، ابدأ بفهم الأساسيات ثم تدرج للميزات المتقدمة.

**س: ما هي فوائد {keyword}؟**
ج: {keyword} يوفر تحسينات كبيرة في الأداء والتجربة العامة للمستخدم.

**س: هل {keyword} يستحق التجربة؟**
ج: نعم، {keyword} يعتبر استثماراً ممتازاً لتحسين تجربة اللعب.
"""

            return content + voice_qa

        except Exception as e:
            logger.error("❌ فشل في تحسين البحث الصوتي", e)
            return content

    def _generate_people_also_ask_content(self, keyword: str) -> str:
        """توليد محتوى محسن لـ People Also Ask"""
        paa_questions = [
            f"هل {keyword} مجاني؟",
            f"كيف أحصل على {keyword}؟",
            f"ما هي بدائل {keyword}؟",
            f"هل {keyword} آمن للاستخدام؟",
            f"كم يستغرق تعلم {keyword}؟",
            f"ما هي متطلبات {keyword}؟"
        ]

        paa_content = "## أسئلة ذات صلة\n\n"

        for question in paa_questions:
            paa_content += f"### {question}\n"
            paa_content += f"إجابة شاملة ومفصلة حول {question.lower()} مع معلومات عملية ومفيدة.\n\n"

        return paa_content
    
    def _generate_faq_section(self, keyword: str) -> str:
        """توليد قسم الأسئلة الشائعة"""
        faqs = [
            f"ما هو {keyword}؟",
            f"كيف يعمل {keyword}؟",
            f"ما هي مميزات {keyword}؟",
            f"هل {keyword} مناسب للمبتدئين؟"
        ]
        
        faq_section = "## الأسئلة الشائعة\n\n"
        for i, faq in enumerate(faqs, 1):
            faq_section += f"### {i}. {faq}\n\n"
            faq_section += f"إجابة مفصلة حول {faq.lower()}\n\n"
        
        return faq_section
    
    def _generate_list_section(self, keyword: str) -> str:
        """توليد قسم القائمة"""
        return f"""
## أفضل خيارات {keyword}

1. **الخيار الأول**: وصف مفصل للخيار الأول
2. **الخيار الثاني**: وصف مفصل للخيار الثاني  
3. **الخيار الثالث**: وصف مفصل للخيار الثالث
4. **الخيار الرابع**: وصف مفصل للخيار الرابع
5. **الخيار الخامس**: وصف مفصل للخيار الخامس
"""
    
    def _generate_comparison_table(self, keyword: str) -> str:
        """توليد جدول المقارنة"""
        return f"""
## جدول مقارنة {keyword}

| الميزة | الخيار الأول | الخيار الثاني | الخيار الثالث |
|--------|-------------|-------------|-------------|
| السعر | متوسط | مرتفع | منخفض |
| الجودة | ممتاز | جيد جداً | جيد |
| سهولة الاستخدام | سهل | متوسط | سهل جداً |
| التقييم العام | 9/10 | 8/10 | 7/10 |
"""
    
    def calculate_seo_score(self, article: Dict, target_keywords: List[str]) -> Dict:
        """حساب نقاط SEO الشاملة"""
        try:
            scores = {
                'content_quality': self._score_content_quality(article),
                'technical_seo': self._score_technical_seo(article),
                'keyword_optimization': self._score_keyword_optimization(article, target_keywords),
                'user_experience': self._score_user_experience(article),
                'mobile_optimization': self._score_mobile_optimization(article)
            }
            
            # حساب النقاط الإجمالية
            weights = {
                'content_quality': 0.3,
                'technical_seo': 0.25,
                'keyword_optimization': 0.25,
                'user_experience': 0.15,
                'mobile_optimization': 0.05
            }
            
            total_score = sum(scores[key] * weights[key] for key in scores)
            
            return {
                'total_score': round(total_score, 2),
                'individual_scores': scores,
                'recommendations': self._generate_seo_recommendations(scores),
                'grade': self._get_seo_grade(total_score)
            }
            
        except Exception as e:
            logger.error("❌ فشل في حساب نقاط SEO", e)
            return {'total_score': 0, 'grade': 'F'}
    
    def _score_content_quality(self, article: Dict) -> float:
        """تقييم جودة المحتوى"""
        score = 0
        content = article.get('content', '')
        
        # طول المحتوى
        word_count = len(content.split())
        if word_count >= 1000:
            score += 25
        elif word_count >= 500:
            score += 15
        else:
            score += 5
        
        # وجود عناوين فرعية
        if '<h' in content or '##' in content:
            score += 20
        
        # وجود قوائم
        if '<ul>' in content or '<ol>' in content or '-' in content:
            score += 15
        
        # وجود صور
        if article.get('image_urls'):
            score += 20
        
        # جودة الكتابة (محاكاة)
        score += 20  # افتراض جودة جيدة
        
        return min(100, score)
    
    def _score_technical_seo(self, article: Dict) -> float:
        """تقييم SEO التقني"""
        score = 0
        
        # وجود meta description
        if article.get('meta_description'):
            score += 25
        
        # طول العنوان مناسب
        title_length = len(article.get('title', ''))
        if 30 <= title_length <= 60:
            score += 25
        
        # وجود كلمات مفتاحية
        if article.get('keywords'):
            score += 25
        
        # هيكل المحتوى
        content = article.get('content', '')
        if '<h' in content:
            score += 25
        
        return score
    
    def _score_keyword_optimization(self, article: Dict, target_keywords: List[str]) -> float:
        """تقييم تحسين الكلمات المفتاحية"""
        if not target_keywords:
            return 50
        
        score = 0
        title = article.get('title', '').lower()
        content = article.get('content', '').lower()
        
        for keyword in target_keywords[:3]:  # أهم 3 كلمات
            keyword_lower = keyword.lower()
            
            # في العنوان
            if keyword_lower in title:
                score += 20
            
            # في المحتوى
            keyword_count = content.count(keyword_lower)
            if 3 <= keyword_count <= 8:
                score += 15
            elif keyword_count > 0:
                score += 5
        
        return min(100, score)
    
    def _score_user_experience(self, article: Dict) -> float:
        """تقييم تجربة المستخدم"""
        score = 60  # نقاط أساسية
        
        # وجود دعوة للعمل
        content = article.get('content', '')
        if any(cta in content for cta in ['شارك', 'تعليق', 'رأيك']):
            score += 20
        
        # سهولة القراءة
        word_count = len(content.split())
        sentence_count = content.count('.') + content.count('!') + content.count('?')
        if sentence_count > 0:
            avg_words_per_sentence = word_count / sentence_count
            if avg_words_per_sentence <= 20:
                score += 20
        
        return min(100, score)
    
    def _score_mobile_optimization(self, article: Dict) -> float:
        """تقييم تحسين الهاتف المحمول"""
        # محاكاة تقييم الهاتف المحمول
        return 85  # افتراض تحسين جيد
    
    def _generate_seo_recommendations(self, scores: Dict) -> List[str]:
        """توليد توصيات تحسين SEO"""
        recommendations = []
        
        if scores['content_quality'] < 70:
            recommendations.append("زيادة طول المحتوى وإضافة المزيد من التفاصيل")
        
        if scores['technical_seo'] < 70:
            recommendations.append("تحسين العناصر التقنية مثل meta description والعناوين")
        
        if scores['keyword_optimization'] < 70:
            recommendations.append("تحسين استخدام الكلمات المفتاحية في العنوان والمحتوى")
        
        if scores['user_experience'] < 70:
            recommendations.append("إضافة المزيد من عناصر التفاعل ودعوات العمل")
        
        return recommendations
    
    def _get_seo_grade(self, score: float) -> str:
        """الحصول على درجة SEO"""
        if score >= 90:
            return 'A+'
        elif score >= 80:
            return 'A'
        elif score >= 70:
            return 'B'
        elif score >= 60:
            return 'C'
        elif score >= 50:
            return 'D'
        else:
            return 'F'

# إنشاء مثيل عام لنظام SEO المتقدم
advanced_seo = AdvancedSEO()
