#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تحضير المشروع للرفع على GitHub
Prepare project for GitHub upload
"""

import os
import sys
import shutil
import json
from datetime import datetime

def check_sensitive_files():
    """فحص الملفات الحساسة"""
    print("🔍 فحص الملفات الحساسة...")
    
    sensitive_files = [
        'config/bot_config.json',
        'config/api_keys.json',
        'config/blogger_token.json',
        'client_secret.json',
        '.env',
        '.env.local',
        '.env.production'
    ]
    
    found_sensitive = []
    for file in sensitive_files:
        if os.path.exists(file):
            found_sensitive.append(file)
    
    if found_sensitive:
        print("⚠️ ملفات حساسة موجودة:")
        for file in found_sensitive:
            print(f"   - {file}")
        print("✅ هذه الملفات محمية بواسطة .gitignore")
    else:
        print("✅ لا توجد ملفات حساسة")
    
    return True

def prepare_readme():
    """تحضير ملف README"""
    print("📝 تحضير ملف README...")
    
    try:
        if os.path.exists('README_GITHUB.md'):
            shutil.copy2('README_GITHUB.md', 'README.md')
            print("✅ تم نسخ README_GITHUB.md إلى README.md")
        else:
            print("⚠️ ملف README_GITHUB.md غير موجود")
            return False
    except Exception as e:
        print(f"❌ خطأ في تحضير README: {e}")
        return False
    
    return True

def check_required_files():
    """فحص الملفات المطلوبة"""
    print("📁 فحص الملفات المطلوبة...")
    
    required_files = [
        'main.py',
        'web_api.py',
        'deployment_config.py',
        'requirements.txt',
        'Procfile',
        'render.yaml',
        'DEPLOYMENT_GUIDE.md',
        'LICENSE'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
            print(f"   ❌ {file} - مفقود")
        else:
            print(f"   ✅ {file}")
    
    if missing_files:
        print(f"⚠️ ملفات مفقودة: {', '.join(missing_files)}")
        return False
    
    print("✅ جميع الملفات المطلوبة موجودة")
    return True

def check_directories():
    """فحص المجلدات المطلوبة"""
    print("📂 فحص المجلدات المطلوبة...")
    
    required_dirs = [
        'modules',
        'config',
        'web_interface'
    ]
    
    missing_dirs = []
    for dir_name in required_dirs:
        if not os.path.exists(dir_name):
            missing_dirs.append(dir_name)
            print(f"   ❌ {dir_name}/ - مفقود")
        else:
            print(f"   ✅ {dir_name}/")
    
    if missing_dirs:
        print(f"⚠️ مجلدات مفقودة: {', '.join(missing_dirs)}")
        return False
    
    print("✅ جميع المجلدات المطلوبة موجودة")
    return True

def create_project_info():
    """إنشاء ملف معلومات المشروع"""
    print("📊 إنشاء ملف معلومات المشروع...")
    
    project_info = {
        "name": "Gaming News Agent",
        "version": "1.0.0",
        "description": "وكيل أخبار الألعاب الذكي مع واجهة ويب تفاعلية",
        "author": "Mcamento8",
        "repository": "https://github.com/Mcamento8/gaming-news-agent",
        "license": "MIT",
        "python_version": "3.8+",
        "main_file": "main.py",
        "web_interface": "http://localhost:5000",
        "deployment_platforms": [
            "Render",
            "Heroku", 
            "Railway"
        ],
        "features": [
            "Smart news collection with AI",
            "Interactive web interface",
            "Real-time monitoring",
            "Secure API management",
            "Multi-platform deployment"
        ],
        "created_date": datetime.now().isoformat(),
        "last_updated": datetime.now().isoformat()
    }
    
    try:
        with open('project_info.json', 'w', encoding='utf-8') as f:
            json.dump(project_info, f, ensure_ascii=False, indent=2)
        print("✅ تم إنشاء project_info.json")
        return True
    except Exception as e:
        print(f"❌ خطأ في إنشاء ملف معلومات المشروع: {e}")
        return False

def clean_temp_files():
    """تنظيف الملفات المؤقتة"""
    print("🧹 تنظيف الملفات المؤقتة...")
    
    temp_patterns = [
        '__pycache__',
        '*.pyc',
        '*.pyo',
        '*.tmp',
        '.pytest_cache',
        'test_*.json'
    ]
    
    cleaned = 0
    
    # تنظيف مجلدات __pycache__
    for root, dirs, files in os.walk('.'):
        if '__pycache__' in dirs:
            pycache_path = os.path.join(root, '__pycache__')
            try:
                shutil.rmtree(pycache_path)
                cleaned += 1
                print(f"   🗑️ حذف {pycache_path}")
            except Exception as e:
                print(f"   ⚠️ لم يتمكن من حذف {pycache_path}: {e}")
    
    # تنظيف ملفات .pyc
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith(('.pyc', '.pyo', '.tmp')):
                file_path = os.path.join(root, file)
                try:
                    os.remove(file_path)
                    cleaned += 1
                    print(f"   🗑️ حذف {file_path}")
                except Exception as e:
                    print(f"   ⚠️ لم يتمكن من حذف {file_path}: {e}")
    
    print(f"✅ تم تنظيف {cleaned} ملف/مجلد مؤقت")
    return True

def show_upload_instructions():
    """عرض تعليمات الرفع"""
    print("\n" + "=" * 60)
    print("📤 تعليمات رفع المشروع إلى GitHub")
    print("=" * 60)
    print()
    print("🎯 المستودع المطلوب:")
    print("   https://github.com/Mcamento8/gaming-news-agent")
    print()
    print("🚀 طرق الرفع:")
    print("   1. Windows: تشغيل upload_to_github.bat")
    print("   2. Linux/Mac: تشغيل ./upload_to_github.sh")
    print("   3. يدوياً: اتبع GITHUB_UPLOAD_GUIDE.md")
    print()
    print("🔐 المصادقة:")
    print("   - Username: Mcamento8")
    print("   - Password: استخدم Personal Access Token")
    print("   - Token: https://github.com/settings/tokens")
    print()
    print("📋 بعد الرفع:")
    print("   1. تأكد من رفع جميع الملفات")
    print("   2. أضف وصف للمشروع")
    print("   3. أضف Topics: python, flask, ai, gaming")
    print("   4. اجعل المشروع خاص")
    print("   5. انشر على Render")
    print()

def main():
    """الدالة الرئيسية"""
    print("🎮 تحضير وكيل أخبار الألعاب للرفع على GitHub")
    print("=" * 60)
    
    success = True
    
    # فحص الملفات الحساسة
    if not check_sensitive_files():
        success = False
    
    print()
    
    # تحضير README
    if not prepare_readme():
        success = False
    
    print()
    
    # فحص الملفات المطلوبة
    if not check_required_files():
        success = False
    
    print()
    
    # فحص المجلدات
    if not check_directories():
        success = False
    
    print()
    
    # إنشاء ملف معلومات المشروع
    if not create_project_info():
        success = False
    
    print()
    
    # تنظيف الملفات المؤقتة
    if not clean_temp_files():
        success = False
    
    print()
    
    if success:
        print("🎉 المشروع جاهز للرفع على GitHub!")
        show_upload_instructions()
    else:
        print("❌ يرجى إصلاح المشاكل المذكورة أعلاه قبل الرفع")
    
    return success

if __name__ == "__main__":
    main()
