-- ===================================================================
-- إدراج بيانات تجريبية لاختبار الجداول
-- Insert Sample Data for Testing Tables
-- ===================================================================

-- ===================================================================
-- 1. بيانات تجريبية للمودات (Sample Mods Data)
-- ===================================================================

INSERT INTO public.mods (
    name, description, description_ar, telegram_description_ar, telegram_description_en,
    category, version, download_url, image_urls, file_size,
    author, mod_type, tags, compatibility, requirements
) VALUES
(
    'Enhanced Survival Mod',
    'A comprehensive survival enhancement mod that adds new tools, weapons, and survival mechanics.',
    'مود تحسين البقاء - يضيف أدوات وأسلحة جديدة وآليات بقاء محسنة لتجربة لعب أكثر تشويقاً',
    'مود تحسين البقاء - يضيف أدوات وأسلحة جديدة وآليات بقاء محسنة لتجربة لعب أكثر تشويقاً',
    'Enhanced Survival Mod - Adds new tools, weapons and improved survival mechanics for a more exciting gameplay experience',
    'addons',
    '1.21+',
    'https://example.com/download/enhanced-survival.mcpack',
    ARRAY['https://example.com/images/enhanced-survival.jpg'],
    '2.5 MB',
    'ModCreator123',
    'mod',
    ARRAY['survival', 'tools', 'weapons', 'enhancement'],
    ARRAY['1.21+', '1.20+'],
    'Minecraft Bedrock Edition 1.20 or higher'
),
(
    'Realistic Water Shader',
    'Beautiful water shader that makes water look more realistic with reflections and waves.',
    'شيدر الماء الواقعي - يجعل الماء يبدو أكثر واقعية مع انعكاسات وأمواج جميلة',
    'شيدر الماء الواقعي - يجعل الماء يبدو أكثر واقعية مع انعكاسات وأمواج جميلة',
    'Realistic Water Shader - Makes water look more realistic with beautiful reflections and waves',
    'shaders',
    '1.20+',
    'https://example.com/download/water-shader.mcpack',
    ARRAY['https://example.com/images/water-shader.jpg'],
    '1.8 MB',
    'ShaderArtist',
    'shader',
    ARRAY['shader', 'water', 'realistic', 'graphics'],
    ARRAY['1.20+', '1.19+'],
    'Device with shader support'
),
(
    'Medieval Castle Map',
    'A massive medieval castle with detailed interiors and surrounding village.',
    'خريطة القلعة الوسطية - قلعة وسطية ضخمة مع تفاصيل داخلية وقرية محيطة',
    'خريطة القلعة الوسطية - قلعة وسطية ضخمة مع تفاصيل داخلية وقرية محيطة',
    'Medieval Castle Map - A massive medieval castle with detailed interiors and surrounding village',
    'maps',
    '1.19+',
    'https://example.com/download/medieval-castle.mcworld',
    ARRAY['https://example.com/images/medieval-castle.jpg'],
    '15.2 MB',
    'MapBuilder99',
    'map',
    ARRAY['medieval', 'castle', 'adventure', 'building'],
    ARRAY['1.19+', '1.18+'],
    'Minecraft Bedrock Edition'
);

-- ===================================================================
-- 2. بيانات تجريبية للإشعارات (Sample Notifications Data)
-- ===================================================================

INSERT INTO public.saved_notifications (
    title, message, message_type, created_by
) VALUES 
(
    'مرحباً بالمستخدمين الجدد',
    'مرحباً بك في بوت المودات! نحن سعداء لانضمامك إلينا. استكشف مجموعتنا الواسعة من المودات والخرائط والشيدرز.',
    'text',
    'admin'
),
(
    'تحديث جديد متوفر',
    'تم إضافة مودات جديدة رائعة! تحقق من أحدث الإضافات في قسم المودات.',
    'text',
    'admin'
),
(
    'نصائح للاستخدام',
    'نصيحة: يمكنك استخدام الفلاتر للعثور على المودات المناسبة لإصدار Minecraft الخاص بك.',
    'text',
    'admin'
);

-- ===================================================================
-- 3. بيانات تجريبية للمهام (Sample Tasks Data)
-- ===================================================================

INSERT INTO public.tasks (
    title, description, task_type, target_url, reward_points, created_by, max_completions
) VALUES 
(
    'انضم إلى قناة التليجرام',
    'انضم إلى قناتنا الرسمية على التليجرام للحصول على آخر التحديثات',
    'join_channel',
    'https://t.me/your_channel',
    10,
    'admin',
    1000
),
(
    'زيارة موقعنا الإلكتروني',
    'قم بزيارة موقعنا الإلكتروني واستكشف المزيد من المحتوى',
    'visit_link',
    'https://your-website.com',
    5,
    'admin',
    500
),
(
    'شارك البوت مع أصدقائك',
    'شارك رابط البوت مع أصدقائك واحصل على نقاط إضافية',
    'share_content',
    NULL,
    15,
    'admin',
    100
);

-- ===================================================================
-- 4. بيانات تجريبية لإعدادات الإعلانات (Sample Ads Settings)
-- ===================================================================

INSERT INTO public.user_ads_settings (
    user_id, ads_enabled, ads_frequency, total_ads_shown
) VALUES 
(
    'sample_user_1',
    true,
    3,
    0
),
(
    'sample_user_2',
    true,
    5,
    0
),
(
    'sample_user_3',
    false,
    0,
    0
);

-- ===================================================================
-- 5. بيانات تجريبية لتقصير الروابط (Sample URL Shortener Settings)
-- ===================================================================

INSERT INTO public.user_url_shortener_settings (
    user_id, service_enabled, service_type
) VALUES 
(
    'sample_user_1',
    true,
    'default'
),
(
    'sample_user_2',
    true,
    'bitly'
),
(
    'sample_user_3',
    false,
    'default'
);

-- ===================================================================
-- 6. بيانات تجريبية لتخصيص الصفحات (Sample Page Customization)
-- ===================================================================

INSERT INTO public.user_page_customization (
    user_id, theme_color, background_color, text_color, page_title, meta_description
) VALUES 
(
    'sample_user_1',
    '#007bff',
    '#ffffff',
    '#333333',
    'My Minecraft Mods',
    'Discover amazing Minecraft mods, maps, and shaders'
),
(
    'sample_user_2',
    '#28a745',
    '#f8f9fa',
    '#212529',
    'Minecraft Adventures',
    'Your source for the best Minecraft content'
);

-- ===================================================================
-- 7. بيانات تجريبية للروابط المخصصة (Sample Custom Download Links)
-- ===================================================================

INSERT INTO public.custom_download_links (
    user_id, mod_id, custom_url, original_url, click_count
) VALUES 
(
    'sample_user_1',
    1,
    'https://short.ly/enhanced-survival',
    'https://example.com/download/enhanced-survival.mcpack',
    0
),
(
    'sample_user_2',
    2,
    'https://short.ly/water-shader',
    'https://example.com/download/water-shader.mcpack',
    0
);

-- ===================================================================
-- 8. تحديث إحصائيات النظام (Update System Statistics)
-- ===================================================================

UPDATE public.system_stats SET 
    stat_value = (SELECT COUNT(*) FROM public.mods),
    updated_at = NOW()
WHERE stat_name = 'total_mods';

UPDATE public.system_stats SET 
    stat_value = 3, -- عدد المستخدمين التجريبيين
    updated_at = NOW()
WHERE stat_name = 'total_users';

-- ===================================================================
-- 9. إنشاء views مفيدة للاستعلامات (Create Useful Views)
-- ===================================================================

-- عرض إحصائيات المودات
CREATE OR REPLACE VIEW mods_stats AS
SELECT 
    category,
    COUNT(*) as total_mods,
    AVG(rating) as avg_rating,
    SUM(download_count) as total_downloads
FROM public.mods 
WHERE is_active = true
GROUP BY category;

-- عرض إحصائيات المستخدمين النشطين
CREATE OR REPLACE VIEW active_users_stats AS
SELECT 
    COUNT(DISTINCT user_id) as total_active_users,
    COUNT(DISTINCT CASE WHEN ads_enabled = true THEN user_id END) as users_with_ads,
    COUNT(DISTINCT CASE WHEN service_enabled = true THEN user_id END) as users_with_url_shortener
FROM public.user_ads_settings uas
FULL OUTER JOIN public.user_url_shortener_settings uus ON uas.user_id = uus.user_id;

-- عرض أحدث الإشعارات
CREATE OR REPLACE VIEW recent_notifications AS
SELECT 
    id,
    title,
    message,
    message_type,
    created_by,
    created_at,
    is_active
FROM public.saved_notifications 
WHERE is_active = true
ORDER BY created_at DESC
LIMIT 10;

-- ===================================================================
-- 10. إنشاء دوال للإحصائيات (Create Statistics Functions)
-- ===================================================================

-- دالة للحصول على إحصائيات شاملة
CREATE OR REPLACE FUNCTION get_system_overview()
RETURNS TABLE (
    total_mods BIGINT,
    total_users BIGINT,
    total_downloads BIGINT,
    total_notifications BIGINT,
    total_tasks BIGINT,
    active_mods BIGINT,
    active_notifications BIGINT,
    active_tasks BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        (SELECT COUNT(*) FROM public.mods)::BIGINT,
        (SELECT COUNT(DISTINCT user_id) FROM public.user_ads_settings)::BIGINT,
        (SELECT COALESCE(SUM(download_count), 0) FROM public.mods)::BIGINT,
        (SELECT COUNT(*) FROM public.saved_notifications)::BIGINT,
        (SELECT COUNT(*) FROM public.tasks)::BIGINT,
        (SELECT COUNT(*) FROM public.mods WHERE is_active = true)::BIGINT,
        (SELECT COUNT(*) FROM public.saved_notifications WHERE is_active = true)::BIGINT,
        (SELECT COUNT(*) FROM public.tasks WHERE is_active = true)::BIGINT;
END;
$$ LANGUAGE plpgsql;

-- دالة لتحديث عداد التحميلات
CREATE OR REPLACE FUNCTION increment_download_count(mod_id_param INTEGER)
RETURNS VOID AS $$
BEGIN
    UPDATE public.mods 
    SET download_count = download_count + 1,
        updated_at = NOW()
    WHERE id = mod_id_param;
    
    -- تحديث الإحصائيات العامة
    UPDATE public.system_stats 
    SET stat_value = stat_value + 1,
        updated_at = NOW()
    WHERE stat_name = 'total_downloads';
END;
$$ LANGUAGE plpgsql;

-- ===================================================================
-- تم إدراج جميع البيانات التجريبية بنجاح!
-- All sample data inserted successfully!
-- ===================================================================

-- للتحقق من البيانات المدرجة، يمكنك تشغيل:
-- To verify the inserted data, you can run:

-- SELECT * FROM get_system_overview();
-- SELECT * FROM mods_stats;
-- SELECT * FROM active_users_stats;
-- SELECT * FROM recent_notifications;
