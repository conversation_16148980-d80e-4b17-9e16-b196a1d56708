# نظام التخزين المؤقت المتقدم
import sqlite3
import json
import time
import hashlib
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
import threading
import asyncio

from .logger import logger

@dataclass
class CacheEntry:
    """مدخل في التخزين المؤقت"""
    key: str
    data: Any
    timestamp: float
    ttl: int
    access_count: int = 0
    last_accessed: float = 0
    tags: List[str] = None
    priority: int = 1  # 1=منخفض، 5=عالي
    
    def __post_init__(self):
        if self.tags is None:
            self.tags = []
        if self.last_accessed == 0:
            self.last_accessed = self.timestamp

class AdvancedCacheSystem:
    """نظام تخزين مؤقت متقدم مع ميزات ذكية"""
    
    def __init__(self, db_path: str = "cache/advanced_cache.db"):
        self.db_path = db_path
        self.lock = threading.RLock()
        
        # إعدادات التخزين المؤقت
        self.settings = {
            'max_entries': 50000,
            'default_ttl': 3600,  # ساعة واحدة
            'cleanup_interval': 1800,  # 30 دقيقة
            'max_memory_usage': 100 * 1024 * 1024,  # 100 MB
            'compression_threshold': 1024,  # ضغط البيانات أكبر من 1KB
        }
        
        # إحصائيات
        self.stats = {
            'hits': 0,
            'misses': 0,
            'sets': 0,
            'deletes': 0,
            'cleanups': 0,
            'memory_usage': 0
        }
        
        # تهيئة قاعدة البيانات
        self._init_database()
        
        # بدء مهمة التنظيف التلقائي
        self._start_cleanup_task()
        
        logger.info("🗄️ تم تهيئة نظام التخزين المؤقت المتقدم")
    
    def _init_database(self):
        """تهيئة قاعدة بيانات التخزين المؤقت"""
        try:
            os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
            
            with sqlite3.connect(self.db_path) as conn:
                conn.execute('''
                    CREATE TABLE IF NOT EXISTS cache_entries (
                        key TEXT PRIMARY KEY,
                        data BLOB NOT NULL,
                        timestamp REAL NOT NULL,
                        ttl INTEGER NOT NULL,
                        access_count INTEGER DEFAULT 0,
                        last_accessed REAL NOT NULL,
                        tags TEXT DEFAULT '[]',
                        priority INTEGER DEFAULT 1,
                        compressed BOOLEAN DEFAULT FALSE,
                        size INTEGER DEFAULT 0
                    )
                ''')
                
                # إنشاء فهارس للأداء
                conn.execute('CREATE INDEX IF NOT EXISTS idx_timestamp ON cache_entries(timestamp)')
                conn.execute('CREATE INDEX IF NOT EXISTS idx_last_accessed ON cache_entries(last_accessed)')
                conn.execute('CREATE INDEX IF NOT EXISTS idx_priority ON cache_entries(priority)')
                conn.execute('CREATE INDEX IF NOT EXISTS idx_ttl ON cache_entries(ttl)')
                
                # جدول الإحصائيات
                conn.execute('''
                    CREATE TABLE IF NOT EXISTS cache_stats (
                        date TEXT PRIMARY KEY,
                        hits INTEGER DEFAULT 0,
                        misses INTEGER DEFAULT 0,
                        sets INTEGER DEFAULT 0,
                        deletes INTEGER DEFAULT 0,
                        memory_usage INTEGER DEFAULT 0
                    )
                ''')
                
        except Exception as e:
            logger.error(f"❌ فشل في تهيئة قاعدة بيانات التخزين المؤقت: {e}")
    
    def _start_cleanup_task(self):
        """بدء مهمة التنظيف التلقائي"""
        def cleanup_worker():
            while True:
                try:
                    time.sleep(self.settings['cleanup_interval'])
                    self.cleanup_expired()
                    self._optimize_cache()
                except Exception as e:
                    logger.error(f"❌ خطأ في مهمة التنظيف: {e}")
        
        cleanup_thread = threading.Thread(target=cleanup_worker, daemon=True)
        cleanup_thread.start()
    
    def set(self, key: str, data: Any, ttl: int = None, tags: List[str] = None, priority: int = 1) -> bool:
        """حفظ بيانات في التخزين المؤقت"""
        try:
            with self.lock:
                if ttl is None:
                    ttl = self.settings['default_ttl']
                
                if tags is None:
                    tags = []
                
                current_time = time.time()
                
                # تحويل البيانات إلى JSON
                data_json = json.dumps(data, ensure_ascii=False)
                data_bytes = data_json.encode('utf-8')
                
                # ضغط البيانات الكبيرة
                compressed = False
                if len(data_bytes) > self.settings['compression_threshold']:
                    try:
                        import gzip
                        data_bytes = gzip.compress(data_bytes)
                        compressed = True
                    except ImportError:
                        pass  # الضغط غير متاح
                
                # حفظ في قاعدة البيانات
                with sqlite3.connect(self.db_path) as conn:
                    conn.execute('''
                        INSERT OR REPLACE INTO cache_entries 
                        (key, data, timestamp, ttl, last_accessed, tags, priority, compressed, size)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        key, data_bytes, current_time, ttl, current_time,
                        json.dumps(tags), priority, compressed, len(data_bytes)
                    ))
                
                self.stats['sets'] += 1
                self._update_memory_usage()
                
                return True
                
        except Exception as e:
            logger.error(f"❌ فشل في حفظ البيانات في التخزين المؤقت: {e}")
            return False
    
    def get(self, key: str) -> Optional[Any]:
        """الحصول على بيانات من التخزين المؤقت"""
        try:
            with self.lock:
                current_time = time.time()
                
                with sqlite3.connect(self.db_path) as conn:
                    cursor = conn.execute('''
                        SELECT data, timestamp, ttl, access_count, compressed
                        FROM cache_entries 
                        WHERE key = ?
                    ''', (key,))
                    
                    row = cursor.fetchone()
                    if not row:
                        self.stats['misses'] += 1
                        return None
                    
                    data_bytes, timestamp, ttl, access_count, compressed = row
                    
                    # فحص انتهاء الصلاحية
                    if current_time - timestamp > ttl:
                        # حذف البيانات المنتهية الصلاحية
                        conn.execute('DELETE FROM cache_entries WHERE key = ?', (key,))
                        self.stats['misses'] += 1
                        return None
                    
                    # تحديث إحصائيات الوصول
                    conn.execute('''
                        UPDATE cache_entries 
                        SET access_count = access_count + 1, last_accessed = ?
                        WHERE key = ?
                    ''', (current_time, key))
                    
                    # إلغاء ضغط البيانات إذا لزم الأمر
                    if compressed:
                        try:
                            import gzip
                            data_bytes = gzip.decompress(data_bytes)
                        except ImportError:
                            logger.warning("⚠️ لا يمكن إلغاء ضغط البيانات")
                            return None
                    
                    # تحويل من JSON
                    data_json = data_bytes.decode('utf-8')
                    data = json.loads(data_json)
                    
                    self.stats['hits'] += 1
                    return data
                    
        except Exception as e:
            logger.error(f"❌ فشل في الحصول على البيانات من التخزين المؤقت: {e}")
            self.stats['misses'] += 1
            return None
    
    def delete(self, key: str) -> bool:
        """حذف مدخل من التخزين المؤقت"""
        try:
            with self.lock:
                with sqlite3.connect(self.db_path) as conn:
                    cursor = conn.execute('DELETE FROM cache_entries WHERE key = ?', (key,))
                    deleted = cursor.rowcount > 0
                    
                    if deleted:
                        self.stats['deletes'] += 1
                        self._update_memory_usage()
                    
                    return deleted
                    
        except Exception as e:
            logger.error(f"❌ فشل في حذف البيانات من التخزين المؤقت: {e}")
            return False
    
    def cleanup_expired(self) -> int:
        """تنظيف البيانات المنتهية الصلاحية"""
        try:
            with self.lock:
                current_time = time.time()
                
                with sqlite3.connect(self.db_path) as conn:
                    cursor = conn.execute('''
                        DELETE FROM cache_entries 
                        WHERE timestamp + ttl < ?
                    ''', (current_time,))
                    
                    deleted_count = cursor.rowcount
                    
                    if deleted_count > 0:
                        self.stats['cleanups'] += 1
                        self._update_memory_usage()
                        logger.info(f"🧹 تم حذف {deleted_count} مدخل منتهي الصلاحية")
                    
                    return deleted_count
                    
        except Exception as e:
            logger.error(f"❌ فشل في تنظيف التخزين المؤقت: {e}")
            return 0
    
    def _optimize_cache(self):
        """تحسين التخزين المؤقت"""
        try:
            with self.lock:
                with sqlite3.connect(self.db_path) as conn:
                    # فحص عدد المدخلات
                    cursor = conn.execute('SELECT COUNT(*) FROM cache_entries')
                    total_entries = cursor.fetchone()[0]
                    
                    if total_entries > self.settings['max_entries']:
                        # حذف المدخلات الأقل استخداماً
                        excess = total_entries - self.settings['max_entries']
                        conn.execute('''
                            DELETE FROM cache_entries 
                            WHERE key IN (
                                SELECT key FROM cache_entries 
                                ORDER BY priority ASC, access_count ASC, last_accessed ASC 
                                LIMIT ?
                            )
                        ''', (excess,))
                        
                        logger.info(f"🗜️ تم حذف {excess} مدخل لتحسين التخزين المؤقت")
                    
                    # ضغط قاعدة البيانات
                    conn.execute('VACUUM')
                    
        except Exception as e:
            logger.error(f"❌ فشل في تحسين التخزين المؤقت: {e}")
    
    def _update_memory_usage(self):
        """تحديث استخدام الذاكرة"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute('SELECT SUM(size) FROM cache_entries')
                total_size = cursor.fetchone()[0] or 0
                self.stats['memory_usage'] = total_size
                
        except Exception as e:
            logger.debug(f"فشل في تحديث استخدام الذاكرة: {e}")
    
    def get_stats(self) -> Dict:
        """الحصول على إحصائيات التخزين المؤقت"""
        try:
            with self.lock:
                # حساب معدل النجاح
                total_requests = self.stats['hits'] + self.stats['misses']
                hit_rate = (self.stats['hits'] / total_requests * 100) if total_requests > 0 else 0
                
                # معلومات قاعدة البيانات
                with sqlite3.connect(self.db_path) as conn:
                    cursor = conn.execute('SELECT COUNT(*) FROM cache_entries')
                    total_entries = cursor.fetchone()[0]
                
                return {
                    'hits': self.stats['hits'],
                    'misses': self.stats['misses'],
                    'hit_rate': round(hit_rate, 2),
                    'total_entries': total_entries,
                    'memory_usage_mb': round(self.stats['memory_usage'] / (1024 * 1024), 2),
                    'sets': self.stats['sets'],
                    'deletes': self.stats['deletes'],
                    'cleanups': self.stats['cleanups']
                }
                
        except Exception as e:
            logger.error(f"❌ فشل في جمع إحصائيات التخزين المؤقت: {e}")
            return {}

# إنشاء مثيل عام
advanced_cache = AdvancedCacheSystem()
