# نظام جذب المستخدمين المتقدم والتسويق الذكي
import re
import random
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import hashlib
from .logger import logger
from .database import db

class UserEngagementEngine:
    """محرك جذب المستخدمين والتسويق الذكي"""
    
    def __init__(self):
        self.viral_patterns = self._load_viral_patterns()
        self.emotional_triggers = self._load_emotional_triggers()
        self.clickbait_formulas = self._load_clickbait_formulas()
        self.trending_keywords = self._load_trending_keywords()
        
    def _load_viral_patterns(self) -> Dict:
        """تحميل أنماط المحتوى الفيروسي"""
        return {
            'curiosity_gaps': [
                "لن تصدق ما حدث عندما...",
                "السر الذي لا يريدونك أن تعرفه عن...",
                "هذا ما يحدث عندما تجرب...",
                "الحقيقة المذهلة وراء...",
                "ما لا تعرفه عن...",
                "الطريقة السرية لـ...",
                "اكتشف السبب الحقيقي وراء..."
            ],
            'urgency_triggers': [
                "عاجل:",
                "حصري:",
                "لأول مرة:",
                "قبل أن يختفي:",
                "لفترة محدودة:",
                "الآن فقط:",
                "لا تفوت الفرصة:"
            ],
            'social_proof': [
                "الجميع يتحدث عن...",
                "ملايين اللاعبين يفعلون هذا...",
                "اللاعبون المحترفون يستخدمون...",
                "الخبراء ينصحون بـ...",
                "الأكثر شعبية في...",
                "المفضل لدى اللاعبين..."
            ],
            'controversy': [
                "الجدل الذي يهز عالم الألعاب:",
                "لماذا يغضب اللاعبون من...",
                "الحقيقة المثيرة للجدل حول...",
                "الفضيحة التي تهز...",
                "الخلاف الذي يقسم المجتمع حول..."
            ]
        }

    def _load_advanced_viral_templates(self) -> Dict:
        """تحميل قوالب العناوين الفيروسية المتقدمة - محدث 2025"""
        return {
            'curiosity_gap_advanced': [
                "لن تصدق ما حدث في {game_name} - النهاية ستصدمك!",
                "السر الذي لا يريدك مطورو {game_name} أن تعرفه",
                "اكتشف الحقيقة المخفية وراء {game_name} التي أخفوها لسنوات",
                "هذا ما لم يخبروك عنه في {game_name} - والسبب مذهل",
                "الكشف عن أسرار {game_name} التي صدمت حتى المطورين",
                "ما يحدث خلف الكواليس في {game_name} سيغير نظرتك للعبة",
                "اللغز الذي حير لاعبي {game_name} لشهور - أخيراً الحل!"
            ],
            'urgency_fomo': [
                "عاجل: تحديث {game_name} يغير كل شيء - لا تفوته!",
                "قبل أن يختفي للأبد: احصل على هذا في {game_name}",
                "آخر {time_period} للحصول على {item} في {game_name}",
                "تحديث {game_name} ينتهي خلال ساعات - هذا ما ستفقده",
                "إنذار أحمر: {game_name} يواجه أزمة حقيقية",
                "الآن أو أبداً: فرصة {game_name} الذهبية تنتهي قريباً"
            ],
            'social_proof_authority': [
                "ملايين اللاعبين اكتشفوا هذا السر في {game_name}",
                "لماذا يتحدث الجميع عن {game_name}؟ الإجابة هنا",
                "اللاعبون المحترفون يكشفون الاستراتيجية السرية في {game_name}",
                "الطريقة التي يستخدمها 95% من أبطال {game_name}",
                "ما يفعله أفضل لاعبي {game_name} وأنت لا تعرفه",
                "خبراء {game_name} يؤكدون: هذه هي الطريقة الوحيدة للفوز"
            ],
            'numbers_power_lists': [
                "أفضل {number} أسرار في {game_name} لم يكشفها أحد من قبل",
                "{number} أخطاء قاتلة تدمر تجربتك في {game_name}",
                "{number} طرق مثبتة علمياً لتصبح محترفاً في {game_name}",
                "تصنيف أفضل {number} {item_type} في {game_name} - النتائج مفاجئة",
                "{number} حقائق مذهلة عن {game_name} ستغير طريقة لعبك"
            ],
            'how_to_guides': [
                "كيف تصبح أسطورة في {game_name} خلال {time_period}",
                "الطريقة المضمونة لـ {action} في {game_name}",
                "دليل المبتدئين الكامل لـ {game_name} - من الصفر للاحتراف",
                "كيف تحصل على {item} في {game_name} بأسرع طريقة"
            ],
            'exclusive_insider': [
                "حصري: معلومات سرية عن تحديث {game_name} القادم",
                "تسريب مثير: ما يخطط له مطورو {game_name}",
                "مقابلة حصرية مع مبدع {game_name} - كشف أسرار جديدة",
                "وثائق مسربة تكشف مستقبل {game_name}"
            ],
            'comparison_vs': [
                "{game_name} ضد {competitor} - من الأفضل؟",
                "مقارنة شاملة: {game_name} أم {competitor}؟",
                "لماذا {game_name} يتفوق على {competitor} في هذا الجانب",
                "الحرب الملحمية: {game_name} vs {competitor}"
            ]
        }

    def _load_emotional_triggers(self) -> Dict:
        """تحميل محفزات المشاعر"""
        return {
            'excitement': [
                "مذهل", "رائع", "لا يصدق", "مثير", "خرافي", "أسطوري",
                "ملحمي", "استثنائي", "فريد", "مدهش", "ساحر"
            ],
            'fear_of_missing_out': [
                "لا تفوت", "آخر فرصة", "قبل فوات الأوان", "محدود",
                "نادر", "حصري", "سري", "مخفي"
            ],
            'curiosity': [
                "سر", "غموض", "مفاجأة", "كشف", "اكتشاف", "حقيقة",
                "وراء الكواليس", "خفايا", "أسرار"
            ],
            'achievement': [
                "أفضل", "الأول", "الأقوى", "الأسرع", "الأذكى",
                "المتقدم", "المحترف", "الخبير", "الماهر"
            ]
        }
    
    def _load_clickbait_formulas(self) -> List[str]:
        """تحميل صيغ العناوين الجذابة"""
        return [
            "{number} أسرار في {game} لن يخبرك بها أحد",
            "هذا اللاعب اكتشف طريقة {action} في {game} - والنتيجة صادمة!",
            "لن تصدق ما فعله هذا اللاعب في {game}",
            "{game}: الحيلة التي يستخدمها المحترفون ولا يعرفها 99% من اللاعبين",
            "إذا كنت تلعب {game}، فأنت بحاجة لمعرفة هذا فوراً",
            "الخطأ الذي يرتكبه كل لاعبي {game} (وكيف تتجنبه)",
            "{number} علامات تدل على أنك محترف في {game}",
            "لماذا يكره الخبراء هذه الطريقة الجديدة في {game}",
            "الطريقة الوحيدة للفوز في {game} (مجربة ومضمونة)",
            "هل تعلم أن {game} يخفي هذا السر المذهل؟"
        ]
    
    def _load_trending_keywords(self) -> Dict:
        """تحميل الكلمات المفتاحية الرائجة"""
        return {
            'gaming_trends': [
                "battle royale", "metaverse", "NFT games", "cloud gaming",
                "VR gaming", "mobile gaming", "esports", "streaming",
                "indie games", "retro gaming", "speedrun", "modding"
            ],
            'emotional_keywords': [
                "epic", "legendary", "ultimate", "insane", "crazy",
                "mind-blowing", "game-changing", "revolutionary"
            ],
            'action_keywords': [
                "dominate", "master", "conquer", "destroy", "crush",
                "annihilate", "obliterate", "demolish"
            ]
        }
    
    def generate_viral_title(self, base_title: str, game_name: str = "", content_type: str = "news") -> str:
        """توليد عنوان فيروسي جذاب بالقوالب المتقدمة"""
        try:
            # تحليل العنوان الأساسي
            title_analysis = self._analyze_title_potential(base_title)

            # اختيار استراتيجية متقدمة بناءً على نوع المحتوى والاتجاهات الحديثة
            advanced_templates = self._load_advanced_viral_templates()

            if content_type == "guide":
                strategy = "how_to_guides"
            elif content_type == "news":
                strategy = "urgency_fomo"
            elif content_type == "review":
                strategy = "social_proof_authority"
            elif "vs" in base_title.lower() or "مقابل" in base_title:
                strategy = "comparison_vs"
            elif any(word in base_title.lower() for word in ["حصري", "تسريب", "سر"]):
                strategy = "exclusive_insider"
            elif any(num in base_title for num in ["5", "10", "أفضل", "أسوأ"]):
                strategy = "numbers_power_lists"
            else:
                strategy = "curiosity_gap_advanced"
            
            # تطبيق الاستراتيجية
            viral_title = self._apply_viral_strategy(base_title, strategy, game_name)
            
            # إضافة محفزات عاطفية
            enhanced_title = self._add_emotional_triggers(viral_title)
            
            # تحسين للـ SEO
            seo_optimized = self._optimize_for_seo(enhanced_title, game_name)
            
            # التحقق من الطول المناسب
            final_title = self._optimize_title_length(seo_optimized)
            
            logger.info(f"🎯 تم توليد عنوان فيروسي: {final_title}")
            
            return final_title

        except Exception as e:
            logger.error("❌ فشل في توليد العنوان الفيروسي", e)
            return self._create_fallback_viral_title(base_title, game_name)

    def _create_fallback_viral_title(self, base_title: str, game_name: str) -> str:
        """إنشاء عنوان فيروسي احتياطي"""
        try:
            fallback_templates = [
                f"🔥 {base_title} - لن تصدق ما حدث!",
                f"⚡ عاجل: {base_title}",
                f"🎮 {game_name}: {base_title} - الحقيقة الكاملة",
                f"💥 {base_title} - هذا ما لم تعرفه!",
                f"🚨 {base_title} - تطورات مثيرة!"
            ]

            return random.choice(fallback_templates)

        except Exception:
            return base_title

    def _customize_advanced_title(self, template: str, original_title: str, game_name: str, content_type: str) -> str:
        """تخصيص القالب المتقدم بمعلومات ذكية"""
        try:
            # استخراج معلومات من العنوان الأصلي
            extracted_info = self._extract_title_info(original_title)

            # تخصيص القالب
            customized = template.format(
                game_name=game_name or extracted_info.get('game_name', 'اللعبة'),
                number=extracted_info.get('number', random.choice(['5', '7', '10', '15'])),
                time_period=random.choice(['أسبوع', 'شهر', '24 ساعة', 'يومين']),
                item=extracted_info.get('item', 'العنصر السري'),
                action=extracted_info.get('action', 'الفوز'),
                competitor=self._get_competitor_game(game_name),
                item_type=extracted_info.get('item_type', 'الأسلحة')
            )

            return customized

        except Exception as e:
            logger.error("❌ فشل في تخصيص القالب المتقدم", e)
            return template

    def _extract_title_info(self, title: str) -> Dict:
        """استخراج معلومات من العنوان الأصلي"""
        info = {}

        # استخراج الأرقام
        numbers = re.findall(r'\d+', title)
        if numbers:
            info['number'] = numbers[0]

        # استخراج أسماء الألعاب الشائعة
        game_patterns = [
            r'\b(Minecraft|Fortnite|Call of Duty|FIFA|GTA|Zelda|Mario|Pokemon)\b',
            r'\b([A-Z][a-zA-Z\s]+(?:of|the|and|&)\s+[A-Z][a-zA-Z\s]*)\b'
        ]

        for pattern in game_patterns:
            match = re.search(pattern, title, re.IGNORECASE)
            if match:
                info['game_name'] = match.group(1)
                break

        # استخراج أنواع العناصر
        if any(word in title.lower() for word in ['weapon', 'سلاح', 'أسلحة']):
            info['item_type'] = 'الأسلحة'
        elif any(word in title.lower() for word in ['character', 'شخصية', 'شخصيات']):
            info['item_type'] = 'الشخصيات'
        elif any(word in title.lower() for word in ['map', 'خريطة', 'خرائط']):
            info['item_type'] = 'الخرائط'

        # استخراج الأفعال
        if any(word in title.lower() for word in ['win', 'فوز', 'انتصار']):
            info['action'] = 'الفوز'
        elif any(word in title.lower() for word in ['build', 'بناء', 'إنشاء']):
            info['action'] = 'البناء'
        elif any(word in title.lower() for word in ['fight', 'قتال', 'محاربة']):
            info['action'] = 'القتال'

        return info

    def _get_competitor_game(self, game_name: str) -> str:
        """الحصول على لعبة منافسة"""
        competitors = {
            'Minecraft': 'Roblox',
            'Fortnite': 'PUBG',
            'Call of Duty': 'Battlefield',
            'FIFA': 'PES',
            'League of Legends': 'Dota 2',
            'Overwatch': 'Valorant'
        }

        return competitors.get(game_name, 'اللعبة المنافسة')

    def _optimize_title_for_virality(self, title: str, game_name: str) -> str:
        """تحسين العنوان للانتشار الفيروسي"""
        try:
            # إضافة رموز تعبيرية جذابة
            emoji_map = {
                'عاجل': '🚨',
                'حصري': '🔥',
                'سر': '🤫',
                'مذهل': '😱',
                'جديد': '✨',
                'أفضل': '🏆',
                'خطير': '⚠️',
                'مثير': '💥'
            }

            optimized = title
            for word, emoji in emoji_map.items():
                if word in optimized and emoji not in optimized:
                    optimized = optimized.replace(word, f"{emoji} {word}")
                    break

            # تحسين للكلمات المفتاحية
            if game_name and game_name not in optimized:
                optimized = f"{game_name}: {optimized}"

            return optimized

        except Exception as e:
            logger.error("❌ فشل في تحسين العنوان للانتشار", e)
            return title

    def _add_engagement_boosters(self, title: str, content_type: str) -> str:
        """إضافة معززات التفاعل النهائية"""
        try:
            boosters = {
                'news': ['🔥', '⚡', '🚨'],
                'guide': ['📖', '🎯', '💡'],
                'review': ['⭐', '🎮', '👍'],
                'update': ['🆕', '🔄', '📢']
            }

            emoji = random.choice(boosters.get(content_type, ['🎮']))

            # إضافة الرمز إذا لم يكن موجوداً
            if not any(e in title for e in ['🔥', '⚡', '🚨', '📖', '🎯', '💡', '⭐', '🎮', '👍', '🆕', '🔄', '📢']):
                title = f"{emoji} {title}"

            return title

        except Exception as e:
            logger.error("❌ فشل في إضافة معززات التفاعل", e)
            return title
    
    def _analyze_title_potential(self, title: str) -> Dict:
        """تحليل إمكانية العنوان للانتشار"""
        analysis = {
            'emotional_score': 0,
            'curiosity_score': 0,
            'urgency_score': 0,
            'clickability_score': 0
        }
        
        title_lower = title.lower()
        
        # تحليل المشاعر
        for emotion, words in self.emotional_triggers.items():
            for word in words:
                if word in title_lower:
                    analysis['emotional_score'] += 10
        
        # تحليل الفضول
        curiosity_indicators = ['كيف', 'لماذا', 'ماذا', 'أين', 'متى', 'سر', 'خفي']
        for indicator in curiosity_indicators:
            if indicator in title_lower:
                analysis['curiosity_score'] += 15
        
        # تحليل الإلحاح
        urgency_indicators = ['عاجل', 'جديد', 'حصري', 'الآن', 'سريع']
        for indicator in urgency_indicators:
            if indicator in title_lower:
                analysis['urgency_score'] += 12
        
        # حساب النقاط الإجمالية
        analysis['clickability_score'] = (
            analysis['emotional_score'] + 
            analysis['curiosity_score'] + 
            analysis['urgency_score']
        )
        
        return analysis
    
    def _apply_viral_strategy(self, title: str, strategy: str, game_name: str) -> str:
        """تطبيق استراتيجية الانتشار الفيروسي"""
        patterns = self.viral_patterns.get(strategy, [])
        if not patterns:
            return title
        
        pattern = random.choice(patterns)
        
        # تطبيق النمط على العنوان
        if "{game}" in pattern and game_name:
            enhanced_title = pattern.replace("{game}", game_name)
        elif strategy == "curiosity_gaps":
            enhanced_title = f"{pattern} {title}"
        elif strategy == "urgency_triggers":
            enhanced_title = f"{pattern} {title}"
        else:
            enhanced_title = f"{pattern} {title}"
        
        return enhanced_title
    
    def _add_emotional_triggers(self, title: str) -> str:
        """إضافة محفزات عاطفية للعنوان"""
        # اختيار محفز عاطفي عشوائي
        emotion_type = random.choice(list(self.emotional_triggers.keys()))
        trigger_words = self.emotional_triggers[emotion_type]
        
        # إضافة كلمة محفزة إذا لم تكن موجودة
        title_lower = title.lower()
        has_trigger = any(word in title_lower for word in trigger_words)
        
        if not has_trigger:
            trigger_word = random.choice(trigger_words)
            # إدراج الكلمة في مكان مناسب
            if emotion_type == 'excitement':
                title = f"{trigger_word}: {title}"
            else:
                title = f"{title} ({trigger_word})"
        
        return title
    
    def _optimize_for_seo(self, title: str, game_name: str) -> str:
        """تحسين العنوان لمحركات البحث"""
        # إضافة كلمات مفتاحية رائجة
        trending = random.choice(self.trending_keywords['gaming_trends'])
        
        if game_name and game_name.lower() not in title.lower():
            title = f"{title} - {game_name}"
        
        # إضافة سنة للحداثة
        current_year = datetime.now().year
        if str(current_year) not in title:
            title = f"{title} {current_year}"
        
        return title
    
    def _optimize_title_length(self, title: str) -> str:
        """تحسين طول العنوان للمنصات المختلفة"""
        # استخدام الحد الأقصى الجديد من الإعدادات
        from config.settings import SEOConfig
        max_length = SEOConfig.TITLE_LENGTH_MAX

        if len(title) > max_length:
            # اختصار العنوان مع الحفاظ على المعنى - بدون نقاط
            words = title.split()
            shortened = ""
            for word in words:
                if len(shortened + word + " ") <= max_length:
                    shortened += word + " "
                else:
                    break
            title = shortened.strip()  # إزالة النقاط الثلاث

        return title
    
    def generate_engaging_content(self, base_content: str, target_audience: str = "gamers") -> str:
        """توليد محتوى جذاب ومثير للاهتمام"""
        try:
            # تحليل المحتوى الأساسي
            content_analysis = self._analyze_content_engagement(base_content)
            
            # إضافة عناصر التفاعل
            enhanced_content = self._add_engagement_elements(base_content)
            
            # إضافة قصص شخصية
            storytelling_content = self._add_storytelling_elements(enhanced_content)
            
            # إضافة دعوات للعمل
            final_content = self._add_strategic_ctas(storytelling_content)
            
            logger.info("✨ تم تحسين المحتوى للتفاعل")
            
            return final_content
            
        except Exception as e:
            logger.error("❌ فشل في تحسين المحتوى للتفاعل", e)
            return base_content
    
    def _analyze_content_engagement(self, content: str) -> Dict:
        """تحليل مستوى التفاعل في المحتوى"""
        analysis = {
            'readability_score': 0,
            'emotional_intensity': 0,
            'interaction_potential': 0,
            'shareability_score': 0
        }
        
        # تحليل سهولة القراءة
        sentences = content.split('.')
        words = content.split()
        avg_sentence_length = len(words) / len(sentences) if sentences else 0
        
        if avg_sentence_length <= 15:
            analysis['readability_score'] = 90
        elif avg_sentence_length <= 20:
            analysis['readability_score'] = 70
        else:
            analysis['readability_score'] = 50
        
        # تحليل الكثافة العاطفية
        emotional_words = 0
        for emotion_list in self.emotional_triggers.values():
            for word in emotion_list:
                emotional_words += content.lower().count(word)
        
        analysis['emotional_intensity'] = min(100, emotional_words * 10)
        
        # تحليل إمكانية التفاعل
        interaction_indicators = ['؟', 'رأيك', 'تجربة', 'شارك', 'أخبرنا']
        interaction_score = 0
        for indicator in interaction_indicators:
            interaction_score += content.count(indicator) * 15
        
        analysis['interaction_potential'] = min(100, interaction_score)
        
        # حساب قابلية المشاركة
        analysis['shareability_score'] = (
            analysis['readability_score'] * 0.3 +
            analysis['emotional_intensity'] * 0.4 +
            analysis['interaction_potential'] * 0.3
        )
        
        return analysis
    
    def _add_engagement_elements(self, content: str) -> str:
        """إضافة عناصر التفاعل للمحتوى"""
        engagement_elements = [
            "\n💡 **نصيحة من الخبراء**: ",
            "\n🎮 **تجربة شخصية**: ",
            "\n⚡ **حقيقة مثيرة**: ",
            "\n🔥 **الأكثر إثارة**: ",
            "\n💎 **سر المحترفين**: "
        ]
        
        # إضافة عنصر تفاعل عشوائي
        element = random.choice(engagement_elements)
        
        # العثور على مكان مناسب للإدراج
        paragraphs = content.split('\n\n')
        if len(paragraphs) > 2:
            insert_position = len(paragraphs) // 2
            paragraphs.insert(insert_position, element + "هذا ما يميز هذا المحتوى عن غيره!")
            content = '\n\n'.join(paragraphs)
        
        return content
    
    def _add_storytelling_elements(self, content: str) -> str:
        """إضافة عناصر السرد للمحتوى"""
        story_starters = [
            "تخيل أنك تلعب لساعات طويلة وفجأة...",
            "في إحدى الليالي، كان أحد اللاعبين يستكشف عندما اكتشف...",
            "قبل أسبوع، حدث شيء غير متوقع في عالم الألعاب...",
            "هل تساءلت يوماً ماذا يحدث عندما...",
            "في تجربة حقيقية، واجه أحد اللاعبين موقفاً..."
        ]
        
        # إضافة بداية قصة
        story_start = random.choice(story_starters)
        content = f"{story_start}\n\n{content}"
        
        return content
    
    def _add_strategic_ctas(self, content: str) -> str:
        """إضافة دعوات استراتيجية للعمل"""
        ctas = [
            "\n\n🎯 **شاركنا تجربتك**: ما رأيك في هذا التحديث؟ اكتب تعليقك أدناه!",
            "\n\n💬 **انضم للنقاش**: هل جربت هذه الطريقة؟ أخبرنا بالنتائج!",
            "\n\n🔔 **لا تفوت الجديد**: تابعنا للحصول على آخر الأخبار والحيل!",
            "\n\n⭐ **قيم المقال**: هل كان مفيداً؟ شاركه مع أصدقائك اللاعبين!",
            "\n\n🎮 **تحدي الأصدقاء**: جرب هذه النصائح وتحدى أصدقاءك!"
        ]
        
        # إضافة دعوة للعمل في النهاية
        cta = random.choice(ctas)
        content += cta
        
        return content
    
    def calculate_viral_potential(self, title: str, content: str) -> Dict:
        """حساب إمكانية الانتشار الفيروسي"""
        try:
            title_analysis = self._analyze_title_potential(title)
            content_analysis = self._analyze_content_engagement(content)
            
            # حساب النقاط الإجمالية
            viral_score = (
                title_analysis['clickability_score'] * 0.4 +
                content_analysis['shareability_score'] * 0.6
            )
            
            # تصنيف الإمكانية
            if viral_score >= 80:
                potential_level = "عالي جداً"
            elif viral_score >= 60:
                potential_level = "عالي"
            elif viral_score >= 40:
                potential_level = "متوسط"
            else:
                potential_level = "منخفض"
            
            return {
                'viral_score': round(viral_score, 2),
                'potential_level': potential_level,
                'title_analysis': title_analysis,
                'content_analysis': content_analysis,
                'recommendations': self._generate_improvement_recommendations(viral_score, title_analysis, content_analysis)
            }
            
        except Exception as e:
            logger.error("❌ فشل في حساب إمكانية الانتشار", e)
            return {'viral_score': 0, 'potential_level': 'غير محدد'}
    
    def _generate_improvement_recommendations(self, viral_score: float, title_analysis: Dict, content_analysis: Dict) -> List[str]:
        """توليد توصيات لتحسين الانتشار"""
        recommendations = []
        
        if title_analysis['emotional_score'] < 30:
            recommendations.append("أضف كلمات عاطفية أكثر للعنوان")
        
        if title_analysis['curiosity_score'] < 20:
            recommendations.append("استخدم عناصر الفضول والغموض في العنوان")
        
        if content_analysis['interaction_potential'] < 40:
            recommendations.append("أضف المزيد من الأسئلة ودعوات التفاعل")
        
        if content_analysis['emotional_intensity'] < 50:
            recommendations.append("استخدم لغة أكثر حماساً وإثارة")
        
        if viral_score < 60:
            recommendations.append("فكر في إضافة قصة شخصية أو تجربة حقيقية")
            recommendations.append("استخدم أرقام وإحصائيات مثيرة")
        
        return recommendations

# إنشاء مثيل عام لمحرك جذب المستخدمين
engagement_engine = UserEngagementEngine()
