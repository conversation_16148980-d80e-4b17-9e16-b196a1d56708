# 🚀 دليل البدء السريع - GitHub Uploader

## التثبيت السريع

```bash
# 1. استنساخ المشروع
git clone https://github.com/yourusername/github-uploader.git
cd github-uploader

# 2. تثبيت المتطلبات
pip install -r requirements.txt

# 3. تشغيل الأداة
python github_uploader.py
```

## إعداد GitHub Token

1. اذهب إلى: https://github.com/settings/tokens
2. انقر "Generate new token"
3. اختر الصلاحيات: `repo` و `user`
4. انسخ الـ token

## الاستخدام السريع

### رفع مشروع جديد:
```bash
python github_uploader.py
# اختر الخيار 1
# أدخل مسار المشروع
# أدخل اسم المستودع
# اتبع التعليمات
```

### استخدام برمجي:
```python
from github_uploader import GitHubUploader

uploader = GitHubUploader("your_token")
uploader.upload_project(
    project_path="./my_project",
    repo_name="awesome-project",
    description="مشروع رائع"
)
```

## أمثلة سريعة

### رفع بوت Python:
```python
uploader.upload_project(
    project_path="./telegram_bot",
    repo_name="my-telegram-bot",
    description="بوت Telegram ذكي",
    private=True
)
```

### رفع مشروع JavaScript:
```python
uploader.upload_project(
    project_path="./react_app",
    repo_name="my-react-app", 
    description="تطبيق React حديث"
)
```

## نصائح سريعة

✅ **تأكد من وجود Git على النظام**
✅ **احتفظ بـ GitHub Token في مكان آمن**
✅ **استخدم أسماء واضحة للمستودعات**
✅ **أضف وصف مفيد لكل مشروع**

## استكشاف الأخطاء

| المشكلة | الحل |
|---------|------|
| خطأ في Token | تحقق من صحة الـ token والصلاحيات |
| خطأ في Git | تأكد من تثبيت Git وإعداده |
| مجلد غير موجود | تحقق من مسار المشروع |
| فشل الرفع | تحقق من الاتصال بالإنترنت |

## الدعم السريع

- 📧 البريد: <EMAIL>
- 🐛 المشاكل: [GitHub Issues](https://github.com/yourusername/github-uploader/issues)
- 📖 التوثيق: [README.md](README.md)

---
**بدء سريع في 3 دقائق! 🚀**
