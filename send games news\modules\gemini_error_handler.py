#!/usr/bin/env python3
"""
معالج أخطاء محسن لـ Gemini API
"""

import logging
from typing import Optional, Dict, Any
from modules.logger import logger

class GeminiErrorHandler:
    """معالج أخطاء متقدم لـ Gemini API"""
    
    def __init__(self, api_manager):
        self.api_manager = api_manager
        self.error_counts = {}
        self.max_retries = 3
    
    def handle_api_error(self, error: Exception, current_key: str) -> Optional[str]:
        """معالجة أخطاء API وإرجاع مفتاح بديل"""
        try:
            error_str = str(error).lower()
            
            # تحديد نوع الخطأ
            if any(keyword in error_str for keyword in ['permission_denied', 'blacklisted', 'invalid']):
                logger.warning(f"🚫 مفتاح محظور أو غير صالح: {current_key[:10]}...")
                
                # وضع المفتاح في القائمة السوداء
                if hasattr(self.api_manager, 'mark_key_failed'):
                    self.api_manager.mark_key_failed(current_key)
                else:
                    # استخدام rotate_key كبديل
                    self.api_manager.rotate_key()
                
                # الحصول على مفتاح جديد
                try:
                    new_key = self.api_manager.get_key()
                    logger.info(f"🔄 التبديل إلى مفتاح جديد: {new_key[:10]}...")
                    return new_key
                except Exception as e:
                    logger.error(f"❌ فشل في الحصول على مفتاح بديل: {e}")
                    return None
            
            elif 'rate_limit' in error_str or '429' in error_str:
                logger.warning(f"⏰ تم تجاوز حد الاستخدام للمفتاح: {current_key[:10]}...")
                
                # تأخير مؤقت للمفتاح
                if hasattr(self.api_manager, 'mark_key_failed'):
                    self.api_manager.mark_key_failed(current_key)
                
                # محاولة مفتاح آخر
                try:
                    new_key = self.api_manager.get_key()
                    return new_key
                except Exception:
                    return None
            
            else:
                logger.warning(f"⚠️ خطأ غير معروف في Gemini API: {error}")
                return current_key  # الاحتفاظ بنفس المفتاح
                
        except Exception as e:
            logger.error(f"❌ خطأ في معالج الأخطاء: {e}")
            return None

# إنشاء مثيل عام
gemini_error_handler = None

def get_gemini_error_handler():
    """الحصول على معالج الأخطاء"""
    global gemini_error_handler
    
    if gemini_error_handler is None:
        try:
            from config.settings import google_api_manager
            if google_api_manager:
                gemini_error_handler = GeminiErrorHandler(google_api_manager)
        except Exception as e:
            logger.error(f"❌ فشل في إنشاء معالج أخطاء Gemini: {e}")
    
    return gemini_error_handler
