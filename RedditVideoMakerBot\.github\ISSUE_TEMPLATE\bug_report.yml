name: Bug Report
title: "[Bug]: "
labels: bug
description: Report broken or incorrect behaviour
body:
  - type: markdown
    attributes:
      value: >
        Thanks for taking the time to fill out a bug.
        Please note that this form is for bugs only!
  - type: textarea
    id: what-happened
    attributes:
      label: Describe the bug
      description: A clear and concise description of what the bug is.
    validations:
      required: true
  - type: textarea
    attributes:
      label: Reproduction Steps
      description: >
         What you did to make it happen.
    validations:
      required: true
  - type: textarea
    attributes:
      label: Expected behavior
      description: >
         A clear and concise description of what you expected to happen.
    validations:
      required: true
  - type: textarea
    attributes:
      label: Screenshots
      description: >
         If applicable, add screenshots to help explain your problem.
    validations:
      required: false
  - type: textarea
    attributes:
      label: System Information
      description: please fill your system informations
      value: >
        Operating System : [e.g. Windows 11]

        Python version : [e.g. Python 3.6]

        App version / Branch : [e.g. latest, V2.0, master, develop]
    validations:
      required: true
  - type: checkboxes
    attributes:
      label: Checklist
      description: >
        Let's make sure you've properly done due diligence when reporting this issue!
      options:
        - label: I have searched the open issues for duplicates.
          required: true
        - label: I have shown the entire traceback, if possible.
          required: true
  - type: textarea
    attributes:
      label: Additional Context
      description: Add any other context about the problem here.
