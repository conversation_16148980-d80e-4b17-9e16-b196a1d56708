#!/usr/bin/env python3
"""
اختبار مبسط لترتيب أولوية محركات TTS
"""

import os
import sys

# إعداد المسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_config_file():
    """اختبار ملف config.toml مباشرة"""
    try:
        print("🔍 فحص ملف config.toml...")
        
        with open("config.toml", 'r', encoding='utf-8') as f:
            content = f.read()
        
        # البحث عن voice_choice
        voice_choice_line = None
        primary_order_line = None
        
        for line_num, line in enumerate(content.split('\n'), 1):
            if 'voice_choice' in line and '=' in line:
                voice_choice_line = line.strip()
                print(f"📝 السطر {line_num}: {voice_choice_line}")
            
            if 'primary = [' in line:
                primary_order_line = line.strip()
                print(f"📝 السطر {line_num}: {primary_order_line}")
        
        # التحقق من voice_choice
        if voice_choice_line and 'voice_choice = "auto"' in voice_choice_line:
            print("✅ voice_choice = 'auto' - صحيح!")
            voice_choice_ok = True
        else:
            print(f"❌ voice_choice غير صحيح: {voice_choice_line}")
            voice_choice_ok = False
        
        # التحقق من primary_order
        if primary_order_line and '"GoogleTranslate"' in primary_order_line:
            # التحقق من أن GoogleTranslate أولاً
            if primary_order_line.find('"GoogleTranslate"') < primary_order_line.find('"ElevenLabs"'):
                print("✅ GoogleTranslate قبل ElevenLabs في الترتيب - صحيح!")
                order_ok = True
            else:
                print("❌ ElevenLabs قبل GoogleTranslate في الترتيب")
                order_ok = False
        else:
            print(f"❌ primary_order غير صحيح: {primary_order_line}")
            order_ok = False
        
        return voice_choice_ok and order_ok
        
    except Exception as e:
        print(f"❌ خطأ في فحص config.toml: {e}")
        return False

def test_import_modules():
    """اختبار استيراد الوحدات الأساسية"""
    try:
        print("\n🔍 اختبار استيراد الوحدات...")
        
        # اختبار utils.settings
        try:
            from utils import settings
            print("✅ تم استيراد utils.settings")
            settings_ok = True
        except Exception as e:
            print(f"❌ فشل استيراد utils.settings: {e}")
            settings_ok = False
        
        # اختبار TTS.elevenlabs
        try:
            from TTS.elevenlabs import elevenlabs
            print("✅ تم استيراد TTS.elevenlabs")
            elevenlabs_ok = True
        except Exception as e:
            print(f"❌ فشل استيراد TTS.elevenlabs: {e}")
            elevenlabs_ok = False
        
        # اختبار TTS.smart_tts_manager
        try:
            from TTS.smart_tts_manager import SmartTTSManager
            print("✅ تم استيراد TTS.smart_tts_manager")
            smart_tts_ok = True
        except Exception as e:
            print(f"❌ فشل استيراد TTS.smart_tts_manager: {e}")
            smart_tts_ok = False
        
        return settings_ok and elevenlabs_ok and smart_tts_ok
        
    except Exception as e:
        print(f"❌ خطأ عام في الاستيراد: {e}")
        return False

def test_priority_logic():
    """اختبار منطق ترتيب الأولوية"""
    try:
        print("\n🔍 اختبار منطق ترتيب الأولوية...")
        
        # محاكاة الكود في smart_tts_manager
        def simulate_priority_logic():
            # محاكاة الإعدادات
            voice_choice = "auto"  # القيمة الجديدة
            primary_order = ["GoogleTranslate", "AWSPolly", "pyttsx", "ElevenLabs"]
            
            if voice_choice == "auto":
                priority_engines = primary_order.copy()
            else:
                chosen_engine = voice_choice
                priority_engines = [chosen_engine]
                other_engines = [engine for engine in primary_order if engine != chosen_engine]
                priority_engines.extend(other_engines)
            
            return priority_engines
        
        result = simulate_priority_logic()
        print(f"📋 ترتيب الأولوية المحاكي: {result}")
        
        # التحقق من النتيجة
        if result[0] == "GoogleTranslate":
            print("✅ GoogleTranslate أولاً - صحيح!")
            first_ok = True
        else:
            print(f"❌ المحرك الأول هو {result[0]} بدلاً من GoogleTranslate")
            first_ok = False
        
        if result[-1] == "ElevenLabs":
            print("✅ ElevenLabs أخيراً - صحيح!")
            last_ok = True
        else:
            print(f"❌ المحرك الأخير هو {result[-1]} بدلاً من ElevenLabs")
            last_ok = False
        
        return first_ok and last_ok
        
    except Exception as e:
        print(f"❌ خطأ في اختبار منطق الأولوية: {e}")
        return False

def main():
    """تشغيل الاختبارات المبسطة"""
    print("🚀 اختبار مبسط لترتيب أولوية محركات TTS...")
    print("=" * 60)
    
    tests = [
        ("فحص ملف config.toml", test_config_file),
        ("استيراد الوحدات", test_import_modules),
        ("منطق ترتيب الأولوية", test_priority_logic),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 40)
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} - خطأ غير متوقع: {e}")
            results.append((test_name, False))
    
    # تلخيص النتائج
    print("\n" + "=" * 60)
    print("📊 ملخص نتائج الاختبارات:")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n📈 النتيجة النهائية: {passed}/{len(results)} اختبارات نجحت")
    
    if passed >= 2:  # على الأقل 2 من 3
        print("🎉 تم تعديل ترتيب الأولوية بنجاح!")
        print("\n💡 الترتيب الجديد:")
        print("   1️⃣ GoogleTranslate أولاً (مجاني وسريع)")
        print("   2️⃣ AWSPolly ثانياً")
        print("   3️⃣ pyttsx ثالثاً") 
        print("   4️⃣ ElevenLabs أخيراً (عند الحاجة)")
        print("\n🚀 يمكنك تشغيل النظام الآن:")
        print("   python main.py")
        print("\n📝 ستلاحظ في السجلات:")
        print("   🎯 ترتيب أولوية المحركات: ['GoogleTranslate', 'AWSPolly', 'pyttsx', 'ElevenLabs']")
    else:
        print("⚠️ بعض الإعدادات تحتاج مراجعة إضافية")
    
    return passed >= 2

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
