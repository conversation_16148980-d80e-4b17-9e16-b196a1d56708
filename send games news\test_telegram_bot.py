#!/usr/bin/env python3
"""
اختبار سريع لبوت تيليجرام
"""

import sys
import os

# إضافة المسار الحالي لـ Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """اختبار استيراد الوحدات"""
    print("🔍 اختبار استيراد الوحدات...")
    
    try:
        from modules.telegram_api_manager import telegram_api_manager
        print("✅ تم استيراد telegram_api_manager")
        
        from modules.telegram_agent_controller import telegram_agent_controller
        print("✅ تم استيراد telegram_agent_controller")
        
        from modules.enhanced_telegram_bot import enhanced_telegram_bot
        print("✅ تم استيراد enhanced_telegram_bot")
        
        from config.settings import BotConfig
        print("✅ تم استيراد BotConfig")
        
        return True
    except Exception as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        return False

def test_api_manager():
    """اختبار مدير API Keys"""
    print("\n🔑 اختبار مدير API Keys...")
    
    try:
        from modules.telegram_api_manager import telegram_api_manager
        
        # اختبار الحصول على المفاتيح
        keys = telegram_api_manager.get_all_keys()
        print(f"✅ تم العثور على {len(keys)} مفتاح API")
        
        # اختبار الحصول على الخدمات
        services = telegram_api_manager.get_services_list()
        print(f"✅ تم العثور على {len(services)} خدمة")
        
        # اختبار الإحصائيات
        stats = telegram_api_manager.get_usage_stats()
        print(f"✅ إحصائيات: {stats['total_keys']} مفتاح، {stats['active_keys']} نشط")
        
        return True
    except Exception as e:
        print(f"❌ خطأ في مدير API Keys: {e}")
        return False

def test_agent_controller():
    """اختبار تحكم الوكيل"""
    print("\n🤖 اختبار تحكم الوكيل...")
    
    try:
        from modules.telegram_agent_controller import telegram_agent_controller
        
        # اختبار الحصول على الحالة
        status = telegram_agent_controller.get_agent_status()
        print(f"✅ حالة الوكيل: {status['status']}")
        
        # اختبار فحص صحة النظام
        health = telegram_agent_controller.get_system_health()
        print(f"✅ صحة النظام: {health['overall']}")
        
        # اختبار السجلات
        logs = telegram_agent_controller.get_recent_logs(5)
        print(f"✅ تم الحصول على {len(logs)} سجل")
        
        return True
    except Exception as e:
        print(f"❌ خطأ في تحكم الوكيل: {e}")
        return False

def test_bot_config():
    """اختبار إعدادات البوت"""
    print("\n⚙️ اختبار إعدادات البوت...")
    
    try:
        from config.settings import BotConfig
        
        # اختبار التوكن
        if BotConfig.TELEGRAM_BOT_TOKEN:
            print("✅ توكن تيليجرام موجود")
        else:
            print("⚠️ توكن تيليجرام غير موجود")
        
        # اختبار معرف المدير
        if BotConfig.TELEGRAM_ADMIN_ID:
            print(f"✅ معرف المدير: {BotConfig.TELEGRAM_ADMIN_ID}")
        else:
            print("⚠️ معرف المدير غير محدد")
        
        # اختبار مفاتيح أخرى
        if BotConfig.GEMINI_API_KEY:
            print("✅ مفتاح Gemini موجود")
        else:
            print("⚠️ مفتاح Gemini غير موجود")
        
        return True
    except Exception as e:
        print(f"❌ خطأ في إعدادات البوت: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء اختبار نظام بوت تيليجرام")
    print("=" * 50)
    
    tests = [
        ("استيراد الوحدات", test_imports),
        ("مدير API Keys", test_api_manager),
        ("تحكم الوكيل", test_agent_controller),
        ("إعدادات البوت", test_bot_config)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 اختبار: {test_name}")
        if test_func():
            passed += 1
            print(f"✅ نجح اختبار: {test_name}")
        else:
            print(f"❌ فشل اختبار: {test_name}")
    
    print("\n" + "=" * 50)
    print(f"📊 النتائج: {passed}/{total} اختبارات نجحت")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! النظام جاهز للتشغيل")
        print("\n🚀 لتشغيل البوت:")
        print("python run_telegram_bot.py")
    else:
        print("⚠️ بعض الاختبارات فشلت. راجع الأخطاء أعلاه")
    
    return passed == total

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n👋 تم إيقاف الاختبار")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ خطأ عام في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
