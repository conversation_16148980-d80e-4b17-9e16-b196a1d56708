#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع للإصلاحات
"""

import sys
import os

# إضافة المسار الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """اختبار الاستيرادات الأساسية"""
    print("🧪 اختبار الاستيرادات...")
    
    try:
        import random
        print("✅ random: تم الاستيراد بنجاح")
    except Exception as e:
        print(f"❌ random: {e}")
        return False
    
    try:
        from modules.analytics import analytics
        print("✅ modules.analytics: تم الاستيراد بنجاح")
    except Exception as e:
        print(f"❌ modules.analytics: {e}")
        return False
    
    try:
        from modules.api_integrations import APIIntegrationManager
        print("✅ modules.api_integrations: تم الاستيراد بنجاح")
    except Exception as e:
        print(f"❌ modules.api_integrations: {e}")
        return False
    
    try:
        from modules.performance_monitor import PerformanceMonitor
        print("✅ modules.performance_monitor: تم الاستيراد بنجاح")
    except Exception as e:
        print(f"❌ modules.performance_monitor: {e}")
        return False
    
    return True

def test_analytics_summary():
    """اختبار دالة get_analytics_summary"""
    print("\n🧪 اختبار get_analytics_summary...")
    
    try:
        from modules.analytics import analytics
        
        # اختبار الدالة
        summary = analytics.get_analytics_summary(7)
        
        # فحص المفاتيح المطلوبة
        required_keys = ['period_days', 'total_articles', 'avg_views', 'engagement_rate']
        has_all_keys = all(key in summary for key in required_keys)
        
        if has_all_keys:
            print("✅ get_analytics_summary: يعمل بشكل صحيح")
            print(f"   📊 البيانات: {summary}")
            return True
        else:
            print("❌ get_analytics_summary: مفاتيح مفقودة")
            return False
            
    except Exception as e:
        print(f"❌ get_analytics_summary: {e}")
        return False

def test_random_usage():
    """اختبار استخدام random في الوحدات"""
    print("\n🧪 اختبار استخدام random...")
    
    try:
        from modules.api_integrations import KeywordResearchAPI, APIIntegrationManager
        
        api_manager = APIIntegrationManager()
        keyword_api = KeywordResearchAPI(api_manager)
        
        # اختبار دالة تستخدم random
        volume = keyword_api._estimate_search_volume("gaming")
        
        if isinstance(volume, int) and volume > 0:
            print(f"✅ random في api_integrations: يعمل بشكل صحيح (حجم البحث: {volume})")
            return True
        else:
            print("❌ random في api_integrations: نتيجة غير صحيحة")
            return False
            
    except Exception as e:
        print(f"❌ random في api_integrations: {e}")
        return False

def test_performance_monitor_random():
    """اختبار random في performance_monitor"""
    print("\n🧪 اختبار random في performance_monitor...")
    
    try:
        from modules.performance_monitor import PerformanceMonitor
        
        monitor = PerformanceMonitor()
        
        # اختبار الدوال
        ranking = monitor._simulate_ranking_position()
        volume = monitor._estimate_search_volume("gaming")
        difficulty = monitor._estimate_difficulty("gaming")
        trend = monitor._simulate_trend()
        
        # فحص النتائج
        valid_ranking = isinstance(ranking, int) and 1 <= ranking <= 100
        valid_volume = isinstance(volume, int) and volume > 0
        valid_difficulty = isinstance(difficulty, int) and 30 <= difficulty <= 80
        valid_trend = trend in ['up', 'down', 'stable']
        
        if all([valid_ranking, valid_volume, valid_difficulty, valid_trend]):
            print(f"✅ random في performance_monitor: يعمل بشكل صحيح")
            print(f"   📊 ترتيب: {ranking}, حجم: {volume}, صعوبة: {difficulty}, اتجاه: {trend}")
            return True
        else:
            print("❌ random في performance_monitor: نتائج غير صحيحة")
            return False
            
    except Exception as e:
        print(f"❌ random في performance_monitor: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء اختبار الإصلاحات السريع...")
    print("=" * 50)
    
    tests = [
        ("الاستيرادات الأساسية", test_imports),
        ("دالة get_analytics_summary", test_analytics_summary),
        ("random في api_integrations", test_random_usage),
        ("random في performance_monitor", test_performance_monitor_random),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}: خطأ غير متوقع - {e}")
            results.append((test_name, False))
    
    # تقرير النتائج
    print("\n" + "=" * 50)
    print("📊 تقرير النتائج النهائية:")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    total = len(results)
    print(f"\n🎯 النتيجة: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("🎉 جميع الإصلاحات تعمل بشكل صحيح!")
        print("✅ البوت جاهز للتشغيل")
    else:
        print("⚠️ بعض المشاكل لا تزال موجودة")
        print("🔧 يرجى مراجعة الأخطاء أعلاه")
    
    return passed == total

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الاختبار")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 خطأ حرج: {e}")
        sys.exit(1)
