#!/usr/bin/env python3
"""
تشغيل البوت المحسن مع واجهة تفاعلية كاملة
يوفر تحكم كامل في النظام مع أزرار تشغيل وإيقاف
"""

import asyncio
import logging
import sys
from pathlib import Path

# إعداد السجلات
Path("logs").mkdir(exist_ok=True)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/telegram_bot.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

async def main():
    """تشغيل البوت المحسن"""
    print("""
    🤖 البوت المحسن - نظام إنشاء الفيديوهات التلقائي
    ================================================
    
    ✅ تحكم كامل في النظام
    ✅ أزرار تشغيل وإيقاف
    ✅ مراقبة مستمرة
    ✅ إشعارات فورية
    ✅ عرض السجلات والإحصائيات
    
    الأوامر المتاحة:
    /start - القائمة الرئيسية
    /run - تشغيل النظام
    /stop - إيقاف النظام
    /status - حالة النظام
    /logs - عرض السجلات
    /help - المساعدة
    
    """)
    
    try:
        # استيراد البوت المحسن
        from automation.telegram_bot import EnhancedTelegramBot
        
        # إنشاء البوت
        bot = EnhancedTelegramBot()
        
        logger.info("🚀 بدء تشغيل البوت المحسن...")
        
        # بدء البوت التفاعلي
        await bot.start_interactive_bot()
        
    except KeyboardInterrupt:
        logger.info("⏹️ تم إيقاف البوت بواسطة المستخدم")
    except Exception as e:
        logger.error(f"❌ خطأ في البوت المحسن: {str(e)}")
        raise

if __name__ == "__main__":
    # التحقق من إصدار Python
    if sys.version_info.major != 3 or sys.version_info.minor < 10:
        print("❌ يتطلب Python 3.10 أو أحدث")
        sys.exit(1)
    
    # تشغيل البوت
    asyncio.run(main())
