#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت لإنشاء مجلدات الخلفيات بشكل صحيح
Script to create background folders correctly
"""

import os
import sys

def create_background_folders():
    """إنشاء جميع مجلدات الخلفيات المطلوبة"""
    
    # المسار الأساسي للخلفيات
    base_path = "assets/backgrounds"
    
    # قائمة المجلدات المطلوبة
    background_folders = [
        "العاب_اكشن",           # Action Games
        "العاب_كلاسيكية",       # Classic Games  
        "العاب_مغامرة",         # Adventure Games
        "العاب_رعب",           # Horror Games
        "العاب_رياضية",        # Sports Games
        "العاب_سباق",          # Racing Games
        "العاب_ار_بي_جي",      # RPG Games
        "العاب_استراتيجية",    # Strategy Games
        "العاب_محاكاة",        # Simulation Games
        "العاب_الغاز",         # Puzzle Games
        "العاب_قتال",          # Fighting Games
        "العاب_اطلاق_نار",     # Shooter Games
        "العاب_منصات",         # Platform Games
        "العاب_متنوعة"         # Miscellaneous Games
    ]
    
    print("🚀 بدء إنشاء مجلدات الخلفيات...")
    print("=" * 50)
    
    # إنشاء المجلد الأساسي
    os.makedirs(base_path, exist_ok=True)
    print(f"✅ تم إنشاء المجلد الأساسي: {base_path}")
    
    created_folders = []
    existing_folders = []
    
    # إنشاء كل مجلد
    for folder_name in background_folders:
        folder_path = os.path.join(base_path, folder_name)
        
        if os.path.exists(folder_path):
            existing_folders.append(folder_name)
            print(f"📁 موجود مسبقاً: {folder_name}")
        else:
            try:
                os.makedirs(folder_path, exist_ok=True)
                created_folders.append(folder_name)
                print(f"✅ تم إنشاء: {folder_name}")
            except Exception as e:
                print(f"❌ فشل في إنشاء {folder_name}: {e}")
        
        # إنشاء ملف README في كل مجلد
        readme_path = os.path.join(folder_path, "README.md")
        if not os.path.exists(readme_path):
            try:
                with open(readme_path, 'w', encoding='utf-8') as f:
                    f.write(f"""# مجلد خلفيات {folder_name}

ضع هنا صور الخلفيات المناسبة لهذه الفئة

## الصيغ المدعومة:
- .jpg
- .jpeg  
- .png
- .webp

## المواصفات المطلوبة:
- الحد الأدنى: 1200x800 بكسل
- الحد الأقصى: 4000x3000 بكسل
- جودة عالية ووضوح جيد

## أمثلة على الصور المناسبة:
- صور من ألعاب هذه الفئة
- خلفيات مناسبة لموضوع الفئة
- صور عالية الجودة وجذابة

## ملاحظة:
تأكد من أن الصور مناسبة لموضوع {folder_name} وذات جودة عالية
""")
                print(f"📝 تم إنشاء README.md في {folder_name}")
            except Exception as e:
                print(f"⚠️ فشل في إنشاء README لـ {folder_name}: {e}")
    
    print("=" * 50)
    print("📊 ملخص العملية:")
    print(f"✅ مجلدات جديدة: {len(created_folders)}")
    print(f"📁 مجلدات موجودة: {len(existing_folders)}")
    print(f"📈 إجمالي المجلدات: {len(background_folders)}")
    
    if created_folders:
        print("\n🆕 المجلدات الجديدة:")
        for folder in created_folders:
            print(f"   - {folder}")
    
    if existing_folders:
        print("\n📁 المجلدات الموجودة مسبقاً:")
        for folder in existing_folders:
            print(f"   - {folder}")
    
    # التحقق من وجود جميع المجلدات
    print("\n🔍 التحقق من وجود جميع المجلدات:")
    all_exist = True
    for folder_name in background_folders:
        folder_path = os.path.join(base_path, folder_name)
        if os.path.exists(folder_path):
            print(f"✅ {folder_name}")
        else:
            print(f"❌ {folder_name}")
            all_exist = False
    
    if all_exist:
        print("\n🎉 تم إنشاء جميع المجلدات بنجاح!")
        print(f"\n📍 المسار الكامل: {os.path.abspath(base_path)}")
        print("\n💡 يمكنك الآن إضافة صور الخلفيات في المجلدات المناسبة")
    else:
        print("\n⚠️ بعض المجلدات لم يتم إنشاؤها بشكل صحيح")
    
    return all_exist

def create_fonts_folders():
    """إنشاء مجلدات الخطوط أيضاً"""
    
    print("\n🔤 إنشاء مجلدات الخطوط...")
    
    fonts_folders = [
        ("assets/fonts/arabic", "الخطوط العربية"),
        ("assets/fonts/english", "الخطوط الإنجليزية")
    ]
    
    for folder_path, description in fonts_folders:
        os.makedirs(folder_path, exist_ok=True)
        print(f"✅ تم إنشاء مجلد {description}: {folder_path}")
        
        # إنشاء README
        readme_path = os.path.join(folder_path, "README.md")
        if not os.path.exists(readme_path):
            language = "العربية" if "arabic" in folder_path else "الإنجليزية"
            with open(readme_path, 'w', encoding='utf-8') as f:
                f.write(f"""# مجلد الخطوط {language}

ضع هنا ملفات الخطوط {language} بصيغة TTF أو OTF

## الصيغ المدعومة:
- .ttf
- .otf

## أمثلة على أسماء الملفات:
""")
                if "arabic" in folder_path:
                    f.write("""- NotoSansArabic-Regular.ttf
- Cairo-Regular.ttf
- Amiri-Regular.ttf
- Tajawal-Regular.ttf
- Almarai-Regular.ttf
""")
                else:
                    f.write("""- Roboto-Regular.ttf
- OpenSans-Regular.ttf
- Montserrat-Regular.ttf
- Lato-Regular.ttf
- SourceSansPro-Regular.ttf
""")
                
                f.write(f"""
## ملاحظة:
تأكد من أن الخطوط تدعم اللغة {language} بشكل صحيح
""")

if __name__ == "__main__":
    print("🎨 سكريبت إنشاء مجلدات النظام المحسن")
    print("=" * 60)
    
    # إنشاء مجلدات الخلفيات
    success = create_background_folders()
    
    # إنشاء مجلدات الخطوط
    create_fonts_folders()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 تم إنشاء جميع المجلدات بنجاح!")
        print("\n📋 الخطوات التالية:")
        print("1. أضف خطوط عربية في assets/fonts/arabic/")
        print("2. أضف خطوط إنجليزية في assets/fonts/english/")
        print("3. أضف صور خلفيات في المجلدات المناسبة")
        print("4. شغل النظام واستمتع بالصور المحسنة!")
    else:
        print("⚠️ حدثت بعض المشاكل في إنشاء المجلدات")
