#!/usr/bin/env python3
"""
معزز أداء SEO - يحسن النقاط تلقائياً
"""

import logging
from typing import Dict, List
from modules.enhanced_seo_analyzer import enhanced_seo_analyzer

logger = logging.getLogger(__name__)

class SEOPerformanceBooster:
    """معزز أداء SEO لرفع النقاط"""
    
    def __init__(self):
        self.target_score = 80
        self.minimum_score = 60
    
    def boost_article_seo(self, article: Dict) -> Dict:
        """تعزيز SEO المقال لرفع النقاط"""
        try:
            # تحليل الوضع الحالي
            current_analysis = enhanced_seo_analyzer.calculate_comprehensive_seo_score(article)
            current_score = current_analysis.get('overall_score', 0)
            
            logger.info(f"📊 النقاط الحالية: {current_score}/100")
            
            if current_score >= self.target_score:
                logger.info("✅ المقال محسن بالفعل!")
                return article
            
            # تطبيق تحسينات تلقائية
            boosted_article = article.copy()
            
            # 1. تحسين العنوان
            boosted_article = self._boost_title_seo(boosted_article)
            
            # 2. تحسين المحتوى
            boosted_article = self._boost_content_seo(boosted_article)
            
            # 3. تحسين الكلمات المفتاحية
            boosted_article = self._boost_keywords_seo(boosted_article)
            
            # 4. تحسين الجوانب التقنية
            boosted_article = self._boost_technical_seo(boosted_article)
            
            # إعادة التحليل
            new_analysis = enhanced_seo_analyzer.calculate_comprehensive_seo_score(boosted_article)
            new_score = new_analysis.get('overall_score', 0)
            
            improvement = new_score - current_score
            logger.info(f"🚀 النقاط بعد التحسين: {new_score}/100 (+{improvement:.1f})")
            
            return boosted_article
            
        except Exception as e:
            logger.error(f"❌ فشل في تعزيز SEO: {e}")
            return article
    
    def _boost_title_seo(self, article: Dict) -> Dict:
        """تعزيز SEO العنوان"""
        title = article.get('title', '')
        if not title:
            return article
        
        # إضافة رموز تعبيرية إذا لم توجد
        gaming_emojis = ['🎮', '🔥', '⚡', '🚀', '💎', '🏆']
        if not any(emoji in title for emoji in gaming_emojis):
            import random
            emoji = random.choice(gaming_emojis)
            title = f"{emoji} {title}"
        
        # إضافة سنة للحداثة
        from datetime import datetime
        current_year = str(datetime.now().year)
        if current_year not in title and len(title) < 50:
            title = f"{title} {current_year}"
        
        # تحسين الطول
        if len(title) < 30:
            keywords = article.get('keywords', [])
            if keywords:
                title = f"{title} - {keywords[0]}"
        
        article['title'] = title
        return article
    
    def _boost_content_seo(self, article: Dict) -> Dict:
        """تعزيز SEO المحتوى"""
        content = article.get('content', '')
        title = article.get('title', '')
        keywords = article.get('keywords', [])

        if not content:
            # إنشاء محتوى أساسي من العنوان
            content = f"في هذا المقال سنتحدث عن {title}. "

        # توسيع المحتوى إذا كان قصيراً جداً
        word_count = len(content.split())
        if word_count < 100:
            # إضافة محتوى إضافي
            additional_content = f"""

## نظرة عامة

{title} موضوع مهم في عالم الألعاب يستحق الاهتمام والمتابعة. سنقدم لكم في هذا المقال معلومات شاملة ومفيدة.

## التفاصيل المهمة

هناك عدة نقاط مهمة يجب معرفتها:

- معلومات أساسية ومفيدة
- تحديثات حديثة ومستمرة
- نصائح وإرشادات عملية
- مراجعات وتقييمات دقيقة

## الخلاصة

في النهاية، {title} يقدم تجربة رائعة للاعبين ويستحق التجربة. نأمل أن تكونوا قد استفدتم من هذا المحتوى.
"""
            content += additional_content

        # إضافة عناوين فرعية إذا لم توجد
        if not any(marker in content for marker in ['##', '<h', '###']):
            lines = content.split('\n')
            if len(lines) > 5:
                lines.insert(len(lines)//3, "\n## معلومات إضافية\n")
                content = '\n'.join(lines)

        # إضافة الكلمات المفتاحية في المحتوى
        if keywords:
            for keyword in keywords[:3]:  # أول 3 كلمات مفتاحية
                if keyword.lower() not in content.lower():
                    content += f" {keyword} موضوع مهم يستحق الاهتمام."

        # إضافة دعوة للعمل إذا لم توجد
        cta_phrases = ['شاركنا رأيك', 'اترك تعليق', 'ما رأيكم', 'شاركونا']
        if not any(phrase in content for phrase in cta_phrases):
            content += "\n\nما رأيكم في هذا المحتوى؟ شاركونا تعليقاتكم!"

        # إضافة قائمة بسيطة إذا لم توجد
        if not any(marker in content for marker in ['*', '-', '1.', '<ul>', '<ol>']):
            content += "\n\nالنقاط الرئيسية:\n- معلومات مفيدة ومحدثة\n- محتوى عالي الجودة\n- تحديثات منتظمة\n- مراجعات دقيقة"
        
        article['content'] = content
        return article
    
    def _boost_keywords_seo(self, article: Dict) -> Dict:
        """تعزيز الكلمات المفتاحية"""
        keywords = article.get('keywords', [])
        
        # إضافة كلمات مفتاحية أساسية إذا كانت قليلة
        if len(keywords) < 5:
            gaming_keywords = ['ألعاب', 'العاب فيديو', 'جيمنج', 'مراجعة', 'نصائح']
            for kw in gaming_keywords:
                if kw not in keywords:
                    keywords.append(kw)
                if len(keywords) >= 8:
                    break
        
        # إضافة كلمات مفتاحية من العنوان
        title = article.get('title', '')
        title_words = [word.strip() for word in title.split() if len(word) > 3]
        for word in title_words[:3]:
            if word not in keywords and len(keywords) < 10:
                keywords.append(word)
        
        article['keywords'] = keywords
        return article
    
    def _boost_technical_seo(self, article: Dict) -> Dict:
        """تعزيز الجوانب التقنية"""
        # إضافة وصف قصير إذا لم يوجد
        if not article.get('summary'):
            content = article.get('content', '')
            if content:
                # أخذ أول 150 حرف كوصف
                summary = content[:150].strip()
                if summary:
                    article['summary'] = summary + "..."
        
        # إضافة تصنيف إذا لم يوجد
        if not article.get('category'):
            article['category'] = 'أخبار الألعاب'
        
        # إضافة تاريخ النشر إذا لم يوجد
        if not article.get('published_date'):
            from datetime import datetime
            article['published_date'] = datetime.now().isoformat()
        
        return article

# إنشاء مثيل عام
seo_performance_booster = SEOPerformanceBooster()
