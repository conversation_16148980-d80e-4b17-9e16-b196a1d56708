version: '3.8'

services:
  telegram-bot:
    build: .
    container_name: minecraft-mods-bot
    restart: unless-stopped
    
    # متغيرات البيئة
    env_file:
      - .env
    
    # المنافذ
    ports:
      - "5000:5000"  # خادم الويب
      - "5001:5001"  # تطبيق تلجرام الويب
    
    # المجلدات المشتركة
    volumes:
      - ./logs:/app/logs
      - ./user_customizations:/app/user_customizations
      - ./temp:/app/temp
    
    # إعدادات الشبكة
    networks:
      - bot-network
    
    # فحص الصحة
    healthcheck:
      test: ["CMD", "python", "-c", "import requests; requests.get('http://localhost:5000/health')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    # إعدادات الموارد
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

networks:
  bot-network:
    driver: bridge

# إعدادات إضافية للإنتاج
# volumes:
#   bot-data:
#     driver: local
