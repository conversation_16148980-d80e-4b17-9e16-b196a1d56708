#!/usr/bin/env python3
"""
إدارة الإعدادات لـ Render
يقوم بإنشاء ملف config.toml من متغيرات البيئة
"""

import os
import toml
import logging
from pathlib import Path

logger = logging.getLogger(__name__)

def create_config_from_env():
    """إنشاء ملف config.toml من متغيرات البيئة"""
    logger.info("🔧 إنشاء ملف الإعدادات من متغيرات البيئة...")
    
    # الإعدادات الافتراضية
    config = {
        "reddit": {
            "creds": {
                "client_id": os.getenv("REDDIT_CLIENT_ID", ""),
                "client_secret": os.getenv("REDDIT_CLIENT_SECRET", ""),
                "username": os.getenv("REDDIT_USERNAME", ""),
                "password": os.getenv("REDDIT_PASSWORD", ""),
                "user_agent": "RedditVideoMakerBot by u/LukaHietala"
            },
            "thread": {
                "subreddit": "AskReddit",
                "post_id": "",
                "max_comment_length": 500,
                "theme": "dark",
                "times_to_run": 1,
                "ai_similarity_enabled": True,
                "ai_similarity_level": 1
            }
        },
        "settings": {
            "allow_nsfw": False,
            "theme": "dark",
            "times_to_run": 1,
            "opacity": 0.9,
            "storymode": False,
            "storymodemethod": 0,
            "storymode_max_length": 1000,
            "resolution_w": 1080,
            "resolution_h": 1920,
            "zoom": 1.0,
            "channel_name": "Reddit Tales"
        },
        "tts": {
            "choice": "gtts",
            "gtts": {
                "temp_audio_folder": "assets/temp/mp3",
                "tld": "com"
            },
            "elevenlabs": {
                "api_key": os.getenv("ELEVENLABS_API_KEY", ""),
                "voice": "Josh",
                "model": "eleven_monolingual_v1"
            },
            "python_voice": "1",
            "py_voice_num": "2",
            "silence_duration": 0.3,
            "no_emojis": False
        },
        "background": {
            "background_choice": "minecraft",
            "background_audio": True,
            "background_audio_volume": 0.3,
            "background_thumbnail": False,
            "background_thumbnail_font_family": "arial",
            "background_thumbnail_font_size": 96,
            "background_thumbnail_font_color": [255, 255, 255]
        },
        "imagemagick": {
            "path": "/usr/bin/"
        },
        "automation": {
            "enabled": True,
            "telegram": {
                "enabled": bool(os.getenv("TELEGRAM_BOT_TOKEN")),
                "bot_token": os.getenv("TELEGRAM_BOT_TOKEN", ""),
                "chat_id": os.getenv("TELEGRAM_CHAT_ID", "")
            },
            "gemini": {
                "enabled": bool(os.getenv("GEMINI_API_KEY")),
                "api_key": os.getenv("GEMINI_API_KEY", "")
            },
            "youtube": {
                "enabled": False,
                "client_id": os.getenv("YOUTUBE_CLIENT_ID", ""),
                "client_secret": os.getenv("YOUTUBE_CLIENT_SECRET", "")
            },
            "schedule": {
                "enabled": True,
                "interval_hours": int(os.getenv("SCHEDULE_INTERVAL_HOURS", "6")),
                "max_videos_per_day": int(os.getenv("MAX_VIDEOS_PER_DAY", "4"))
            }
        },
        "render": {
            "enabled": True,
            "video_quality": os.getenv("VIDEO_QUALITY", "medium"),
            "max_video_duration": int(os.getenv("MAX_VIDEO_DURATION", "45")),
            "auto_cleanup_hours": int(os.getenv("AUTO_CLEANUP_HOURS", "1")),
            "storage_limit_mb": int(os.getenv("STORAGE_LIMIT_MB", "1000"))
        }
    }
    
    # حفظ الملف
    config_path = Path("config.toml")
    try:
        with open(config_path, 'w', encoding='utf-8') as f:
            toml.dump(config, f)
        logger.info("✅ تم إنشاء ملف config.toml بنجاح")
        return True
    except Exception as e:
        logger.error(f"❌ خطأ في إنشاء ملف config.toml: {e}")
        return False

def validate_config():
    """التحقق من صحة الإعدادات"""
    logger.info("🔍 التحقق من صحة الإعدادات...")
    
    required_vars = {
        "REDDIT_CLIENT_ID": "معرف تطبيق Reddit",
        "REDDIT_CLIENT_SECRET": "سر تطبيق Reddit", 
        "REDDIT_USERNAME": "اسم مستخدم Reddit",
        "REDDIT_PASSWORD": "كلمة مرور Reddit"
    }
    
    missing_vars = []
    for var, description in required_vars.items():
        value = os.getenv(var)
        if not value or value.startswith("YOUR_"):
            missing_vars.append(f"{var} ({description})")
    
    if missing_vars:
        logger.warning("⚠️ متغيرات البيئة المفقودة:")
        for var in missing_vars:
            logger.warning(f"   - {var}")
        logger.warning("يرجى تحديث متغيرات البيئة في إعدادات Render")
        return False
    
    logger.info("✅ جميع المتغيرات المطلوبة متوفرة")
    return True

def setup_render_environment():
    """إعداد البيئة الكاملة لـ Render"""
    logger.info("🚀 إعداد البيئة لـ Render...")
    
    # إنشاء المجلدات المطلوبة
    required_dirs = [
        "logs",
        "results", 
        "assets/temp/mp3",
        "assets/backgrounds/video",
        "assets/backgrounds/audio"
    ]
    
    for dir_path in required_dirs:
        Path(dir_path).mkdir(parents=True, exist_ok=True)
        logger.info(f"✅ مجلد: {dir_path}")
    
    # إنشاء ملف الإعدادات
    config_created = create_config_from_env()
    
    # التحقق من الإعدادات
    config_valid = validate_config()
    
    if config_created and config_valid:
        logger.info("✅ تم إعداد البيئة بنجاح")
        return True
    else:
        logger.warning("⚠️ تم إعداد البيئة مع تحذيرات")
        return False

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    setup_render_environment()
