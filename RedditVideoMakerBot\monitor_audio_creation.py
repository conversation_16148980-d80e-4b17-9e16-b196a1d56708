#!/usr/bin/env python3
"""
مراقبة إنشاء الملفات الصوتية في الوقت الفعلي
"""

import os
import time
import threading
from pathlib import Path

class AudioFileMonitor:
    def __init__(self, temp_dir="assets/temp"):
        self.temp_dir = temp_dir
        self.monitoring = False
        self.file_count = 0
        self.total_size = 0
        self.monitor_thread = None
        
    def start_monitoring(self):
        """بدء المراقبة"""
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop)
        self.monitor_thread.daemon = True
        self.monitor_thread.start()
        print("🔍 بدء مراقبة إنشاء الملفات الصوتية...")
        
    def stop_monitoring(self):
        """إيقاف المراقبة"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join()
        print("⏹️ تم إيقاف المراقبة")
        
    def _monitor_loop(self):
        """حلقة المراقبة"""
        last_count = 0
        last_size = 0
        
        while self.monitoring:
            current_count, current_size = self._count_audio_files()
            
            if current_count != last_count or current_size != last_size:
                size_mb = current_size / (1024 * 1024)
                print(f"📊 الملفات الصوتية: {current_count} ملف ({size_mb:.1f} MB)")
                
                # تحذير إذا كان العدد كبير
                if current_count > 20:
                    print(f"⚠️ تحذير: عدد كبير من الملفات ({current_count})!")
                
                if current_count > 50:
                    print(f"🚨 خطر: عدد مفرط من الملفات ({current_count})!")
                
                last_count = current_count
                last_size = current_size
            
            time.sleep(2)  # فحص كل ثانيتين
    
    def _count_audio_files(self):
        """عد الملفات الصوتية"""
        count = 0
        total_size = 0
        
        if os.path.exists(self.temp_dir):
            for root, dirs, files in os.walk(self.temp_dir):
                for file in files:
                    if file.endswith('.mp3'):
                        count += 1
                        file_path = os.path.join(root, file)
                        try:
                            total_size += os.path.getsize(file_path)
                        except:
                            pass
        
        return count, total_size
    
    def get_final_report(self):
        """تقرير نهائي"""
        final_count, final_size = self._count_audio_files()
        size_mb = final_size / (1024 * 1024)
        
        print("\n" + "="*50)
        print("📊 التقرير النهائي للملفات الصوتية")
        print("="*50)
        print(f"📁 إجمالي الملفات: {final_count}")
        print(f"💾 إجمالي الحجم: {size_mb:.1f} MB")
        
        if final_count <= 10:
            print("✅ عدد الملفات طبيعي")
        elif final_count <= 20:
            print("⚠️ عدد الملفات مرتفع قليلاً")
        else:
            print("🚨 عدد الملفات مفرط - يحتاج تحسين!")
        
        return final_count, final_size

def test_monitor():
    """اختبار المراقب"""
    monitor = AudioFileMonitor()
    
    try:
        monitor.start_monitoring()
        
        print("🎬 ابدأ تشغيل البوت الآن...")
        print("اضغط Ctrl+C لإيقاف المراقبة")
        
        # انتظار إيقاف المستخدم
        while True:
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("\n⏹️ إيقاف المراقبة...")
        monitor.stop_monitoring()
        monitor.get_final_report()

if __name__ == "__main__":
    test_monitor()
