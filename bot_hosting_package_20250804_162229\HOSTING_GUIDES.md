# 🌐 دليل الاستضافات المختلفة
# Different Hosting Platforms Guide

## 🚀 Railway (مجاني - موصى به)

### الخطوات:
1. اذهب إلى [Railway.app](https://railway.app)
2. سجل دخول بحساب GitHub
3. اضغط "New Project" → "Deploy from GitHub repo"
4. ارفع هذا المجلد إلى GitHub repository
5. اختر المستودع
6. أضف متغيرات البيئة من ملف `.env`
7. انشر المشروع

### الإعدادات:
```bash
# في Railway Dashboard
Start Command: python main.py
Port: 5000
```

---

## ☁️ Render (مجاني)

### الخطوات:
1. اذهب إلى [Render.com](https://render.com)
2. سجل دخول بحساب GitHub
3. اضغ<PERSON> "New" → "Web Service"
4. اربط GitHub repository
5. أض<PERSON> متغيرات البيئة
6. انشر الخدمة

### الإعدادات:
```yaml
# render.yaml (موجود في المجلد)
services:
  - type: web
    name: minecraft-mods-bot
    env: python
    buildCommand: pip install -r requirements.txt
    startCommand: python main.py
```

---

## 🐳 Docker (أي استضافة تدعم Docker)

### التشغيل المحلي:
```bash
# بناء الصورة
docker build -t minecraft-bot .

# تشغيل الحاوية
docker run -d --name minecraft-bot \
  --env-file .env \
  -p 5000:5000 \
  -p 5001:5001 \
  minecraft-bot
```

### باستخدام Docker Compose:
```bash
# تشغيل جميع الخدمات
docker-compose up -d

# إيقاف الخدمات
docker-compose down

# عرض السجلات
docker-compose logs -f
```

---

## 🖥️ VPS/Dedicated Server

### Ubuntu/Debian:
```bash
# تحديث النظام
sudo apt update && sudo apt upgrade -y

# تثبيت Python
sudo apt install python3 python3-pip python3-venv -y

# إنشاء بيئة افتراضية
python3 -m venv bot_env
source bot_env/bin/activate

# تثبيت المتطلبات
pip install -r requirements.txt

# تشغيل البوت
python main.py
```

### CentOS/RHEL:
```bash
# تثبيت Python
sudo yum install python3 python3-pip -y

# باقي الخطوات مشابهة لـ Ubuntu
```

### تشغيل كخدمة (systemd):
```bash
# إنشاء ملف الخدمة
sudo nano /etc/systemd/system/minecraft-bot.service

# محتوى الملف:
[Unit]
Description=Minecraft Mods Bot
After=network.target

[Service]
Type=simple
User=your_user
WorkingDirectory=/path/to/bot
Environment=PATH=/path/to/bot/bot_env/bin
ExecStart=/path/to/bot/bot_env/bin/python main.py
Restart=always

[Install]
WantedBy=multi-user.target

# تفعيل الخدمة
sudo systemctl enable minecraft-bot
sudo systemctl start minecraft-bot
```

---

## 🌊 DigitalOcean App Platform

### الخطوات:
1. اذهب إلى [DigitalOcean Apps](https://cloud.digitalocean.com/apps)
2. اضغط "Create App"
3. اربط GitHub repository
4. اختر "Web Service"
5. أضف متغيرات البيئة
6. انشر التطبيق

### الإعدادات:
```yaml
# .do/app.yaml
name: minecraft-mods-bot
services:
- name: web
  source_dir: /
  github:
    repo: your-username/your-repo
    branch: main
  run_command: python main.py
  environment_slug: python
  instance_count: 1
  instance_size_slug: basic-xxs
  envs:
  - key: BOT_TOKEN
    value: YOUR_BOT_TOKEN
```

---

## ⚡ Vercel (للملفات الثابتة فقط)

**ملاحظة:** Vercel مناسب فقط لصفحة التحميل، ليس للبوت نفسه.

### نشر صفحة التحميل:
```bash
# تثبيت Vercel CLI
npm i -g vercel

# في مجلد cloudflare_ready
cd cloudflare_ready
vercel --prod
```

---

## 🔧 استكشاف الأخطاء

### مشاكل شائعة:

#### 1. خطأ في المنافذ:
```bash
# تغيير المنفذ في متغيرات البيئة
PORT=8080
WEB_SERVER_PORT=8080
```

#### 2. مشاكل الذاكرة:
```bash
# تقليل استخدام الذاكرة
export PYTHONOPTIMIZE=1
```

#### 3. مشاكل الشبكة:
```bash
# اختبار الاتصال
python network_config.py
```

### فحص الحالة:
```bash
# فحص حالة البوت
curl http://localhost:5000/health

# عرض السجلات
tail -f logs/bot.log
```

---

## 📊 مراقبة الأداء

### أدوات المراقبة:
- **htop** - مراقبة الموارد
- **journalctl** - سجلات النظام
- **docker stats** - إحصائيات Docker

### تنبيهات:
```bash
# إعداد تنبيهات البريد الإلكتروني
# أضف إعدادات SMTP في .env
```

---

## 💰 مقارنة التكاليف

| الاستضافة | المجاني | المدفوع | المميزات |
|-----------|---------|---------|----------|
| Railway | ✅ 500 ساعة | $5/شهر | سهل الاستخدام |
| Render | ✅ محدود | $7/شهر | موثوق |
| Heroku | ❌ | $7/شهر | مشهور |
| DigitalOcean | ❌ | $5/شهر | مرونة عالية |
| VPS | ❌ | $3-20/شهر | تحكم كامل |

---

## 🎯 التوصيات

### للمبتدئين:
1. **Railway** - الأسهل والأسرع
2. **Render** - بديل ممتاز

### للمحترفين:
1. **VPS** - تحكم كامل
2. **Docker** - قابلية النقل

### للإنتاج:
1. **DigitalOcean** - موثوق ومرن
2. **AWS/GCP** - للمشاريع الكبيرة
