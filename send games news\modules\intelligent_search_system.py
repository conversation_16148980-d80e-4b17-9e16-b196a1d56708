# نظام البحث الذكي البديل (بدون Google Search API)
import asyncio
import aiohttp
import json
import random
import time
from typing import List, Dict, Optional
from urllib.parse import quote_plus, urljoin
from bs4 import BeautifulSoup
import re
from .logger import logger

class IntelligentSearchSystem:
    """نظام البحث الذكي البديل - بدون استخدام Google Search API"""
    
    def __init__(self):
        self.search_engines = {
            'duckduckgo': {
                'url': 'https://duckduckgo.com/html/',
                'params': {'q': '{query}', 'kl': 'us-en'},
                'headers': {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
                },
                'enabled': True,
                'priority': 1
            },
            'bing': {
                'url': 'https://www.bing.com/search',
                'params': {'q': '{query}', 'count': '20'},
                'headers': {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
                },
                'enabled': True,
                'priority': 2
            },
            'yandex': {
                'url': 'https://yandex.com/search/',
                'params': {'text': '{query}', 'lr': '213'},
                'headers': {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
                },
                'enabled': True,
                'priority': 3
            }
        }
        
        # مواقع أخبار الألعاب المعروفة
        self.gaming_news_sites = [
            'gamespot.com',
            'ign.com', 
            'polygon.com',
            'kotaku.com',
            'eurogamer.net',
            'destructoid.com',
            'pcgamer.com',
            'gamesindustry.biz',
            'gamasutra.com',
            'rockpapershotgun.com',
            'gamesradar.com',
            'gameinformer.com'
        ]
        
        # إعدادات التأخير لتجنب الحظر
        self.request_delays = {
            'min_delay': 2,  # ثانيتين كحد أدنى
            'max_delay': 5,  # 5 ثوان كحد أقصى
            'backoff_multiplier': 1.5
        }
        
        self.session_timeout = aiohttp.ClientTimeout(total=30)
        self.max_retries = 3
        
        # إحصائيات الاستخدام
        self.usage_stats = {
            'total_searches': 0,
            'successful_searches': 0,
            'failed_searches': 0,
            'engines_used': {},
            'last_search_time': 0
        }
        
        logger.info("🔍 تم تهيئة نظام البحث الذكي البديل")
    
    async def search(self, query: str, max_results: int = 10, 
                    search_type: str = 'gaming_news') -> List[Dict]:
        """البحث الذكي باستخدام محركات بحث متعددة"""
        try:
            self.usage_stats['total_searches'] += 1
            
            # تحسين الاستعلام للألعاب
            enhanced_query = self._enhance_gaming_query(query, search_type)
            
            # البحث باستخدام محركات متعددة
            all_results = []
            
            # ترتيب المحركات حسب الأولوية
            sorted_engines = sorted(
                self.search_engines.items(),
                key=lambda x: x[1]['priority']
            )
            
            for engine_name, engine_config in sorted_engines:
                if not engine_config['enabled']:
                    continue
                
                try:
                    # تأخير ذكي بين الطلبات
                    await self._smart_delay()
                    
                    results = await self._search_with_engine(
                        enhanced_query, engine_name, engine_config, max_results
                    )
                    
                    if results:
                        all_results.extend(results)
                        self.usage_stats['engines_used'][engine_name] = \
                            self.usage_stats['engines_used'].get(engine_name, 0) + 1
                        
                        logger.info(f"✅ {engine_name}: {len(results)} نتيجة")
                        
                        # إذا حصلنا على نتائج كافية، توقف
                        if len(all_results) >= max_results:
                            break
                    
                except Exception as e:
                    logger.warning(f"⚠️ فشل البحث في {engine_name}: {e}")
                    continue
            
            # تنظيف وترتيب النتائج
            final_results = self._process_and_rank_results(all_results, query)
            final_results = final_results[:max_results]
            
            if final_results:
                self.usage_stats['successful_searches'] += 1
                logger.info(f"🎯 تم العثور على {len(final_results)} نتيجة نهائية")
            else:
                self.usage_stats['failed_searches'] += 1
                logger.warning("⚠️ لم يتم العثور على نتائج")
            
            return final_results
            
        except Exception as e:
            logger.error(f"❌ خطأ في البحث الذكي: {e}")
            self.usage_stats['failed_searches'] += 1
            return []
    
    def _enhance_gaming_query(self, query: str, search_type: str) -> str:
        """تحسين استعلام البحث للألعاب"""
        try:
            enhanced_query = query.strip()
            
            # إضافة كلمات مفتاحية حسب نوع البحث
            if search_type == 'gaming_news':
                enhanced_query += ' gaming news game'
            elif search_type == 'game_review':
                enhanced_query += ' game review gameplay'
            elif search_type == 'game_update':
                enhanced_query += ' game update patch news'
            elif search_type == 'game_release':
                enhanced_query += ' game release announcement'
            
            # إضافة تاريخ حديث للأخبار
            if 'news' in search_type:
                enhanced_query += ' 2024 2025'
            
            return enhanced_query
            
        except Exception as e:
            logger.warning(f"⚠️ خطأ في تحسين الاستعلام: {e}")
            return query
    
    async def _search_with_engine(self, query: str, engine_name: str, 
                                 engine_config: Dict, max_results: int) -> List[Dict]:
        """البحث باستخدام محرك بحث واحد"""
        try:
            # تحضير URL والمعاملات
            url = engine_config['url']
            params = {}
            
            for key, value in engine_config['params'].items():
                if isinstance(value, str) and '{query}' in value:
                    params[key] = value.format(query=quote_plus(query))
                else:
                    params[key] = value
            
            headers = engine_config['headers'].copy()
            
            # إضافة headers إضافية لتجنب الحظر
            headers.update({
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
            })
            
            async with aiohttp.ClientSession(timeout=self.session_timeout) as session:
                async with session.get(url, params=params, headers=headers) as response:
                    if response.status == 200:
                        html_content = await response.text()
                        results = self._parse_search_results(html_content, engine_name)
                        return results[:max_results]
                    else:
                        logger.warning(f"⚠️ {engine_name} استجاب بـ {response.status}")
                        return []
            
        except Exception as e:
            logger.error(f"❌ خطأ في البحث باستخدام {engine_name}: {e}")
            return []
    
    def _parse_search_results(self, html_content: str, engine_name: str) -> List[Dict]:
        """تحليل نتائج البحث من HTML"""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            results = []
            
            if engine_name == 'duckduckgo':
                results = self._parse_duckduckgo_results(soup)
            elif engine_name == 'bing':
                results = self._parse_bing_results(soup)
            elif engine_name == 'yandex':
                results = self._parse_yandex_results(soup)
            
            return results
            
        except Exception as e:
            logger.error(f"❌ خطأ في تحليل نتائج {engine_name}: {e}")
            return []
    
    def _parse_duckduckgo_results(self, soup: BeautifulSoup) -> List[Dict]:
        """تحليل نتائج DuckDuckGo"""
        results = []
        try:
            # البحث عن النتائج في DuckDuckGo
            result_elements = soup.find_all('div', class_='result')
            
            for element in result_elements:
                try:
                    title_elem = element.find('a', class_='result__a')
                    snippet_elem = element.find('a', class_='result__snippet')
                    
                    if title_elem and title_elem.get('href'):
                        title = title_elem.get_text(strip=True)
                        url = title_elem.get('href')
                        snippet = snippet_elem.get_text(strip=True) if snippet_elem else ""
                        
                        # تنظيف URL
                        if url.startswith('//'):
                            url = 'https:' + url
                        
                        results.append({
                            'title': title,
                            'url': url,
                            'snippet': snippet,
                            'source': 'duckduckgo'
                        })
                        
                except Exception as e:
                    continue
            
            return results
            
        except Exception as e:
            logger.error(f"❌ خطأ في تحليل DuckDuckGo: {e}")
            return []
    
    def _parse_bing_results(self, soup: BeautifulSoup) -> List[Dict]:
        """تحليل نتائج Bing"""
        results = []
        try:
            # البحث عن النتائج في Bing
            result_elements = soup.find_all('li', class_='b_algo')
            
            for element in result_elements:
                try:
                    title_elem = element.find('h2')
                    if title_elem:
                        link_elem = title_elem.find('a')
                        if link_elem:
                            title = link_elem.get_text(strip=True)
                            url = link_elem.get('href')
                            
                            snippet_elem = element.find('p')
                            snippet = snippet_elem.get_text(strip=True) if snippet_elem else ""
                            
                            results.append({
                                'title': title,
                                'url': url,
                                'snippet': snippet,
                                'source': 'bing'
                            })
                            
                except Exception as e:
                    continue
            
            return results
            
        except Exception as e:
            logger.error(f"❌ خطأ في تحليل Bing: {e}")
            return []
    
    def _parse_yandex_results(self, soup: BeautifulSoup) -> List[Dict]:
        """تحليل نتائج Yandex"""
        results = []
        try:
            # البحث عن النتائج في Yandex
            result_elements = soup.find_all('div', class_='organic')
            
            for element in result_elements:
                try:
                    title_elem = element.find('h2')
                    if title_elem:
                        link_elem = title_elem.find('a')
                        if link_elem:
                            title = link_elem.get_text(strip=True)
                            url = link_elem.get('href')
                            
                            snippet_elem = element.find('div', class_='text-container')
                            snippet = snippet_elem.get_text(strip=True) if snippet_elem else ""
                            
                            results.append({
                                'title': title,
                                'url': url,
                                'snippet': snippet,
                                'source': 'yandex'
                            })
                            
                except Exception as e:
                    continue
            
            return results
            
        except Exception as e:
            logger.error(f"❌ خطأ في تحليل Yandex: {e}")
            return []
    
    def _process_and_rank_results(self, results: List[Dict], query: str) -> List[Dict]:
        """معالجة وترتيب النتائج"""
        try:
            # إزالة التكرارات
            unique_results = []
            seen_urls = set()
            
            for result in results:
                url = result.get('url', '')
                if url and url not in seen_urls:
                    seen_urls.add(url)
                    unique_results.append(result)
            
            # ترتيب النتائج حسب الصلة
            ranked_results = []
            
            for result in unique_results:
                score = self._calculate_relevance_score(result, query)
                result['relevance_score'] = score
                ranked_results.append(result)
            
            # ترتيب حسب النقاط
            ranked_results.sort(key=lambda x: x['relevance_score'], reverse=True)
            
            return ranked_results
            
        except Exception as e:
            logger.error(f"❌ خطأ في معالجة النتائج: {e}")
            return results
    
    def _calculate_relevance_score(self, result: Dict, query: str) -> float:
        """حساب نقاط الصلة للنتيجة"""
        try:
            score = 0.0
            title = result.get('title', '').lower()
            snippet = result.get('snippet', '').lower()
            url = result.get('url', '').lower()
            query_lower = query.lower()
            
            # نقاط للكلمات المفتاحية في العنوان
            query_words = query_lower.split()
            for word in query_words:
                if word in title:
                    score += 2.0
                if word in snippet:
                    score += 1.0
                if word in url:
                    score += 0.5
            
            # نقاط إضافية لمواقع الألعاب المعروفة
            for gaming_site in self.gaming_news_sites:
                if gaming_site in url:
                    score += 3.0
                    break
            
            # نقاط للكلمات المفتاحية للألعاب
            gaming_keywords = ['game', 'gaming', 'video game', 'pc', 'console', 'playstation', 'xbox', 'nintendo']
            for keyword in gaming_keywords:
                if keyword in title:
                    score += 1.0
                if keyword in snippet:
                    score += 0.5
            
            return score
            
        except Exception as e:
            logger.warning(f"⚠️ خطأ في حساب نقاط الصلة: {e}")
            return 0.0
    
    async def _smart_delay(self):
        """تأخير ذكي لتجنب الحظر"""
        try:
            current_time = time.time()
            time_since_last = current_time - self.usage_stats['last_search_time']
            
            # حساب التأخير المطلوب
            min_delay = self.request_delays['min_delay']
            max_delay = self.request_delays['max_delay']
            
            if time_since_last < min_delay:
                delay = min_delay - time_since_last
                # إضافة عشوائية للتأخير
                delay += random.uniform(0, 1)
                await asyncio.sleep(delay)
            
            self.usage_stats['last_search_time'] = time.time()
            
        except Exception as e:
            logger.warning(f"⚠️ خطأ في التأخير الذكي: {e}")
    
    def get_usage_stats(self) -> Dict:
        """الحصول على إحصائيات الاستخدام"""
        return self.usage_stats.copy()

# إنشاء كائن النظام
intelligent_search = IntelligentSearchSystem()
