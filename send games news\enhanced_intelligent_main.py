#!/usr/bin/env python3
# النظام الذكي المحسن المتكامل
import asyncio
import time
import sys
import os
from datetime import datetime
from typing import List, Dict

# إضافة المسار الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.logger import logger
from modules.intelligent_content_processor import intelligent_content_processor
from modules.enhanced_search_integration import enhanced_search
from modules.intelligent_news_tracker import intelligent_news_tracker
from modules.enhanced_image_manager import enhanced_image_manager

class EnhancedIntelligentNewsBot:
    """الوكيل الذكي المحسن للأخبار"""
    
    def __init__(self):
        self.session_stats = {
            'session_start': datetime.now(),
            'articles_collected': 0,
            'important_stories': 0,
            'follow_up_searches': 0,
            'images_generated': 0,
            'total_cost': 0.0
        }
        
        # إعدادات الجلسة
        self.settings = {
            'max_articles_per_run': 15,
            'enable_intelligent_tracking': True,
            'enable_enhanced_images': True,
            'max_images_per_article': 3,
            'quality_threshold': 70.0
        }
        
        logger.info("🤖 تم تهيئة الوكيل الذكي المحسن للأخبار")
    
    async def run_intelligent_news_collection(self) -> List[Dict]:
        """تشغيل جمع الأخبار الذكي"""
        try:
            session_start = time.time()
            logger.info("🚀 بدء جلسة جمع الأخبار الذكية...")
            
            # المرحلة 1: جمع المحتوى الأولي
            logger.info("📰 المرحلة 1: جمع المحتوى الأولي...")
            initial_articles = await self._collect_initial_content()
            
            if not initial_articles:
                logger.warning("⚠️ لم يتم العثور على مقالات في المرحلة الأولية")
                return []
            
            self.session_stats['articles_collected'] = len(initial_articles)
            logger.info(f"✅ تم جمع {len(initial_articles)} مقال في المرحلة الأولية")
            
            # المرحلة 2: المعالجة الذكية
            logger.info("🧠 المرحلة 2: المعالجة الذكية للمحتوى...")
            processed_articles = await intelligent_content_processor.process_articles_intelligently(
                initial_articles[:self.settings['max_articles_per_run']]
            )
            
            # المرحلة 3: التحليل النهائي والإحصائيات
            final_articles = await self._finalize_and_analyze(processed_articles)
            
            # تحديث الإحصائيات
            session_time = time.time() - session_start
            await self._update_session_stats(final_articles, session_time)
            
            # عرض الملخص النهائي
            self._display_session_summary(final_articles, session_time)
            
            return final_articles
            
        except Exception as e:
            logger.error(f"❌ فشل في تشغيل جمع الأخبار الذكي: {e}")
            return []
    
    async def _collect_initial_content(self) -> List[Dict]:
        """جمع المحتوى الأولي"""
        try:
            # كلمات مفتاحية ذكية للبحث - محسنة لـ Tavily
            smart_keywords = [
                'latest gaming news today',
                'video game announcements 2025',
                'breaking gaming industry news',
                'new game releases updates',
                'gaming industry developments'
            ]
            
            all_articles = []
            
            for i, keyword in enumerate(smart_keywords[:3], 1):  # أفضل 3 كلمات
                try:
                    logger.info(f"🔍 البحث الذكي {i}/3: '{keyword}'")
                    
                    # استخدام النظام المحسن للبحث - Tavily أولاً
                    search_results = await enhanced_search.enhanced_search(
                        query=keyword,
                        max_results=8,
                        search_type="gaming_news",
                        priority="free"  # يبدأ بـ Tavily (مجاني وقوي)
                    )
                    
                    if search_results:
                        all_articles.extend(search_results)
                        logger.info(f"✅ تم العثور على {len(search_results)} مقال لـ '{keyword}'")
                    else:
                        logger.info(f"📭 لم يتم العثور على نتائج لـ '{keyword}'")
                    
                    # تأخير ذكي بين البحثات
                    await asyncio.sleep(2)
                    
                except Exception as e:
                    logger.warning(f"⚠️ فشل في البحث عن '{keyword}': {e}")
                    continue
            
            # إزالة المقالات المكررة
            unique_articles = self._remove_duplicates(all_articles)
            
            logger.info(f"📊 إجمالي المقالات الفريدة: {len(unique_articles)}")
            return unique_articles
            
        except Exception as e:
            logger.error(f"❌ فشل في جمع المحتوى الأولي: {e}")
            return []
    
    def _remove_duplicates(self, articles: List[Dict]) -> List[Dict]:
        """إزالة المقالات المكررة"""
        try:
            seen_titles = set()
            seen_urls = set()
            unique_articles = []
            
            for article in articles:
                title = article.get('title', '').lower().strip()
                url = article.get('url', '').strip()
                
                # تنظيف العنوان للمقارنة
                clean_title = self._clean_title_for_comparison(title)
                
                # فحص التكرار
                if clean_title not in seen_titles and url not in seen_urls:
                    unique_articles.append(article)
                    seen_titles.add(clean_title)
                    if url:
                        seen_urls.add(url)
            
            removed_count = len(articles) - len(unique_articles)
            if removed_count > 0:
                logger.info(f"🧹 تم إزالة {removed_count} مقال مكرر")
            
            return unique_articles
            
        except Exception as e:
            logger.error(f"❌ فشل في إزالة المقالات المكررة: {e}")
            return articles
    
    def _clean_title_for_comparison(self, title: str) -> str:
        """تنظيف العنوان للمقارنة"""
        import re
        
        # إزالة علامات الترقيم والمسافات الزائدة
        clean_title = re.sub(r'[^\w\s]', '', title)
        clean_title = re.sub(r'\s+', ' ', clean_title).strip()
        
        return clean_title
    
    async def _finalize_and_analyze(self, articles: List[Dict]) -> List[Dict]:
        """التحليل النهائي والتنظيم"""
        try:
            if not articles:
                return []
            
            # فلترة المقالات عالية الجودة
            quality_articles = [
                article for article in articles 
                if article.get('final_quality_score', 0) >= self.settings['quality_threshold']
            ]
            
            if not quality_articles:
                logger.warning("⚠️ لا توجد مقالات تلبي معايير الجودة، استخدام جميع المقالات")
                quality_articles = articles
            
            # ترتيب نهائي حسب الأهمية والجودة
            sorted_articles = sorted(
                quality_articles,
                key=lambda x: (
                    x.get('importance_score', 0) * 0.4 +
                    x.get('final_quality_score', 0) * 0.3 +
                    (len(x.get('additional_info', [])) * 10) * 0.2 +
                    (x.get('images_generated', 0) * 5) * 0.1
                ),
                reverse=True
            )
            
            # إضافة ترقيم نهائي
            for i, article in enumerate(sorted_articles):
                article['final_rank'] = i + 1
                article['session_id'] = int(time.time())
            
            logger.info(f"📊 تم تنظيم {len(sorted_articles)} مقال نهائي")
            return sorted_articles
            
        except Exception as e:
            logger.error(f"❌ فشل في التحليل النهائي: {e}")
            return articles
    
    async def _update_session_stats(self, articles: List[Dict], session_time: float):
        """تحديث إحصائيات الجلسة"""
        try:
            # إحصائيات المقالات
            important_articles = [a for a in articles if a.get('importance_score', 0) >= 80]
            self.session_stats['important_stories'] = len(important_articles)
            
            # إحصائيات المتابعة
            follow_up_articles = [a for a in articles if a.get('additional_info')]
            self.session_stats['follow_up_searches'] = len(follow_up_articles)
            
            # إحصائيات الصور
            total_images = sum(a.get('images_generated', 0) for a in articles)
            self.session_stats['images_generated'] = total_images
            
            # إحصائيات التكلفة
            image_cost = sum(a.get('total_cost', 0) for a in articles)
            search_cost = 0.05 * len(articles)  # تقدير تكلفة البحث
            self.session_stats['total_cost'] = image_cost + search_cost
            
            # إحصائيات الوقت
            self.session_stats['session_duration'] = session_time
            
        except Exception as e:
            logger.error(f"❌ فشل في تحديث إحصائيات الجلسة: {e}")
    
    def _display_session_summary(self, articles: List[Dict], session_time: float):
        """عرض ملخص الجلسة"""
        try:
            print("\n" + "="*80)
            print("📊 ملخص جلسة الوكيل الذكي المحسن")
            print("="*80)
            
            # إحصائيات أساسية
            print(f"⏱️  مدة الجلسة: {session_time:.1f} ثانية")
            print(f"📰 إجمالي المقالات: {len(articles)}")
            print(f"⭐ المقالات المهمة: {self.session_stats['important_stories']}")
            print(f"🔍 عمليات المتابعة: {self.session_stats['follow_up_searches']}")
            print(f"🎨 الصور المولدة: {self.session_stats['images_generated']}")
            print(f"💰 التكلفة التقديرية: ${self.session_stats['total_cost']:.3f}")
            
            # أفضل المقالات
            print(f"\n🏆 أفضل 3 مقالات:")
            for i, article in enumerate(articles[:3], 1):
                title = article.get('title', 'بدون عنوان')[:60]
                importance = article.get('importance_score', 0)
                quality = article.get('final_quality_score', 0)
                images = article.get('images_generated', 0)
                
                print(f"  {i}. {title}...")
                print(f"     🎯 الأهمية: {importance:.1f} | 💎 الجودة: {quality:.1f} | 🎨 الصور: {images}")
            
            # إحصائيات النظام
            print(f"\n🔧 إحصائيات النظام:")
            
            # إحصائيات التتبع
            tracking_stats = intelligent_news_tracker.get_tracking_stats()
            print(f"  📋 القصص المتتبعة: {tracking_stats.get('active_stories', 0)}")
            print(f"  🔄 المتابعات المعلقة: {tracking_stats.get('pending_follow_ups', 0)}")
            
            # إحصائيات الصور
            image_stats = enhanced_image_manager.get_daily_stats()
            print(f"  🎨 حصة الصور المتبقية: {image_stats.get('remaining_quota', 0)}")
            print(f"  📦 معدل نجاح التخزين المؤقت: {image_stats.get('cache_hit_rate', 0)}%")
            
            # إحصائيات البحث
            search_stats = enhanced_search.get_system_status()
            emergency_mode = search_stats.get('emergency_mode', False)
            print(f"  🚨 وضع الطوارئ: {'مفعل' if emergency_mode else 'غير مفعل'}")
            
            print("="*80)
            print("✅ اكتملت جلسة الوكيل الذكي بنجاح!")
            print("="*80)
            
        except Exception as e:
            logger.error(f"❌ فشل في عرض ملخص الجلسة: {e}")
    
    def get_detailed_stats(self) -> Dict:
        """الحصول على إحصائيات مفصلة"""
        try:
            # جمع إحصائيات من جميع المكونات
            processing_stats = intelligent_content_processor.get_processing_stats()
            tracking_stats = intelligent_news_tracker.get_tracking_stats()
            image_stats = enhanced_image_manager.get_daily_stats()
            search_stats = enhanced_search.get_system_status()
            
            return {
                'session_stats': self.session_stats,
                'processing_stats': processing_stats,
                'tracking_stats': tracking_stats,
                'image_stats': image_stats,
                'search_stats': search_stats,
                'settings': self.settings
            }
            
        except Exception as e:
            logger.error(f"❌ فشل في جمع الإحصائيات المفصلة: {e}")
            return {}

async def main():
    """الدالة الرئيسية"""
    try:
        print("🤖 مرحباً بك في الوكيل الذكي المحسن للأخبار!")
        print("🚀 بدء تشغيل النظام...")
        
        # إنشاء الوكيل
        bot = EnhancedIntelligentNewsBot()
        
        # تشغيل جمع الأخبار
        articles = await bot.run_intelligent_news_collection()
        
        if articles:
            print(f"\n🎉 تم جمع {len(articles)} مقال بنجاح!")
            
            # عرض عينة من النتائج
            print("\n📰 عينة من أفضل المقالات:")
            for i, article in enumerate(articles[:3], 1):
                title = article.get('title', 'بدون عنوان')
                importance = article.get('importance_score', 0)
                images = article.get('images_generated', 0)
                
                print(f"\n{i}. {title}")
                print(f"   🎯 نقاط الأهمية: {importance:.1f}")
                print(f"   🎨 عدد الصور: {images}")
                
                if article.get('additional_info'):
                    print(f"   🔍 معلومات إضافية: {len(article['additional_info'])} مصدر")
        else:
            print("❌ لم يتم العثور على مقالات")
        
        # عرض الإحصائيات المفصلة
        detailed_stats = bot.get_detailed_stats()
        
        print(f"\n📊 إحصائيات مفصلة محفوظة في: detailed_stats.json")
        
        # حفظ الإحصائيات
        import json
        with open('detailed_stats.json', 'w', encoding='utf-8') as f:
            json.dump(detailed_stats, f, ensure_ascii=False, indent=2, default=str)
        
    except Exception as e:
        print(f"❌ خطأ في التشغيل: {e}")
        logger.error(f"خطأ في الدالة الرئيسية: {e}")

if __name__ == "__main__":
    asyncio.run(main())
