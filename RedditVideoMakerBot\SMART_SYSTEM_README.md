# 🤖 النظام الذكي - Reddit Video Maker Bot

## 🌟 المميزات الجديدة

### 🔧 إدارة ذكية للأخطاء
- **تبديل تلقائي** بين محركات TTS عند الفشل
- **إصلاح تلقائي** للمشاكل الشائعة
- **إشعارات فورية** عبر التيليجرام عند حدوث مشاكل

### 🔑 إدارة APIs متقدمة
- **مفاتيح متعددة** لكل خدمة
- **تبديل تلقائي** عند انتهاء الرصيد
- **إدارة عن بُعد** عبر بوت التيليجرام
- **اختبار APIs** بضغطة زر

### 📊 مراقبة مستمرة
- **فحص دوري** لصحة النظام
- **تقارير تفصيلية** عن الأداء
- **إحصائيات الاستخدام** لكل API

## 🚀 التشغيل

### الطريقة العادية (مع النظام الذكي):
```bash
python main.py
```

### تشغيل مدير API عبر التيليجرام:
```bash
python start_telegram_manager.py
```

### تشغيل النظام الذكي الكامل:
```bash
python start_smart_system.py
```

## ⚙️ الإعدادات الجديدة

### في `config.toml`:

```toml
[settings.tts]
voice_choice = "auto"  # للتبديل الذكي
auto_fallback = true   # تفعيل النظام الذكي

# ترتيب أولوية المحركات
[settings.tts.priority_order]
primary = ["ElevenLabs", "GoogleTranslate", "AWSPolly", "pyttsx"]
fallback_on_error = true
max_retries_per_engine = 3

# مفاتيح API متعددة
[settings.tts.api_keys]
elevenlabs_keys = [
    "***************************************************",
    "sk_your_second_key_here"
]
aws_profiles = ["default", "polly"]

# إعدادات ElevenLabs المجاني
elevenlabs_use_free = true
elevenlabs_voice_id = "21m00Tcm4TlvDq8ikWAM"  # Rachel
```

## 🎤 الأصوات المجانية في ElevenLabs

### الأصوات المتاحة مجاناً:
- **Rachel** (21m00Tcm4TlvDq8ikWAM) - صوت أنثوي واضح
- **Drew** (29vD33N1CtxCmqQRPOHJ) - صوت ذكوري شاب
- **Clyde** (2EiwWnXFnvU5JabPnv8n) - صوت ذكوري متوسط
- **Paul** (5Q0t7uMcjvnagumLfvZi) - صوت ذكوري عميق
- **Domi** (AZnzlk1XvdvUeBnXmlld) - صوت أنثوي شاب
- **Dave** (CYw3kZ02Hs0563khs1Fj) - صوت ذكوري ودود
- **Fin** (D38z5RcWu1voky8WS1ja) - صوت ذكوري أيرلندي
- **Sarah** (EXAVITQu4vr4xnSDxMaL) - صوت أنثوي ناعم
- **Antoni** (ErXwobaYiN019PkySvjV) - صوت ذكوري أمريكي
- **Thomas** (GBv7mTt0atIp3Br8iCZE) - صوت ذكوري بريطاني

### كيفية الاستخدام:
1. في التيليجرام: `/start` → "🎤 إعداد ElevenLabs مجاني"
2. اختر Voice ID من القائمة أعلاه
3. أرسل الـ ID (مثل: 21m00Tcm4TlvDq8ikWAM)
4. ✅ سيتم إعداد الصوت تلقائياً بدون API key!

## 🎛️ أوامر بوت التيليجرام

### الأوامر الأساسية:
- `/start` - القائمة الرئيسية
- `/status` - حالة النظام
- `/apis` - معلومات APIs
- `/add_key` - إضافة مفتاح جديد
- `/test_key` - اختبار جميع المفاتيح
- `/run_bot` - تشغيل البوت
- `/stop_bot` - إيقاف البوت
- `/bot_status` - حالة البوت

### إدارة المفاتيح:
1. **إضافة مفتاح ElevenLabs**: `/add_key` → اختر ElevenLabs → أرسل المفتاح
2. **إضافة AWS Profile**: `/add_key` → اختر AWS → أرسل اسم Profile
3. **تحديث TikTok Session**: `/add_key` → اختر TikTok → أرسل Session ID
4. **إعداد ElevenLabs مجاني**: `/start` → "🎤 إعداد ElevenLabs مجاني" → أرسل Voice ID

### التحكم في البوت:
1. **تشغيل البوت**: `/start` → "▶️ تشغيل البوت" أو `/run_bot`
2. **إيقاف البوت**: `/start` → "⏹️ إيقاف البوت" أو `/stop_bot`
3. **حالة البوت**: `/start` → "🤖 البوت: حالة" أو `/bot_status`

## 🔄 كيف يعمل النظام الذكي

### 1. تبديل تلقائي للمحركات:
```
ElevenLabs (فشل) → GoogleTranslate (نجح) ✅
```

### 2. إدارة مفاتيح متعددة:
```
ElevenLabs Key 1 (انتهى الرصيد) → ElevenLabs Key 2 ✅
```

### 3. إصلاح تلقائي:
```
خطأ في لقطات الشاشة → إعادة تثبيت Playwright → إصلاح ✅
```

## 📈 مراقبة الأداء

### تقارير تلقائية:
- **كل ساعة**: تقرير استخدام APIs
- **عند الأخطاء**: إشعار فوري
- **يومياً**: تقرير شامل عن الأداء

### إحصائيات متاحة:
- عدد الفيديوهات المنشأة
- معدل نجاح كل محرك TTS
- استهلاك APIs
- أوقات الاستجابة

## 🛠️ استكشاف الأخطاء

### المشاكل الشائعة:

#### 1. فشل جميع محركات TTS:
```
الحل: تحقق من مفاتيح API في /apis
```

#### 2. مشاكل لقطات الشاشة:
```
الحل: النظام سيصلح تلقائياً أو أرسل /status
```

#### 3. انقطاع الإنترنت:
```
الحل: النظام سينتظر ويعيد المحاولة تلقائياً
```

## 🔐 الأمان

### حماية المفاتيح:
- **تشفير** في ملفات السجلات
- **إخفاء** في الرسائل (يظهر فقط أول 10 أحرف)
- **نسخ احتياطي** آمن للإعدادات

### التحكم في الوصول:
- **قائمة مستخدمين مصرح لهم** لبوت التيليجرام
- **تسجيل** جميع العمليات
- **إشعارات** عند محاولات الوصول غير المصرح

## 📞 الدعم

### في حالة المشاكل:
1. تحقق من `/status` في التيليجرام
2. راجع ملف `logs/smart_system.log`
3. أرسل تقرير الخطأ عبر التيليجرام

### الحصول على المساعدة:
- **التيليجرام**: سيرسل النظام تقارير تلقائية
- **السجلات**: ملفات مفصلة في مجلد `logs/`
- **المراقبة**: فحص مستمر وتقارير دورية

---

## 🎯 الخلاصة

النظام الذكي يجعل البوت:
- ✅ **أكثر استقراراً** - إصلاح تلقائي للمشاكل
- ✅ **أكثر ذكاءً** - تبديل تلقائي بين الخدمات  
- ✅ **أسهل في الإدارة** - تحكم عن بُعد عبر التيليجرام
- ✅ **أكثر موثوقية** - مراقبة مستمرة وإشعارات فورية

🚀 **استمتع بتجربة إنشاء فيديوهات بدون مشاكل!**
