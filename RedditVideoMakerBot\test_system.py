#!/usr/bin/env python3
"""
اختبار شامل للنظام قبل تشغيل البوت
"""

import os
import sys
from pathlib import Path
import json

def test_files():
    """اختبار وجود الملفات الأساسية"""
    print("🔍 فحص الملفات الأساسية...")
    print("=" * 40)
    
    required_files = [
        'config.toml',
        'telegram_chat_id.txt',
        'video_creation/screenshot_downloader.py',
        'screenshot_fallback.py',
        'assets/backgrounds/video/simple-background.mp4',
        'assets/backgrounds/audio/silent.mp3'
    ]
    
    missing_files = []
    
    for file_path in required_files:
        if Path(file_path).exists():
            size = Path(file_path).stat().st_size
            print(f"✅ {file_path} ({size} bytes)")
        else:
            print(f"❌ {file_path} - مفقود")
            missing_files.append(file_path)
    
    return len(missing_files) == 0

def test_config():
    """اختبار إعدادات config.toml"""
    print("\n🔍 فحص الإعدادات...")
    print("=" * 25)
    
    try:
        import toml
        with open('config.toml', 'r', encoding='utf-8') as f:
            config = toml.load(f)
        
        # فحص الإعدادات المهمة
        checks = [
            ("Chat ID", config.get("telegram", {}).get("chat_id", "").strip() != "YOUR_CHAT_ID_HERE"),
            ("Background Video", config.get("settings", {}).get("background", {}).get("background_video") == "simple"),
            ("Background Audio", config.get("settings", {}).get("background", {}).get("background_audio") == "silent"),
            ("Audio Volume", config.get("settings", {}).get("background", {}).get("background_audio_volume") == 0),
            ("TTS Engine", config.get("settings", {}).get("tts", {}).get("voice_choice") in ["ElevenLabs", "GoogleTranslate"])
        ]
        
        all_good = True
        for check_name, result in checks:
            if result:
                print(f"✅ {check_name}")
            else:
                print(f"❌ {check_name}")
                all_good = False
        
        return all_good
        
    except Exception as e:
        print(f"❌ خطأ في قراءة الإعدادات: {e}")
        return False

def test_chat_id():
    """اختبار معرف المحادثة"""
    print("\n🔍 فحص معرف المحادثة...")
    print("=" * 30)
    
    try:
        with open('telegram_chat_id.txt', 'r') as f:
            chat_id = f.read().strip()
        
        if chat_id and chat_id != "YOUR_CHAT_ID_HERE" and chat_id.isdigit():
            print(f"✅ معرف المحادثة: {chat_id}")
            return True
        else:
            print(f"❌ معرف المحادثة غير صحيح: {chat_id}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في قراءة معرف المحادثة: {e}")
        return False

def test_screenshot_system():
    """اختبار النظام البديل للقطات الشاشة"""
    print("\n🔍 اختبار النظام البديل للقطات الشاشة...")
    print("=" * 45)
    
    try:
        from screenshot_fallback import create_fallback_screenshots
        
        test_reddit_object = {
            'thread_id': 'test123',
            'thread_title': 'اختبار العنوان - هل يعمل النظام البديل؟',
            'comments': [
                {'comment_body': 'هذا تعليق اختبار رقم 1'},
                {'comment_body': 'هذا تعليق اختبار رقم 2'}
            ]
        }
        
        result = create_fallback_screenshots(test_reddit_object, 2)
        
        if result:
            print("✅ النظام البديل يعمل بشكل صحيح")
            
            # فحص الملفات المنشأة
            test_dir = Path("assets/temp/test123/png")
            if test_dir.exists():
                files = list(test_dir.glob("*.png"))
                print(f"✅ تم إنشاء {len(files)} ملف صورة")
                
                # حذف ملفات الاختبار
                import shutil
                shutil.rmtree("assets/temp/test123")
                print("🧹 تم حذف ملفات الاختبار")
            
            return True
        else:
            print("❌ النظام البديل لا يعمل")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار النظام البديل: {e}")
        return False

def test_imports():
    """اختبار استيراد المكتبات المطلوبة"""
    print("\n🔍 اختبار المكتبات المطلوبة...")
    print("=" * 35)
    
    required_modules = [
        ('toml', 'toml'),
        ('requests', 'requests'),
        ('PIL', 'Pillow'),
        ('numpy', 'numpy'),
        ('rich', 'rich'),
        ('praw', 'praw'),
        ('gtts', 'gTTS')
    ]
    
    missing_modules = []
    
    for module_name, package_name in required_modules:
        try:
            __import__(module_name)
            print(f"✅ {package_name}")
        except ImportError:
            print(f"❌ {package_name} - غير مثبت")
            missing_modules.append(package_name)
    
    if missing_modules:
        print(f"\n💡 لتثبيت المكتبات المفقودة:")
        print(f"pip install {' '.join(missing_modules)}")
    
    return len(missing_modules) == 0

def test_background_files():
    """اختبار ملفات الخلفية"""
    print("\n🔍 اختبار ملفات الخلفية...")
    print("=" * 30)
    
    video_file = Path("assets/backgrounds/video/simple-background.mp4")
    audio_file = Path("assets/backgrounds/audio/silent.mp3")
    
    video_ok = video_file.exists() and video_file.stat().st_size > 1000
    audio_ok = audio_file.exists() and audio_file.stat().st_size > 100
    
    if video_ok:
        print(f"✅ فيديو الخلفية: {video_file.stat().st_size} bytes")
    else:
        print("❌ فيديو الخلفية مفقود أو تالف")
    
    if audio_ok:
        print(f"✅ صوت الخلفية: {audio_file.stat().st_size} bytes")
    else:
        print("❌ صوت الخلفية مفقود أو تالف")
    
    return video_ok and audio_ok

def main():
    """الاختبار الرئيسي"""
    print("🧪 اختبار شامل للنظام")
    print("=" * 25)
    print("يتم فحص جميع المكونات قبل تشغيل البوت...\n")
    
    tests = [
        ("الملفات الأساسية", test_files),
        ("الإعدادات", test_config),
        ("معرف المحادثة", test_chat_id),
        ("المكتبات المطلوبة", test_imports),
        ("ملفات الخلفية", test_background_files),
        ("النظام البديل للقطات الشاشة", test_screenshot_system)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {e}")
            results.append((test_name, False))
    
    # النتائج النهائية
    print("\n" + "=" * 50)
    print("📊 ملخص نتائج الاختبار")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\nالنتيجة: {passed}/{total} اختبارات نجحت")
    
    if passed == total:
        print("\n🎉 جميع الاختبارات نجحت! البوت جاهز للتشغيل")
        print("💡 يمكنك الآن تشغيل: python main.py")
        return True
    else:
        print(f"\n⚠️ {total - passed} اختبارات فشلت")
        print("💡 يُنصح بإصلاح المشاكل قبل تشغيل البوت")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
