import logging
import time
from typing import List, Dict, Optional, Tuple
from pathlib import Path
import json

from TTS.elevenlabs import elevenlabs
from TTS.GTTS import GTTS
from TTS.aws_polly import AWSPolly
from TTS.pyttsx import pyttsx
from TTS.TikTok import TikTok
from TTS.streamlabs_polly import StreamlabsPolly

from utils import settings
from automation.telegram_bot import send_error, send_notification

logger = logging.getLogger(__name__)

class SmartTTSManager:
    """مدير TTS ذكي مع إدارة متقدمة للأخطاء والـ APIs"""
    
    def __init__(self):
        self.engines = {
            "ElevenLabs": elevenlabs,
            "GoogleTranslate": GTTS,
            "AWSPolly": AWS<PERSON>olly,
            "pyttsx": pyttsx,
            "TikTok": TikTok,
            "StreamlabsPolly": StreamlabsPolly
        }
        
        self.current_engine = None
        self.failed_engines = set()
        self.api_usage = self._load_api_usage()
        self.retry_counts = {}
        
    def _load_api_usage(self) -> Dict:
        """تحميل إحصائيات استخدام الـ APIs"""
        usage_file = Path("logs/api_usage.json")
        if usage_file.exists():
            try:
                with open(usage_file, 'r') as f:
                    return json.load(f)
            except:
                pass
        return {}
    
    def _save_api_usage(self):
        """حفظ إحصائيات استخدام الـ APIs"""
        usage_file = Path("logs/api_usage.json")
        usage_file.parent.mkdir(exist_ok=True)
        with open(usage_file, 'w') as f:
            json.dump(self.api_usage, f, indent=2)
    
    def _get_priority_engines(self) -> List[str]:
        """الحصول على ترتيب أولوية المحركات حسب الإعدادات"""
        # استخدام ترتيب الأولوية من الإعدادات مباشرة
        if settings.config["settings"]["tts"]["voice_choice"] == "auto":
            priority_engines = settings.config["settings"]["tts"]["priority_order"]["primary"].copy()
        else:
            chosen_engine = settings.config["settings"]["tts"]["voice_choice"]
            priority_engines = [chosen_engine]
            # إضافة باقي المحركات كبدائل
            other_engines = [engine for engine in settings.config["settings"]["tts"]["priority_order"]["primary"]
                           if engine != chosen_engine]
            priority_engines.extend(other_engines)

        logger.info(f"🎯 ترتيب أولوية المحركات: {priority_engines}")
        return priority_engines
    
    def _is_engine_available(self, engine_name: str) -> bool:
        """فحص توفر المحرك"""
        if engine_name in self.failed_engines:
            return False
            
        if engine_name == "ElevenLabs":
            return self._check_elevenlabs_availability()
        elif engine_name == "AWSPolly":
            return self._check_aws_availability()
        elif engine_name == "TikTok":
            return self._check_tiktok_availability()
        
        return True
    
    def _check_elevenlabs_availability(self) -> bool:
        """فحص توفر ElevenLabs - متوفر دائماً مع المفاتيح الجديدة"""
        keys = settings.config["settings"]["tts"]["api_keys"]["elevenlabs_keys"]
        available_keys = [key for key in keys if key.strip() and not self._is_key_exhausted("ElevenLabs", key)]

        logger.info(f"🔑 مفاتيح ElevenLabs المتاحة: {len(available_keys)}/{len(keys)}")

        # ElevenLabs متوفر إذا كان هناك مفتاح واحد على الأقل
        return len(available_keys) > 0
    
    def _check_aws_availability(self) -> bool:
        """فحص توفر AWS Polly"""
        try:
            from boto3 import Session
            profiles = settings.config["settings"]["tts"]["api_keys"]["aws_profiles"]
            for profile in profiles:
                try:
                    session = Session(profile_name=profile)
                    session.client("polly")
                    return True
                except:
                    continue
            return False
        except ImportError:
            return False
    
    def _check_tiktok_availability(self) -> bool:
        """فحص توفر TikTok TTS"""
        session_id = settings.config["settings"]["tts"]["tiktok_sessionid"]
        return bool(session_id and session_id.strip())
    
    def _get_next_api_key(self, engine_name: str) -> Optional[str]:
        """الحصول على مفتاح API التالي"""
        if engine_name == "ElevenLabs":
            keys = settings.config["settings"]["tts"]["api_keys"]["elevenlabs_keys"]
            for key in keys:
                if key.strip() and not self._is_key_exhausted(engine_name, key):
                    return key
        return None
    
    def _is_key_exhausted(self, engine_name: str, api_key: str) -> bool:
        """فحص إذا كان المفتاح منتهي الرصيد"""
        key_usage = self.api_usage.get(f"{engine_name}_{api_key[:10]}", {})
        return key_usage.get("exhausted", False)
    
    def _mark_key_exhausted(self, engine_name: str, api_key: str):
        """تمييز المفتاح كمنتهي الرصيد"""
        key_id = f"{engine_name}_{api_key[:10]}"
        self.api_usage[key_id] = {
            "exhausted": True,
            "exhausted_at": time.time()
        }
        self._save_api_usage()
        
        # إرسال إشعار تيليجرام
        message = f"⚠️ انتهى رصيد {engine_name} API Key: {api_key[:10]}..."
        send_notification(message)
    
    def _try_engine(self, engine_name: str, text: str, filepath: str) -> bool:
        """محاولة استخدام محرك معين"""
        try:
            logger.info(f"🔄 محاولة استخدام {engine_name}")
            
            # إعداد المحرك حسب النوع
            if engine_name == "ElevenLabs":
                return self._try_elevenlabs(text, filepath)
            else:
                engine_class = self.engines[engine_name]
                engine = engine_class()
                engine.run(text, filepath)
                return True
                
        except Exception as e:
            logger.error(f"❌ فشل {engine_name}: {str(e)}")
            self._handle_engine_error(engine_name, str(e))
            return False
    
    def _try_elevenlabs(self, text: str, filepath: str) -> bool:
        """محاولة استخدام ElevenLabs مع مفاتيح متعددة"""
        keys = settings.config["settings"]["tts"]["api_keys"]["elevenlabs_keys"]
        
        for api_key in keys:
            if not api_key.strip() or self._is_key_exhausted("ElevenLabs", api_key):
                continue
                
            try:
                # تحديث المفتاح في الإعدادات مؤقتاً
                original_key = settings.config["settings"]["tts"]["elevenlabs_api_key"]
                settings.config["settings"]["tts"]["elevenlabs_api_key"] = api_key
                
                engine = elevenlabs()
                engine.run(text, filepath)
                
                # استعادة المفتاح الأصلي
                settings.config["settings"]["tts"]["elevenlabs_api_key"] = original_key
                
                logger.info(f"✅ نجح ElevenLabs مع المفتاح: {api_key[:10]}...")
                return True
                
            except Exception as e:
                error_msg = str(e).lower()
                if "quota" in error_msg or "limit" in error_msg or "insufficient" in error_msg or "detected_unusual_activity" in error_msg:
                    self._mark_key_exhausted("ElevenLabs", api_key)
                
                logger.warning(f"⚠️ فشل مفتاح ElevenLabs {api_key[:10]}...: {str(e)}")
                continue
        
        return False
    
    def _handle_engine_error(self, engine_name: str, error: str):
        """معالجة أخطاء المحركات"""
        self.retry_counts[engine_name] = self.retry_counts.get(engine_name, 0) + 1
        max_retries = settings.config["settings"]["tts"]["priority_order"]["max_retries_per_engine"]
        
        if self.retry_counts[engine_name] >= max_retries:
            self.failed_engines.add(engine_name)
            logger.warning(f"🚫 تم تعطيل {engine_name} بعد {max_retries} محاولات فاشلة")
            
            # إرسال إشعار تيليجرام
            message = f"🚫 تم تعطيل محرك الصوت {engine_name} بسبب أخطاء متكررة: {error}"
            send_error(message)
    
    def generate_speech(self, text: str, filepath: str) -> bool:
        """إنشاء الصوت مع إدارة ذكية للأخطاء"""
        engines_to_try = self._get_priority_engines()
        
        for engine_name in engines_to_try:
            if not self._is_engine_available(engine_name):
                logger.info(f"⏭️ تخطي {engine_name} - غير متوفر")
                continue
            
            if self._try_engine(engine_name, text, filepath):
                logger.info(f"✅ نجح إنشاء الصوت باستخدام {engine_name}")
                self.current_engine = engine_name
                return True
        
        # إذا فشلت جميع المحركات
        error_msg = "❌ فشل جميع محركات الصوت المتاحة"
        logger.error(error_msg)
        send_error(f"{error_msg}\nالمحركات المجربة: {', '.join(engines_to_try)}")
        return False
    
    def get_status_report(self) -> str:
        """تقرير حالة المحركات"""
        report = "📊 تقرير حالة محركات الصوت:\n\n"
        
        for engine_name in self.engines.keys():
            status = "✅ متوفر" if self._is_engine_available(engine_name) else "❌ غير متوفر"
            retries = self.retry_counts.get(engine_name, 0)
            report += f"{engine_name}: {status} (محاولات: {retries})\n"
        
        report += f"\nالمحرك الحالي: {self.current_engine or 'لا يوجد'}"
        return report
