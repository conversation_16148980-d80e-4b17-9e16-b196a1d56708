#!/usr/bin/env python3
"""
ملف الإعدادات لأداة GitHub Uploader
"""

import os
from pathlib import Path

# إعدادات GitHub API
GITHUB_API_BASE_URL = "https://api.github.com"
GITHUB_API_VERSION = "application/vnd.github.v3+json"

# إعدادات الملفات
DEFAULT_GITIGNORE_TEMPLATES = {
    "python": [
        "__pycache__/",
        "*.py[cod]",
        "*$py.class",
        "*.so",
        ".Python",
        "build/",
        "develop-eggs/",
        "dist/",
        "downloads/",
        "eggs/",
        ".eggs/",
        "lib/",
        "lib64/",
        "parts/",
        "sdist/",
        "var/",
        "wheels/",
        "*.egg-info/",
        ".installed.cfg",
        "*.egg",
        "MANIFEST",
        "*.manifest",
        "*.spec",
        "pip-log.txt",
        "pip-delete-this-directory.txt",
        "htmlcov/",
        ".tox/",
        ".coverage",
        ".coverage.*",
        ".cache",
        "nosetests.xml",
        "coverage.xml",
        "*.cover",
        ".hypothesis/",
        ".pytest_cache/",
        "*.mo",
        "*.pot",
        "*.log",
        "local_settings.py",
        "db.sqlite3",
        "instance/",
        ".webassets-cache",
        ".scrapy",
        "docs/_build/",
        "target/",
        ".ipynb_checkpoints",
        ".python-version",
        "celerybeat-schedule",
        "*.sage.py",
        ".env",
        ".venv",
        "env/",
        "venv/",
        "ENV/",
        "env.bak/",
        "venv.bak/",
        ".spyderproject",
        ".spyproject",
        ".ropeproject",
        "/site",
        ".mypy_cache/",
        ".dmypy.json",
        "dmypy.json"
    ],
    "javascript": [
        "logs",
        "*.log",
        "npm-debug.log*",
        "yarn-debug.log*",
        "yarn-error.log*",
        "pids",
        "*.pid",
        "*.seed",
        "*.pid.lock",
        "lib-cov",
        "coverage",
        ".nyc_output",
        ".grunt",
        "bower_components",
        ".lock-wscript",
        "build/Release",
        "node_modules/",
        "jspm_packages/",
        "typings/",
        ".npm",
        ".eslintcache",
        ".node_repl_history",
        "*.tgz",
        ".yarn-integrity",
        ".env",
        ".next",
        ".nuxt",
        ".vuepress/dist",
        ".serverless"
    ],
    "java": [
        "*.class",
        "*.log",
        "*.ctxt",
        ".mtj.tmp/",
        "*.jar",
        "*.war",
        "*.nar",
        "*.ear",
        "*.zip",
        "*.tar.gz",
        "*.rar",
        "hs_err_pid*",
        "target/",
        "pom.xml.tag",
        "pom.xml.releaseBackup",
        "pom.xml.versionsBackup",
        "pom.xml.next",
        "release.properties",
        "dependency-reduced-pom.xml",
        "buildNumber.properties",
        ".mvn/timing.properties",
        ".mvn/wrapper/maven-wrapper.jar"
    ]
}

# إعدادات README
README_TEMPLATES = {
    "python": {
        "installation": "pip install -r requirements.txt",
        "run": "python main.py",
        "requirements": "Python 3.6+"
    },
    "javascript": {
        "installation": "npm install",
        "run": "npm start",
        "requirements": "Node.js, npm"
    },
    "java": {
        "installation": "javac *.java",
        "run": "java Main",
        "requirements": "Java JDK 8+"
    }
}

# إعدادات الرسائل
MESSAGES = {
    "ar": {
        "success_repo_created": "✅ تم إنشاء المستودع بنجاح",
        "error_repo_creation": "❌ فشل في إنشاء المستودع",
        "success_git_init": "✅ تم تهيئة مستودع Git محلي",
        "error_git_init": "❌ فشل في تهيئة Git",
        "success_remote_added": "✅ تم إضافة remote origin",
        "error_remote_add": "❌ فشل في إضافة remote origin",
        "success_files_added": "✅ تم إضافة الملفات",
        "success_commit": "✅ تم عمل commit",
        "success_push": "✅ تم رفع المشروع على GitHub",
        "error_push": "❌ فشل في رفع المشروع",
        "success_gitignore": "✅ تم إنشاء ملف .gitignore",
        "success_readme": "✅ تم إنشاء ملف README.md",
        "project_uploaded": "🎉 تم رفع المشروع بنجاح!",
        "project_type_detected": "📁 نوع المشروع",
        "starting_upload": "🚀 بدء رفع المشروع",
        "folder_not_found": "❌ المجلد غير موجود"
    },
    "en": {
        "success_repo_created": "✅ Repository created successfully",
        "error_repo_creation": "❌ Failed to create repository",
        "success_git_init": "✅ Git repository initialized",
        "error_git_init": "❌ Failed to initialize Git",
        "success_remote_added": "✅ Remote origin added",
        "error_remote_add": "❌ Failed to add remote origin",
        "success_files_added": "✅ Files added",
        "success_commit": "✅ Commit created",
        "success_push": "✅ Project uploaded to GitHub",
        "error_push": "❌ Failed to upload project",
        "success_gitignore": "✅ .gitignore file created",
        "success_readme": "✅ README.md file created",
        "project_uploaded": "🎉 Project uploaded successfully!",
        "project_type_detected": "📁 Project type",
        "starting_upload": "🚀 Starting project upload",
        "folder_not_found": "❌ Folder not found"
    }
}

# إعدادات افتراضية
DEFAULT_SETTINGS = {
    "language": "ar",  # اللغة الافتراضية
    "default_branch": "main",
    "default_commit_message": "Initial commit",
    "auto_create_gitignore": True,
    "auto_create_readme": True,
    "default_private": False
}

# مسارات الملفات
CONFIG_DIR = Path.home() / ".github-uploader"
TOKEN_FILE = CONFIG_DIR / "token.txt"
SETTINGS_FILE = CONFIG_DIR / "settings.json"

# إنشاء مجلد الإعدادات إذا لم يكن موجوداً
CONFIG_DIR.mkdir(exist_ok=True)

def get_message(key: str, language: str = "ar") -> str:
    """
    الحصول على رسالة بلغة محددة
    
    Args:
        key: مفتاح الرسالة
        language: اللغة (ar أو en)
        
    Returns:
        نص الرسالة
    """
    return MESSAGES.get(language, MESSAGES["ar"]).get(key, key)

def save_token(token: str) -> bool:
    """
    حفظ GitHub Token
    
    Args:
        token: GitHub Token
        
    Returns:
        True إذا تم الحفظ بنجاح
    """
    try:
        with open(TOKEN_FILE, 'w') as f:
            f.write(token)
        return True
    except Exception:
        return False

def load_token() -> str:
    """
    تحميل GitHub Token المحفوظ
    
    Returns:
        GitHub Token أو نص فارغ
    """
    try:
        if TOKEN_FILE.exists():
            with open(TOKEN_FILE, 'r') as f:
                return f.read().strip()
    except Exception:
        pass
    return ""
