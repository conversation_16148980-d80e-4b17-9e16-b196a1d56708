#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح مشاكل APIs في وكيل أخبار الألعاب
"""

import os
import sys
import json
from typing import Dict, List

def update_env_file():
    """تحديث ملف .env بالمفاتيح الجديدة"""
    print("🔧 تحديث ملف .env...")
    
    env_updates = {
        'GOOGLE_SEARCH_KEY': 'AIzaSyC3ihvVEvFSgaw_MAjcJLBZQno_8L1F-Tk',
        'FREEPIK_API_KEY': 'FPSX1ee910637a8ec349e6d8c7f17a57740b',
        'FLUXAI_API_KEY': 'b6863038ac459a1f8cd9e30d82cdd989',
        'GOOGLE_API_KEYS_LIST': 'AIzaSyDjjgZIkGyC0i8RVM9IA14WFJBqmEQHhHE,AIzaSyC3ihvVEvFSgaw_MAjcJLBZQno_8L1F-Tk'
    }
    
    env_file_path = '.env'
    
    # قراءة الملف الحالي
    existing_vars = {}
    if os.path.exists(env_file_path):
        with open(env_file_path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and '=' in line and not line.startswith('#'):
                    key, value = line.split('=', 1)
                    existing_vars[key] = value
    
    # تحديث المتغيرات
    existing_vars.update(env_updates)
    
    # كتابة الملف المحدث
    with open(env_file_path, 'w', encoding='utf-8') as f:
        f.write("# متغيرات البيئة لوكيل أخبار الألعاب\n")
        f.write("# تم التحديث تلقائياً\n\n")
        
        for key, value in existing_vars.items():
            f.write(f"{key}={value}\n")
    
    print("✅ تم تحديث ملف .env بنجاح")

def update_config_json():
    """تحديث ملف config/bot_config.json"""
    print("🔧 تحديث ملف التكوين...")
    
    config_file_path = 'config/bot_config.json'
    
    # قراءة التكوين الحالي
    config = {}
    if os.path.exists(config_file_path):
        with open(config_file_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
    
    # إضافة المفاتيح الجديدة
    config.update({
        'GOOGLE_SEARCH_KEY': 'AIzaSyC3ihvVEvFSgaw_MAjcJLBZQno_8L1F-Tk',
        'FREEPIK_API_KEY': 'FPSX1ee910637a8ec349e6d8c7f17a57740b',
        'FLUXAI_API_KEY': 'b6863038ac459a1f8cd9e30d82cdd989'
    })
    
    # كتابة التكوين المحدث
    with open(config_file_path, 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    
    print("✅ تم تحديث ملف التكوين بنجاح")

def create_api_status_report():
    """إنشاء تقرير حالة APIs"""
    print("📊 إنشاء تقرير حالة APIs...")
    
    api_status = {
        'Google Search API': {
            'key': 'AIzaSyC3ihvVEvFSgaw_MAjcJLBZQno_8L1F-Tk',
            'status': 'محدث',
            'description': 'مفتاح جديد للبحث عبر Google Custom Search'
        },
        'Freepik API': {
            'key': 'FPSX1ee910637a8ec349e6d8c7f17a57740b',
            'status': 'محدث',
            'description': 'مفتاح محدث لإنشاء الصور بالذكاء الاصطناعي'
        },
        'FluxAI API': {
            'key': 'b6863038ac459a1f8cd9e30d82cdd989',
            'status': 'قيد المراجعة',
            'description': 'قد تكون هناك مشاكل في الخدمة، تم تحسين معالجة الأخطاء'
        },
        'IGN RSS Feed': {
            'url': 'https://feeds.ign.com/ign/news',
            'status': 'محدث',
            'description': 'تم تحديث رابط RSS feed إلى الرابط الصحيح'
        }
    }
    
    report_file = 'api_status_report.json'
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(api_status, f, indent=2, ensure_ascii=False)
    
    print(f"✅ تم إنشاء تقرير حالة APIs في {report_file}")
    
    # طباعة التقرير
    print("\n📋 تقرير حالة APIs:")
    print("-" * 50)
    for api_name, info in api_status.items():
        print(f"🔧 {api_name}:")
        print(f"   الحالة: {info['status']}")
        print(f"   الوصف: {info['description']}")
        if 'key' in info:
            print(f"   المفتاح: {info['key'][:10]}...")
        if 'url' in info:
            print(f"   الرابط: {info['url']}")
        print()

def create_troubleshooting_guide():
    """إنشاء دليل استكشاف الأخطاء"""
    print("📖 إنشاء دليل استكشاف الأخطاء...")
    
    guide_content = """# دليل استكشاف أخطاء APIs

## المشاكل التي تم إصلاحها:

### 1. Google Search API - خطأ 403 Forbidden
**المشكلة**: المفتاح القديم لم يعد يعمل
**الحل**: تم تحديث المفتاح إلى: AIzaSyC3ihvVEvFSgaw_MAjcJLBZQno_8L1F-Tk

### 2. Freepik API - خطأ 401 Unauthorized  
**المشكلة**: مفتاح المصادقة غير صحيح
**الحل**: تم تحديث المفتاح إلى: FPSX1ee910637a8ec349e6d8c7f17a57740b

### 3. FluxAI API - فشل الاتصال
**المشكلة**: مشاكل في الشبكة أو الخدمة غير متاحة
**الحل**: تم تحسين معالجة الأخطاء وإضافة timeout أطول

### 4. IGN RSS Feed - خطأ 404 Not Found
**المشكلة**: الرابط القديم لم يعد يعمل
**الحل**: تم تحديث الرابط إلى: https://feeds.ign.com/ign/news

## كيفية اختبار الإصلاحات:

```bash
# تشغيل اختبار شامل للمفاتيح
python test_api_keys.py

# تشغيل البوت للتأكد من عمله
python main.py
```

## إذا استمرت المشاكل:

1. تأكد من اتصال الإنترنت
2. تحقق من صحة المفاتيح في ملف .env
3. راجع ملفات السجل في مجلد logs/
4. تأكد من تحديث جميع المتطلبات: pip install -r requirements.txt

## معلومات الاتصال:
- في حالة استمرار المشاكل، راجع ملفات السجل للحصول على تفاصيل أكثر
- تأكد من أن جميع المفاتيح محدثة في متغيرات البيئة
"""
    
    with open('TROUBLESHOOTING.md', 'w', encoding='utf-8') as f:
        f.write(guide_content)
    
    print("✅ تم إنشاء دليل استكشاف الأخطاء في TROUBLESHOOTING.md")

def main():
    """الدالة الرئيسية للإصلاح"""
    print("🚀 بدء إصلاح مشاكل APIs...")
    print("=" * 60)
    
    try:
        # تحديث ملفات التكوين
        update_env_file()
        update_config_json()
        
        # إنشاء التقارير والأدلة
        create_api_status_report()
        create_troubleshooting_guide()
        
        print("\n" + "=" * 60)
        print("🎉 تم إصلاح جميع مشاكل APIs بنجاح!")
        print("\n📋 الخطوات التالية:")
        print("1. تشغيل اختبار المفاتيح: python test_api_keys.py")
        print("2. تشغيل البوت: python main.py")
        print("3. مراجعة ملفات السجل في مجلد logs/")
        print("\n📖 راجع ملف TROUBLESHOOTING.md للمزيد من المعلومات")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ أثناء الإصلاح: {e}")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الإصلاح بواسطة المستخدم")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        sys.exit(1)
