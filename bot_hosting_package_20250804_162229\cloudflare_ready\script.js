/**
 * ملف الجافاسكريبت لصفحة عرض المودات - محسن للهواتف
 * JavaScript for Mod Details Page - Mobile Optimized
 * Cloudflare Pages Version - READY FOR DEPLOYMENT
 */

// متغيرات عامة
let currentImageIndex = 0;
let imageUrls = [];
let modData = null;
let adsSettings = null;
let pageCustomization = null;
let modDownloadUrl = '';
let modId = '';
let modTitle = '';
let lang = 'ar';
let userId = '';
let channelId = '';
let isPreview = false;
let adShown = false;
let isDownloading = false;
let isDownloaded = false;
let downloadProgress = 0;
let modFileSize = 0;
let downloadedFileName = '';
let downloadStartTime = 0;

// عناصر DOM
let mainModImage, prevButton, nextButton, thumbnailContainer;
let downloadButton, downloadIcon, downloadText, progressBar;
let loadingScreen, errorScreen, mainContent;

// إعدادات Supabase
const SUPABASE_URL = "https://ytqxxodyecdeosnqoure.supabase.co";
const SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4";

/**
 * إصلاح مباشر لمشكلة modId
 */
function fixModId(rawId) {
    if (!rawId) return '1';
    
    // تحويل إلى نص وتنظيف
    let cleanId = String(rawId).trim();
    
    // إزالة أي شيء بعد ":" 
    if (cleanId.includes(':')) {
        cleanId = cleanId.split(':')[0];
        console.log('🔧 Fixed modId from', rawId, 'to', cleanId);
    }
    
    // إزالة أي أحرف غير مسموحة
    cleanId = cleanId.replace(/[^a-zA-Z0-9\-_]/g, '');
    
    // إذا كان فارغ، استخدم القيمة الافتراضية
    if (!cleanId) {
        cleanId = '1';
    }
    
    return cleanId;
}

/**
 * تهيئة الصفحة
 */
async function initializePage() {
    try {
        console.log('🚀 بدء تهيئة الصفحة...');
        
        // الحصول على عناصر DOM
        initializeElements();
        
        // استخراج المعاملات من الرابط
        extractUrlParameters();
        
        // تحميل البيانات
        await loadPageData();
        
        // إعداد مستمعي الأحداث
        setupEventListeners();
        
        console.log('✅ تم تهيئة الصفحة بنجاح');
        
    } catch (error) {
        console.error('❌ فشل في تهيئة الصفحة:', error);
        showErrorScreen(error.message);
    }
}

/**
 * تهيئة عناصر DOM
 */
function initializeElements() {
    mainModImage = document.getElementById('main-mod-image');
    prevButton = document.getElementById('prev-image');
    nextButton = document.getElementById('next-image');
    thumbnailContainer = document.getElementById('thumbnail-container');
    downloadButton = document.getElementById('download-button');
    downloadIcon = document.getElementById('download-icon');
    downloadText = document.getElementById('download-text');
    progressBar = document.getElementById('progress-bar');
    loadingScreen = document.getElementById('loading-screen');
    errorScreen = document.getElementById('error-screen');
    mainContent = document.getElementById('main-content');
}

/**
 * استخراج المعاملات من الرابط - مع إصلاح مباشر
 */
function extractUrlParameters() {
    const urlParams = new URLSearchParams(window.location.search);
    
    // الحصول على المعاملات الخام
    const rawModId = urlParams.get('id') || '1';
    const rawLang = urlParams.get('lang') || 'ar';
    const rawUserId = urlParams.get('user_id') || '';
    const rawChannelId = urlParams.get('channel') || '';
    const rawPreview = urlParams.get('preview') === '1';
    
    // إصلاح modId مباشرة
    modId = fixModId(rawModId);
    lang = rawLang;
    userId = rawUserId;
    channelId = rawChannelId;
    isPreview = rawPreview;
    
    console.log('📋 المعاملات المستخرجة:', {
        original_id: rawModId,
        fixed_id: modId,
        lang: lang,
        userId: userId,
        channelId: channelId,
        isPreview: isPreview
    });
    
    // تحديث اتجاه الصفحة
    document.documentElement.lang = lang;
    document.documentElement.dir = lang === 'ar' ? 'rtl' : 'ltr';
}

/**
 * تحميل بيانات الصفحة
 */
async function loadPageData() {
    try {
        console.log('📥 بدء تحميل بيانات الصفحة...');
        
        if (isPreview) {
            console.log('👁️ تحميل بيانات المعاينة...');
            await loadPreviewData();
        } else {
            console.log('📦 تحميل بيانات المود...');
            await loadModData();
        }
        
        // تحميل إعدادات إضافية
        if (userId) {
            console.log('⚙️ تحميل الإعدادات الإضافية...');
            try {
                await Promise.all([
                    loadAdsSettings(),
                    loadPageCustomization()
                ]);
            } catch (settingsError) {
                console.warn('⚠️ فشل في تحميل بعض الإعدادات:', settingsError);
            }
        }
        
        // عرض البيانات
        console.log('🎨 عرض البيانات...');
        displayModData();
        applyCustomization();
        hideLoadingScreen();
        
        console.log('✅ تم تحميل البيانات بنجاح');
        
    } catch (error) {
        console.error('❌ خطأ في تحميل البيانات:', error);
        
        // تحسين رسالة الخطأ
        let errorMessage = error.message;
        if (error.message.includes('HTTP 400')) {
            errorMessage = lang === 'ar' ? 
                'خطأ في طلب البيانات. يرجى التحقق من صحة رابط المود.' : 
                'Invalid data request. Please check the mod link.';
        } else if (error.message.includes('HTTP 404')) {
            errorMessage = lang === 'ar' ? 
                'المود غير موجود.' : 
                'Mod not found.';
        } else if (error.message.includes('Failed to fetch')) {
            errorMessage = lang === 'ar' ? 
                'فشل في الاتصال بالخادم. يرجى التحقق من اتصال الإنترنت.' : 
                'Failed to connect to server. Please check your internet connection.';
        }
        
        showErrorScreen(errorMessage);
    }
}

/**
 * تحميل بيانات المود من Supabase - مع إصلاح شامل
 */
async function loadModData() {
    try {
        console.log('🔍 البحث عن المود بالمعرف:', modId);

        // إصلاح modId مع تسجيل مفصل
        const originalModId = modId;
        const cleanModId = fixModId(modId);

        if (originalModId !== cleanModId) {
            console.log('🔧 تم إصلاح modId من', originalModId, 'إلى', cleanModId);
        }

        // محاولة عدة استراتيجيات للبحث
        const searchStrategies = [
            // استراتيجية 1: البحث بالـ id المُصلح
            () => `${SUPABASE_URL}/rest/v1/mods?id=eq.${cleanModId}`,

            // استراتيجية 2: البحث بدون limit
            () => `${SUPABASE_URL}/rest/v1/mods?id=eq.${cleanModId}&limit=1`,

            // استراتيجية 3: البحث بـ select محدد
            () => `${SUPABASE_URL}/rest/v1/mods?select=id,name,description,version,category,download_url,image_urls&id=eq.${cleanModId}`,

            // استراتيجية 4: البحث العام
            () => `${SUPABASE_URL}/rest/v1/mods?limit=1&order=created_at.desc`
        ];

        for (let i = 0; i < searchStrategies.length; i++) {
            try {
                const url = searchStrategies[i]();
                console.log(`🌐 محاولة ${i + 1}: ${url}`);

                const response = await fetch(url, {
                    headers: {
                        'apikey': SUPABASE_KEY,
                        'Authorization': `Bearer ${SUPABASE_KEY}`,
                        'Content-Type': 'application/json'
                    }
                });

                console.log(`📡 استجابة المحاولة ${i + 1}:`, response.status, response.statusText);

                if (response.ok) {
                    const data = await response.json();
                    console.log(`📊 بيانات المحاولة ${i + 1}:`, data);

                    if (data && data.length > 0) {
                        modData = data[0];
                        console.log('✅ نجح تحميل البيانات في المحاولة', i + 1);
                        return;
                    }
                } else {
                    const errorText = await response.text();
                    console.warn(`⚠️ فشلت المحاولة ${i + 1}:`, response.status, errorText);
                }

            } catch (strategyError) {
                console.warn(`⚠️ خطأ في المحاولة ${i + 1}:`, strategyError.message);
            }
        }

        // إذا فشلت جميع المحاولات، استخدم بيانات تجريبية
        console.log('🔄 فشلت جميع المحاولات، استخدام بيانات تجريبية...');
        modData = createFallbackData();

    } catch (error) {
        console.error('❌ خطأ عام في loadModData:', error);
        console.log('🔄 استخدام بيانات تجريبية كحل أخير...');
        modData = createFallbackData();
    }
}

/**
 * إنشاء بيانات احتياطية
 */
function createFallbackData() {
    return {
        id: modId || '1',
        name: 'مود تجريبي',
        description: 'هذا مود تجريبي يظهر عندما تفشل عملية تحميل البيانات من قاعدة البيانات. يرجى التحقق من الاتصال والمحاولة مرة أخرى.',
        version: '1.0.0',
        category: 'addons',
        download_url: '#fallback-download',
        image_urls: [
            'https://via.placeholder.com/800x450/4F46E5/FFFFFF?text=' + encodeURIComponent('مود تجريبي')
        ]
    };
}

/**
 * تحميل بيانات المعاينة
 */
async function loadPreviewData() {
    modData = {
        id: 'preview-mod-id',
        name: lang === 'ar' ? 'مود تجريبي للمعاينة' : 'Preview Demo Mod',
        description: lang === 'ar' ?
            'هذا مود تجريبي لمعاينة تصميم الصفحة.' :
            'This is a demo mod for page preview.',
        description_ar: 'هذا مود تجريبي لمعاينة تصميم الصفحة.',
        version: '1.0.0',
        category: 'addons',
        download_url: '#preview-download',
        image_urls: [
            'https://via.placeholder.com/800x450/4F46E5/FFFFFF?text=' + encodeURIComponent(lang === 'ar' ? 'صورة تجريبية 1' : 'Demo Image 1'),
            'https://via.placeholder.com/800x450/7C3AED/FFFFFF?text=' + encodeURIComponent(lang === 'ar' ? 'صورة تجريبية 2' : 'Demo Image 2')
        ]
    };

    console.log('✅ تم تحميل بيانات المعاينة');
}

/**
 * تحميل إعدادات الإعلانات
 */
async function loadAdsSettings() {
    try {
        const response = await fetch(`${SUPABASE_URL}/rest/v1/user_ads_settings?user_id=eq.${userId}`, {
            headers: {
                'apikey': SUPABASE_KEY,
                'Authorization': `Bearer ${SUPABASE_KEY}`,
                'Content-Type': 'application/json'
            }
        });

        if (response.ok) {
            const data = await response.json();
            adsSettings = data.length > 0 ? data[0] : null;
        }
    } catch (error) {
        console.warn('⚠️ فشل في تحميل إعدادات الإعلانات:', error);
    }
}

/**
 * تحميل إعدادات تخصيص الصفحة
 */
async function loadPageCustomization() {
    try {
        const response = await fetch(`${SUPABASE_URL}/rest/v1/page_customization_settings?user_id=eq.${userId}`, {
            headers: {
                'apikey': SUPABASE_KEY,
                'Authorization': `Bearer ${SUPABASE_KEY}`,
                'Content-Type': 'application/json'
            }
        });

        if (response.ok) {
            const data = await response.json();
            pageCustomization = data.length > 0 ? data[0] : null;
        }
    } catch (error) {
        console.warn('⚠️ فشل في تحميل إعدادات التخصيص:', error);
    }
}

/**
 * عرض بيانات المود
 */
function displayModData() {
    if (!modData) return;

    // تحديث العنوان
    modTitle = modData.name || 'N/A';
    const titleElement = document.getElementById('mod-title');
    const pageTitleElement = document.getElementById('page-title');

    if (titleElement) titleElement.textContent = modTitle;
    if (pageTitleElement) pageTitleElement.textContent = `${modTitle} - ${lang === 'ar' ? 'تفاصيل المود' : 'Mod Details'}`;

    // تحديث الصور
    imageUrls = modData.image_urls || [];
    if (imageUrls.length > 0 && mainModImage) {
        mainModImage.src = imageUrls[0];
        updateThumbnails();
    }

    // تحديث المعلومات
    const versionElement = document.getElementById('mod-version');
    if (versionElement) versionElement.textContent = modData.version || 'N/A';

    // تحديث التصنيف
    const categoryNames = {
        'ar': {
            'addons': 'إضافات',
            'shaders': 'شيدرات',
            'texture_packs': 'حزم النسيج',
            'seeds': 'بذور',
            'maps': 'خرائط',
            'unknown': 'غير محدد'
        },
        'en': {
            'addons': 'Add-ons',
            'shaders': 'Shaders',
            'texture_packs': 'Texture Packs',
            'seeds': 'Seeds',
            'maps': 'Maps',
            'unknown': 'Not specified'
        }
    };

    const categoryKey = (modData.category || 'unknown').toLowerCase();
    const categoryName = categoryNames[lang][categoryKey] || categoryNames[lang]['unknown'];
    const categoryElement = document.getElementById('mod-category');
    if (categoryElement) categoryElement.textContent = categoryName;

    // تحديث الوصف
    const description = lang === 'ar' ?
        (modData.description_ar || modData.description) :
        (modData.description || modData.description_ar);
    const descriptionElement = document.getElementById('mod-description');
    if (descriptionElement) {
        descriptionElement.textContent = description ||
            (lang === 'ar' ? 'لا يوجد وصف متاح' : 'No description available');
    }

    // تحديث رابط التحميل
    modDownloadUrl = modData.download_url || '#';

    // إظهار إشعار المعاينة
    if (isPreview) {
        const previewNotice = document.getElementById('preview-notice');
        if (previewNotice) previewNotice.classList.remove('hidden');
    }

    console.log('✅ تم عرض بيانات المود');
}

/**
 * تطبيق التخصيصات
 */
function applyCustomization() {
    if (!pageCustomization) return;

    // تطبيق الستايل
    const styleTemplate = pageCustomization.style_template || 'default';
    document.body.className = `style-template-${styleTemplate}`;
    document.body.setAttribute('data-style', styleTemplate);

    // تطبيق الألوان المخصصة
    const root = document.documentElement;
    if (pageCustomization.custom_bg_color) {
        root.style.setProperty('--bg-color', pageCustomization.custom_bg_color);
    }
    if (pageCustomization.custom_header_color) {
        root.style.setProperty('--header-color', pageCustomization.custom_header_color);
    }
    if (pageCustomization.custom_button_color) {
        root.style.setProperty('--button-color', pageCustomization.custom_button_color);
    }
    if (pageCustomization.custom_text_color) {
        root.style.setProperty('--text-color', pageCustomization.custom_text_color);
    }

    // تطبيق اسم الموقع
    const siteNameElement = document.getElementById('site-name');
    if (pageCustomization.site_name && siteNameElement) {
        siteNameElement.textContent = pageCustomization.site_name;
    }

    // تطبيق نص زر التحميل
    const downloadButtonText = lang === 'ar' ?
        (pageCustomization.download_button_text_ar || 'تحميل المود') :
        (pageCustomization.download_button_text_en || 'Download Mod');
    if (downloadText) downloadText.textContent = downloadButtonText;

    console.log('✅ تم تطبيق التخصيصات');
}

/**
 * إعداد مستمعي الأحداث
 */
function setupEventListeners() {
    // أزرار التنقل بين الصور
    if (prevButton) prevButton.addEventListener('click', previousImage);
    if (nextButton) nextButton.addEventListener('click', nextImage);

    // منع إغلاق الصفحة أثناء التحميل
    window.addEventListener('beforeunload', function(event) {
        if (isDownloading) {
            event.preventDefault();
            return '';
        }
    });

    console.log('✅ تم إعداد مستمعي الأحداث');
}

/**
 * تحديث الصور المصغرة
 */
function updateThumbnails() {
    if (!thumbnailContainer) return;

    thumbnailContainer.innerHTML = '';
    if (imageUrls.length === 0) return;

    for (let i = 0; i < imageUrls.length; i++) {
        const thumbnail = document.createElement('img');
        thumbnail.src = imageUrls[i];
        thumbnail.classList.add('thumbnail');
        thumbnail.loading = 'lazy';

        if (i === currentImageIndex) {
            thumbnail.classList.add('active');
        }

        thumbnail.dataset.index = i;
        thumbnail.addEventListener('click', (event) => {
            event.preventDefault();
            currentImageIndex = parseInt(event.target.dataset.index);
            updateImage();
        });

        thumbnailContainer.appendChild(thumbnail);
    }
}

/**
 * الانتقال للصورة السابقة
 */
function previousImage() {
    if (imageUrls.length === 0) return;
    currentImageIndex = (currentImageIndex - 1 + imageUrls.length) % imageUrls.length;
    updateImage();
}

/**
 * الانتقال للصورة التالية
 */
function nextImage() {
    if (imageUrls.length === 0) return;
    currentImageIndex = (currentImageIndex + 1) % imageUrls.length;
    updateImage();
}

/**
 * تحديث الصورة الرئيسية
 */
function updateImage() {
    if (imageUrls.length > 0 && mainModImage) {
        mainModImage.src = imageUrls[currentImageIndex];
        mainModImage.classList.add('fade-in');
        setTimeout(() => mainModImage.classList.remove('fade-in'), 500);
    }
    updateThumbnails();
}

/**
 * معالجة التحميل
 */
async function handleDownload() {
    if (isDownloading || isDownloaded) return;

    // التحقق من وجود إعلان
    if (adsSettings && adsSettings.ads_enabled && !adShown) {
        showAd();
        return;
    }

    startDownload();
}

/**
 * بدء التحميل
 */
function startDownload() {
    if (isDownloading || isDownloaded) return;

    isDownloading = true;
    downloadStartTime = Date.now();

    // تحديث واجهة زر التحميل
    if (downloadButton) downloadButton.classList.add('downloading');
    if (downloadIcon) downloadIcon.innerHTML = '<div class="spinner"></div>';
    if (downloadText) downloadText.textContent = lang === 'ar' ? 'جاري التحميل...' : 'Downloading...';

    // محاكاة شريط التقدم
    simulateDownloadProgress();

    // فتح رابط التحميل
    if (modDownloadUrl && modDownloadUrl !== '#' && modDownloadUrl !== '#preview-download') {
        window.open(modDownloadUrl, '_blank');
    }

    // إظهار إشعار
    showNotification(
        lang === 'ar' ? 'بدء التحميل...' : 'Download started...',
        'success'
    );
}

/**
 * محاكاة تقدم التحميل
 */
function simulateDownloadProgress() {
    let progress = 0;
    const interval = setInterval(() => {
        progress += Math.random() * 15;
        if (progress >= 100) {
            progress = 100;
            clearInterval(interval);
            completeDownload();
        }

        if (progressBar) progressBar.style.width = progress + '%';
        downloadProgress = progress;
    }, 200);
}

/**
 * إكمال التحميل
 */
function completeDownload() {
    isDownloading = false;
    isDownloaded = true;

    // تحديث واجهة زر التحميل
    if (downloadButton) {
        downloadButton.classList.remove('downloading');
        downloadButton.classList.add('downloaded');
    }
    if (downloadIcon) downloadIcon.innerHTML = '✅';
    if (downloadText) downloadText.textContent = lang === 'ar' ? 'تم التحميل' : 'Downloaded';

    // إظهار إشعار النجاح
    showNotification(
        lang === 'ar' ? 'تم التحميل بنجاح!' : 'Download completed successfully!',
        'success'
    );
}

/**
 * عرض الإعلان
 */
function showAd() {
    // تنفيذ بسيط للإعلان
    adShown = true;
    setTimeout(() => {
        startDownload();
    }, 2000);
}

/**
 * إغلاق الإعلان
 */
function closeAd() {
    const adOverlay = document.getElementById('ad-overlay');
    if (adOverlay) adOverlay.classList.add('hidden');
    startDownload();
}

/**
 * إظهار الإشعارات
 */
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification ${type} notification-enter`;
    notification.textContent = message;

    document.body.appendChild(notification);

    // إزالة الإشعار بعد 3 ثواني
    setTimeout(() => {
        notification.classList.remove('notification-enter');
        notification.classList.add('notification-exit');
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

/**
 * إخفاء شاشة التحميل
 */
function hideLoadingScreen() {
    if (loadingScreen) loadingScreen.classList.add('hidden');
    if (mainContent) {
        mainContent.classList.remove('hidden');
        mainContent.classList.add('fade-in');
    }
}

/**
 * إظهار شاشة الخطأ
 */
function showErrorScreen(message) {
    if (loadingScreen) loadingScreen.classList.add('hidden');

    const errorMessageElement = document.getElementById('error-message-text');
    if (errorMessageElement) errorMessageElement.textContent = message;

    if (errorScreen) errorScreen.classList.remove('hidden');

    // إضافة زر إعادة المحاولة
    const retryButton = document.createElement('button');
    retryButton.textContent = lang === 'ar' ? 'إعادة المحاولة' : 'Retry';
    retryButton.className = 'retry-button';
    retryButton.onclick = () => {
        window.location.reload();
    };

    // إضافة الزر إذا لم يكن موجوداً
    if (errorMessageElement && !errorMessageElement.parentElement.querySelector('.retry-button')) {
        errorMessageElement.parentElement.appendChild(retryButton);
    }
}

// تصدير الدوال للاستخدام العام
window.handleDownload = handleDownload;
window.closeAd = closeAd;
window.previousImage = previousImage;
window.nextImage = nextImage;

// تهيئة الصفحة عند التحميل
document.addEventListener('DOMContentLoaded', initializePage);

console.log('🚀 Cloudflare Ready - تم تحميل JavaScript بنجاح');
