#!/usr/bin/env python3
"""
فاحص الإعدادات الذكي مع إشعارات التيليجرام
"""

import logging
import asyncio
from typing import Dict, List, Optional, Tuple
from utils import settings
from automation.telegram_bot import send_notification, send_error
from automation.config_notifications import (
    notify_tiktok_session_missing,
    notify_elevenlabs_api_missing,
    notify_reddit_credentials_missing,
    notify_auto_fix_success,
    config_notifier
)

logger = logging.getLogger(__name__)

class SmartConfigChecker:
    """فاحص إعدادات ذكي مع إشعارات تيليجرام"""
    
    def __init__(self):
        self.config_issues = []
        self.auto_fix_enabled = True
    
    def check_all_settings(self) -> Tuple[bool, List[str]]:
        """فحص جميع الإعدادات وإرجاع النتائج"""
        self.config_issues = []
        
        # فحص إعدادات TTS
        self._check_tts_settings()
        
        # فحص إعدادات Reddit
        self._check_reddit_settings()
        
        # فحص إعدادات التيليجرام
        self._check_telegram_settings()
        
        # إرسال تقرير إذا وجدت مشاكل
        if self.config_issues:
            try:
                # محاولة إرسال الإشعارات بشكل متزامن
                import threading
                def send_notifications():
                    try:
                        asyncio.run(self._send_detailed_notifications())
                    except Exception as e:
                        print(f"خطأ في إرسال الإشعارات: {e}")

                # تشغيل في خيط منفصل
                notification_thread = threading.Thread(target=send_notifications, daemon=True)
                notification_thread.start()
            except Exception as e:
                print(f"خطأ في إعداد الإشعارات: {e}")

            return False, self.config_issues
        
        return True, []
    
    def _check_tts_settings(self):
        """فحص إعدادات TTS"""
        voice_choice = settings.config["settings"]["tts"]["voice_choice"]
        
        if voice_choice == "tiktok" or voice_choice == "TikTok":
            session_id = settings.config["settings"]["tts"]["tiktok_sessionid"]
            if not session_id or session_id.strip() == "":
                issue = {
                    "type": "tts_config",
                    "problem": "TikTok voice requires sessionid",
                    "solution": "إعداد TikTok Session ID عبر التيليجرام",
                    "auto_fix": "switch_to_free_elevenlabs",
                    "notification_type": "tiktok_session_missing"
                }
                self.config_issues.append(issue)
        
        elif voice_choice == "ElevenLabs" or voice_choice == "elevenlabs":
            api_key = settings.config["settings"]["tts"]["elevenlabs_api_key"]
            use_free = settings.config["settings"]["tts"].get("elevenlabs_use_free", False)
            
            if not use_free and (not api_key or api_key.strip() == ""):
                issue = {
                    "type": "tts_config",
                    "problem": "ElevenLabs requires API key or free mode",
                    "solution": "إعداد ElevenLabs مجاني أو إضافة API key",
                    "auto_fix": "enable_free_elevenlabs",
                    "notification_type": "elevenlabs_api_missing"
                }
                self.config_issues.append(issue)
    
    def _check_reddit_settings(self):
        """فحص إعدادات Reddit"""
        reddit_creds = settings.config["reddit"]["creds"]
        
        required_fields = ["client_id", "client_secret", "username", "password"]
        for field in required_fields:
            if not reddit_creds.get(field) or reddit_creds[field].strip() == "":
                issue = {
                    "type": "reddit_config",
                    "problem": f"Reddit {field} is missing",
                    "solution": f"إعداد {field} في التكوين",
                    "auto_fix": None
                }
                self.config_issues.append(issue)
    
    def _check_telegram_settings(self):
        """فحص إعدادات التيليجرام"""
        telegram_config = settings.config.get("telegram", {})
        bot_token = telegram_config.get("bot_token", "")
        
        if not bot_token or bot_token.strip() == "":
            issue = {
                "type": "telegram_config",
                "problem": "Telegram bot token is missing",
                "solution": "إعداد رمز بوت التيليجرام",
                "auto_fix": None
            }
            self.config_issues.append(issue)
    
    async def _send_detailed_notifications(self):
        """إرسال إشعارات مفصلة لكل مشكلة"""
        try:
            # إرسال إشعار مفصل لكل مشكلة
            for issue in self.config_issues:
                notification_type = issue.get('notification_type')
                if notification_type:
                    config_notifier.send_config_issue_notification(notification_type)
                    await asyncio.sleep(1)  # تأخير قصير بين الإشعارات

            # إرسال ملخص عام
            config_notifier.send_startup_issues_summary(self.config_issues)

        except Exception as e:
            logger.error(f"فشل في إرسال الإشعارات المفصلة: {str(e)}")
    
    def auto_fix_issues(self) -> bool:
        """إصلاح تلقائي للمشاكل البسيطة"""
        if not self.auto_fix_enabled:
            return False
        
        fixed_any = False
        
        for issue in self.config_issues:
            if issue.get('auto_fix') == "switch_to_free_elevenlabs":
                self._switch_to_free_elevenlabs()
                fixed_any = True
                logger.info("🔧 تم التبديل إلى ElevenLabs المجاني تلقائياً")
                self._safe_notify_success("switched_to_elevenlabs_free")

            elif issue.get('auto_fix') == "enable_free_elevenlabs":
                self._enable_free_elevenlabs()
                fixed_any = True
                logger.info("🔧 تم تفعيل ElevenLabs المجاني تلقائياً")
                self._safe_notify_success("enabled_elevenlabs_free")

        if fixed_any:
            self._save_config()
        
        return fixed_any

    def _safe_notify_success(self, fix_type: str):
        """إرسال إشعار نجاح بشكل آمن"""
        try:
            # إرسال الإشعار مباشرة (لأن الدالة لم تعد async)
            notify_auto_fix_success(fix_type)
        except Exception as e:
            print(f"خطأ في إرسال إشعار النجاح: {e}")

    def _switch_to_free_elevenlabs(self):
        """التبديل إلى ElevenLabs المجاني"""
        settings.config["settings"]["tts"]["voice_choice"] = "ElevenLabs"
        settings.config["settings"]["tts"]["elevenlabs_use_free"] = True
        settings.config["settings"]["tts"]["elevenlabs_voice_id"] = "21m00Tcm4TlvDq8ikWAM"  # Rachel
        settings.config["settings"]["tts"]["elevenlabs_api_key"] = ""
    
    def _enable_free_elevenlabs(self):
        """تفعيل ElevenLabs المجاني"""
        settings.config["settings"]["tts"]["elevenlabs_use_free"] = True
        settings.config["settings"]["tts"]["elevenlabs_voice_id"] = "21m00Tcm4TlvDq8ikWAM"  # Rachel
        if not settings.config["settings"]["tts"]["elevenlabs_api_key"]:
            settings.config["settings"]["tts"]["elevenlabs_api_key"] = ""
    
    def _save_config(self):
        """حفظ التكوين"""
        import toml
        with open("config.toml", "w", encoding="utf-8") as f:
            toml.dump(settings.config, f)
    


# إنشاء مثيل عام
smart_config_checker = SmartConfigChecker()

def check_config_with_telegram_notifications() -> bool:
    """فحص الإعدادات مع إشعارات التيليجرام"""
    is_valid, issues = smart_config_checker.check_all_settings()
    
    if not is_valid:
        # محاولة الإصلاح التلقائي
        if smart_config_checker.auto_fix_issues():
            logger.info("✅ تم إصلاح بعض المشاكل تلقائياً")
            # إعادة فحص بعد الإصلاح
            is_valid, _ = smart_config_checker.check_all_settings()
    
    return is_valid
