#!/usr/bin/env python3
"""
تحليل وتحسين استهلاك API لتحويل النص إلى صوت
يحدد المشاكل ويقترح حلول لتوفير الاستهلاك
"""

import os
import sys
import logging
from pathlib import Path

# إعداد السجلات
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def analyze_current_usage():
    """تحليل الاستهلاك الحالي"""
    print("🔍 تحليل استهلاك API الحالي...")
    
    issues = []
    optimizations = []
    
    # 1. فحص إعدادات التكرار
    try:
        import toml
        with open("config.toml", 'r', encoding='utf-8') as f:
            config = toml.load(f)
        
        times_to_run = config.get("settings", {}).get("times_to_run", 1)
        if times_to_run > 1:
            issues.append(f"⚠️ times_to_run = {times_to_run} - سيتم إنشاء {times_to_run} فيديوهات")
            optimizations.append("تقليل times_to_run إلى 1 للاختبار")
        else:
            print(f"✅ times_to_run = {times_to_run} (مثالي)")
        
        # 2. فحص طول الفيديو
        storymode = config.get("settings", {}).get("storymode", False)
        storymode_max_length = config.get("settings", {}).get("storymode_max_length", 1000)
        
        if storymode:
            print(f"⚠️ storymode مفعل - قد يستهلك API أكثر")
            print(f"📏 storymode_max_length = {storymode_max_length}")
        else:
            print("✅ storymode معطل (توفير في الاستهلاك)")
        
        # 3. فحص إعدادات TTS
        tts_config = config.get("settings", {}).get("tts", {})
        voice_choice = tts_config.get("voice_choice", "")
        
        if voice_choice.lower() == "elevenlabs":
            print("✅ ElevenLabs مُختار كأولوية أولى")
        else:
            issues.append(f"⚠️ voice_choice = {voice_choice} - ليس ElevenLabs")
        
        # 4. فحص عدد المفاتيح
        api_keys = tts_config.get("api_keys", {}).get("elevenlabs_keys", [])
        working_keys = [key for key in api_keys if key.strip()]
        print(f"🔑 عدد مفاتيح ElevenLabs: {len(working_keys)}")
        
        if len(working_keys) >= 2:
            print("✅ عدد مفاتيح كافي للـ fallback")
        else:
            issues.append("⚠️ عدد مفاتيح قليل - قد يحدث فشل")
        
    except Exception as e:
        issues.append(f"❌ خطأ في قراءة الإعدادات: {e}")
    
    return issues, optimizations

def check_file_caching():
    """فحص نظام التخزين المؤقت للملفات"""
    print("\n💾 فحص نظام التخزين المؤقت...")
    
    issues = []
    optimizations = []
    
    # فحص وجود ملفات مؤقتة قديمة
    temp_dirs = ["assets/temp", "results"]
    
    for temp_dir in temp_dirs:
        if os.path.exists(temp_dir):
            # عد الملفات في المجلد
            total_files = 0
            total_size = 0
            
            for root, dirs, files in os.walk(temp_dir):
                for file in files:
                    if file.endswith('.mp3'):
                        file_path = os.path.join(root, file)
                        total_files += 1
                        total_size += os.path.getsize(file_path)
            
            if total_files > 0:
                size_mb = total_size / (1024 * 1024)
                print(f"📁 {temp_dir}: {total_files} ملف صوتي ({size_mb:.1f} MB)")
                
                if total_files > 50:
                    issues.append(f"⚠️ {temp_dir} يحتوي على {total_files} ملف - قد يحتاج تنظيف")
                    optimizations.append(f"تنظيف الملفات القديمة في {temp_dir}")
            else:
                print(f"✅ {temp_dir}: نظيف")
        else:
            print(f"📁 {temp_dir}: غير موجود")
    
    return issues, optimizations

def check_duplicate_calls():
    """فحص الاستدعاءات المكررة"""
    print("\n🔄 فحص الاستدعاءات المكررة...")
    
    issues = []
    optimizations = []
    
    # فحص كود engine_wrapper للاستدعاءات المكررة
    try:
        with open("TTS/engine_wrapper.py", 'r', encoding='utf-8') as f:
            content = f.read()
        
        # فحص عدد استدعاءات call_tts
        call_tts_count = content.count("self.call_tts(")
        print(f"📞 عدد استدعاءات call_tts في الكود: {call_tts_count}")
        
        if call_tts_count > 5:
            issues.append(f"⚠️ عدد استدعاءات call_tts كثير: {call_tts_count}")
        
        # فحص وجود حلقات تكرار
        if "for idx, comment in" in content:
            print("✅ يتم إنشاء صوت لكل تعليق (طبيعي)")
        
        # فحص وجود split_post (تقسيم النصوص الطويلة)
        if "split_post" in content:
            print("⚠️ يتم تقسيم النصوص الطويلة - قد يزيد الاستهلاك")
            optimizations.append("تقليل طول النصوص لتجنب التقسيم")
        
    except Exception as e:
        issues.append(f"❌ خطأ في فحص الكود: {e}")
    
    return issues, optimizations

def check_error_retries():
    """فحص إعادة المحاولة عند الأخطاء"""
    print("\n🔁 فحص إعادة المحاولة...")
    
    issues = []
    optimizations = []
    
    try:
        import toml
        with open("config.toml", 'r', encoding='utf-8') as f:
            config = toml.load(f)
        
        max_retries = config.get("settings", {}).get("tts", {}).get("priority_order", {}).get("max_retries_per_engine", 3)
        
        print(f"🔄 max_retries_per_engine = {max_retries}")
        
        if max_retries > 3:
            issues.append(f"⚠️ max_retries_per_engine = {max_retries} - قد يستهلك API كثيراً")
            optimizations.append("تقليل max_retries_per_engine إلى 2")
        else:
            print("✅ عدد إعادة المحاولة معقول")
        
    except Exception as e:
        issues.append(f"❌ خطأ في فحص إعادة المحاولة: {e}")
    
    return issues, optimizations

def suggest_optimizations():
    """اقتراح تحسينات لتوفير API"""
    print("\n💡 اقتراحات التحسين:")
    
    optimizations = [
        "1. تقليل عدد التعليقات في الفيديو",
        "2. استخدام نصوص أقصر لتجنب التقسيم", 
        "3. تفعيل التخزين المؤقت للنصوص المكررة",
        "4. تقليل max_retries_per_engine إلى 2",
        "5. استخدام فلترة أفضل للتعليقات",
        "6. تجنب إنشاء فيديوهات متعددة في نفس الوقت"
    ]
    
    for opt in optimizations:
        print(f"   {opt}")

def create_optimized_config():
    """إنشاء إعدادات محسنة"""
    print("\n⚙️ إنشاء إعدادات محسنة...")
    
    optimized_settings = {
        "times_to_run": 1,  # فيديو واحد فقط
        "storymode": False,  # تعطيل storymode لتوفير API
        "max_comment_length": 200,  # تقليل طول التعليقات
        "max_comments": 4,  # حد أقصى 4 تعليقات
        "tts": {
            "max_retries_per_engine": 2,  # تقليل إعادة المحاولة
            "voice_choice": "elevenlabs",  # ElevenLabs أولاً
            "auto_fallback": True  # تفعيل fallback
        }
    }
    
    print("📋 الإعدادات المحسنة:")
    for key, value in optimized_settings.items():
        if isinstance(value, dict):
            print(f"  {key}:")
            for sub_key, sub_value in value.items():
                print(f"    {sub_key} = {sub_value}")
        else:
            print(f"  {key} = {value}")

def run_comprehensive_analysis():
    """تشغيل تحليل شامل"""
    print("🔍 تحليل شامل لاستهلاك API")
    print("=" * 60)
    
    all_issues = []
    all_optimizations = []
    
    # تشغيل جميع الفحوصات
    checks = [
        ("الاستهلاك الحالي", analyze_current_usage),
        ("التخزين المؤقت", check_file_caching),
        ("الاستدعاءات المكررة", check_duplicate_calls),
        ("إعادة المحاولة", check_error_retries),
    ]
    
    for check_name, check_func in checks:
        try:
            issues, optimizations = check_func()
            all_issues.extend(issues)
            all_optimizations.extend(optimizations)
        except Exception as e:
            print(f"❌ خطأ في فحص {check_name}: {e}")
    
    # تلخيص النتائج
    print("\n" + "=" * 60)
    print("📊 ملخص التحليل")
    print("=" * 60)
    
    if all_issues:
        print("⚠️ المشاكل المكتشفة:")
        for issue in all_issues:
            print(f"  {issue}")
    else:
        print("✅ لا توجد مشاكل في الاستهلاك")
    
    if all_optimizations:
        print("\n💡 التحسينات المقترحة:")
        for opt in all_optimizations:
            print(f"  {opt}")
    
    # اقتراح تحسينات إضافية
    suggest_optimizations()
    
    # إنشاء إعدادات محسنة
    create_optimized_config()
    
    print(f"\n{'='*60}")
    print("🏁 انتهى التحليل")
    print(f"{'='*60}")

if __name__ == "__main__":
    try:
        run_comprehensive_analysis()
        
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف التحليل بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ حرج في التحليل: {e}")
        import traceback
        print(f"تفاصيل الخطأ:\n{traceback.format_exc()}")
