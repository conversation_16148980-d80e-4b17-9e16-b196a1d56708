#!/usr/bin/env python3
"""
Setup script for GitHub Uploader Tool
"""

from setuptools import setup, find_packages
import os

# قراءة محتوى README
def read_readme():
    with open("README.md", "r", encoding="utf-8") as fh:
        return fh.read()

# قراءة المتطلبات
def read_requirements():
    with open("requirements.txt", "r", encoding="utf-8") as fh:
        return [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="github-uploader",
    version="1.0.0",
    author="Your Name",
    author_email="<EMAIL>",
    description="أداة شاملة لرفع المشاريع على GitHub بشكل تلقائي",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    url="https://github.com/yourusername/github-uploader",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 5 - Production/Stable",
        "Intended Audience :: Developers",
        "Topic :: Software Development :: Version Control :: Git",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.6",
        "Programming Language :: Python :: 3.7",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Operating System :: OS Independent",
        "Natural Language :: Arabic",
        "Natural Language :: English",
    ],
    python_requires=">=3.6",
    install_requires=read_requirements(),
    entry_points={
        "console_scripts": [
            "github-uploader=github_uploader:main",
        ],
    },
    keywords="github git upload automation python arabic",
    project_urls={
        "Bug Reports": "https://github.com/yourusername/github-uploader/issues",
        "Source": "https://github.com/yourusername/github-uploader",
        "Documentation": "https://github.com/yourusername/github-uploader#readme",
    },
)
