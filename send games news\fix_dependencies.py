#!/usr/bin/env python3
"""
سكريبت إصلاح المشاكل والتبعيات
يحل المشاكل المحددة في البوت
"""

import subprocess
import sys
import os
import logging

# إعداد التسجيل
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def run_command(command, description):
    """تشغيل أمر مع معالجة الأخطاء"""
    try:
        logger.info(f"🔧 {description}...")
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        
        if result.returncode == 0:
            logger.info(f"✅ {description} - نجح")
            if result.stdout:
                logger.info(f"📝 الإخراج: {result.stdout.strip()}")
        else:
            logger.error(f"❌ {description} - فشل")
            if result.stderr:
                logger.error(f"📝 الخطأ: {result.stderr.strip()}")
        
        return result.returncode == 0
    except Exception as e:
        logger.error(f"❌ خطأ في تشغيل {description}: {e}")
        return False

def fix_telegram_bot():
    """إصلاح مشكلة Telegram Bot"""
    logger.info("🔧 إصلاح مشكلة Telegram Bot...")
    
    # إلغاء تثبيت الإصدار الحالي
    run_command("pip uninstall python-telegram-bot -y", "إلغاء تثبيت python-telegram-bot")
    
    # تثبيت الإصدار المحدد
    run_command("pip install python-telegram-bot==20.6", "تثبيت python-telegram-bot==20.6")

def install_whisper_dependencies():
    """تثبيت تبعيات Whisper"""
    logger.info("🎤 تثبيت تبعيات Whisper...")
    
    dependencies = [
        "yt-dlp>=2023.12.30",
        "ffmpeg-python>=0.2.0"
    ]
    
    for dep in dependencies:
        run_command(f"pip install {dep}", f"تثبيت {dep}")

def install_optional_dependencies():
    """تثبيت التبعيات الاختيارية"""
    logger.info("📦 تثبيت التبعيات الاختيارية...")
    
    optional_deps = [
        "scikit-image>=0.21.0",
        "imageio-ffmpeg>=0.4.0"
    ]
    
    for dep in optional_deps:
        success = run_command(f"pip install {dep}", f"تثبيت {dep}")
        if not success:
            logger.warning(f"⚠️ فشل في تثبيت {dep} - سيتم تخطيه")

def check_ffmpeg():
    """فحص وجود FFmpeg"""
    logger.info("🎬 فحص FFmpeg...")
    
    result = subprocess.run("ffmpeg -version", shell=True, capture_output=True, text=True)
    
    if result.returncode == 0:
        logger.info("✅ FFmpeg متوفر")
        return True
    else:
        logger.warning("⚠️ FFmpeg غير متوفر")
        logger.info("💡 لتثبيت FFmpeg:")
        logger.info("   Windows: تحميل من https://ffmpeg.org/download.html")
        logger.info("   Linux: sudo apt install ffmpeg")
        logger.info("   macOS: brew install ffmpeg")
        return False

def update_requirements():
    """تحديث requirements.txt"""
    logger.info("📝 تحديث requirements.txt...")
    
    run_command("pip install -r requirements.txt", "تثبيت جميع المتطلبات")

def main():
    """الدالة الرئيسية"""
    logger.info("🚀 بدء إصلاح المشاكل والتبعيات...")
    
    # 1. إصلاح مشكلة Telegram Bot
    fix_telegram_bot()
    
    # 2. تثبيت تبعيات Whisper
    install_whisper_dependencies()
    
    # 3. فحص FFmpeg
    check_ffmpeg()
    
    # 4. تثبيت التبعيات الاختيارية
    install_optional_dependencies()
    
    # 5. تحديث جميع المتطلبات
    update_requirements()
    
    logger.info("✅ اكتمل إصلاح المشاكل!")
    logger.info("🔄 يُنصح بإعادة تشغيل البوت الآن")

if __name__ == "__main__":
    main()
