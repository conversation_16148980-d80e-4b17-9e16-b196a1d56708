#!/usr/bin/env python3
"""
تشغيل آمن لـ Reddit Video Maker Bot مع معالجة الأخطاء
"""

import os
import sys
import time
import logging
from pathlib import Path

# إعداد نظام السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/safe_run.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def check_prerequisites():
    """فحص المتطلبات الأساسية"""
    logger.info("🔍 فحص المتطلبات الأساسية...")
    
    # فحص ملف التكوين
    if not os.path.exists('config.toml'):
        logger.error("❌ ملف config.toml غير موجود")
        return False
    
    # فحص مجلدات مطلوبة
    required_dirs = ['assets/temp', 'logs']
    for directory in required_dirs:
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    logger.info("✅ تم فحص المتطلبات بنجاح")
    return True

def run_with_retry(max_retries=3):
    """تشغيل البرنامج مع إعادة المحاولة عند الفشل"""
    
    if not check_prerequisites():
        return False
    
    for attempt in range(1, max_retries + 1):
        logger.info(f"🚀 محاولة التشغيل رقم {attempt}/{max_retries}")
        
        try:
            # استيراد وتشغيل البرنامج الرئيسي
            import main
            logger.info("✅ تم تشغيل البرنامج بنجاح")
            return True
            
        except KeyboardInterrupt:
            logger.info("⏹️ تم إيقاف البرنامج بواسطة المستخدم")
            return False
            
        except Exception as e:
            logger.error(f"❌ خطأ في المحاولة {attempt}: {str(e)}")
            
            if attempt < max_retries:
                wait_time = attempt * 10  # زيادة وقت الانتظار مع كل محاولة
                logger.info(f"⏳ انتظار {wait_time} ثانية قبل المحاولة التالية...")
                time.sleep(wait_time)
            else:
                logger.error("❌ فشل في جميع المحاولات")
                return False
    
    return False

def main():
    """الدالة الرئيسية"""
    print("""
    🎥 Reddit Video Maker Bot - التشغيل الآمن
    ==========================================
    """)
    
    success = run_with_retry()
    
    if success:
        print("\n✅ تم إنشاء الفيديو بنجاح!")
    else:
        print("\n❌ فشل في إنشاء الفيديو")
        print("💡 تحقق من ملف logs/safe_run.log للمزيد من التفاصيل")

if __name__ == "__main__":
    main()
