#!/usr/bin/env python3
"""
ملف تشغيل طوارئ - للحالات الصعبة
Emergency starter - for difficult cases
"""

import os
import sys

print("🚨 تشغيل طوارئ للبوت...")

# إعداد فوري لمتغيرات البيئة
os.environ['BOT_TOKEN'] = '**********************************************'
os.environ['TELEGRAM_BOT_TOKEN'] = '**********************************************'
os.environ['SUPABASE_URL'] = 'https://ytqxxodyecdeosnqoure.supabase.co'
os.environ['SUPABASE_KEY'] = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4'
os.environ['ADMIN_CHAT_ID'] = '7513880877'
os.environ['USE_NGROK'] = 'false'
os.environ['ENVIRONMENT'] = 'production'
os.environ['PYTHONUNBUFFERED'] = '1'
os.environ['RENDER'] = 'true'

print("✅ تم إعداد متغيرات البيئة")

# إصلاح سريع لـ Telegram
try:
    import requests
    bot_token = os.environ['BOT_TOKEN']
    requests.post(f"https://api.telegram.org/bot{bot_token}/deleteWebhook", 
                 json={"drop_pending_updates": True}, timeout=5)
    print("✅ تم إصلاح Telegram")
except:
    print("⚠️ تحذير: فشل في إصلاح Telegram")

# تشغيل خادم بسيط
try:
    from flask import Flask
    import threading
    
    app = Flask(__name__)
    
    @app.route('/')
    def health():
        return {"status": "healthy", "service": "minecraft_mods_bot"}
    
    port = int(os.getenv('PORT', 10000))
    
    def run_server():
        app.run(host='0.0.0.0', port=port, debug=False)
    
    server_thread = threading.Thread(target=run_server)
    server_thread.daemon = True
    server_thread.start()
    
    print(f"🌐 خادم يعمل على المنفذ {port}")
except:
    print("⚠️ فشل في تشغيل الخادم")

# تشغيل البوت
print("🤖 تشغيل البوت...")

try:
    import main
    print("✅ تم استيراد main")
    
    # محاولة تشغيل البوت
    if hasattr(main, 'main'):
        try:
            import asyncio
            if asyncio.iscoroutinefunction(main.main):
                asyncio.run(main.main())
            else:
                main.main()
        except:
            main.main()
    
    print("🎉 البوت يعمل!")
    
    # إبقاء البرنامج يعمل
    import time
    while True:
        time.sleep(60)
        print("💓 البوت لا يزال يعمل...")
        
except Exception as e:
    print(f"❌ خطأ: {e}")
    print("🔄 محاولة بديلة...")
    
    try:
        import start_render
        import asyncio
        asyncio.run(start_render.main())
        print("✅ تم تشغيل البوت بالطريقة البديلة!")
    except Exception as e2:
        print(f"❌ فشل أيضاً: {e2}")
        print("💡 تحقق من السجلات للمزيد من التفاصيل")
        sys.exit(1)
