#!/usr/bin/env python3
"""
تشغيل النظام الذكي مع مدير API عبر التيليجرام
"""

import asyncio
import logging
import sys
from pathlib import Path

# إعداد السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/smart_system.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

async def main():
    """تشغيل النظام الذكي"""
    print("""
    🤖 Reddit Video Maker Bot - النظام الذكي
    ==========================================
    
    ✅ إدارة ذكية للأخطاء
    ✅ تبديل تلقائي بين APIs
    ✅ إدارة عن بُعد عبر التيليجرام
    ✅ مراقبة مستمرة للنظام
    
    """)
    
    try:
        # بدء نظام المراقبة الذكي
        from automation.smart_monitor import smart_monitor
        monitor_task = asyncio.create_task(smart_monitor.start_monitoring())
        
        # بدء مدير API عبر التيليجرام
        from automation.telegram_api_manager import start_api_manager
        api_manager_task = asyncio.create_task(start_api_manager())
        
        logger.info("🚀 تم بدء النظام الذكي بنجاح")
        
        # انتظار المهام
        await asyncio.gather(monitor_task, api_manager_task)
        
    except KeyboardInterrupt:
        logger.info("⏹️ تم إيقاف النظام بواسطة المستخدم")
    except Exception as e:
        logger.error(f"❌ خطأ في النظام الذكي: {str(e)}")
        raise

if __name__ == "__main__":
    # التحقق من إصدار Python
    if sys.version_info.major != 3 or sys.version_info.minor < 10:
        print("❌ يتطلب Python 3.10 أو أحدث")
        sys.exit(1)
    
    # إنشاء المجلدات المطلوبة
    Path("logs").mkdir(exist_ok=True)
    
    # تشغيل النظام
    asyncio.run(main())
