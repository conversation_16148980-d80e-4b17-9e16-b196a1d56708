#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مراقب استهلاك API للصور - تحليل وتحسين الاستخدام
"""

import asyncio
import sys
import os
import json
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import pandas as pd

# إضافة المسار الحالي لـ Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.logger import logger

class ImageAPIMonitor:
    """مراقب استهلاك API للصور"""
    
    def __init__(self):
        self.stats_file = "cache/daily_image_stats.json"
        self.history_file = "cache/image_api_history.json"
        
    def load_current_stats(self) -> dict:
        """تحميل إحصائيات اليوم الحالي"""
        try:
            if os.path.exists(self.stats_file):
                with open(self.stats_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return {}
        except Exception as e:
            print(f"خطأ في تحميل الإحصائيات: {e}")
            return {}
    
    def load_history(self) -> list:
        """تحميل تاريخ الاستخدام"""
        try:
            if os.path.exists(self.history_file):
                with open(self.history_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return []
        except Exception as e:
            print(f"خطأ في تحميل التاريخ: {e}")
            return []
    
    def save_to_history(self, stats: dict):
        """حفظ الإحصائيات في التاريخ"""
        try:
            history = self.load_history()
            
            # إضافة الإحصائيات الحالية
            history.append({
                **stats,
                'timestamp': datetime.now().isoformat()
            })
            
            # الاحتفاظ بآخر 30 يوم فقط
            if len(history) > 30:
                history = history[-30:]
            
            with open(self.history_file, 'w', encoding='utf-8') as f:
                json.dump(history, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            print(f"خطأ في حفظ التاريخ: {e}")
    
    def analyze_current_usage(self):
        """تحليل الاستخدام الحالي"""
        print("📊 تحليل استهلاك API للصور - اليوم الحالي")
        print("=" * 50)
        
        stats = self.load_current_stats()
        
        if not stats:
            print("❌ لا توجد إحصائيات متاحة")
            return
        
        print(f"📅 التاريخ: {stats.get('date', 'غير محدد')}")
        print(f"🎨 الصور المُنشأة: {stats.get('images_generated', 0)}")
        print(f"📰 المقالات المُعالجة: {stats.get('articles_processed', 0)}")
        
        # تحليل استدعاءات API
        api_calls = stats.get('api_calls', {})
        print(f"\n🔌 استدعاءات API:")
        for api, calls in api_calls.items():
            if calls > 0:
                print(f"   {api}: {calls} استدعاء")
        
        # تحليل التخزين المؤقت
        cache_hits = stats.get('cache_hits', 0)
        cache_misses = stats.get('cache_misses', 0)
        total_requests = cache_hits + cache_misses
        
        if total_requests > 0:
            cache_hit_rate = (cache_hits / total_requests) * 100
            print(f"\n💾 التخزين المؤقت:")
            print(f"   إجمالي الطلبات: {total_requests}")
            print(f"   نجح من التخزين: {cache_hits}")
            print(f"   فشل من التخزين: {cache_misses}")
            print(f"   معدل النجاح: {cache_hit_rate:.1f}%")
        
        # تحليل الكفاءة
        if stats.get('articles_processed', 0) > 0:
            efficiency = (stats.get('images_generated', 0) / stats.get('articles_processed', 1)) * 100
            print(f"\n⚡ الكفاءة:")
            print(f"   نسبة إنشاء الصور: {efficiency:.1f}%")
            
            if efficiency > 80:
                print("   🟢 كفاءة عالية")
            elif efficiency > 50:
                print("   🟡 كفاءة متوسطة")
            else:
                print("   🔴 كفاءة منخفضة")
    
    def analyze_trends(self):
        """تحليل الاتجاهات"""
        print("\n📈 تحليل الاتجاهات (آخر 7 أيام)")
        print("-" * 40)
        
        history = self.load_history()
        
        if len(history) < 2:
            print("❌ لا توجد بيانات كافية للتحليل")
            return
        
        # أخذ آخر 7 أيام
        recent_history = history[-7:]
        
        # تحليل الاتجاهات
        dates = []
        images_generated = []
        articles_processed = []
        api_calls_total = []
        
        for day in recent_history:
            dates.append(day.get('date', ''))
            images_generated.append(day.get('images_generated', 0))
            articles_processed.append(day.get('articles_processed', 0))
            
            total_api = sum(day.get('api_calls', {}).values())
            api_calls_total.append(total_api)
        
        # عرض الاتجاهات
        print(f"📊 ملخص آخر {len(recent_history)} أيام:")
        print(f"   متوسط الصور/يوم: {sum(images_generated)/len(images_generated):.1f}")
        print(f"   متوسط المقالات/يوم: {sum(articles_processed)/len(articles_processed):.1f}")
        print(f"   متوسط استدعاءات API/يوم: {sum(api_calls_total)/len(api_calls_total):.1f}")
        
        # اتجاه التغيير
        if len(images_generated) >= 2:
            recent_avg = sum(images_generated[-3:]) / min(3, len(images_generated))
            older_avg = sum(images_generated[:-3]) / max(1, len(images_generated) - 3)
            
            if recent_avg > older_avg * 1.1:
                print("   📈 اتجاه تصاعدي في إنشاء الصور")
            elif recent_avg < older_avg * 0.9:
                print("   📉 اتجاه تنازلي في إنشاء الصور")
            else:
                print("   ➡️ اتجاه مستقر في إنشاء الصور")
    
    def generate_recommendations(self):
        """إنشاء توصيات للتحسين"""
        print("\n💡 توصيات التحسين")
        print("-" * 25)
        
        stats = self.load_current_stats()
        
        if not stats:
            print("❌ لا توجد بيانات للتحليل")
            return
        
        recommendations = []
        
        # تحليل معدل التخزين المؤقت
        cache_hits = stats.get('cache_hits', 0)
        cache_misses = stats.get('cache_misses', 0)
        total_requests = cache_hits + cache_misses
        
        if total_requests > 0:
            cache_hit_rate = (cache_hits / total_requests) * 100
            
            if cache_hit_rate < 30:
                recommendations.append("🔄 تحسين نظام التخزين المؤقت - معدل النجاح منخفض")
            elif cache_hit_rate > 70:
                recommendations.append("✅ نظام التخزين المؤقت يعمل بكفاءة عالية")
        
        # تحليل استهلاك API
        api_calls = stats.get('api_calls', {})
        total_api_calls = sum(api_calls.values())
        images_generated = stats.get('images_generated', 0)
        
        if total_api_calls > images_generated * 1.5:
            recommendations.append("⚠️ استهلاك API مرتفع - فحص فشل الطلبات")
        
        # تحليل كفاءة الإنشاء
        articles_processed = stats.get('articles_processed', 0)
        if articles_processed > 0:
            generation_rate = (images_generated / articles_processed) * 100
            
            if generation_rate < 20:
                recommendations.append("📈 معدل إنشاء الصور منخفض - مراجعة معايير الجودة")
            elif generation_rate > 80:
                recommendations.append("⚡ معدل إنشاء عالي - قد تحتاج زيادة الحد اليومي")
        
        # عرض التوصيات
        if recommendations:
            for i, rec in enumerate(recommendations, 1):
                print(f"   {i}. {rec}")
        else:
            print("   ✅ النظام يعمل بكفاءة جيدة")
    
    def create_visual_report(self):
        """إنشاء تقرير مرئي"""
        print("\n📊 إنشاء تقرير مرئي...")
        
        try:
            history = self.load_history()
            
            if len(history) < 3:
                print("❌ لا توجد بيانات كافية للتقرير المرئي")
                return
            
            # تحضير البيانات
            dates = [datetime.fromisoformat(day.get('timestamp', day.get('date', ''))).strftime('%m-%d') for day in history[-14:]]
            images = [day.get('images_generated', 0) for day in history[-14:]]
            articles = [day.get('articles_processed', 0) for day in history[-14:]]
            
            # إنشاء الرسم البياني
            fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))
            
            # رسم الصور المُنشأة
            ax1.plot(dates, images, marker='o', linewidth=2, label='الصور المُنشأة')
            ax1.set_title('الصور المُنشأة يومياً (آخر 14 يوم)')
            ax1.set_ylabel('عدد الصور')
            ax1.grid(True, alpha=0.3)
            ax1.legend()
            
            # رسم المقالات المُعالجة
            ax2.plot(dates, articles, marker='s', color='orange', linewidth=2, label='المقالات المُعالجة')
            ax2.set_title('المقالات المُعالجة يومياً')
            ax2.set_xlabel('التاريخ')
            ax2.set_ylabel('عدد المقالات')
            ax2.grid(True, alpha=0.3)
            ax2.legend()
            
            # تحسين التخطيط
            plt.xticks(rotation=45)
            plt.tight_layout()
            
            # حفظ التقرير
            report_path = f"reports/image_api_report_{datetime.now().strftime('%Y%m%d')}.png"
            os.makedirs("reports", exist_ok=True)
            plt.savefig(report_path, dpi=300, bbox_inches='tight')
            
            print(f"✅ تم حفظ التقرير المرئي: {report_path}")
            
        except ImportError:
            print("⚠️ matplotlib غير مثبت - تخطي التقرير المرئي")
        except Exception as e:
            print(f"❌ خطأ في إنشاء التقرير المرئي: {e}")
    
    def export_data(self):
        """تصدير البيانات"""
        print("\n💾 تصدير البيانات...")
        
        try:
            history = self.load_history()
            current_stats = self.load_current_stats()
            
            # إنشاء تقرير شامل
            report = {
                'generated_at': datetime.now().isoformat(),
                'current_stats': current_stats,
                'history': history,
                'summary': {
                    'total_days': len(history),
                    'total_images_generated': sum(day.get('images_generated', 0) for day in history),
                    'total_articles_processed': sum(day.get('articles_processed', 0) for day in history),
                    'average_images_per_day': sum(day.get('images_generated', 0) for day in history) / max(len(history), 1),
                    'average_articles_per_day': sum(day.get('articles_processed', 0) for day in history) / max(len(history), 1)
                }
            }
            
            # حفظ التقرير
            export_path = f"reports/image_api_data_{datetime.now().strftime('%Y%m%d_%H%M')}.json"
            os.makedirs("reports", exist_ok=True)
            
            with open(export_path, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            
            print(f"✅ تم تصدير البيانات: {export_path}")
            
        except Exception as e:
            print(f"❌ خطأ في تصدير البيانات: {e}")

def main():
    """الدالة الرئيسية"""
    print("🔍 مراقب استهلاك API للصور")
    print("=" * 40)
    
    monitor = ImageAPIMonitor()
    
    try:
        # تحليل الاستخدام الحالي
        monitor.analyze_current_usage()
        
        # تحليل الاتجاهات
        monitor.analyze_trends()
        
        # إنشاء توصيات
        monitor.generate_recommendations()
        
        # إنشاء تقرير مرئي
        monitor.create_visual_report()
        
        # تصدير البيانات
        monitor.export_data()
        
        # حفظ الإحصائيات الحالية في التاريخ
        current_stats = monitor.load_current_stats()
        if current_stats:
            monitor.save_to_history(current_stats)
        
        print("\n✅ تم إنجاز تحليل استهلاك API للصور بنجاح!")
        
    except Exception as e:
        print(f"\n❌ خطأ في التحليل: {e}")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف المراقب بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
