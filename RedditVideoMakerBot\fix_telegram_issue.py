#!/usr/bin/env python3
"""
إصلاح مشكلة Telegram HTTP 400
يفحص ويصلح إعدادات Telegram تلقائياً
"""

import os
import sys
import requests
import json
import logging
from pathlib import Path

# إعداد السجلات
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_telegram_bot(bot_token: str) -> bool:
    """اختبار صحة رمز البوت"""
    print(f"🔍 اختبار رمز البوت: {bot_token[:10]}...")
    
    try:
        response = requests.get(f"https://api.telegram.org/bot{bot_token}/getMe", timeout=10)
        if response.status_code == 200:
            bot_info = response.json()
            if bot_info['ok']:
                print(f"✅ البوت صحيح: @{bot_info['result']['username']}")
                return True
            else:
                print(f"❌ رمز البوت غير صحيح: {bot_info}")
                return False
        else:
            print(f"❌ خطأ HTTP: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ خطأ في الاتصال: {e}")
        return False

def get_chat_updates(bot_token: str) -> list:
    """الحصول على آخر الرسائل لاكتشاف معرف المحادثة"""
    print("🔍 البحث عن معرف المحادثة...")
    
    try:
        response = requests.get(f"https://api.telegram.org/bot{bot_token}/getUpdates", timeout=10)
        if response.status_code == 200:
            updates = response.json()
            if updates['ok'] and updates['result']:
                print(f"✅ تم العثور على {len(updates['result'])} رسالة")
                return updates['result']
            else:
                print("⚠️ لا توجد رسائل")
                return []
        else:
            print(f"❌ خطأ في الحصول على التحديثات: {response.status_code}")
            return []
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return []

def extract_chat_ids(updates: list) -> list:
    """استخراج معرفات المحادثة من التحديثات"""
    chat_ids = set()
    
    for update in updates:
        if 'message' in update:
            chat_id = update['message']['chat']['id']
            chat_type = update['message']['chat']['type']
            
            if chat_type == 'private':
                username = update['message']['from'].get('username', 'غير متوفر')
                first_name = update['message']['from'].get('first_name', 'غير متوفر')
                print(f"💬 محادثة خاصة: {chat_id} (@{username} - {first_name})")
            else:
                chat_title = update['message']['chat'].get('title', 'غير متوفر')
                print(f"👥 مجموعة: {chat_id} ({chat_title})")
            
            chat_ids.add(str(chat_id))
    
    return list(chat_ids)

def test_send_message(bot_token: str, chat_id: str) -> bool:
    """اختبار إرسال رسالة"""
    print(f"📤 اختبار إرسال رسالة إلى: {chat_id}")
    
    test_message = "🧪 رسالة اختبار من Reddit Video Maker Bot"
    
    try:
        # محاولة مع Markdown
        data = {
            'chat_id': chat_id,
            'text': test_message,
            'parse_mode': 'Markdown'
        }
        
        response = requests.post(f"https://api.telegram.org/bot{bot_token}/sendMessage", 
                               data=data, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            if result['ok']:
                print("✅ تم إرسال الرسالة بنجاح (مع Markdown)")
                return True
        
        # محاولة بدون تنسيق
        data = {
            'chat_id': chat_id,
            'text': test_message
        }
        
        response = requests.post(f"https://api.telegram.org/bot{bot_token}/sendMessage", 
                               data=data, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            if result['ok']:
                print("✅ تم إرسال الرسالة بنجاح (بدون تنسيق)")
                return True
            else:
                print(f"❌ فشل الإرسال: {result}")
                return False
        else:
            error_details = response.json() if response.content else {}
            print(f"❌ خطأ HTTP {response.status_code}: {error_details}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في إرسال الرسالة: {e}")
        return False

def save_chat_id(chat_id: str):
    """حفظ معرف المحادثة في الملف"""
    try:
        with open("telegram_chat_id.txt", "w") as f:
            f.write(chat_id)
        print(f"✅ تم حفظ معرف المحادثة: {chat_id}")
        
        # تحديث ملف الإعدادات أيضاً
        try:
            import toml
            if os.path.exists("config.toml"):
                with open("config.toml", "r", encoding="utf-8") as f:
                    config = toml.load(f)
                
                if "telegram" not in config:
                    config["telegram"] = {}
                
                config["telegram"]["chat_id"] = chat_id
                
                with open("config.toml", "w", encoding="utf-8") as f:
                    toml.dump(config, f)
                
                print("✅ تم تحديث ملف config.toml")
        except Exception as e:
            print(f"⚠️ لم يتم تحديث config.toml: {e}")
            
    except Exception as e:
        print(f"❌ فشل في حفظ معرف المحادثة: {e}")

def fix_telegram_setup():
    """إصلاح إعدادات Telegram"""
    print("🔧 إصلاح إعدادات Telegram")
    print("=" * 50)
    
    # قراءة رمز البوت من الإعدادات
    bot_token = None
    
    try:
        import toml
        if os.path.exists("config.toml"):
            with open("config.toml", "r", encoding="utf-8") as f:
                config = toml.load(f)
            bot_token = config.get("telegram", {}).get("bot_token")
    except Exception as e:
        print(f"⚠️ لم يتم قراءة الإعدادات: {e}")
    
    if not bot_token:
        print("❌ رمز البوت غير موجود في الإعدادات")
        print("💡 يرجى إضافة bot_token في قسم [telegram] في config.toml")
        return False
    
    # اختبار البوت
    if not test_telegram_bot(bot_token):
        print("❌ رمز البوت غير صحيح")
        return False
    
    # الحصول على التحديثات
    updates = get_chat_updates(bot_token)
    
    if not updates:
        print("\n💡 لم يتم العثور على رسائل. للحصول على معرف المحادثة:")
        print("1. ابحث عن البوت في Telegram")
        print("2. أرسل رسالة /start")
        print("3. شغل هذا الأمر مرة أخرى")
        return False
    
    # استخراج معرفات المحادثة
    chat_ids = extract_chat_ids(updates)
    
    if not chat_ids:
        print("❌ لم يتم العثور على معرفات محادثة")
        return False
    
    # اختبار كل معرف محادثة
    working_chat_id = None
    
    for chat_id in chat_ids:
        if test_send_message(bot_token, chat_id):
            working_chat_id = chat_id
            break
    
    if working_chat_id:
        save_chat_id(working_chat_id)
        print(f"\n🎉 تم إصلاح إعدادات Telegram بنجاح!")
        print(f"معرف المحادثة: {working_chat_id}")
        return True
    else:
        print("❌ لم يتم العثور على معرف محادثة يعمل")
        return False

def show_instructions():
    """عرض تعليمات الإعداد"""
    print("\n" + "=" * 50)
    print("📋 تعليمات إعداد Telegram")
    print("=" * 50)
    
    print("""
إذا لم يعمل الإصلاح التلقائي:

1. إنشاء بوت جديد:
   - ابحث عن @BotFather في Telegram
   - أرسل /newbot
   - اتبع التعليمات
   - احفظ الرمز المميز (Token)

2. إضافة الرمز في config.toml:
   [telegram]
   bot_token = "YOUR_BOT_TOKEN_HERE"

3. الحصول على معرف المحادثة:
   - ابحث عن البوت الجديد
   - أرسل /start
   - شغل: python get_chat_id.py

4. اختبار الإعدادات:
   - شغل: python fix_telegram_issue.py
    """)

if __name__ == "__main__":
    try:
        success = fix_telegram_setup()
        
        if not success:
            show_instructions()
        
        print(f"\n{'='*50}")
        print("🏁 انتهى إصلاح Telegram")
        print(f"{'='*50}")
        
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الإصلاح بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ حرج: {e}")
        import traceback
        print(f"تفاصيل الخطأ:\n{traceback.format_exc()}")
