#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاحات الشبكة لبيئة Render - مجلد send games news
"""

import os
import sys
import logging
from pathlib import Path

# إعداد نظام السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger(__name__)

def test_environment_variables():
    """اختبار متغيرات البيئة"""
    logger.info("🔍 اختبار متغيرات البيئة...")
    
    # محاكاة بيئة Render
    os.environ['RENDER'] = 'true'
    
    # التحقق من وجود المتغيرات المطلوبة
    required_vars = [
        'BOT_TOKEN',
        'TELEGRAM_BOT_TOKEN', 
        'SUPABASE_URL',
        'SUPABASE_KEY'
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        logger.warning(f"⚠️ متغيرات البيئة المفقودة: {', '.join(missing_vars)}")
        
        # محاولة إصلاح المتغيرات المفقودة
        if 'TELEGRAM_BOT_TOKEN' in missing_vars and os.getenv('BOT_TOKEN'):
            os.environ['TELEGRAM_BOT_TOKEN'] = os.getenv('BOT_TOKEN')
            logger.info("✅ تم إصلاح TELEGRAM_BOT_TOKEN باستخدام BOT_TOKEN")
            missing_vars.remove('TELEGRAM_BOT_TOKEN')
    
    if not missing_vars:
        logger.info("✅ جميع متغيرات البيئة متوفرة")
        return True
    else:
        logger.error(f"❌ متغيرات البيئة المفقودة: {', '.join(missing_vars)}")
        return False

def test_network_fixes():
    """اختبار إصلاحات الشبكة"""
    logger.info("🔍 اختبار إصلاحات الشبكة...")
    
    try:
        from render_network_fix import RenderNetworkFixer
        fixer = RenderNetworkFixer()
        
        # تطبيق الإصلاحات
        result = fixer.apply_network_fixes()
        
        if result:
            logger.info("✅ تم تطبيق إصلاحات الشبكة بنجاح")
            return True
        else:
            logger.error("❌ فشل في تطبيق إصلاحات الشبكة")
            return False
            
    except Exception as e:
        logger.error(f"❌ خطأ في اختبار إصلاحات الشبكة: {e}")
        return False

def test_connectivity():
    """اختبار الاتصال"""
    logger.info("🔍 اختبار الاتصال...")
    
    try:
        from render_network_fix import RenderNetworkFixer
        fixer = RenderNetworkFixer()
        
        # فحص الاتصال الأساسي
        basic_ok = fixer.check_basic_connectivity()
        
        # فحص اتصال Telegram
        telegram_ok = fixer.check_telegram_connectivity()
        
        if basic_ok:
            logger.info("✅ فحص الاتصال الأساسي نجح")
        else:
            logger.warning("⚠️ فحص الاتصال الأساسي فشل")
        
        if telegram_ok:
            logger.info("✅ فحص اتصال Telegram نجح")
        else:
            logger.warning("⚠️ فحص اتصال Telegram فشل")
        
        # نعتبر الاختبار ناجحاً إذا نجح أحد الفحوصات على الأقل
        return basic_ok or telegram_ok
        
    except Exception as e:
        logger.error(f"❌ خطأ في اختبار الاتصال: {e}")
        return False

def test_file_structure():
    """اختبار بنية الملفات"""
    logger.info("🔍 اختبار بنية الملفات...")
    
    required_files = [
        'render_network_fix.py',
        'start_render.py',
        'main.py',
        'Procfile',
        'render.yaml',
        'requirements.txt'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        logger.error(f"❌ ملفات مفقودة: {', '.join(missing_files)}")
        return False
    else:
        logger.info("✅ جميع الملفات المطلوبة موجودة")
        return True

def test_start_render():
    """اختبار ملف start_render.py"""
    logger.info("🔍 اختبار ملف start_render.py...")
    
    try:
        # محاولة استيراد الملف
        import start_render
        
        # التحقق من وجود الدوال المطلوبة
        required_functions = [
            'setup_environment',
            'apply_network_fixes',
            'check_required_env_vars',
            'main'
        ]
        
        missing_functions = []
        for func_name in required_functions:
            if not hasattr(start_render, func_name):
                missing_functions.append(func_name)
        
        if missing_functions:
            logger.warning(f"⚠️ دوال مفقودة في start_render.py: {', '.join(missing_functions)}")
        else:
            logger.info("✅ جميع الدوال المطلوبة موجودة في start_render.py")
        
        return len(missing_functions) == 0
        
    except Exception as e:
        logger.error(f"❌ خطأ في اختبار start_render.py: {e}")
        return False

def test_main_py():
    """اختبار ملف main.py"""
    logger.info("🔍 اختبار ملف main.py...")
    
    try:
        # التحقق من وجود الملف
        if not Path('main.py').exists():
            logger.error("❌ ملف main.py غير موجود")
            return False
        
        # قراءة محتوى الملف للتحقق من وجود دالة main
        with open('main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        if 'async def main(' in content:
            logger.info("✅ دالة main async موجودة في main.py")
            return True
        elif 'def main(' in content:
            logger.info("✅ دالة main موجودة في main.py")
            return True
        else:
            logger.warning("⚠️ دالة main غير موجودة في main.py")
            return False
        
    except Exception as e:
        logger.error(f"❌ خطأ في اختبار main.py: {e}")
        return False

def run_all_tests():
    """تشغيل جميع الاختبارات"""
    logger.info("🚀 بدء تشغيل جميع الاختبارات")
    logger.info("=" * 50)
    
    tests = [
        ("بنية الملفات", test_file_structure),
        ("متغيرات البيئة", test_environment_variables),
        ("إصلاحات الشبكة", test_network_fixes),
        ("فحص الاتصال", test_connectivity),
        ("ملف start_render.py", test_start_render),
        ("ملف main.py", test_main_py),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n🔍 تشغيل اختبار: {test_name}")
        try:
            result = test_func()
            results[test_name] = result
            status = "✅ نجح" if result else "❌ فشل"
            logger.info(f"   النتيجة: {status}")
        except Exception as e:
            results[test_name] = False
            logger.error(f"   ❌ خطأ: {e}")
    
    # عرض النتائج النهائية
    logger.info("\n" + "=" * 50)
    logger.info("📊 ملخص النتائج:")
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅" if result else "❌"
        logger.info(f"   {status} {test_name}")
        if result:
            passed += 1
    
    logger.info(f"\n🎯 النتيجة النهائية: {passed}/{total} اختبارات نجحت")
    
    if passed == total:
        logger.info("🎉 جميع الاختبارات نجحت! الإصلاحات جاهزة للاستخدام")
        return True
    elif passed >= total * 0.7:  # 70% نجاح
        logger.info("✅ معظم الاختبارات نجحت، الإصلاحات قابلة للاستخدام")
        return True
    else:
        logger.warning("⚠️ بعض الاختبارات فشلت، راجع الأخطاء أعلاه")
        return False

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار إصلاحات الشبكة لبيئة Render - send games news")
    print("=" * 60)
    
    try:
        result = run_all_tests()
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        logger.info("⏹️ تم إيقاف الاختبارات")
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ خطأ في تشغيل الاختبارات: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
