from github_uploader import G<PERSON><PERSON><PERSON><PERSON>ploader
import os

# Your GitHub Personal Access Token
TOKEN = "*********************************************************************************************"

# Project details
PROJECT_PATH = "send games news"
REPO_NAME = "redit-video" # Using a valid repo name, spaces are not allowed
DESCRIPTION = "A bot to send gaming news."
PRIVATE = True
COMMIT_MESSAGE = "Initial commit: Uploading the bot project"

def run_upload():
    """
    Runs the GitHub uploader with predefined settings.
    """
    if not TOKEN:
        print("❌ GitHub Token is missing.")
        return

    if not os.path.exists(PROJECT_PATH):
        print(f"❌ Project path does not exist: {PROJECT_PATH}")
        return

    uploader = GitHubUploader(token=TOKEN)
    
    success = uploader.upload_project(
        project_path=PROJECT_PATH,
        repo_name=REPO_NAME,
        description=DESCRIPTION,
        private=PRIVATE,
        commit_message=COMMIT_MESSAGE
    )

    if success:
        print("✅ Project uploaded successfully!")
    else:
        print("❌ Failed to upload the project.")

if __name__ == "__main__":
    run_upload()
