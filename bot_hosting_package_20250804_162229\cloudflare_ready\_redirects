# Redirects for Cloudflare Pages - Minecraft Mod Bot
# URL Redirects and Rewrites

# Redirect old mod URLs to new format
/mod/:id  /index.html?id=:id  200
/mods/:id  /index.html?id=:id  200

# Preview mode redirects
/preview  /index.html?preview=1  200
/preview/:id  /index.html?id=:id&preview=1  200

# Language specific redirects
/ar/*  /index.html?lang=ar  200
/en/*  /index.html?lang=en  200

# User specific redirects
/user/:userId  /index.html?user_id=:userId  200
/channel/:channelId  /index.html?channel=:channelId  200

# API proxy redirects (if needed)
/api/mods/*  https://ytqxxodyecdeosnqoure.supabase.co/rest/v1/mods/:splat  200
/api/users/*  https://ytqxxodyecdeosnqoure.supabase.co/rest/v1/user_ads_settings/:splat  200

# Legacy URL support
/mod.html  /index.html  301
/mod.php  /index.html  301
/details.html  /index.html  301

# Handle missing files
/favicon.ico  /index.html  404
/robots.txt  /robots.txt  200

# SPA fallback - catch all other routes and serve index.html
/*  /index.html  404
