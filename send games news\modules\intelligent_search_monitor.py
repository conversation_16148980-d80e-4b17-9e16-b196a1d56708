# نظام مراقبة وتشخيص البحث الذكي المتقدم
import asyncio
import time
import json
import psutil
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, asdict
from enum import Enum
from collections import defaultdict, deque
import statistics

from .logger import logger
from .intelligent_search_config import config_manager

class HealthStatus(Enum):
    """حالة صحة النظام"""
    EXCELLENT = "excellent"    # ممتاز
    GOOD = "good"             # جيد
    WARNING = "warning"       # تحذير
    CRITICAL = "critical"     # حرج
    UNKNOWN = "unknown"       # غير معروف

class AlertLevel(Enum):
    """مستوى التنبيه"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"

@dataclass
class PerformanceMetric:
    """مقياس أداء"""
    name: str
    value: float
    unit: str
    timestamp: datetime
    threshold_warning: Optional[float] = None
    threshold_critical: Optional[float] = None
    trend: Optional[str] = None  # improving, stable, degrading

@dataclass
class SystemAlert:
    """تنبيه النظام"""
    id: str
    level: AlertLevel
    title: str
    message: str
    component: str
    timestamp: datetime
    resolved: bool = False
    resolution_time: Optional[datetime] = None

@dataclass
class HealthReport:
    """تقرير صحة النظام"""
    overall_status: HealthStatus
    component_status: Dict[str, HealthStatus]
    performance_metrics: List[PerformanceMetric]
    active_alerts: List[SystemAlert]
    recommendations: List[str]
    generated_at: datetime

class IntelligentSearchMonitor:
    """نظام مراقبة البحث الذكي المتقدم"""
    
    def __init__(self):
        # إعدادات المراقبة
        self.monitoring_config = {
            'enable_real_time_monitoring': True,
            'monitoring_interval': 30,  # ثانية
            'metrics_retention_hours': 24,
            'alert_retention_hours': 72,
            'enable_auto_diagnostics': True,
            'enable_performance_alerts': True
        }
        
        # مقاييس الأداء
        self.performance_metrics = {
            'search_response_time': deque(maxlen=100),
            'search_success_rate': deque(maxlen=100),
            'search_quality_score': deque(maxlen=100),
            'system_cpu_usage': deque(maxlen=100),
            'system_memory_usage': deque(maxlen=100),
            'active_searches': deque(maxlen=100),
            'cache_hit_rate': deque(maxlen=100),
            'engine_availability': defaultdict(lambda: deque(maxlen=100))
        }
        
        # التنبيهات النشطة
        self.active_alerts = []
        self.alert_history = deque(maxlen=1000)
        
        # إحصائيات المراقبة
        self.monitoring_stats = {
            'total_monitoring_cycles': 0,
            'alerts_generated': 0,
            'alerts_resolved': 0,
            'health_checks_performed': 0,
            'auto_optimizations': 0
        }
        
        # عتبات التنبيه
        self.alert_thresholds = {
            'response_time_warning': 10.0,
            'response_time_critical': 20.0,
            'success_rate_warning': 0.8,
            'success_rate_critical': 0.6,
            'cpu_usage_warning': 80.0,
            'cpu_usage_critical': 95.0,
            'memory_usage_warning': 80.0,
            'memory_usage_critical': 95.0,
            'quality_score_warning': 0.6,
            'quality_score_critical': 0.4
        }
        
        # خيط المراقبة
        self.monitoring_thread = None
        self.monitoring_active = False
        
        # معالجات التنبيهات
        self.alert_handlers = []
        
        logger.info("🔍 تم تهيئة نظام مراقبة البحث الذكي")
    
    def start_monitoring(self):
        """بدء المراقبة المستمرة"""
        if self.monitoring_active:
            logger.warning("⚠️ المراقبة نشطة بالفعل")
            return
        
        self.monitoring_active = True
        self.monitoring_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.monitoring_thread.start()
        
        logger.info("🟢 تم بدء المراقبة المستمرة")
    
    def stop_monitoring(self):
        """إيقاف المراقبة"""
        self.monitoring_active = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=5)
        
        logger.info("🔴 تم إيقاف المراقبة")
    
    def _monitoring_loop(self):
        """حلقة المراقبة الرئيسية"""
        while self.monitoring_active:
            try:
                # جمع المقاييس
                self._collect_metrics()
                
                # فحص الصحة
                health_report = self._perform_health_check()
                
                # فحص التنبيهات
                self._check_alerts()
                
                # التشخيص التلقائي
                if self.monitoring_config['enable_auto_diagnostics']:
                    self._auto_diagnostics()
                
                # تحديث الإحصائيات
                self.monitoring_stats['total_monitoring_cycles'] += 1
                
                # انتظار الفترة التالية
                time.sleep(self.monitoring_config['monitoring_interval'])
                
            except Exception as e:
                logger.error(f"❌ خطأ في حلقة المراقبة: {e}")
                time.sleep(5)  # انتظار قصير قبل المحاولة مرة أخرى
    
    def _collect_metrics(self):
        """جمع مقاييس الأداء"""
        try:
            # مقاييس النظام
            cpu_percent = psutil.cpu_percent(interval=1)
            memory_percent = psutil.virtual_memory().percent
            
            self.performance_metrics['system_cpu_usage'].append({
                'value': cpu_percent,
                'timestamp': datetime.now()
            })
            
            self.performance_metrics['system_memory_usage'].append({
                'value': memory_percent,
                'timestamp': datetime.now()
            })
            
            # مقاييس البحث (من النظام الموحد)
            try:
                from .unified_intelligent_search import unified_intelligent_search
                insights = unified_intelligent_search.get_system_insights()
                
                if insights and 'performance_metrics' in insights:
                    metrics = insights['performance_metrics']
                    
                    self.performance_metrics['search_success_rate'].append({
                        'value': metrics.get('success_rate', 0),
                        'timestamp': datetime.now()
                    })
                    
                    self.performance_metrics['search_response_time'].append({
                        'value': metrics.get('average_execution_time', 0),
                        'timestamp': datetime.now()
                    })
                    
                    self.performance_metrics['search_quality_score'].append({
                        'value': metrics.get('average_quality_score', 0),
                        'timestamp': datetime.now()
                    })
            
            except Exception as e:
                logger.debug(f"تعذر جمع مقاييس البحث: {e}")
            
        except Exception as e:
            logger.error(f"❌ فشل في جمع المقاييس: {e}")
    
    def _perform_health_check(self) -> HealthReport:
        """فحص صحة النظام"""
        try:
            self.monitoring_stats['health_checks_performed'] += 1
            
            component_status = {}
            recommendations = []
            
            # فحص أداء البحث
            search_status = self._check_search_performance()
            component_status['search_performance'] = search_status
            
            # فحص موارد النظام
            system_status = self._check_system_resources()
            component_status['system_resources'] = system_status
            
            # فحص المحركات
            engines_status = self._check_engines_health()
            component_status['search_engines'] = engines_status
            
            # فحص التعلم والتكيف
            learning_status = self._check_learning_system()
            component_status['learning_system'] = learning_status
            
            # تحديد الحالة العامة
            overall_status = self._determine_overall_status(component_status)
            
            # إنشاء التوصيات
            recommendations = self._generate_recommendations(component_status)
            
            # إنشاء مقاييس الأداء
            performance_metrics = self._create_performance_metrics()
            
            health_report = HealthReport(
                overall_status=overall_status,
                component_status=component_status,
                performance_metrics=performance_metrics,
                active_alerts=self.active_alerts.copy(),
                recommendations=recommendations,
                generated_at=datetime.now()
            )
            
            return health_report
            
        except Exception as e:
            logger.error(f"❌ فشل في فحص الصحة: {e}")
            return HealthReport(
                overall_status=HealthStatus.UNKNOWN,
                component_status={},
                performance_metrics=[],
                active_alerts=[],
                recommendations=["فشل في فحص صحة النظام"],
                generated_at=datetime.now()
            )
    
    def _check_search_performance(self) -> HealthStatus:
        """فحص أداء البحث"""
        try:
            if not self.performance_metrics['search_response_time']:
                return HealthStatus.UNKNOWN
            
            # متوسط وقت الاستجابة
            recent_times = [m['value'] for m in list(self.performance_metrics['search_response_time'])[-10:]]
            avg_response_time = statistics.mean(recent_times) if recent_times else 0
            
            # معدل النجاح
            recent_success = [m['value'] for m in list(self.performance_metrics['search_success_rate'])[-10:]]
            avg_success_rate = statistics.mean(recent_success) if recent_success else 0
            
            # تحديد الحالة
            if avg_response_time > self.alert_thresholds['response_time_critical'] or avg_success_rate < self.alert_thresholds['success_rate_critical']:
                return HealthStatus.CRITICAL
            elif avg_response_time > self.alert_thresholds['response_time_warning'] or avg_success_rate < self.alert_thresholds['success_rate_warning']:
                return HealthStatus.WARNING
            elif avg_response_time < 5.0 and avg_success_rate > 0.9:
                return HealthStatus.EXCELLENT
            else:
                return HealthStatus.GOOD
                
        except Exception as e:
            logger.error(f"❌ فشل في فحص أداء البحث: {e}")
            return HealthStatus.UNKNOWN
    
    def _check_system_resources(self) -> HealthStatus:
        """فحص موارد النظام"""
        try:
            if not self.performance_metrics['system_cpu_usage'] or not self.performance_metrics['system_memory_usage']:
                return HealthStatus.UNKNOWN
            
            # أحدث قيم للموارد
            latest_cpu = self.performance_metrics['system_cpu_usage'][-1]['value']
            latest_memory = self.performance_metrics['system_memory_usage'][-1]['value']
            
            # تحديد الحالة
            if latest_cpu > self.alert_thresholds['cpu_usage_critical'] or latest_memory > self.alert_thresholds['memory_usage_critical']:
                return HealthStatus.CRITICAL
            elif latest_cpu > self.alert_thresholds['cpu_usage_warning'] or latest_memory > self.alert_thresholds['memory_usage_warning']:
                return HealthStatus.WARNING
            elif latest_cpu < 50.0 and latest_memory < 60.0:
                return HealthStatus.EXCELLENT
            else:
                return HealthStatus.GOOD
                
        except Exception as e:
            logger.error(f"❌ فشل في فحص موارد النظام: {e}")
            return HealthStatus.UNKNOWN
    
    def _check_engines_health(self) -> HealthStatus:
        """فحص صحة محركات البحث"""
        try:
            # فحص توفر المحركات
            from .engine_coordination_system import engine_coordination_system
            insights = engine_coordination_system.get_coordination_insights()
            
            if insights and 'success_rate' in insights:
                success_rate = insights['success_rate']
                
                if success_rate > 0.9:
                    return HealthStatus.EXCELLENT
                elif success_rate > 0.8:
                    return HealthStatus.GOOD
                elif success_rate > 0.6:
                    return HealthStatus.WARNING
                else:
                    return HealthStatus.CRITICAL
            
            return HealthStatus.UNKNOWN
            
        except Exception as e:
            logger.error(f"❌ فشل في فحص صحة المحركات: {e}")
            return HealthStatus.UNKNOWN
    
    def _check_learning_system(self) -> HealthStatus:
        """فحص نظام التعلم"""
        try:
            from .adaptive_learning_system import adaptive_learning_system
            insights = adaptive_learning_system.get_learning_insights()
            
            if insights and 'performance_metrics' in insights:
                metrics = insights['performance_metrics']
                
                # فحص عدد الأحداث والأنماط
                events_count = metrics.get('total_learning_events', 0)
                patterns_count = metrics.get('patterns_discovered', 0)
                
                if events_count > 100 and patterns_count > 10:
                    return HealthStatus.EXCELLENT
                elif events_count > 50 and patterns_count > 5:
                    return HealthStatus.GOOD
                elif events_count > 10:
                    return HealthStatus.WARNING
                else:
                    return HealthStatus.CRITICAL
            
            return HealthStatus.UNKNOWN
            
        except Exception as e:
            logger.error(f"❌ فشل في فحص نظام التعلم: {e}")
            return HealthStatus.UNKNOWN
    
    def _determine_overall_status(self, component_status: Dict[str, HealthStatus]) -> HealthStatus:
        """تحديد الحالة العامة للنظام"""
        if not component_status:
            return HealthStatus.UNKNOWN
        
        status_values = list(component_status.values())
        
        # إذا كان أي مكون في حالة حرجة
        if HealthStatus.CRITICAL in status_values:
            return HealthStatus.CRITICAL
        
        # إذا كان أي مكون في حالة تحذير
        if HealthStatus.WARNING in status_values:
            return HealthStatus.WARNING
        
        # إذا كانت جميع المكونات ممتازة
        if all(status == HealthStatus.EXCELLENT for status in status_values):
            return HealthStatus.EXCELLENT
        
        # إذا كانت معظم المكونات جيدة أو ممتازة
        good_or_excellent = sum(1 for status in status_values if status in [HealthStatus.GOOD, HealthStatus.EXCELLENT])
        if good_or_excellent >= len(status_values) * 0.8:
            return HealthStatus.GOOD
        
        return HealthStatus.WARNING
    
    def _generate_recommendations(self, component_status: Dict[str, HealthStatus]) -> List[str]:
        """إنشاء توصيات التحسين"""
        recommendations = []
        
        # توصيات بناءً على حالة المكونات
        if component_status.get('search_performance') == HealthStatus.CRITICAL:
            recommendations.append("أداء البحث حرج - قم بتقليل timeout وعدد المحركات المتوازية")
        elif component_status.get('search_performance') == HealthStatus.WARNING:
            recommendations.append("أداء البحث يحتاج تحسين - راجع إعدادات البحث")
        
        if component_status.get('system_resources') == HealthStatus.CRITICAL:
            recommendations.append("موارد النظام مستنزفة - قم بتقليل العمليات المتوازية")
        elif component_status.get('system_resources') == HealthStatus.WARNING:
            recommendations.append("استخدام موارد عالي - راقب الأداء عن كثب")
        
        if component_status.get('search_engines') == HealthStatus.CRITICAL:
            recommendations.append("مشاكل في محركات البحث - تحقق من الاتصال والحدود")
        
        if component_status.get('learning_system') == HealthStatus.WARNING:
            recommendations.append("نظام التعلم يحتاج المزيد من البيانات")
        
        # توصيات عامة
        if not recommendations:
            recommendations.append("النظام يعمل بكفاءة - استمر في المراقبة")
        
        return recommendations
    
    def _create_performance_metrics(self) -> List[PerformanceMetric]:
        """إنشاء قائمة مقاييس الأداء"""
        metrics = []
        
        try:
            # متوسط وقت الاستجابة
            if self.performance_metrics['search_response_time']:
                recent_times = [m['value'] for m in list(self.performance_metrics['search_response_time'])[-10:]]
                avg_time = statistics.mean(recent_times) if recent_times else 0
                
                metrics.append(PerformanceMetric(
                    name="متوسط وقت الاستجابة",
                    value=avg_time,
                    unit="ثانية",
                    timestamp=datetime.now(),
                    threshold_warning=self.alert_thresholds['response_time_warning'],
                    threshold_critical=self.alert_thresholds['response_time_critical']
                ))
            
            # معدل النجاح
            if self.performance_metrics['search_success_rate']:
                recent_success = [m['value'] for m in list(self.performance_metrics['search_success_rate'])[-10:]]
                avg_success = statistics.mean(recent_success) if recent_success else 0
                
                metrics.append(PerformanceMetric(
                    name="معدل نجاح البحث",
                    value=avg_success,
                    unit="%",
                    timestamp=datetime.now(),
                    threshold_warning=self.alert_thresholds['success_rate_warning'],
                    threshold_critical=self.alert_thresholds['success_rate_critical']
                ))
            
            # استخدام المعالج
            if self.performance_metrics['system_cpu_usage']:
                latest_cpu = self.performance_metrics['system_cpu_usage'][-1]['value']
                
                metrics.append(PerformanceMetric(
                    name="استخدام المعالج",
                    value=latest_cpu,
                    unit="%",
                    timestamp=datetime.now(),
                    threshold_warning=self.alert_thresholds['cpu_usage_warning'],
                    threshold_critical=self.alert_thresholds['cpu_usage_critical']
                ))
            
            # استخدام الذاكرة
            if self.performance_metrics['system_memory_usage']:
                latest_memory = self.performance_metrics['system_memory_usage'][-1]['value']
                
                metrics.append(PerformanceMetric(
                    name="استخدام الذاكرة",
                    value=latest_memory,
                    unit="%",
                    timestamp=datetime.now(),
                    threshold_warning=self.alert_thresholds['memory_usage_warning'],
                    threshold_critical=self.alert_thresholds['memory_usage_critical']
                ))
            
        except Exception as e:
            logger.error(f"❌ فشل في إنشاء مقاييس الأداء: {e}")
        
        return metrics

# إنشاء مثيل عام
intelligent_search_monitor = IntelligentSearchMonitor()

# دوال مساعدة
def start_monitoring():
    """بدء المراقبة"""
    intelligent_search_monitor.start_monitoring()

def stop_monitoring():
    """إيقاف المراقبة"""
    intelligent_search_monitor.stop_monitoring()

def get_health_report() -> HealthReport:
    """الحصول على تقرير صحة النظام"""
    return intelligent_search_monitor._perform_health_check()
