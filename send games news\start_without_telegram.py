#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
تشغيل البرنامج بدون Telegram (Blogger فقط)
"""

import os
import sys
import warnings
import asyncio
from datetime import datetime

# إخفاء جميع التحذيرات
warnings.filterwarnings("ignore")

# إضافة المسار الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# إنشاء وحدة cgi بديلة إذا لم تكن موجودة
try:
    import cgi
except ImportError:
    import types
    import urllib.parse
    import html
    
    cgi_module = types.ModuleType('cgi')
    cgi_module.parse_qs = urllib.parse.parse_qs
    cgi_module.parse_qsl = urllib.parse.parse_qsl
    cgi_module.escape = html.escape
    cgi_module.unescape = html.unescape
    
    class FieldStorage:
        def __init__(self, *args, **kwargs):
            self.list = []
            self.file = None
            self.filename = None
            self.name = None
            self.value = None
        
        def getvalue(self, key, default=None):
            return default
            
        def getlist(self, key):
            return []
    
    cgi_module.FieldStorage = FieldStorage
    cgi_module.maxlen = 0
    sys.modules['cgi'] = cgi_module

def patch_publisher_for_blogger_only():
    """تصحيح الناشر للعمل مع Blogger فقط"""
    try:
        # قراءة ملف الناشر
        publisher_path = "modules/publisher.py"
        
        if os.path.exists(publisher_path):
            with open(publisher_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # إنشاء نسخة معدلة
            modified_content = content.replace(
                "import telegram",
                "# import telegram  # معطل للتشغيل بدون Telegram"
            )
            
            # استبدال استخدامات telegram
            modified_content = modified_content.replace(
                "self.telegram_bot = telegram.Bot(token=self.telegram_config['bot_token'])",
                "self.telegram_bot = None  # معطل"
            )
            
            # كتابة الملف المعدل
            with open("modules/publisher_blogger_only.py", 'w', encoding='utf-8') as f:
                f.write(modified_content)
            
            print("✅ تم إنشاء ناشر Blogger فقط")
            return True
    except Exception as e:
        print(f"⚠️ تحذير في تصحيح الناشر: {e}")
        return False

class SimpleBloggerBot:
    """بوت مبسط للعمل مع Blogger فقط"""
    
    def __init__(self):
        self.running = False
        print("🤖 تهيئة بوت أخبار الألعاب (Blogger فقط)...")
        
    async def initialize(self):
        """تهيئة البوت"""
        try:
            # تهيئة قاعدة البيانات
            from modules.database import db
            print("✅ تم تهيئة قاعدة البيانات")
            
            # تهيئة مولد المحتوى
            from modules.content_generator import ContentGenerator
            self.content_generator = ContentGenerator()
            print("✅ تم تهيئة مولد المحتوى")
            
            # تهيئة جامع المحتوى
            from modules.content_scraper import ContentScraper
            self.content_scraper = ContentScraper()
            print("✅ تم تهيئة جامع المحتوى")
            
            # تهيئة الناشر (Blogger فقط)
            from config.settings import BotConfig
            blogger_config = {
                'client_id': BotConfig.BLOGGER_CLIENT_ID,
                'client_secret': BotConfig.BLOGGER_CLIENT_SECRET,
                'blog_id': BotConfig.BLOGGER_BLOG_ID
            }
            
            # استيراد الناشر بدون telegram
            try:
                from modules.publisher_blogger_only import PublisherManager
                self.publisher = PublisherManager(blogger_config, None)
                print("✅ تم تهيئة الناشر (Blogger فقط)")
            except ImportError:
                # استخدام الناشر العادي مع تعطيل telegram
                from modules.publisher import PublisherManager
                self.publisher = PublisherManager(blogger_config, None)
                print("✅ تم تهيئة الناشر (Blogger فقط)")
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في التهيئة: {e}")
            return False
    
    async def process_articles(self):
        """معالجة المقالات"""
        try:
            print("🔍 البحث عن مقالات جديدة...")
            
            # البحث عن مقالات
            articles = await self.content_scraper.scrape_gaming_news()
            
            if not articles:
                print("ℹ️ لم يتم العثور على مقالات جديدة")
                return
            
            print(f"📰 تم العثور على {len(articles)} مقال")
            
            # معالجة كل مقال
            for article in articles[:3]:  # معالجة 3 مقالات كحد أقصى
                try:
                    print(f"📝 معالجة: {article.get('title', 'بدون عنوان')}")
                    
                    # توليد المحتوى
                    generated_content = await self.content_generator.generate_article(
                        article['title'],
                        article.get('content', ''),
                        article.get('source_url', '')
                    )
                    
                    if generated_content:
                        # نشر على Blogger
                        success = await self.publisher.publish_to_blogger(
                            generated_content['title'],
                            generated_content['content'],
                            generated_content.get('keywords', [])
                        )
                        
                        if success:
                            print(f"✅ تم نشر: {generated_content['title']}")
                        else:
                            print(f"❌ فشل نشر: {generated_content['title']}")
                    
                except Exception as e:
                    print(f"❌ خطأ في معالجة المقال: {e}")
                    continue
            
        except Exception as e:
            print(f"❌ خطأ في معالجة المقالات: {e}")
    
    async def run(self):
        """تشغيل البوت"""
        print("🚀 بدء تشغيل البوت...")
        
        # التهيئة
        if not await self.initialize():
            print("❌ فشل في التهيئة")
            return
        
        self.running = True
        print("✅ البوت يعمل الآن...")
        
        try:
            while self.running:
                await self.process_articles()
                
                # انتظار 30 دقيقة
                print("⏰ انتظار 30 دقيقة...")
                await asyncio.sleep(1800)  # 30 دقيقة
                
        except KeyboardInterrupt:
            print("\n🛑 تم إيقاف البوت بواسطة المستخدم")
        except Exception as e:
            print(f"❌ خطأ في تشغيل البوت: {e}")
        finally:
            self.running = False
            print("🔚 تم إيقاف البوت")

async def main():
    """الدالة الرئيسية"""
    print("="*60)
    print("🎮 وكيل أخبار الألعاب - إصدار Blogger فقط")
    print("="*60)
    print(f"⏰ الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("🌐 سيتم النشر على Blogger فقط")
    print("="*60)
    
    # تصحيح الناشر
    patch_publisher_for_blogger_only()
    
    # إنشاء وتشغيل البوت
    bot = SimpleBloggerBot()
    await bot.run()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف البرنامج")
    except Exception as e:
        print(f"❌ خطأ في البرنامج: {e}")
        import traceback
        traceback.print_exc()
