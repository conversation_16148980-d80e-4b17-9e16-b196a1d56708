#!/usr/bin/env python3
"""
تنظيف الملفات المؤقتة لتوفير مساحة وتحسين الأداء
"""

import os
import shutil
import time
from pathlib import Path

def cleanup_temp_files():
    """تنظيف الملفات المؤقتة"""
    print("🧹 بدء تنظيف الملفات المؤقتة...")
    
    temp_dirs = ["assets/temp"]
    total_deleted = 0
    total_size_freed = 0
    
    for temp_dir in temp_dirs:
        if os.path.exists(temp_dir):
            print(f"\n📁 تنظيف {temp_dir}...")
            
            # حساب الحجم قبل التنظيف
            before_size = 0
            file_count = 0
            
            for root, dirs, files in os.walk(temp_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    try:
                        before_size += os.path.getsize(file_path)
                        file_count += 1
                    except:
                        pass
            
            print(f"   📊 قبل التنظيف: {file_count} ملف ({before_size / (1024*1024):.1f} MB)")
            
            # حذف المجلد وإعادة إنشاؤه
            try:
                shutil.rmtree(temp_dir)
                os.makedirs(temp_dir, exist_ok=True)
                
                total_deleted += file_count
                total_size_freed += before_size
                
                print(f"   ✅ تم حذف {file_count} ملف")
                print(f"   💾 تم توفير {before_size / (1024*1024):.1f} MB")
                
            except Exception as e:
                print(f"   ❌ خطأ في التنظيف: {e}")
        else:
            print(f"📁 {temp_dir}: غير موجود")
    
    print(f"\n🎉 انتهى التنظيف:")
    print(f"   📁 إجمالي الملفات المحذوفة: {total_deleted}")
    print(f"   💾 إجمالي المساحة المحررة: {total_size_freed / (1024*1024):.1f} MB")

def cleanup_old_results():
    """تنظيف نتائج قديمة (اختياري)"""
    print("\n🗂️ فحص مجلد النتائج...")
    
    results_dir = "results"
    if os.path.exists(results_dir):
        video_count = 0
        total_size = 0
        
        for root, dirs, files in os.walk(results_dir):
            for file in files:
                if file.endswith('.mp4'):
                    file_path = os.path.join(root, file)
                    try:
                        total_size += os.path.getsize(file_path)
                        video_count += 1
                    except:
                        pass
        
        print(f"   📹 عدد الفيديوهات: {video_count}")
        print(f"   💾 الحجم الإجمالي: {total_size / (1024*1024):.1f} MB")
        
        if video_count > 10:
            print("   ⚠️ عدد كبير من الفيديوهات - قد تحتاج تنظيف يدوي")
        else:
            print("   ✅ عدد معقول من الفيديوهات")
    else:
        print("   📁 مجلد النتائج غير موجود")

if __name__ == "__main__":
    try:
        cleanup_temp_files()
        cleanup_old_results()
        
        print(f"\n{'='*50}")
        print("🏁 انتهى التنظيف")
        print(f"{'='*50}")
        
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف التنظيف بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في التنظيف: {e}")
