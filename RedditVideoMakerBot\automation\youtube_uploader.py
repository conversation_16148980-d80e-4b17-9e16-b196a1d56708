#!/usr/bin/env python3
"""
وحدة رفع الفيديوهات على YouTube Shorts تلقائياً
تستخدم OAuth 2.0 للمصادقة مع إدارة التوكن التلقائية
"""

import os
import json
import logging
from pathlib import Path
from typing import Optional, Dict, Any
import time
import random
from datetime import datetime, timedelta
import pickle

from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from googleapiclient.http import MediaFileUpload
from google_auth_oauthlib.flow import InstalledAppFlow
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials

# إعداد السجلات
logger = logging.getLogger(__name__)

class YouTubeTokenManager:
    """مدير التوكن لـ YouTube OAuth"""

    def __init__(self, client_secret_file: str = "client_secret.json",
                 token_file: str = "youtube_token.pickle"):
        """
        إعداد مدير التوكن

        Args:
            client_secret_file: ملف client secret من Google Cloud Console
            token_file: ملف حفظ التوكن
        """
        self.client_secret_file = client_secret_file
        self.token_file = token_file
        self.scopes = ['https://www.googleapis.com/auth/youtube.upload']
        self.credentials = None

    def load_credentials(self) -> Optional[Credentials]:
        """تحميل التوكن المحفوظ"""
        try:
            if os.path.exists(self.token_file):
                with open(self.token_file, 'rb') as token:
                    self.credentials = pickle.load(token)
                    logger.info("✅ تم تحميل التوكن المحفوظ")
                    return self.credentials
        except Exception as e:
            logger.warning(f"فشل في تحميل التوكن: {e}")
        return None

    def save_credentials(self, credentials: Credentials):
        """حفظ التوكن"""
        try:
            with open(self.token_file, 'wb') as token:
                pickle.dump(credentials, token)
            logger.info("✅ تم حفظ التوكن بنجاح")
        except Exception as e:
            logger.error(f"فشل في حفظ التوكن: {e}")

    def is_token_expired(self) -> bool:
        """التحقق من انتهاء صلاحية التوكن"""
        if not self.credentials:
            return True

        if not self.credentials.valid:
            return True

        # التحقق من انتهاء الصلاحية خلال الساعة القادمة
        if self.credentials.expiry:
            time_until_expiry = self.credentials.expiry - datetime.utcnow()
            hours_until_expiry = time_until_expiry.total_seconds() / 3600

            # إرسال تحذير إذا كان التوكن سينتهي خلال 6 ساعات
            if 1 < hours_until_expiry <= 6:
                try:
                    from .telegram_bot import send_token_expiry_warning
                    send_token_expiry_warning(int(hours_until_expiry))
                except:
                    pass

            # اعتبار التوكن منتهي إذا كان أقل من ساعة
            if hours_until_expiry < 1:
                return True

        return False

    def refresh_token(self) -> bool:
        """تجديد التوكن"""
        try:
            if self.credentials and self.credentials.refresh_token:
                self.credentials.refresh(Request())
                self.save_credentials(self.credentials)
                logger.info("✅ تم تجديد التوكن بنجاح")

                # إرسال إشعار نجاح التجديد
                try:
                    from .telegram_bot import send_token_refresh_success
                    send_token_refresh_success()
                except:
                    pass

                return True
            else:
                logger.warning("لا يوجد refresh token متاح")

                # إرسال إشعار فشل التجديد
                try:
                    from .telegram_bot import send_token_refresh_failed
                    send_token_refresh_failed("لا يوجد refresh token متاح")
                except:
                    pass

                return False
        except Exception as e:
            logger.error(f"فشل في تجديد التوكن: {e}")

            # إرسال إشعار فشل التجديد
            try:
                from .telegram_bot import send_token_refresh_failed
                send_token_refresh_failed(str(e))
            except:
                pass

            return False

    def get_authorization_url(self) -> str:
        """الحصول على رابط المصادقة"""
        try:
            flow = InstalledAppFlow.from_client_secrets_file(
                self.client_secret_file, self.scopes)
            flow.redirect_uri = 'urn:ietf:wg:oauth:2.0:oob'  # استخدام OOB بدلاً من localhost

            auth_url, _ = flow.authorization_url(
                access_type='offline',
                include_granted_scopes='true'
            )
            return auth_url
        except Exception as e:
            logger.error(f"فشل في إنشاء رابط المصادقة: {e}")
            return ""

    def exchange_code_for_token(self, authorization_code: str) -> bool:
        """تبديل كود المصادقة بالتوكن"""
        try:
            flow = InstalledAppFlow.from_client_secrets_file(
                self.client_secret_file, self.scopes)
            flow.redirect_uri = 'urn:ietf:wg:oauth:2.0:oob'  # استخدام OOB بدلاً من localhost

            flow.fetch_token(code=authorization_code)
            self.credentials = flow.credentials
            self.save_credentials(self.credentials)
            logger.info("✅ تم الحصول على التوكن بنجاح")
            return True
        except Exception as e:
            logger.error(f"فشل في تبديل الكود بالتوكن: {e}")
            return False

    def get_valid_credentials(self) -> Optional[Credentials]:
        """الحصول على تصريح صالح"""
        # تحميل التوكن المحفوظ
        self.load_credentials()

        # التحقق من صلاحية التوكن
        if self.credentials and self.credentials.valid:
            return self.credentials

        # محاولة تجديد التوكن
        if self.credentials and self.credentials.expired and self.credentials.refresh_token:
            if self.refresh_token():
                return self.credentials

        # إذا فشل كل شيء، نحتاج مصادقة جديدة
        logger.warning("⚠️ التوكن غير صالح - مطلوب مصادقة جديدة")
        return None

class YouTubeUploader:
    def __init__(self, client_secret_file: str = "client_secret.json"):
        """
        إعداد رافع YouTube

        Args:
            client_secret_file: ملف client secret من Google Cloud Console
        """
        self.client_secret_file = client_secret_file
        self.youtube = None
        self.token_manager = YouTubeTokenManager(client_secret_file)

        # إعداد الاتصال
        self._setup_youtube_service()

    def _setup_youtube_service(self):
        """إعداد خدمة YouTube API"""
        try:
            # الحصول على تصريح صالح
            credentials = self.token_manager.get_valid_credentials()

            if not credentials:
                # إرسال إشعار Telegram للحاجة لمصادقة جديدة
                self._send_auth_required_notification()
                raise Exception("مطلوب مصادقة جديدة - تحقق من رسائل Telegram")

            # إنشاء خدمة YouTube
            self.youtube = build('youtube', 'v3', credentials=credentials)
            logger.info("✅ تم إعداد خدمة YouTube API بنجاح")

        except Exception as e:
            logger.error(f"❌ فشل في إعداد خدمة YouTube API: {e}")
            raise

    def _send_auth_required_notification(self):
        """إرسال إشعار الحاجة لمصادقة جديدة"""
        try:
            from .telegram_bot import send_auth_setup_instructions

            auth_url = self.token_manager.get_authorization_url()
            send_auth_setup_instructions(auth_url)
            logger.info("تم إرسال إشعار الحاجة لمصادقة جديدة")

        except Exception as e:
            logger.error(f"فشل في إرسال إشعار المصادقة: {e}")

    def check_token_expiry(self):
        """التحقق من انتهاء صلاحية التوكن وإرسال إشعار"""
        try:
            if self.token_manager.is_token_expired():
                self._send_token_expiry_notification()

        except Exception as e:
            logger.error(f"خطأ في التحقق من انتهاء التوكن: {e}")

    def _send_token_expiry_notification(self):
        """إرسال إشعار انتهاء صلاحية التوكن"""
        try:
            from .telegram_bot import send_token_expiry_warning

            # حساب الوقت المتبقي
            hours_until_expiry = 0
            if (self.token_manager.credentials and
                self.token_manager.credentials.expiry):
                time_until_expiry = (self.token_manager.credentials.expiry -
                                   datetime.utcnow())
                hours_until_expiry = max(0, int(time_until_expiry.total_seconds() / 3600))

            send_token_expiry_warning(hours_until_expiry)
            logger.info("تم إرسال إشعار انتهاء صلاحية التوكن")

        except Exception as e:
            logger.error(f"فشل في إرسال إشعار انتهاء التوكن: {e}")
            
    def upload_video(self, video_path: str, title: str, description: str,
                    tags: list = None, category_id: str = "22") -> Optional[str]:
        """
        رفع فيديو على YouTube

        Args:
            video_path: مسار ملف الفيديو
            title: عنوان الفيديو
            description: وصف الفيديو
            tags: قائمة الكلمات المفتاحية
            category_id: معرف الفئة (22 = People & Blogs)

        Returns:
            رابط الفيديو أو None في حالة الفشل
        """
        try:
            # التحقق من صلاحية التوكن قبل الرفع
            self.check_token_expiry()

            if not os.path.exists(video_path):
                raise FileNotFoundError(f"ملف الفيديو غير موجود: {video_path}")

            # التأكد من أن الخدمة متاحة
            if not self.youtube:
                self._setup_youtube_service()

            # إعداد metadata الفيديو
            body = {
                'snippet': {
                    'title': title[:100],  # YouTube يحدد العنوان بـ 100 حرف
                    'description': description[:5000],  # الوصف محدود بـ 5000 حرف
                    'tags': tags[:500] if tags else [],  # حد أقصى 500 كلمة مفتاحية
                    'categoryId': category_id,
                    'defaultLanguage': 'ar',
                    'defaultAudioLanguage': 'ar'
                },
                'status': {
                    'privacyStatus': 'public',  # عام
                    'selfDeclaredMadeForKids': False,
                    'embeddable': True,
                    'license': 'youtube',
                    'publicStatsViewable': True
                }
            }
            
            # إعداد رفع الملف
            media = MediaFileUpload(
                video_path,
                chunksize=-1,
                resumable=True,
                mimetype='video/mp4'
            )
            
            logger.info(f"🚀 بدء رفع الفيديو: {os.path.basename(video_path)}")
            
            # تنفيذ الرفع
            insert_request = self.youtube.videos().insert(
                part=','.join(body.keys()),
                body=body,
                media_body=media
            )
            
            # رفع مع إعادة المحاولة
            video_id = self._resumable_upload(insert_request)
            
            if video_id:
                video_url = f"https://www.youtube.com/watch?v={video_id}"
                logger.info(f"✅ تم رفع الفيديو بنجاح: {video_url}")
                
                # إعداد الفيديو كـ Short إذا كان أقل من 60 ثانية
                self._set_as_short(video_id)
                
                return video_url
            else:
                logger.error("❌ فشل في رفع الفيديو")
                return None
                
        except HttpError as e:
            logger.error(f"❌ خطأ HTTP في رفع الفيديو: {e}")
            return None
        except Exception as e:
            logger.error(f"❌ خطأ عام في رفع الفيديو: {e}")
            return None
            
    def _resumable_upload(self, insert_request, max_retries: int = 3):
        """رفع قابل للاستئناف مع إعادة المحاولة"""
        response = None
        error = None
        retry = 0
        
        while response is None:
            try:
                status, response = insert_request.next_chunk()
                if response is not None:
                    if 'id' in response:
                        return response['id']
                    else:
                        raise Exception(f"رد غير متوقع من YouTube API: {response}")
                        
            except HttpError as e:
                if e.resp.status in [500, 502, 503, 504]:
                    # أخطاء خادم قابلة للإعادة
                    error = f"خطأ خادم قابل للإعادة: {e}"
                    retry += 1
                    if retry > max_retries:
                        logger.error(f"تم تجاوز عدد المحاولات المسموح: {error}")
                        return None
                        
                    # انتظار متزايد
                    wait_time = (2 ** retry) + random.uniform(0, 1)
                    logger.warning(f"إعادة المحاولة {retry}/{max_retries} بعد {wait_time:.1f} ثانية...")
                    time.sleep(wait_time)
                else:
                    # خطأ غير قابل للإعادة
                    logger.error(f"خطأ غير قابل للإعادة: {e}")
                    return None
                    
            except Exception as e:
                logger.error(f"خطأ غير متوقع في الرفع: {e}")
                return None
                
        return None
        
    def _set_as_short(self, video_id: str):
        """تعيين الفيديو كـ YouTube Short"""
        try:
            # للأسف، لا يوجد API مباشر لتعيين الفيديو كـ Short
            # YouTube يحدد ذلك تلقائياً بناءً على المدة والأبعاد
            # لكن يمكننا إضافة #Shorts في الوصف
            logger.info(f"📱 الفيديو {video_id} سيظهر كـ Short تلقائياً إذا كان أقل من 60 ثانية")
            
        except Exception as e:
            logger.warning(f"تحذير: فشل في تعيين الفيديو كـ Short: {e}")
            
    def get_channel_info(self) -> Dict[str, Any]:
        """الحصول على معلومات القناة"""
        try:
            request = self.youtube.channels().list(
                part="snippet,statistics",
                mine=True
            )
            response = request.execute()
            
            if response['items']:
                channel = response['items'][0]
                return {
                    'id': channel['id'],
                    'title': channel['snippet']['title'],
                    'description': channel['snippet']['description'],
                    'subscriber_count': channel['statistics'].get('subscriberCount', 0),
                    'video_count': channel['statistics'].get('videoCount', 0),
                    'view_count': channel['statistics'].get('viewCount', 0)
                }
            else:
                return {}
                
        except Exception as e:
            logger.error(f"خطأ في الحصول على معلومات القناة: {e}")
            return {}

# دالة مساعدة للاستخدام السهل
def upload_to_youtube(video_path: str, title: str = None, description: str = None,
                     tags: list = None) -> Optional[str]:
    """
    دالة مساعدة لرفع فيديو على YouTube

    Args:
        video_path: مسار ملف الفيديو
        title: عنوان الفيديو (اختياري)
        description: وصف الفيديو (اختياري)
        tags: كلمات مفتاحية (اختياري)

    Returns:
        رابط الفيديو أو None
    """
    try:
        uploader = YouTubeUploader()

        # استخدام عنوان افتراضي إذا لم يتم تمريره
        if not title:
            video_name = Path(video_path).stem
            title = f"Reddit Story - {video_name}"

        # استخدام وصف افتراضي إذا لم يتم تمريره
        if not description:
            description = "قصة مثيرة من Reddit\n\n#Shorts #Reddit #قصص"

        # كلمات مفتاحية افتراضية
        if not tags:
            tags = ["reddit", "shorts", "قصص", "مثير", "ترفيه"]

        return uploader.upload_video(video_path, title, description, tags)

    except Exception as e:
        logger.error(f"خطأ في رفع الفيديو: {e}")
        # إرسال إشعار خطأ عبر Telegram
        try:
            from .telegram_bot import send_error
            send_error("فشل في رفع الفيديو على YouTube", str(e))
        except:
            pass
        return None

if __name__ == "__main__":
    # اختبار الوحدة
    test_video = "test_video.mp4"
    if os.path.exists(test_video):
        result = upload_to_youtube(test_video)
        print(f"نتيجة الرفع: {result}")
    else:
        print("ملف الاختبار غير موجود")
