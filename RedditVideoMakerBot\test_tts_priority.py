#!/usr/bin/env python3
"""
اختبار ترتيب أولوية محركات TTS
"""

import os
import sys
import logging

# إعداد المسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# إعداد logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_tts_priority_order():
    """اختبار ترتيب أولوية محركات TTS"""
    try:
        from TTS.smart_tts_manager import SmartTTSManager
        from utils import settings
        
        print("🔍 اختبار ترتيب أولوية محركات TTS...")
        
        # إنشاء instance من SmartTTSManager
        tts_manager = SmartTTSManager()
        
        # الحصول على ترتيب الأولوية
        priority_engines = tts_manager._get_priority_engines()
        
        print(f"📋 ترتيب الأولوية الحالي: {priority_engines}")
        
        # التحقق من الإعدادات
        voice_choice = settings.config["settings"]["tts"]["voice_choice"]
        primary_order = settings.config["settings"]["tts"]["priority_order"]["primary"]
        
        print(f"⚙️ voice_choice: {voice_choice}")
        print(f"📝 primary_order في config: {primary_order}")
        
        # التحقق من أن GoogleTranslate أولاً
        if priority_engines[0] == "GoogleTranslate":
            print("✅ GoogleTranslate في المقدمة - صحيح!")
        else:
            print(f"❌ المحرك الأول هو {priority_engines[0]} بدلاً من GoogleTranslate")
        
        # التحقق من أن ElevenLabs أخيراً أو ليس أولاً
        if priority_engines[0] != "ElevenLabs":
            print("✅ ElevenLabs ليس أولاً - صحيح!")
        else:
            print("❌ ElevenLabs لا يزال أولاً")
        
        return priority_engines[0] == "GoogleTranslate"
        
    except Exception as e:
        print(f"❌ خطأ في اختبار ترتيب الأولوية: {e}")
        return False

def test_config_settings():
    """اختبار إعدادات config.toml"""
    try:
        from utils import settings
        
        print("\n🔍 اختبار إعدادات config.toml...")
        
        voice_choice = settings.config["settings"]["tts"]["voice_choice"]
        primary_order = settings.config["settings"]["tts"]["priority_order"]["primary"]
        
        print(f"voice_choice: {voice_choice}")
        print(f"primary_order: {primary_order}")
        
        # التحقق من أن voice_choice = "auto"
        if voice_choice == "auto":
            print("✅ voice_choice = 'auto' - صحيح!")
        else:
            print(f"❌ voice_choice = '{voice_choice}' - يجب أن يكون 'auto'")
        
        # التحقق من أن GoogleTranslate أولاً في primary_order
        if primary_order[0] == "GoogleTranslate":
            print("✅ GoogleTranslate أولاً في primary_order - صحيح!")
        else:
            print(f"❌ المحرك الأول في primary_order هو {primary_order[0]}")
        
        # التحقق من أن ElevenLabs أخيراً في primary_order
        if primary_order[-1] == "ElevenLabs":
            print("✅ ElevenLabs أخيراً في primary_order - صحيح!")
        else:
            print(f"❌ المحرك الأخير في primary_order هو {primary_order[-1]}")
        
        return (voice_choice == "auto" and 
                primary_order[0] == "GoogleTranslate" and 
                primary_order[-1] == "ElevenLabs")
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الإعدادات: {e}")
        return False

def test_elevenlabs_import():
    """اختبار أن ElevenLabs يستورد os بشكل صحيح"""
    try:
        print("\n🔍 اختبار استيراد os في ElevenLabs...")
        
        from TTS.elevenlabs import elevenlabs

        # محاولة إنشاء instance
        elevenlabs_instance = elevenlabs()
        
        print("✅ تم استيراد ElevenLabs بنجاح")
        print("✅ لا توجد مشكلة في استيراد os")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في استيراد ElevenLabs: {e}")
        return False

def main():
    """تشغيل جميع الاختبارات"""
    print("🚀 اختبار ترتيب أولوية محركات TTS...")
    print("=" * 60)
    
    tests = [
        ("إعدادات config.toml", test_config_settings),
        ("ترتيب أولوية محركات TTS", test_tts_priority_order),
        ("استيراد ElevenLabs", test_elevenlabs_import),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 40)
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} - خطأ غير متوقع: {e}")
            results.append((test_name, False))
    
    # تلخيص النتائج
    print("\n" + "=" * 60)
    print("📊 ملخص نتائج الاختبارات:")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n📈 النتيجة النهائية: {passed}/{len(results)} اختبارات نجحت")
    
    if passed == len(results):
        print("🎉 تم تعديل ترتيب الأولوية بنجاح!")
        print("\n💡 الآن سيتم استخدام:")
        print("   1️⃣ GoogleTranslate أولاً")
        print("   2️⃣ AWSPolly ثانياً") 
        print("   3️⃣ pyttsx ثالثاً")
        print("   4️⃣ ElevenLabs أخيراً")
        print("\n🚀 يمكنك تشغيل النظام الآن:")
        print("   python main.py")
    else:
        print("⚠️ بعض الإعدادات تحتاج مراجعة إضافية")
    
    return passed == len(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
