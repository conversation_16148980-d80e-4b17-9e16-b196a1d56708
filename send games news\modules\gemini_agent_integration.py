# نظام التكامل المحسن باستخدام Gemini 2.5 Pro بدلاً من RAG
import asyncio
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from enum import Enum

from .logger import logger
from .gemini_enhanced_system import (
    gemini_enhanced_system, 
    GeminiAnalysisRequest, 
    AnalysisMode, 
    ContentType
)
from .multimodal_analyzer import multimodal_analyzer, MediaAnalysisRequest, MediaType
from .memory_system import memory_system, Memory, MemoryType, MemoryImportance

class EnhancementLevel(Enum):
    """مستويات التحسين"""
    BASIC = "basic"
    ADVANCED = "advanced"
    PREMIUM = "premium"

@dataclass
class GeminiEnhancedAnalysisResult:
    """نتيجة التحليل المحسن باستخدام Gemini"""
    content: str
    confidence: float
    gemini_results: Dict[str, Any]
    multimodal_results: List[Any]
    memory_insights: Dict[str, Any]
    enhancement_level: EnhancementLevel
    processing_time: float
    created_at: datetime = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()

class GeminiAgentIntegration:
    """نظام التكامل المحسن باستخدام Gemini 2.5 Pro"""
    
    def __init__(self):
        self.enabled = True
        self.enhancement_level = EnhancementLevel.ADVANCED
        
        # إحصائيات التحسينات
        self.enhancement_stats = {
            'total_enhanced_analyses': 0,
            'gemini_usage_count': 0,
            'multimodal_usage_count': 0,
            'memory_usage_count': 0,
            'avg_enhancement_time': 0,
            'success_rate': 0,
            'last_update': datetime.now()
        }
        
        # إعدادات التحسين
        self.config = {
            'enable_gemini': True,
            'enable_multimodal': True,
            'enable_advanced_memory': True,
            'auto_memory_storage': True,
            'content_quality_threshold': 0.7,
            'multimodal_confidence_threshold': 0.6,
            'gemini_confidence_threshold': 0.7
        }
        
        logger.info("🚀 تم تهيئة نظام التكامل المحسن باستخدام Gemini")
    
    async def enhance_content_analysis(self, content: str, content_type: str = "text", 
                                     media_path: Optional[str] = None) -> GeminiEnhancedAnalysisResult:
        """تحليل محتوى محسن باستخدام Gemini والأنظمة المتقدمة"""
        try:
            start_time = time.time()
            self.enhancement_stats['total_enhanced_analyses'] += 1
            
            logger.info(f"🔍 بدء التحليل المحسن باستخدام Gemini: {content[:100]}...")
            
            # 1. تحليل Gemini للسياق والمعلومات ذات الصلة
            gemini_results = {}
            if self.config['enable_gemini'] and gemini_enhanced_system.enabled:
                gemini_results = await self._perform_gemini_analysis(content, content_type)
                self.enhancement_stats['gemini_usage_count'] += 1
            
            # 2. تحليل متعدد الوسائط إذا توفرت وسائط
            multimodal_results = []
            if (self.config['enable_multimodal'] and media_path and 
                multimodal_analyzer.enabled):
                multimodal_results = await self._perform_multimodal_analysis(media_path)
                self.enhancement_stats['multimodal_usage_count'] += 1
            
            # 3. استرجاع رؤى الذاكرة
            memory_insights = {}
            if self.config['enable_advanced_memory']:
                memory_insights = await self._get_memory_insights(content)
                self.enhancement_stats['memory_usage_count'] += 1
            
            # 4. دمج النتائج وحساب الثقة الإجمالية
            enhanced_content, confidence = await self._merge_analysis_results(
                content, gemini_results, multimodal_results, memory_insights)
            
            # 5. حفظ في الذاكرة إذا كانت الجودة عالية
            if (self.config['auto_memory_storage'] and 
                confidence >= self.config['content_quality_threshold']):
                await self._store_in_memory(enhanced_content, gemini_results, confidence)
            
            # حساب وقت المعالجة
            processing_time = time.time() - start_time
            self.enhancement_stats['avg_enhancement_time'] = (
                (self.enhancement_stats['avg_enhancement_time'] * 
                 (self.enhancement_stats['total_enhanced_analyses'] - 1) + processing_time) 
                / self.enhancement_stats['total_enhanced_analyses']
            )
            
            logger.info(f"✅ تم التحليل المحسن بنجاح في {processing_time:.2f}s (ثقة: {confidence:.2f})")
            
            return GeminiEnhancedAnalysisResult(
                content=enhanced_content,
                confidence=confidence,
                gemini_results=gemini_results,
                multimodal_results=multimodal_results,
                memory_insights=memory_insights,
                enhancement_level=self.enhancement_level,
                processing_time=processing_time
            )
            
        except Exception as e:
            logger.error(f"❌ خطأ في التحليل المحسن: {e}")
            return GeminiEnhancedAnalysisResult(
                content=content,
                confidence=0.5,
                gemini_results={},
                multimodal_results=[],
                memory_insights={},
                enhancement_level=EnhancementLevel.BASIC,
                processing_time=0
            )
    
    async def _perform_gemini_analysis(self, content: str, content_type: str) -> Dict[str, Any]:
        """تنفيذ تحليل Gemini"""
        try:
            # تحديد نوع المحتوى
            gemini_content_type = self._map_content_type(content_type)
            
            # تحليل شامل للمحتوى
            content_analysis_request = GeminiAnalysisRequest(
                content=content,
                mode=AnalysisMode.CONTENT_ANALYSIS,
                content_type=gemini_content_type,
                max_output_length=2000
            )
            
            content_analysis = await gemini_enhanced_system.analyze_content(content_analysis_request)
            
            # استخراج المعلومات
            info_extraction_request = GeminiAnalysisRequest(
                content=content,
                mode=AnalysisMode.INFORMATION_EXTRACTION,
                content_type=gemini_content_type,
                max_output_length=1500
            )
            
            info_extraction = await gemini_enhanced_system.analyze_content(info_extraction_request)
            
            # خبرة الألعاب إذا كان المحتوى متعلق بالألعاب
            gaming_analysis = None
            if 'game' in content.lower() or 'gaming' in content.lower():
                gaming_request = GeminiAnalysisRequest(
                    content=content,
                    mode=AnalysisMode.GAMING_EXPERTISE,
                    content_type=ContentType.GAMING_NEWS,
                    max_output_length=1500
                )
                gaming_analysis = await gemini_enhanced_system.analyze_content(gaming_request)
            
            # دمج النتائج
            combined_results = {
                'content_analysis': content_analysis,
                'information_extraction': info_extraction,
                'gaming_expertise': gaming_analysis,
                'overall_confidence': (content_analysis.confidence_score + info_extraction.confidence_score) / 2
            }
            
            logger.debug(f"🤖 Gemini: تم التحليل بثقة {combined_results['overall_confidence']:.2f}")
            return combined_results
            
        except Exception as e:
            logger.error(f"❌ خطأ في تحليل Gemini: {e}")
            return {}
    
    def _map_content_type(self, content_type: str) -> ContentType:
        """تحويل نوع المحتوى إلى تنسيق Gemini"""
        mapping = {
            'أخبار_عامة': ContentType.NEWS_ARTICLE,
            'مراجعة_لعبة': ContentType.GAME_REVIEW,
            'أخبار_ألعاب': ContentType.GAMING_NEWS,
            'معلومات_تقنية': ContentType.TECHNICAL_INFO,
            'text': ContentType.TEXT
        }
        return mapping.get(content_type, ContentType.TEXT)
    
    async def _perform_multimodal_analysis(self, media_path: str) -> List[Any]:
        """تنفيذ التحليل متعدد الوسائط"""
        try:
            # تحديد نوع الوسائط
            media_type = self._determine_media_type(media_path)
            
            # إنشاء طلب التحليل
            from .multimodal_analyzer import AnalysisType

            analysis_request = MediaAnalysisRequest(
                media_path=media_path,
                media_type=media_type,
                analysis_types=[
                    AnalysisType.OCR,
                    AnalysisType.SCENE_DESCRIPTION,
                    AnalysisType.OBJECT_DETECTION
                ],
                enhance_quality=True,
                extract_gaming_content=True
            )
            
            # تنفيذ التحليل
            results = await multimodal_analyzer.analyze_media(analysis_request)
            
            # فلترة النتائج عالية الثقة
            high_confidence_results = [
                r for r in results 
                if r.confidence >= self.config['multimodal_confidence_threshold']
            ]
            
            logger.debug(f"🖼️ Multimodal: تم تحليل {len(results)} عنصر، {len(high_confidence_results)} عالي الثقة")
            return high_confidence_results
            
        except Exception as e:
            logger.error(f"❌ خطأ في التحليل متعدد الوسائط: {e}")
            return []
    
    def _determine_media_type(self, media_path: str) -> MediaType:
        """تحديد نوع الوسائط"""
        extension = media_path.lower().split('.')[-1]
        
        if extension in ['jpg', 'jpeg', 'png', 'gif', 'bmp']:
            return MediaType.IMAGE
        elif extension in ['mp4', 'avi', 'mov', 'mkv']:
            return MediaType.VIDEO
        elif extension in ['mp3', 'wav', 'ogg']:
            return MediaType.AUDIO
        else:
            return MediaType.IMAGE  # افتراضي

    async def _get_memory_insights(self, content: str) -> Dict[str, Any]:
        """الحصول على رؤى الذاكرة"""
        try:
            # البحث في الذاكرة عن محتوى مشابه
            related_memories = await memory_system.smart_memory_retrieval(
                query=content,
                max_results=5,
                use_relations=True
            )

            # تحليل الذكريات المرتبطة
            insights = {
                'related_count': len(related_memories),
                'related_memories': [],
                'patterns': [],
                'recommendations': []
            }

            for memory in related_memories:
                insights['related_memories'].append({
                    'content': memory.content[:200],
                    'importance': memory.importance.value,
                    'created_at': memory.created_at.isoformat(),
                    'similarity': memory.metadata.get('similarity_score', 0.5)
                })

            # استخراج الأنماط
            if len(related_memories) >= 2:
                insights['patterns'].append("محتوى مشابه موجود في الذاكرة")
                insights['recommendations'].append("يمكن الاستفادة من المحتوى السابق")

            logger.debug(f"🧠 Memory: تم العثور على {len(related_memories)} ذكريات مرتبطة")
            return insights

        except Exception as e:
            logger.error(f"❌ خطأ في استرجاع رؤى الذاكرة: {e}")
            return {}

    async def _merge_analysis_results(self, original_content: str, gemini_results: Dict[str, Any],
                                    multimodal_results: List[Any], memory_insights: Dict[str, Any]) -> Tuple[str, float]:
        """دمج نتائج التحليل"""
        try:
            enhanced_content = original_content
            confidence_scores = []

            # دمج نتائج Gemini
            if gemini_results:
                overall_confidence = gemini_results.get('overall_confidence', 0.7)
                confidence_scores.append(overall_confidence)

                # إضافة رؤى من تحليل المحتوى
                content_analysis = gemini_results.get('content_analysis')
                if content_analysis and content_analysis.key_insights:
                    enhanced_content += "\n\n**رؤى إضافية:**\n"
                    for insight in content_analysis.key_insights[:3]:
                        enhanced_content += f"- {insight}\n"

                # إضافة معلومات مستخرجة
                info_extraction = gemini_results.get('information_extraction')
                if info_extraction and info_extraction.extracted_info:
                    games = info_extraction.extracted_info.get('games_mentioned', [])
                    if games:
                        enhanced_content += f"\n**الألعاب المذكورة:** {', '.join(games[:5])}\n"

            # دمج نتائج متعددة الوسائط
            if multimodal_results:
                multimodal_confidence = sum(r.confidence for r in multimodal_results) / len(multimodal_results)
                confidence_scores.append(multimodal_confidence)

                enhanced_content += "\n\n**تحليل الوسائط:**\n"
                for result in multimodal_results[:2]:
                    enhanced_content += f"- {result.description}\n"

            # دمج رؤى الذاكرة
            if memory_insights and memory_insights.get('related_count', 0) > 0:
                confidence_scores.append(0.8)  # ثقة عالية للذاكرة

                if memory_insights.get('recommendations'):
                    enhanced_content += "\n\n**توصيات من الذاكرة:**\n"
                    for rec in memory_insights['recommendations'][:2]:
                        enhanced_content += f"- {rec}\n"

            # حساب الثقة الإجمالية
            overall_confidence = sum(confidence_scores) / len(confidence_scores) if confidence_scores else 0.5

            return enhanced_content, overall_confidence

        except Exception as e:
            logger.error(f"❌ خطأ في دمج النتائج: {e}")
            return original_content, 0.5

    async def _store_in_memory(self, content: str, gemini_results: Dict[str, Any], confidence: float):
        """حفظ المحتوى في الذاكرة"""
        try:
            # تحديد أهمية الذاكرة بناءً على الثقة
            if confidence >= 0.9:
                importance = MemoryImportance.HIGH
            elif confidence >= 0.7:
                importance = MemoryImportance.MEDIUM
            else:
                importance = MemoryImportance.LOW

            # إنشاء ذاكرة جديدة
            memory = Memory(
                id=f"gemini_analysis_{int(time.time())}",
                content=content[:1000],  # حد أقصى للطول
                memory_type=MemoryType.ARTICLE,  # استخدام ARTICLE بدلاً من CONTENT_ANALYSIS
                importance=importance,
                metadata={
                    'confidence': confidence,
                    'gemini_analysis': True,
                    'source': 'gemini_enhanced_analysis'
                }
            )

            # حفظ في نظام الذاكرة
            await memory_system.store_memory(memory)
            logger.debug(f"💾 تم حفظ المحتوى في الذاكرة بأهمية {importance.value}")

        except Exception as e:
            logger.error(f"❌ خطأ في حفظ الذاكرة: {e}")

    async def search_similar_content(self, query: str, max_results: int = 5) -> List[Dict[str, Any]]:
        """البحث عن محتوى مشابه باستخدام Gemini"""
        try:
            # استخدام Gemini للبحث السياقي
            similar_content = await gemini_enhanced_system.search_similar_content(query, max_results)

            # إضافة نتائج من الذاكرة
            memory_results = await memory_system.smart_memory_retrieval(
                query=query,
                max_results=max_results,
                use_relations=True
            )

            # دمج النتائج
            for memory in memory_results:
                similar_content.append({
                    'content': memory.content,
                    'similarity_score': memory.metadata.get('similarity_score', 0.7),
                    'source': 'memory_system',
                    'type': 'stored_memory',
                    'importance': memory.importance.value
                })

            # ترتيب حسب النقاط
            similar_content.sort(key=lambda x: x['similarity_score'], reverse=True)
            return similar_content[:max_results]

        except Exception as e:
            logger.error(f"❌ فشل في البحث عن محتوى مشابه: {e}")
            return []

    async def extract_gaming_entities(self, content: str) -> List[str]:
        """استخراج كيانات الألعاب من المحتوى"""
        try:
            return await gemini_enhanced_system.extract_gaming_entities(content)
        except Exception as e:
            logger.error(f"❌ فشل في استخراج الكيانات: {e}")
            return []

    async def get_enhancement_stats(self) -> Dict[str, Any]:
        """الحصول على إحصائيات التحسين"""
        try:
            gemini_stats = await gemini_enhanced_system.get_stats()

            return {
                **self.enhancement_stats,
                'gemini_stats': gemini_stats,
                'success_rate': (self.enhancement_stats['total_enhanced_analyses'] -
                               self.enhancement_stats.get('failed_analyses', 0)) /
                               max(1, self.enhancement_stats['total_enhanced_analyses']) * 100,
                'enabled': self.enabled
            }
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على الإحصائيات: {e}")
            return self.enhancement_stats

    async def clear_cache(self):
        """مسح جميع التخزين المؤقت"""
        try:
            await gemini_enhanced_system.clear_cache()
            logger.info("🧹 تم مسح تخزين النظام المحسن المؤقت")
        except Exception as e:
            logger.error(f"❌ خطأ في مسح التخزين المؤقت: {e}")

# إنشاء مثيل عام
gemini_agent_integration = GeminiAgentIntegration()
