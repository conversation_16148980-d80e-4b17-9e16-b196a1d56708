# -*- coding: utf-8 -*-
"""
اختبار مبسط للنظام الجديد
"""

def test_imports():
    """اختبار استيراد الوحدات"""
    try:
        print("🔍 اختبار استيراد الوحدات...")
        
        # اختبار التكوين
        from config.new_image_apis_config import new_image_apis_config
        print("✅ تم استيراد التكوين")
        
        # اختبار المولد
        from modules.advanced_image_generator import advanced_image_generator
        print("✅ تم استيراد المولد المتقدم")
        
        # فحص APIs المفعلة
        enabled_apis = new_image_apis_config.get_enabled_apis()
        print(f"📊 APIs مفعلة: {len(enabled_apis)}")
        
        for api in enabled_apis:
            status = "🟢" if api.api_key else "🔴"
            print(f"  {status} {api.name}")
        
        if len(enabled_apis) == 0:
            print("💡 لا توجد APIs مفعلة - أضف مفاتيح في .env")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        return False

def test_configuration():
    """اختبار التكوين"""
    try:
        print("\n📋 اختبار التكوين...")
        
        from config.new_image_apis_config import new_image_apis_config
        
        # فحص الإعدادات العامة
        settings = new_image_apis_config.general_settings
        print(f"✅ العرض الافتراضي: {settings['default_width']}")
        print(f"✅ الارتفاع الافتراضي: {settings['default_height']}")
        print(f"✅ التخزين المؤقت: {'مفعل' if settings['enable_caching'] else 'معطل'}")
        
        # فحص APIs
        for api_name, api_config in new_image_apis_config.apis.items():
            print(f"📡 {api_config.name}:")
            print(f"  - مفعل: {api_config.enabled}")
            print(f"  - مفتاح متوفر: {'نعم' if api_config.api_key else 'لا'}")
            print(f"  - الأولوية: {api_config.priority}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في التكوين: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🎨 اختبار مبسط للنظام الجديد")
    print("=" * 40)
    
    # اختبار الاستيراد
    if not test_imports():
        return False
    
    # اختبار التكوين
    if not test_configuration():
        return False
    
    print("\n" + "=" * 40)
    print("✅ جميع الاختبارات نجحت!")
    print("📖 راجع NEW_IMAGE_APIS_GUIDE.md للتفاصيل")
    print("🚀 استخدم test_new_image_apis.py للاختبار الشامل")
    
    return True

if __name__ == "__main__":
    main()
